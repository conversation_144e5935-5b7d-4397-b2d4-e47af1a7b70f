"""
Copyright (C) 2024 Interactive Brokers LLC. All rights reserved. This code is subject to the terms
 and conditions of the IB API Non-Commercial License or the IB API Commercial License, as applicable.
"""


from ibapi.order import (OrderComboLeg, Order, COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID)
from ibapi.common import * # @UnusedWildImport
from ibapi.tag_value import TagValue
from ibapi import order_condition
from ibapi.order_condition import * # @UnusedWildImport
from ibapi.order_cancel import OrderCancel
from decimal import Decimal


class OrderSamples:

    """ <summary>
    #/ An auction order is entered into the electronic trading system during the pre-market opening period for execution at the 
    #/ Calculated Opening Price (COP). If your order is not filled on the open, the order is re-submitted as a limit order with 
    #/ the limit price set to the COP or the best bid/ask after the market opens.
    #/ Products: FUT, STK
    #/ 
    #/ 拍卖单在盘前开盘期间进入电子交易系统，以在计算开盘价（COP）执行。如果您的订单未在开盘时成交，订单将作为限价单重新提交，限价设为COP或开盘后最佳买/卖价。
    #/ 产品：期货，股票
    </summary>"""
    @staticmethod
    def AtAuction(action:str, quantity:Decimal, price:float):
        #! [auction]
        order = Order()
        order.action = action
        order.tif = "AUC"
        order.orderType = "MTL"
        order.totalQuantity = quantity
        order.lmtPrice = price
        #! [auction]
        return order

    """ <summary>
    #/ A Discretionary order is a limit order submitted with a hidden, specified 'discretionary' amount off the limit price which
    #/ may be used to increase the price range over which the limit order is eligible to execute. The market sees only the limit price.
    #/ Products: STK
    #/ 
    #/ 斟酌单是一种限价单，提交时带有隐藏的“斟酌”金额，可扩大限价单的可成交价格区间。市场只看到限价。
    #/ 产品：股票
    </summary>"""
    @staticmethod
    def Discretionary(action:str, quantity:Decimal, price:float, discretionaryAmount:float):
    
        #! [discretionary]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = price
        order.discretionaryAmt = discretionaryAmount
        #! [discretionary]
        return order
    

    """ <summary>
    #/ A Market order is an order to buy or sell at the market bid or offer price. A market order may increase the likelihood of a fill 
    #/ and the speed of execution, but unlike the Limit order a Market order provides no price protection and may fill at a price far 
    #/ lower/higher than the current displayed bid/ask.
    #/ Products: BOND, CFD, EFP, CASH, FUND, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 市价单是以市场买价或卖价买入或卖出的订单。市价单可能提高成交概率和速度，但不像限价单那样有价格保护，可能以远高于/低于当前显示买/卖价的价格成交。
    #/ 产品：债券、差价合约、EFP、现金、基金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def MarketOrder(action:str, quantity:Decimal):
    
        #! [market]
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        #! [market]
        return order
    

    """ <summary>
    #/ A Market if Touched (MIT) is an order to buy (or sell) a contract below (or above) the market. Its purpose is to take advantage 
    #/ of sudden or unexpected changes in share or other prices and provides investors with a trigger price to set an order in motion. 
    #/ Investors may be waiting for excessive strength (or weakness) to cease, which might be represented by a specific price point. 
    #/ MIT orders can be used to determine whether or not to enter the market once a specific price level has been achieved. This order 
    #/ is held in the system until the trigger price is touched, and is then submitted as a market order. An MIT order is similar to a 
    #/ stop order, except that an MIT sell order is placed above the current market price, and a stop sell order is placed below
    #/ Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 触发市价单（MIT）是在市场价下方（买入）或上方（卖出）挂单，目的是利用价格的突然或意外变动，为投资者提供触发价。订单在触发价被触及前一直保留，触发后以市价单提交。MIT卖单挂在市价上方，止损卖单挂在下方。
    #/ 产品：债券、差价合约、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def MarketIfTouched(action:str, quantity:Decimal, price:float):
    
        #! [market_if_touched]
        order = Order()
        order.action = action
        order.orderType = "MIT"
        order.totalQuantity = quantity
        order.auxPrice = price
        #! [market_if_touched]
        return order
    

    """ <summary>
    #/ A Market-on-Close (MOC) order is a market order that is submitted to execute as close to the closing price as possible.
    #/ Products: CFD, FUT, STK, WAR
    #/ 
    #/ 收盘市价单（MOC）是在收盘价附近尽量成交的市价单。
    #/ 产品：差价合约、期货、股票、权证
    </summary>"""
    @staticmethod
    def MarketOnClose(action:str, quantity:Decimal):
    
        #! [market_on_close]
        order = Order()
        order.action = action
        order.orderType = "MOC"
        order.totalQuantity = quantity
        #! [market_on_close]
        return order
    

    """ <summary>
    #/ A Market-on-Open (MOO) order combines a market order with the OPG time in force to create an order that is automatically
    #/ submitted at the market's open and fills at the market price.
    #/ Products: CFD, STK, OPT, WAR
    #/ 
    #/ 开盘市价单（MOO）结合市价单和OPG时效，在开盘时自动提交并以市价成交。
    #/ 产品：差价合约、股票、期权、权证
    </summary>"""
    @staticmethod
    def MarketOnOpen(action:str, quantity:Decimal):
    
        #! [market_on_open]
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        order.tif = "OPG"
        #! [market_on_open]
        return order
    

    """ <summary>
    #/ ISE MidpoMatch:int (MPM) orders always execute at the midpoof:the:int NBBO. You can submit market and limit orders direct-routed 
    #/ to ISE for MPM execution. Market orders execute at the midpowhenever:an:int eligible contra-order is available. Limit orders 
    #/ execute only when the midpoprice:is:int better than the limit price. Standard MPM orders are completely anonymous.
    #/ Products: STK
    #/ 
    #/ ISE中点撮合（MPM）订单总是在NBBO中点成交。市价单在有合适对手单时以中点成交，限价单仅在中点优于限价时成交，完全匿名。
    #/ 产品：股票
    </summary>"""
    @staticmethod
    def MidpointMatch(action:str, quantity:Decimal):
    
        #! [midpoint_match]
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        #! [midpoint_match]
        return order
    

    """ <summary>
	#/ A Midprice order is designed to split the difference between the bid and ask prices, and fill at the current midpoint of 
	#/ the NBBO or better. Set an optional price cap to define the highest price (for a buy order) or the lowest price (for a sell 
	#/ order) you are willing to accept. Requires TWS 975+. Smart-routing to US stocks only.
    #/ 
    #/ 中间价订单旨在以买卖价差的中点或更优价格成交。可设置价格上限（买单）或下限（卖单）。仅支持美股智能路由，需TWS 975+。
    </summary>"""
    @staticmethod
    def Midprice(action:str, quantity:Decimal, priceCap:float):
    
        #! [midprice]
        order = Order()
        order.action = action
        order.orderType = "MIDPRICE"
        order.totalQuantity = quantity
        order.lmtPrice = priceCap # optional
        #! [midprice]
        return order

    """ <summary>
    #/ A pegged-to-market order is designed to maintain a purchase price relative to the national best offer (NBO) or a sale price 
    #/ relative to the national best bid (NBB). Depending on the width of the quote, this order may be passive or aggressive. 
    #/ The trader creates the order by entering a limit price which defines the worst limit price that they are willing to accept. 
    #/ Next, the trader enters an offset amount which computes the active limit price as follows:
    #/     Sell order price = Bid price + offset amount
    #/     Buy order price = Ask price - offset amount
    #/ Products: STK
    #/ 
    #/ 市价挂钩单旨在保持买入价相对于全国最佳卖价（NBO）或卖出价相对于全国最佳买价（NBB）的关系。根据报价宽度，订单可能是被动或主动的。输入限价定义最差成交价，再输入偏移量，主动限价=买单：卖价-偏移，卖单：买价+偏移。
    #/ 产品：股票
    </summary>"""
    @staticmethod
    def PeggedToMarket(action:str, quantity:Decimal, marketOffset:float):
    
        #! [pegged_market]
        order = Order()
        order.action = action
        order.orderType = "PEG MKT"
        order.totalQuantity = quantity
        order.auxPrice = marketOffset#Offset price
        #! [pegged_market]
        return order
    

    """ <summary>
    #/ A Pegged to Stock order continually adjusts the option order price by the product of a signed user-define delta and the change of 
    #/ the option's underlying stock price. The delta is entered as an absolute and assumed to be positive for calls and negative for puts. 
    #/ A buy or sell call order price is determined by adding the delta times a change in an underlying stock price to a specified starting 
    #/ price for the call. To determine the change in price, the stock reference price is subtracted from the current NBBO midpoint. 
    #/ The Stock Reference Price can be defined by the user, or defaults to the NBBO midpoat:the:int time of the order if no reference price 
    #/ is entered. You may also enter a high/low stock price range which cancels the order when reached. The delta times the change in stock 
    #/ price will be rounded to the nearest penny in favor of the order.
    #/ Products: OPT
    #/ 
    #/ 股票挂钩单根据用户定义的delta和标的股票价格变动不断调整期权订单价格。delta为绝对值，认购为正，认沽为负。订单价格=起始价+delta*股票变动。参考价可自定义，默认NBBO中点。可设置高/低价区间，触及即取消。delta*变动四舍五入有利于订单。
    #/ 产品：期权
    </summary>"""
    @staticmethod
    def PeggedToStock(action:str, quantity:Decimal, delta:float, stockReferencePrice:float, startingPrice:float):
    
        #! [pegged_stock]
        order = Order()
        order.action = action
        order.orderType = "PEG STK"
        order.totalQuantity = quantity
        order.delta = delta
        order.stockRefPrice = stockReferencePrice
        order.startingPrice = startingPrice
        #! [pegged_stock]
        return order
    

    """ <summary>
    #/ Relative (a.k.a. Pegged-to-Primary) orders provide a means for traders to seek a more aggressive price than the National Best Bid 
    #/ and Offer (NBBO). By acting as liquidity providers, and placing more aggressive bids and offers than the current best bids and offers, 
    #/ traders increase their odds of filling their order. Quotes are automatically adjusted as the markets move, to remain aggressive. 
    #/ For a buy order, your bid is pegged to the NBB by a more aggressive offset, and if the NBB moves up, your bid will also move up. 
    #/ If the NBB moves down, there will be no adjustment because your bid will become even more aggressive and execute. For sales, your 
    #/ offer is pegged to the NBO by a more aggressive offset, and if the NBO moves down, your offer will also move down. If the NBO moves up, 
    #/ there will be no adjustment because your offer will become more aggressive and execute. In addition to the offset, you can define an 
    #/ absolute cap, which works like a limit price, and will prevent your order from being executed above or below a specified level.
    #/ Stocks, Options and Futures - not available on paper trading
    #/ Products: CFD, STK, OPT, FUT
    #/ 
    #/ 主动挂钩单（又称主挂单）让交易者以比NBBO更积极的价格挂单，提高成交概率。买单挂NBB加偏移，NBB上升则买价上升，下降则更积极成交。卖单挂NBO加偏移，NBO下降则卖价下降，上升则更积极成交。可设置价格上/下限防止超价成交。
    #/ 产品：差价合约、股票、期权、期货（模拟盘不可用）
    </summary>"""
    @staticmethod
    def RelativePeggedToPrimary(action:str, quantity:Decimal, priceCap:float, 
                                offsetAmount:float):
    
        #! [relative_pegged_primary]
        order = Order()
        order.action = action
        order.orderType = "REL"
        order.totalQuantity = quantity
        order.lmtPrice = priceCap
        order.auxPrice = offsetAmount
        #! [relative_pegged_primary]
        return order
    

    """ <summary>
    #/ Sweep-to-fill orders are useful when a trader values speed of execution over price. A sweep-to-fill order identifies the best price 
    #/ and the exact quantity offered/available at that price, and transmits the corresponding portion of your order for immediate execution. 
    #/ Simultaneously it identifies the next best price and quantity offered/available, and submits the matching quantity of your order for 
    #/ immediate execution.
    #/ Products: CFD, STK, WAR
    #/ 
    #/ 扫单成交单适用于优先速度而非价格的交易者。该单会识别最佳价格及对应数量，立即提交相应部分成交，同时识别下一个最佳价和数量，继续提交剩余订单。
    #/ 产品：差价合约、股票、权证
    </summary>"""
    @staticmethod
    def SweepToFill(action:str, quantity:Decimal, price:float):
    
        #! [sweep_to_fill]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = price
        order.sweepToFill = True
        #! [sweep_to_fill]
        return order
    

    """ <summary>
    #/ For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in 
    #/ pennies. All BOX-directed price improvement orders are immediately sent from Interactive Brokers to the BOX order book, and when the 
    #/ terms allow, IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your 
    #/ order will have priority over broker-dealer price improvement orders at the same price.
    #/ An Auction Limit order at a specified price. Use of a limit order ensures that you will not receive an execution at a price less favorable 
    #/ than the limit price. Enter limit orders in penny increments with your auction improvement amount computed as the difference between your 
    #/ limit order price and the nearest listed increment.
    #/ Products: OPT
    #/ Supported Exchanges: BOX
    #/ 
    #/ BOX期权订单可参与价格改善拍卖。所有BOX价格改善单立即发送至BOX订单簿，IB会根据价格和数量优先级评估是否参与拍卖。拍卖限价单确保成交价不劣于限价，限价以分为单位递增，改善金额为限价与最近档位差。
    #/ 产品：期权
    #/ 支持交易所：BOX
    </summary>"""
    @staticmethod
    def AuctionLimit(action:str, quantity:Decimal, price:float, 
                     auctionStrategy:int):
    
        #! [auction_limit]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = price
        order.auctionStrategy = auctionStrategy
        #! [auction_limit]
        return order
    

    """ <summary>
    #/ For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in pennies. 
    #/ All BOX-directed price improvement orders are immediately sent from Interactive Brokers to the BOX order book, and when the terms allow, 
    #/ IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your order will have 
    #/ priority over broker-dealer price improvement orders at the same price.
    #/ An Auction Pegged to Stock order adjusts the order price by the product of a signed delta (which is entered as an absolute and assumed to be 
    #/ positive for calls, negative for puts) and the change of the option's underlying stock price. A buy or sell call order price is determined 
    #/ by adding the delta times a change in an underlying stock price change to a specified starting price for the call. To determine the change 
    #/ in price, a stock reference price (NBBO midpoat:the:int time of the order is assumed if no reference price is entered) is subtracted from 
    #/ the current NBBO midpoint. A stock range may also be entered that cancels an order when reached. The delta times the change in stock price 
    #/ will be rounded to the nearest penny in favor of the order and will be used as your auction improvement amount.
    #/ Products: OPT
    #/ Supported Exchanges: BOX
    #/ 
    #/ BOX期权订单可参与价格改善拍卖。拍卖股票挂钩单根据delta和标的股票变动调整订单价格，delta为绝对值，认购为正，认沽为负。订单价格=起始价+delta*股票变动。参考价默认NBBO中点。可设置区间，触及即取消。delta*变动四舍五入有利于订单，作为改善金额。
    #/ 产品：期权
    #/ 支持交易所：BOX
    </summary>"""
    @staticmethod
    def AuctionPeggedToStock(action:str, quantity:Decimal, startingPrice:float, 
                             delta:float):
    
        #! [auction_pegged_stock]
        order = Order()
        order.action = action
        order.orderType = "PEG STK"
        order.totalQuantity = quantity
        order.delta = delta
        order.startingPrice = startingPrice
        #! [auction_pegged_stock]
        return order
    

    """ <summary>
    #/ For option orders routed to the Boston Options Exchange (BOX) you may elect to participate in the BOX's price improvement auction in pennies. 
    #/ All BOX-directed price improvement orders are immediately sent from Interactive Brokers to the BOX order book, and when the terms allow, 
    #/ IB will evaluate it for inclusion in a price improvement auction based on price and volume priority. In the auction, your order will have 
    #/ priority over broker-dealer price improvement orders at the same price.
    #/ An Auction Relative order that adjusts the order price by the product of a signed delta (which is entered as an absolute and assumed to be 
    #/ positive for calls, negative for puts) and the change of the option's underlying stock price. A buy or sell call order price is determined 
    #/ by adding the delta times a change in an underlying stock price change to a specified starting price for the call. To determine the change 
    #/ in price, a stock reference price (NBBO midpoat:the:int time of the order is assumed if no reference price is entered) is subtracted from 
    #/ the current NBBO midpoint. A stock range may also be entered that cancels an order when reached. The delta times the change in stock price 
    #/ will be rounded to the nearest penny in favor of the order and will be used as your auction improvement amount.
    #/ Products: OPT
    #/ Supported Exchanges: BOX
    #/ 
    #/ BOX期权订单可参与价格改善拍卖。拍卖相对单根据delta和标的股票变动调整订单价格，delta为绝对值，认购为正，认沽为负。订单价格=起始价+delta*股票变动。参考价默认NBBO中点。可设置区间，触及即取消。delta*变动四舍五入有利于订单，作为改善金额。
    #/ 产品：期权
    #/ 支持交易所：BOX
    </summary>"""
    @staticmethod
    def AuctionRelative(action:str, quantity:Decimal, offset:float):
    
        #! [auction_relative]
        order = Order()
        order.action = action
        order.orderType = "REL"
        order.totalQuantity = quantity
        order.auxPrice = offset
        #! [auction_relative]
        return order
    

    """ <summary>
    #/ The Block attribute is used for large volume option orders on ISE that consist of at least 50 contracts. To execute large-volume 
    #/ orders over time without moving the market, use the Accumulate/Distribute algorithm.
    #/ Products: OPT
    #/ 
    #/ Block属性用于ISE大额期权订单（至少50张合约）。如需分批成交避免冲击市场，可用累积/分配算法。
    #/ 产品：期权
    </summary>"""
    @staticmethod
    def Block(action:str, quantity:Decimal, price:float):
    
        # ! [block]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity#Large volumes!
        order.lmtPrice = price
        order.blockOrder = True
        # ! [block]
        return order
    

    """ <summary>
    #/ A Box Top order executes as a market order at the current best price. If the order is only partially filled, the remainder is submitted as 
    #/ a limit order with the limit price equal to the price at which the filled portion of the order executed.
    #/ Products: OPT
    #/ Supported Exchanges: BOX
    #/ 
    #/ Box Top订单以当前最佳价市价成交，若部分成交，剩余部分以成交价为限价单提交。
    #/ 产品：期权
    #/ 支持交易所：BOX
    </summary>"""
    @staticmethod
    def BoxTop(action:str, quantity:Decimal):
    
        # ! [boxtop]
        order = Order()
        order.action = action
        order.orderType = "BOX TOP"
        order.totalQuantity = quantity
        # ! [boxtop]
        return order
    

    """ <summary>
    #/ A Limit order is an order to buy or sell at a specified price or better. The Limit order ensures that if the order fills, 
    #/ it will not fill at a price less favorable than your limit price, but it does not guarantee a fill.
    #/ Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 限价单是在指定价格或更优价格买入或卖出的订单。限价单保证成交价不劣于限价，但不保证一定成交。
    #/ 产品：债券、差价合约、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def LimitOrder(action:str, quantity:Decimal, limitPrice:float):
    
        # ! [limitorder]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        # ! [limitorder]
        return order
        
    """ <summary>            
    #/ Forex orders can be placed in demonination of second currency in pair using cashQty field
    #/ Requires TWS or IBG 963+
    #/ https://www.interactivebrokers.com/en/index.php?f=23876#963-02
    #/ 
    #/ 外汇订单可通过cashQty字段以货币对第二币种下单。需TWS或IBG 963+。
    </summary>"""
        
    @staticmethod
    def LimitOrderWithCashQty(action:str, limitPrice:float, cashQty:float):
    
        # ! [limitorderwithcashqty]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.lmtPrice = limitPrice
        order.cashQty = cashQty
        # ! [limitorderwithcashqty]
        return order


    """ <summary>
    #/ A Limit if Touched is an order to buy (or sell) a contract at a specified price or better, below (or above) the market. This order is 
    #/ held in the system until the trigger price is touched. An LIT order is similar to a stop limit order, except that an LIT sell order is 
    #/ placed above the current market price, and a stop limit sell order is placed below.
    #/ Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 触发限价单是在市场价下方（买入）或上方（卖出）以指定价格或更优价格成交的订单。订单在触发价被触及前一直保留。LIT卖单挂在市价上方，止损限价卖单挂在下方。
    #/ 产品：债券、差价合约、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def LimitIfTouched(action:str, quantity:Decimal, limitPrice:float, 
                        triggerPrice:float):
    
        # ! [limitiftouched]
        order = Order()
        order.action = action
        order.orderType = "LIT"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        order.auxPrice = triggerPrice
        # ! [limitiftouched]
        return order
    

    """ <summary>
    #/ A Limit-on-close (LOC) order will be submitted at the close and will execute if the closing price is at or better than the submitted 
    #/ limit price.
    #/ Products: CFD, FUT, STK, WAR
    #/ 
    #/ 收盘限价单（LOC）在收盘时提交，若收盘价达到或优于限价则成交。
    #/ 产品：差价合约、期货、股票、权证
    </summary>"""
    @staticmethod
    def LimitOnClose(action:str, quantity:Decimal, limitPrice:float):
    
        # ! [limitonclose]
        order = Order()
        order.action = action
        order.orderType = "LOC"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        # ! [limitonclose]
        return order
    

    """ <summary>
    #/ A Limit-on-Open (LOO) order combines a limit order with the OPG time in force to create an order that is submitted at the market's open, 
    #/ and that will only execute at the specified limit price or better. Orders are filled in accordance with specific exchange rules.
    #/ Products: CFD, STK, OPT, WAR
    #/ 
    #/ 开盘限价单（LOO）结合限价单和OPG时效，在开盘时提交，仅在指定限价或更优价成交，按交易所规则撮合。
    #/ 产品：差价合约、股票、期权、权证
    </summary>"""
    @staticmethod
    def LimitOnOpen(action:str, quantity:Decimal, limitPrice:float):
    
        # ! [limitonopen]
        order = Order()
        order.action = action
        order.tif = "OPG"
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        # ! [limitonopen]
        return order
    

    """ <summary>
    #/ Passive Relative orders provide a means for traders to seek a less aggressive price than the National Best Bid and Offer (NBBO) while 
    #/ keeping the order pegged to the best bid (for a buy) or ask (for a sell). The order price is automatically adjusted as the markets move 
    #/ to keep the order less aggressive. For a buy order, your order price is pegged to the NBB by a less aggressive offset, and if the NBB 
    #/ moves up, your bid will also move up. If the NBB moves down, there will be no adjustment because your bid will become aggressive and execute. 
    #/ For a sell order, your price is pegged to the NBO by a less aggressive offset, and if the NBO moves down, your offer will also move down. 
    #/ If the NBO moves up, there will be no adjustment because your offer will become aggressive and execute. In addition to the offset, you can 
    #/ define an absolute cap, which works like a limit price, and will prevent your order from being executed above or below a specified level. 
    #/ The Passive Relative order is similar to the Relative/Pegged-to-Primary order, except that the Passive relative subtracts the offset from 
    #/ the bid and the Relative adds the offset to the bid.
    #/ Products: STK, WAR
    #/ 
    #/ 被动挂钩单让交易者以比NBBO更不积极的价格挂单，订单价格随市场变动自动调整以保持不积极。买单挂NBB减偏移，NBB上升则买价上升，下降则更积极成交。卖单挂NBO减偏移，NBO下降则卖价下降，上升则更积极成交。可设置价格上/下限防止超价成交。与主动挂钩单不同，被动挂钩单偏移量为减法。
    #/ 产品：股票、权证
    </summary>"""
    @staticmethod
    def PassiveRelative(action:str, quantity:Decimal, offset:float):
    
        # ! [passive_relative]
        order = Order()
        order.action = action
        order.orderType = "PASSV REL"
        order.totalQuantity = quantity
        order.auxPrice = offset
        # ! [passive_relative]
        return order
    

    """ <summary>
    #/ A pegged-to-midpoorder:provides:int a means for traders to seek a price at the midpoof:the:int National Best Bid and Offer (NBBO). 
    #/ The price automatically adjusts to peg the midpoas:the:int markets move, to remain aggressive. For a buy order, your bid is pegged to 
    #/ the NBBO midpoand:the:int order price adjusts automatically to continue to peg the midpoif:the:int market moves. The price only adjusts 
    #/ to be more aggressive. If the market moves in the opposite direction, the order will execute.
    #/ Products: STK
    #/ 
    #/ 挂钩中间价单允许交易者以全国最佳买卖价（NBBO）中间价挂单。价格会随着市场变动自动调整以保持挂钩中间价的积极性。对于买单，买价挂在NBBO中间价，市场变动时订单价格自动调整以继续挂钩中间价，且只会向更积极方向调整。如果市场反向变动，订单会被执行。
    #/ 产品：股票
    </summary>"""
    @staticmethod
    def PeggedToMidpoint(action:str, quantity:Decimal, offset:float, limitPrice:float):
    
        # ! [pegged_midpoint]
        order = Order()
        order.action = action
        order.orderType = "PEG MID"
        order.totalQuantity = quantity
        order.auxPrice = offset
        order.lmtPrice = limitPrice
        # ! [pegged_midpoint]
        return order
    

    """ <summary>
    #/ Bracket orders are designed to help limit your loss and lock in a profit by "bracketing" an order with two opposite-side orders. 
    #/ A BUY order is bracketed by a high-side sell limit order and a low-side sell stop order. A SELL order is bracketed by a high-side buy 
    #/ stop order and a low side buy limit order.
    #/ Products: CFD, BAG, FOP, CASH, FUT, OPT, STK, WAR
    #/ 
    #/ 括号单用于帮助限制亏损并锁定利润，通过在主订单两侧分别设置止盈限价单和止损止损单实现。买单被高位卖出限价单和低位卖出止损单包围，卖单则被高位买入止损单和低位买入限价单包围。
    #/ 产品：差价合约、篮子、期权、现金、期货、期权、股票、权证
    </summary>"""
    #! [bracket]
    @staticmethod
    def BracketOrder(parentOrderId:int, action:str, quantity:Decimal, 
                     limitPrice:float, takeProfitLimitPrice:float, 
                     stopLossPrice:float):
    
        #This will be our main or "parent" order
        # 这是主订单
        parent = Order()
        parent.orderId = parentOrderId
        parent.action = action
        parent.orderType = "LMT"
        parent.totalQuantity = quantity
        parent.lmtPrice = limitPrice
        #The parent and children orders will need this attribute set to False to prevent accidental executions.
        #The LAST CHILD will have it set to True, 
        # 主订单和子订单需设置transmit为False以防止意外成交，最后一个子订单设置为True以激活所有订单
        parent.transmit = False

        takeProfit = Order()
        takeProfit.orderId = parent.orderId + 1
        takeProfit.action = "SELL" if action == "BUY" else "BUY"
        takeProfit.orderType = "LMT"
        takeProfit.totalQuantity = quantity
        takeProfit.lmtPrice = takeProfitLimitPrice
        takeProfit.parentId = parentOrderId
        takeProfit.transmit = False

        stopLoss = Order()
        stopLoss.orderId = parent.orderId + 2
        stopLoss.action = "SELL" if action == "BUY" else "BUY"
        stopLoss.orderType = "STP"
        #Stop trigger price
        # 止损触发价
        stopLoss.auxPrice = stopLossPrice
        stopLoss.totalQuantity = quantity
        stopLoss.parentId = parentOrderId
        #In this case, the low side order will be the last child being sent. Therefore, it needs to set this attribute to True 
        #to activate all its predecessors
        # 在此情况下，低位订单为最后一个子订单，需设置transmit为True以激活所有前序订单
        stopLoss.transmit = True

        bracketOrder = [parent, takeProfit, stopLoss]
        return bracketOrder
    
    #! [bracket]

    """ <summary>
    #/ Products:CFD, FUT, FOP, OPT, STK, WAR
    #/ A Market-to-Limit (MTL) order is submitted as a market order to execute at the current best market price. If the order is only 
    #/ partially filled, the remainder of the order is canceled and re-submitted as a limit order with the limit price equal to the price 
    #/ at which the filled portion of the order executed.
    #/ 
    #/ 市价转限价单（MTL）以市价单提交，按当前最佳市场价格成交。如果只部分成交，剩余部分会被取消并以已成交价格作为限价重新提交为限价单。
    #/ 产品：差价合约、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def MarketToLimit(action:str, quantity:Decimal):
    
        # ! [markettolimit]
        order = Order()
        order.action = action
        order.orderType = "MTL"
        order.totalQuantity = quantity
        # ! [markettolimit]
        return order
    

    """ <summary>
    #/ This order type is useful for futures traders using Globex. A Market with Protection order is a market order that will be cancelled and 
    #/ resubmitted as a limit order if the entire order does not immediately execute at the market price. The limit price is set by Globex to be 
    #/ close to the current market price, slightly higher for a sell order and lower for a buy order.
    #/ Products: FUT, FOP
    #/ 
    #/ 保护性市价单适用于使用Globex的期货交易者。该订单为市价单，若未能全部立即成交，则会被取消并以接近当前市价的限价单重新提交。卖单限价略高于市价，买单限价略低于市价。
    #/ 产品：期货、期权
    </summary>"""
    @staticmethod
    def MarketWithProtection(action:str, quantity:Decimal):
    
        # ! [marketwithprotection]
        order = Order()
        order.action = action
        order.orderType = "MKT PRT"
        order.totalQuantity = quantity
        # ! [marketwithprotection]
        return order
    

    """ <summary>
    #/ A Stop order is an instruction to submit a buy or sell market order if and when the user-specified stop trigger price is attained or 
    #/ penetrated. A Stop order is not guaranteed a specific execution price and may execute significantly away from its stop price. A Sell 
    #/ Stop order is always placed below the current market price and is typically used to limit a loss or protect a profit on a long stock 
    #/ position. A Buy Stop order is always placed above the current market price. It is typically used to limit a loss or help protect a 
    #/ profit on a short sale.
    #/ Products: CFD, BAG, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 止损单是在达到或穿透用户指定的止损触发价时提交市价买入或卖出订单。止损单不保证具体成交价，可能远离止损价成交。卖出止损单总是低于当前市价，通常用于限制多头亏损或保护利润。买入止损单总是高于当前市价，通常用于限制空头亏损或保护利润。
    #/ 产品：差价合约、篮子、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def Stop(action:str, quantity:Decimal, stopPrice:float):
    
        # ! [stop]
        order = Order()
        order.action = action
        order.orderType = "STP"
        order.auxPrice = stopPrice
        order.totalQuantity = quantity
        # ! [stop]
        return order
    

    """ <summary>
    #/ A Stop-Limit order is an instruction to submit a buy or sell limit order when the user-specified stop trigger price is attained or 
    #/ penetrated. The order has two basic components: the stop price and the limit price. When a trade has occurred at or through the stop 
    #/ price, the order becomes executable and enters the market as a limit order, which is an order to buy or sell at a specified price or better.
    #/ Products: CFD, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 止损限价单是在达到或穿透用户指定的止损触发价时提交限价买入或卖出订单。该订单有两个基本要素：止损价和限价。当成交价达到或穿透止损价时，订单变为可执行并以限价单进入市场，即以指定价格或更优价格买入或卖出。
    #/ 产品：差价合约、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def StopLimit(action:str, quantity:Decimal, limitPrice:float, stopPrice:float):
    
        # ! [stoplimit]
        order = Order()
        order.action = action
        order.orderType = "STP LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        order.auxPrice = stopPrice
        # ! [stoplimit]
        return order
    

    """ <summary>
    #/ A Stop with Protection order combines the functionality of a stop limit order with a market with protection order. The order is set 
    #/ to trigger at a specified stop price. When the stop price is penetrated, the order is triggered as a market with protection order, 
    #/ which means that it will fill within a specified protected price range equal to the trigger price +/- the exchange-defined protection 
    #/ porange:int. Any portion of the order that does not fill within this protected range is submitted as a limit order at the exchange-defined 
    #/ trigger price +/- the protection points.
    #/ Products: FUT
    #/ 
    #/ 保护性止损单结合了止损限价单和保护性市价单的功能。订单在指定止损价触发，触发后以保护性市价单提交，在交易所定义的保护区间内成交，未成交部分以保护区间边界为限价单提交。
    #/ 产品：期货
    </summary>"""
    @staticmethod
    def StopWithProtection(action:str, quantity:Decimal, stopPrice:float):
    
        # ! [stopwithprotection]
        order = Order()
        order.totalQuantity = quantity
        order.action = action
        order.orderType = "STP PRT"
        order.auxPrice = stopPrice
        # ! [stopwithprotection]
        return order
    

    """ <summary>
    #/ A sell trailing stop order sets the stop price at a fixed amount below the market price with an attached "trailing" amount. As the 
    #/ market price rises, the stop price rises by the trail amount, but if the stock price falls, the stop loss price doesn't change, 
    #/ and a market order is submitted when the stop price is hit. This technique is designed to allow an investor to specify a limit on the 
    #/ maximum possible loss, without setting a limit on the maximum possible gain. "Buy" trailing stop orders are the mirror image of sell 
    #/ trailing stop orders, and are most appropriate for use in falling markets.
    #/ Products: CFD, CASH, FOP, FUT, OPT, STK, WAR
    #/ 
    #/ 卖出跟踪止损单将止损价设在市价下方固定距离，并附带“跟踪”金额。市价上涨时止损价随之上升，市价下跌时止损价不变，触及止损价时以市价单成交。该方法可让投资者限定最大亏损而不限制最大收益。买入跟踪止损单为卖出单的镜像，适用于下跌市场。
    #/ 产品：差价合约、现金、期权、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def TrailingStop(action:str, quantity:Decimal, trailingPercent:float,
                     trailStopPrice:float):
    
        # ! [trailingstop]
        order = Order()
        order.action = action
        order.orderType = "TRAIL"
        order.totalQuantity = quantity
        order.trailingPercent = trailingPercent
        order.trailStopPrice = trailStopPrice
        # ! [trailingstop]
        return order
    

    """ <summary>
    #/ A trailing stop limit order is designed to allow an investor to specify a limit on the maximum possible loss, without setting a limit 
    #/ on the maximum possible gain. A SELL trailing stop limit moves with the market price, and continually recalculates the stop trigger 
    #/ price at a fixed amount below the market price, based on the user-defined "trailing" amount. The limit order price is also continually 
    #/ recalculated based on the limit offset. As the market price rises, both the stop price and the limit price rise by the trail amount and 
    #/ limit offset respectively, but if the stock price falls, the stop price remains unchanged, and when the stop price is hit a limit order 
    #/ is submitted at the last calculated limit price. A "Buy" trailing stop limit order is the mirror image of a sell trailing stop limit, 
    #/ and is generally used in falling markets.
    #/ Products: BOND, CFD, CASH, FUT, FOP, OPT, STK, WAR
    #/ 
    #/ 跟踪止损限价单允许投资者限定最大亏损而不限制最大收益。卖出跟踪止损限价单随市价变动，止损触发价和限价会根据用户设定的“跟踪”金额和限价偏移不断调整。市价上涨时止损价和限价随之上升，市价下跌时止损价不变，触及止损价时以最后计算的限价单成交。买入跟踪止损限价单为卖出单的镜像，适用于下跌市场。
    #/ 产品：债券、差价合约、现金、期货、期权、股票、权证
    </summary>"""
    @staticmethod
    def TrailingStopLimit(action:str, quantity:Decimal, lmtPriceOffset:float, 
                          trailingAmount:float, trailStopPrice:float):
    
        # ! [trailingstoplimit]
        order = Order()
        order.action = action
        order.orderType = "TRAIL LIMIT"
        order.totalQuantity = quantity
        order.trailStopPrice = trailStopPrice
        order.lmtPriceOffset = lmtPriceOffset
        order.auxPrice = trailingAmount
        # ! [trailingstoplimit]
        return order
    

    """ <summary>
    #/ Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed 
    #/ through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction 
    #/ if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure 
    #/ best execution.
    #/ Products: OPT, STK, FUT
    #/ 
    #/ 创建包含期权、股票和期货腿的组合订单（如通过SmartRouting路由也可包含股票腿）。虽然组合/价差订单由多个腿组成，但若直接路由到交易所则作为单笔交易执行。SmartRouting的组合订单每条腿可能单独成交以确保最佳执行。
    #/ 产品：期权、股票、期货
    </summary>"""
    @staticmethod
    def ComboLimitOrder(action:str, quantity:Decimal, limitPrice:float, 
                        nonGuaranteed:bool):
    
        # ! [combolimit]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.lmtPrice = limitPrice
        if nonGuaranteed:
        
            order.smartComboRoutingParams = []
            order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
        # ! [combolimit]
        return order
    

    """ <summary>
    #/ Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed 
    #/ through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction 
    #/ if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure 
    #/ best execution.
    #/ Products: OPT, STK, FUT
    #/ 
    #/ 创建包含期权、股票和期货腿的组合订单（如通过SmartRouting路由也可包含股票腿）。虽然组合/价差订单由多个腿组成，但若直接路由到交易所则作为单笔交易执行。SmartRouting的组合订单每条腿可能单独成交以确保最佳执行。
    #/ 产品：期权、股票、期货
    </summary>"""
    @staticmethod
    def ComboMarketOrder(action:str, quantity:Decimal, nonGuaranteed:bool):
    
        # ! [combomarket]
        order = Order()
        order.action = action
        order.orderType = "MKT"
        order.totalQuantity = quantity
        if nonGuaranteed:
        
            order.smartComboRoutingParams = []
            order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
        # ! [combomarket]
        return order
    

    """ <summary>
    #/ Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed 
    #/ through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction 
    #/ if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure 
    #/ best execution.
    #/ Products: OPT, STK, FUT
    #/ 
    #/ 创建包含期权、股票和期货腿的组合订单（如通过SmartRouting路由也可包含股票腿）。虽然组合/价差订单由多个腿组成，但若直接路由到交易所则作为单笔交易执行。SmartRouting的组合订单每条腿可能单独成交以确保最佳执行。
    #/ 产品：期权、股票、期货
    </summary>"""
    @staticmethod
    def LimitOrderForComboWithLegPrices(action:str, quantity:Decimal,
                                        legPrices:list, nonGuaranteed:bool):
    
        # ! [limitordercombolegprices]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.totalQuantity = quantity
        order.orderComboLegs = []
        for price in legPrices:
        
            comboLeg = OrderComboLeg()
            comboLeg.price = price
            order.orderComboLegs.append(comboLeg)
                   
        if nonGuaranteed:
            order.smartComboRoutingParams = []
            order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
        # ! [limitordercombolegprices]
        return order
    

    """ <summary>
    #/ Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed 
    #/ through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction 
    #/ if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure 
    #/ best execution.
    #/ Products: OPT, STK, FUT
    #/ 
    #/ 创建包含期权、股票和期货腿的组合订单（如通过SmartRouting路由也可包含股票腿）。虽然组合/价差订单由多个腿组成，但若直接路由到交易所则作为单笔交易执行。SmartRouting的组合订单每条腿可能单独成交以确保最佳执行。
    #/ 产品：期权、股票、期货
    </summary>"""
    @staticmethod
    def RelativeLimitCombo(action:str, quantity:Decimal, limitPrice:float, 
                           nonGuaranteed:bool):
    
        # ! [relativelimitcombo]
        order = Order()
        order.action = action
        order.totalQuantity = quantity
        order.orderType = "REL + LMT"
        order.lmtPrice = limitPrice
        if nonGuaranteed:
        
            order.smartComboRoutingParams = []
            order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
        # ! [relativelimitcombo]
        return order
    

    """ <summary>
    #/ Create combination orders that include options, stock and futures legs (stock legs can be included if the order is routed 
    #/ through SmartRouting). Although a combination/spread order is constructed of separate legs, it is executed as a single transaction 
    #/ if it is routed directly to an exchange. For combination orders that are SmartRouted, each leg may be executed separately to ensure 
    #/ best execution.
    #/ Products: OPT, STK, FUT
    #/ 
    #/ 创建包含期权、股票和期货腿的组合订单（如通过SmartRouting路由也可包含股票腿）。虽然组合/价差订单由多个腿组成，但若直接路由到交易所则作为单笔交易执行。SmartRouting的组合订单每条腿可能单独成交以确保最佳执行。
    #/ 产品：期权、股票、期货
    </summary>"""
    @staticmethod
    def RelativeMarketCombo(action:str, quantity:Decimal, nonGuaranteed:bool):
    
        # ! [relativemarketcombo]
        order = Order()
        order.action = action
        order.totalQuantity = quantity
        order.orderType = "REL + MKT"
        if nonGuaranteed:
        
            order.smartComboRoutingParams = []
            order.smartComboRoutingParams.append(TagValue("NonGuaranteed", "1"))
        
        # ! [relativemarketcombo]
        return order
    

    """ <summary>
    #/ One-Cancels All (OCA) order type allows an investor to place multiple and possibly unrelated orders assigned to a group. The aim is 
    #/ to complete just one of the orders, which in turn will cause TWS to cancel the remaining orders. The investor may submit several 
    #/ orders aimed at taking advantage of the most desirable price within the group. Completion of one piece of the group order causes 
    #/ cancellation of the remaining group orders while partial completion causes the group to rebalance. An investor might desire to sell 
    #/ 1000 shares of only ONE of three positions held above prevailing market prices. The OCA order group allows the investor to enter prices 
    #/ at specified target levels and if one is completed, the other two will automatically cancel. Alternatively, an investor may wish to take 
    #/ a LONG position in eMini S&P stock index futures in a falling market or else SELL US treasury futures at a more favorable price. 
    #/ Grouping the two orders using an OCA order type offers the investor two chance to enter a similar position, while only running the risk 
    #/ of taking on a single position.
    #/ Products: BOND, CASH, FUT, FOP, STK, OPT, WAR
    #/ 
    #/ 一撤全撤（OCA）订单类型允许投资者将多个（可能无关的）订单分配到同一组，目标是只完成其中一个订单，TWS会自动取消剩余订单。投资者可提交多个订单以争取组内最优价格，完成一笔后其余自动取消，部分成交则组内订单自动再平衡。适用于多头或空头多种策略。
    #/ 产品：债券、现金、期货、期权、股票、权证
    </summary>"""
    # ! [oca]
    @staticmethod
    def OneCancelsAll(ocaGroup:str, ocaOrders:ListOfOrder, ocaType:int):
    
        for o in ocaOrders:
        
            o.ocaGroup = ocaGroup
            o.ocaType = ocaType
        
        return ocaOrders
    
    # ! [oca]

    """ <summary>
    #/ Specific to US options, investors are able to create and enter Volatility-type orders for options and combinations rather than price orders. 
    #/ Option traders may wish to trade and position for movements in the price of the option determined by its implied volatility. Because 
    #/ implied volatility is a key determinant of the premium on an option, traders position in specific contract months in an effort to take 
    #/ advantage of perceived changes in implied volatility arising before, during or after earnings or when company specific or broad market 
    #/ volatility is predicted to change. In order to create a Volatility order, clients must first create a Volatility Trader page from the 
    #/ Trading Tools menu and as they enter option contracts, premiums will display in percentage terms rather than premium. The buy/sell process 
    #/ is the same as for regular orders priced in premium terms except that the client can limit the volatility level they are willing to pay or 
    #/ receive.
    #/ Products: FOP, OPT
    #/ 
    #/ 仅针对美股期权，投资者可为期权及其组合创建波动率类型订单而非价格订单。期权交易者可根据隐含波动率变化进行交易和持仓，隐含波动率是期权溢价的关键决定因素。可在特定合约月份布局以利用波动率变化。创建波动率订单需先在交易工具菜单新建波动率交易页面，输入期权合约后溢价以百分比显示。买卖流程与普通订单一致，但可限定愿意支付或接受的波动率水平。
    #/ 产品：期权、期货期权
    </summary>"""
    @staticmethod
    def Volatility(action:str, quantity:Decimal, volatilityPercent:float, 
                   volatilityType:int):
    
        # ! [volatility]
        order = Order()
        order.action = action
        order.orderType = "VOL"
        order.totalQuantity = quantity
        order.volatility = volatilityPercent#Expressed in percentage (40%)
        order.volatilityType = volatilityType# 1=daily, 2=annual
        # ! [volatility]
        return order
    

    #! [fhedge]
    @staticmethod
    def MarketFHedge(parentOrderId:int, action:str):
    
        #FX Hedge orders can only have a quantity of 0
        # 外汇对冲单只能数量为0
        order = OrderSamples.MarketOrder(action, 0)
        order.parentId = parentOrderId
        order.hedgeType = "F"
        return order
    
    #! [fhedge]

    @staticmethod
    def PeggedToBenchmark(action:str, quantity:Decimal, startingPrice:float, 
                           peggedChangeAmountDecrease:bool, 
                           peggedChangeAmount:float,
                           referenceChangeAmount:float, referenceConId:int, 
                           referenceExchange:str, stockReferencePrice:float,  
                           referenceContractLowerRange:float, 
                           referenceContractUpperRange:float):
    
        #! [pegged_benchmark]
        order = Order()
        order.orderType = "PEG BENCH"
        #BUY or SELL
        # 买入或卖出
        order.action = action
        order.totalQuantity = quantity
        #Beginning with price...
        # 起始价格
        order.startingPrice = startingPrice
        #increase/decrease price..
        # 增加/减少价格
        order.isPeggedChangeAmountDecrease = peggedChangeAmountDecrease
        #by... (and likewise for price moving in opposite direction)
        # 调整金额
        order.peggedChangeAmount = peggedChangeAmount
        #whenever there is a price change of...
        # 参考合约价格变动
        order.referenceChangeAmount = referenceChangeAmount
        #in the reference contract...
        # 参考合约ID
        order.referenceContractId = referenceConId
        #being traded at...
        # 参考交易所
        order.referenceExchange = referenceExchange
        #starting reference price is...
        # 参考起始价格
        order.stockRefPrice = stockReferencePrice
        #Keep order active as long as reference contract trades between...
        # 参考合约价格下限
        order.stockRangeLower = referenceContractLowerRange
        #and...
        # 参考合约价格上限
        order.stockRangeUpper = referenceContractUpperRange
        #! [pegged_benchmark]
        return order
    
    

    @staticmethod
    def AttachAdjustableToStop(parent:Order , attachedOrderStopPrice:float, 
                               triggerPrice:float, adjustStopPrice:float):
    
        #! [adjustable_stop]
        # Attached order is a conventional STP order in opposite direction
        # 附加订单为反向普通止损单
        order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
                     parent.totalQuantity, attachedOrderStopPrice)
        order.parentId = parent.orderId
        #When trigger price is penetrated
        # 触发价被穿透时
        order.triggerPrice = triggerPrice
        #The parent order will be turned into a STP order
        # 父订单将变为止损单
        order.adjustedOrderType = "STP"
        #With the given STP price
        # 指定止损价
        order.adjustedStopPrice = adjustStopPrice
        #! [adjustable_stop]
        return order
    

    @staticmethod
    def AttachAdjustableToStopLimit(parent:Order, attachedOrderStopPrice:float, 
                                    triggerPrice:float, adjustedStopPrice:float, 
                                    adjustedStopLimitPrice:float):
    
        #! [adjustable_stop_limit]
        #Attached order is a conventional STP order
        # 附加订单为普通止损单
        order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
                                  parent.totalQuantity, attachedOrderStopPrice)
        order.parentId = parent.orderId
        #When trigger price is penetrated
        # 触发价被穿透时
        order.triggerPrice = triggerPrice
        #The parent order will be turned into a STP LMT order
        # 父订单将变为止损限价单
        order.adjustedOrderType = "STP LMT"
        #With the given stop price
        # 指定止损价
        order.adjustedStopPrice = adjustedStopPrice
        #And the given limit price
        # 指定限价
        order.adjustedStopLimitPrice = adjustedStopLimitPrice
        #! [adjustable_stop_limit]
        return order


    @staticmethod
    def  AttachAdjustableToTrail(parent:Order, attachedOrderStopPrice:float, 
                                 triggerPrice:float, adjustedStopPrice:float, 
                                 adjustedTrailAmount:float, trailUnit:int):
        #! [adjustable_trail]
        #Attached order is a conventional STP order
        # 附加订单为普通止损单
        order = OrderSamples.Stop("SELL" if parent.action == "BUY" else "BUY",
                     parent.totalQuantity, attachedOrderStopPrice)
        order.parentId = parent.orderId
        #When trigger price is penetrated
        # 触发价被穿透时
        order.triggerPrice = triggerPrice
        #The parent order will be turned into a TRAIL order
        # 父订单将变为跟踪止损单
        order.adjustedOrderType = "TRAIL"
        #With a stop price of...
        # 指定止损价
        order.adjustedStopPrice = adjustedStopPrice
        #traling by and amount (0) or a percent (100)...
        # 跟踪单位（0为金额，100为百分比）
        order.adjustableTrailingUnit = trailUnit
        #of...
        # 跟踪金额或百分比
        order.adjustedTrailingAmount = adjustedTrailAmount
        #! [adjustable_trail]        
        return order
 

    @staticmethod
    def PriceCondition(triggerMethod:int, conId:int, exchange:str, price:float, 
                        isMore:bool, isConjunction:bool):
    
        #! [price_condition]
        #Conditions have to be created via the OrderCondition.create 
        # 条件需通过OrderCondition.create创建
        priceCondition = order_condition.Create(OrderCondition.Price)
        #When this contract...
        # 合约ID
        priceCondition.conId = conId
        #traded on this exchange
        # 交易所
        priceCondition.exchange = exchange
        #has a price above/below
        # 价格高于/低于
        priceCondition.isMore = isMore
        priceCondition.triggerMethod = triggerMethod
        #this quantity
        # 价格
        priceCondition.price = price
        #AND | OR next condition (will be ignored if no more conditions are added)
        # 与/或下一个条件（如无更多条件则忽略）
        priceCondition.isConjunctionConnection = isConjunction
        #! [price_condition]
        return priceCondition
    

    @staticmethod
    def ExecutionCondition(symbol:str, secType:str, exchange:str, 
                           isConjunction:bool):
    
        #! [execution_condition]
        execCondition = order_condition.Create(OrderCondition.Execution)
        #When an execution on symbol
        # 合约代码
        execCondition.symbol = symbol
        #at exchange
        # 交易所
        execCondition.exchange = exchange
        #for this secType
        # 合约类型
        execCondition.secType = secType
        #AND | OR next condition (will be ignored if no more conditions are added)
        # 与/或下一个条件（如无更多条件则忽略）
        execCondition.isConjunctionConnection = isConjunction
        #! [execution_condition]
        return execCondition
    

    @staticmethod
    def MarginCondition(percent:int, isMore:bool, isConjunction:bool):
    
        #! [margin_condition]
        marginCondition = order_condition.Create(OrderCondition.Margin)
        #If margin is above/below
        # 保证金高于/低于
        marginCondition.isMore = isMore
        #given percent
        # 百分比
        marginCondition.percent = percent
        #AND | OR next condition (will be ignored if no more conditions are added)
        # 与/或下一个条件（如无更多条件则忽略）
        marginCondition.isConjunctionConnection = isConjunction
        #! [margin_condition]
        return marginCondition
    

    @staticmethod
    def PercentageChangeCondition(pctChange:float, conId:int, exchange:str, 
                                  isMore:bool, isConjunction:bool):
    
        #! [percentage_condition]
        pctChangeCondition = order_condition.Create(OrderCondition.PercentChange)
        #If there is a price percent change measured against last close price above or below...
        # 相较于前收盘价的百分比变动高于/低于
        pctChangeCondition.isMore = isMore
        #this amount...
        # 百分比变动
        pctChangeCondition.changePercent = pctChange
        #on this contract
        # 合约ID
        pctChangeCondition.conId = conId
        #when traded on this exchange...
        # 交易所
        pctChangeCondition.exchange = exchange
        #AND | OR next condition (will be ignored if no more conditions are added)
        # 与/或下一个条件（如无更多条件则忽略）
        pctChangeCondition.isConjunctionConnection = isConjunction
        #! [percentage_condition]
        return pctChangeCondition
    

    @staticmethod
    def TimeCondition(time:str, isMore:bool, isConjunction:bool):
    
        #! [time_condition]
        timeCondition = order_condition.Create(OrderCondition.Time)
        #Before or after...
        # 之前/之后
        timeCondition.isMore = isMore
        #this time..
        # 时间
        timeCondition.time = time
        #AND | OR next condition (will be ignored if no more conditions are added)     
        # 与/或下一个条件（如无更多条件则忽略）
        timeCondition.isConjunctionConnection = isConjunction
        #! [time_condition]
        return timeCondition
    

    @staticmethod
    def VolumeCondition(conId:int, exchange:str, isMore:bool, volume:int, 
                        isConjunction:bool):
    
        #! [volume_condition]
        volCond = order_condition.Create(OrderCondition.Volume)
        #Whenever contract...
        # 合约ID
        volCond.conId = conId
        #When traded at
        # 交易所
        volCond.exchange = exchange
        #reaches a volume higher/lower
        # 成交量高于/低于
        volCond.isMore = isMore
        #than this...
        # 成交量
        volCond.volume = volume
        #AND | OR next condition (will be ignored if no more conditions are added)
        # 与/或下一个条件（如无更多条件则忽略）
        volCond.isConjunctionConnection = isConjunction
        #! [volume_condition]
        return volCond
        
    @staticmethod
    def LimitIBKRATS(action:str, quantity:Decimal, limitPrice:float):
    
        # ! [limit_ibkrats]
        order = Order()
        order.action = action
        order.orderType = "LMT"
        order.lmtPrice = limitPrice
        order.totalQuantity = quantity
        order.notHeld = True
        # ! [limit_ibkrats]
        return order

    @staticmethod
    def LimitOrderWithManualOrderTime(action:str, quantity:Decimal, limitPrice:float, manualOrderTime:str):

        # ! [limit_order_with_manual_order_time]
        order = OrderSamples.LimitOrder(action, quantity, limitPrice)
        order.manualOrderTime = manualOrderTime
        # ! [limit_order_with_manual_order_time]
        return order

    @staticmethod
    def PegBestUpToMidOrder(action:str, quantity:Decimal, limitPrice:float, minTradeQty:int, minCompeteSize:int, midOffsetAtWhole:float, midOffsetAtHalf:float):

        # ! [peg_best_up_to_mid_order]
        order = Order()
        order.action = action
        order.orderType = "PEG BEST"
        order.lmtPrice = limitPrice
        order.totalQuantity = quantity
        order.notHeld = True
        order.minTradeQty = minTradeQty
        order.minCompeteSize = minCompeteSize
        order.competeAgainstBestOffset = COMPETE_AGAINST_BEST_OFFSET_UP_TO_MID
        order.midOffsetAtWhole = midOffsetAtWhole
        order.midOffsetAtHalf = midOffsetAtHalf
        # ! [peg_best_up_to_mid_order]
        return order

    @staticmethod
    def PegBestOrder(action:str, quantity:Decimal, limitPrice:float, minTradeQty:int, minCompeteSize:int, competeAgainstBestOffset:float):

        # ! [peg_best_order]
        order = Order()
        order.action = action
        order.orderType = "PEG BEST"
        order.lmtPrice = limitPrice
        order.totalQuantity = quantity
        order.notHeld = True
        order.minTradeQty = minTradeQty
        order.minCompeteSize = minCompeteSize
        order.competeAgainstBestOffset = competeAgainstBestOffset
        # ! [peg_best_order]
        return order

    @staticmethod
    def PegMidOrder(action:str, quantity:Decimal, limitPrice:float, minTradeQty:int, midOffsetAtWhole:float, midOffsetAtHalf:float):

        # ! [peg_mid_order]
        order = Order()
        order.action = action
        order.orderType = "PEG MID"
        order.lmtPrice = limitPrice
        order.totalQuantity = quantity
        order.notHeld = True
        order.minTradeQty = minTradeQty
        order.midOffsetAtWhole = midOffsetAtWhole
        order.midOffsetAtHalf = midOffsetAtHalf
        # ! [peg_mid_order]
        return order

    @staticmethod
    def LimitOrderWithCustomerAccount(action:str, quantity:Decimal, limitPrice:float, customerAccount:str):

        # ! [limit_order_with_customer_account]
        order = OrderSamples.LimitOrder(action, quantity, limitPrice)
        order.customerAccount = customerAccount
        # ! [limit_order_with_customer_account]
        return order

    @staticmethod
    def CancelOrderEmpty():
        # ! [cancel_order_empty]
        orderCancel = OrderCancel()
        # ! [cancel_order_empty]
        return orderCancel

    @staticmethod
    def CancelOrderWithManualTime(manualOrderCancelTime: str):
        # ! [cancel_order_with_manual_time]
        orderCancel = OrderCancel()
        orderCancel.manualOrderCancelTime = manualOrderCancelTime
        # ! [cancel_order_with_manual_time]
        return orderCancel
    
    @staticmethod
    def Rfq():
        # ! [rfq]
        order = OrderSamples.RfqEmpty()
        order.extOperator = "Ext Operator 1"
        order.externalUserId = "External User Id 1"
        order.manualOrderIndicator = 1
        # ! [rfq]
        return order
    
    @staticmethod
    def RfqEmpty():
        # ! [rfq_empty]
        order = Order()
        order.orderType = "QUOTE"
        order.totalQuantity = Decimal("1")
        # ! [rfq_empty]
        return order

    @staticmethod
    def RfqCancel():
        # ! [rfq_cancel]
        orderCancel = OrderCancel()
        orderCancel.extOperator = "Ext Operator 2"
        orderCancel.externalUserId = "External User Id 2"
        orderCancel.manualOrderIndicator = 1
        # ! [rfq_cancel]
        return orderCancel

def Test():
    os = OrderSamples() # @UnusedVariable

if "__main__" == __name__:
    Test()
