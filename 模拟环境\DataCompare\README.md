# DataCompare tools related
  - 天勤、米匡每日数据对比 for http://1.13.161.17/zentao/task-view-13.html
  - 同花顺、米匡每日数据对比 for http://1.13.161.17/zentao/task-view-7.html
  - 数据库对比脚本

## 安装步骤

Using npm:

```sh
python aaa.py
```

## 脚本运行


## 使用指南

#################  数据库对比脚本 ###################
## 安装步骤
无需安装，当简单的工具脚本使用，目前无传参，无config，python version=3.10

## 脚本运行
python check_database_everyday.py

## 使用指南
目前已经写在cron中，因为需要在8：00后几个库才会更新前一交易日的数据，建议08：10分之后跑
10 08 * * 1-5 nohup /home/<USER>/anaconda3/bin/python /home/<USER>/stock_data_update/check_database_everyday.py ->> /home/<USER>/stock_data_update/logs/data_update_error.log &

#  此脚本用于检查数据库当天最新数据是否有问题：
  1.检查股票池需要的股票是否齐全, 是否有股票缺失
  2.分钟线根数检查, 理论上每天都是一样的(股票240个1min kline,期货看交易时间),不同品种做一个列表, 看是否有缺失
  3.检查价格异常: 价格为0, 价格特别大
  4.ohlc volume turnover oi 和其他数据源对比(mysql和ck两库对比)

