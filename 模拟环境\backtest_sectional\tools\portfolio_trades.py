# -*- coding: utf-8 -*-
"""
Portfolio_trades脚本: 
将所有筛选的trades合并并评估绩效
"""
import pandas as pd
import pymysql
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
from openpyxl.drawing.image import Image
from openpyxl import load_workbook 

conn_mysql = pymysql.connect(host='*************', port=3306, database='TradeInfoRest',user='zh',password='zhP@55word')

all_sizes = {'a': 10, 'ag': 15, 'al': 5, 'ao': 20, 'AP': 10, 'au': 1000, 'b': 10, 'bb': 500, 'bc': 5, 'br': 5, 'bu': 10,
             'c': 10, 'CF': 5, 'CJ': 5, 'cs': 10, 'cu': 5, 'CY': 5, 'eb': 5, 'ec': 50, 'eg': 10, 'fb': 10, 'FG': 20,
             'fu': 10, 'hc': 10, 'i': 100, 'j': 100, 'jd': 10, 'jm': 60, 'JR': 20, 'l': 5, 'lc': 1, 'lh': 16, 'LR': 20,
             'lu': 10, 'm': 10, 'MA': 10, 'ni': 1, 'nr': 10, 'OI': 10, 'p': 10, 'pb': 5, 'PF': 5, 'pg': 20, 'PK': 5,
             'PM': 50, 'pp': 5, 'PX': 5, 'rb': 10, 'RI': 20, 'RM': 10, 'rr': 10, 'RS': 10, 'ru': 10, 'SA': 20,
             'sc': 1000, 'sctas': 1000, 'SF': 5, 'SH': 30, 'si': 5, 'SM': 5, 'sn': 1, 'sp': 10, 'SR': 10, 'ss': 5,
             'TA': 5, 'UR': 20, 'v': 5, 'WH': 20, 'wr': 10, 'y': 10, 'ZC': 100, 'zn': 5, 'IC': 200, 'IF': 300,
             'IH': 300, 'IM': 200, 'T': 10000, 'TF': 10000, 'TS': 20000}

all_priceticks = {'a': 1.0, 'ag': 1.0, 'al': 5.0, 'ao': 1.0, 'AP': 1.0, 'au': 0.02, 'b': 1.0, 'bb': 0.05, 'bc': 10.0,
                  'br': 5.0, 'bu': 1.0, 'c': 1.0, 'CF': 5.0, 'CJ': 5.0, 'cs': 1.0, 'cu': 10.0, 'CY': 5.0, 'eb': 1.0,
                  'ec': 0.1, 'eg': 1.0, 'fb': 0.5, 'FG': 1.0, 'fu': 1.0, 'hc': 1.0, 'i': 0.5, 'j': 0.5, 'jd': 1.0,
                  'jm': 0.5, 'JR': 1.0, 'l': 1.0, 'lc': 50.0, 'lh': 5.0, 'LR': 1.0, 'lu': 1.0, 'm': 1.0, 'MA': 1.0,
                  'ni': 10.0, 'nr': 5.0, 'OI': 1.0, 'p': 2.0, 'pb': 5.0, 'PF': 2.0, 'pg': 1.0, 'PK': 2.0, 'PM': 1.0,
                  'pp': 1.0, 'PX': 2.0, 'rb': 1.0, 'RI': 1.0, 'RM': 1.0, 'rr': 1.0, 'RS': 1.0, 'ru': 5.0, 'SA': 1.0,
                  'sc': 0.1, 'sctas': 0.1, 'SF': 2.0, 'SH': 1.0, 'si': 5.0, 'SM': 2.0, 'sn': 10.0, 'sp': 2.0, 'SR': 1.0,
                  'ss': 5.0, 'TA': 2.0, 'UR': 1.0, 'v': 1.0, 'WH': 1.0, 'wr': 1.0, 'y': 2.0, 'ZC': 0.2, 'zn': 5.0,
                  'IC': 0.2, 'IF': 0.2, 'IH': 0.2, 'IM': 0.2, 'T': 0.005, 'TF': 0.005, 'TS': 0.002}


class Portfolio():
    def get_trades(self, strategy_name, author, symbol='all'):
        """
        处理trades, 根据strategy_name和author筛选出对应的记录
        """
        if len(strategy_name) == 1:
            strategy_name_str = f"('{strategy_name[0]}')"
        else:
            strategy_name_str = str(tuple(strategy_name))
        if symbol == 'all':
            sql = f"select symbol,datetime as 'current_time',direction,offset,price,volume from TradeInfoRest.trade_info where strategy_name in {strategy_name_str} and author='{author}'"
        else:
            if len(symbol) == 1:
                symbol_str = f"('{symbol[0]}')"
            else:
                symbol_str = str(tuple(symbol))
            sql = f"select symbol,datetime as 'current_time',direction,offset,price,volume from TradeInfoRest.trade_info where strategy_name in {strategy_name_str} and author='{author}' and symbol in {symbol_str}"
        data = pd.read_sql(sql, conn_mysql)
        if data.empty:
            print(f'没有找到对应的交易记录，strategy_name:{strategy_name}，author:{author}，symbol:{symbol}')
            return data
        data['current_time'] = pd.to_datetime(data['current_time'])
        data = data.sort_values(by='current_time')
        return data

    def get_raw_trade(self, trades, symbol): 
        # Generate DataFrame with datetime, direction, offset, price, volume
        size = all_sizes[symbol[:-3]]
        df = trades
        if df.empty:
            return pd.DataFrame()
        df['size'] = size
        df["last_time"] = df["current_time"].shift(1)
        df["entry_price"] = df["price"].shift(1)
        df["amount"] = df["price"] * df["volume"] * size
        df["acum_amount"] = df["amount"].cumsum()
        # print(f"=================debug middle1============")
        # print(df)
        def calculate_pos(df):
            if df["direction"] == "多":
                result = df["volume"]
            else:
                result = - df["volume"]
            return result
        
        df["pos"] = df.apply(calculate_pos, axis=1)
        df["net_pos"] = df["pos"].cumsum()     # 净持仓
        df["acum_pos"] = df["volume"].cumsum() # 总交易手数
        # print(f"=================debug middle2============")
        # print(df)
        
        df["result"] = -1 * df["pos"] * df["price"] * size  # 每手交易对balance影响
        df["acum_result"] = df["result"].cumsum()           # 
        
        def get_acum_trade_duration(df):
            if df["net_pos"] == 0:
                return df["current_time"] - df["last_time"]
        df["acum_trade_duration"] = df.apply(get_acum_trade_duration, axis=1)
        
        # Filter column data when net pos comes to zero
        def get_acum_trade_result(df):
            if df["net_pos"] == 0:
                return df["acum_result"]
        df["acum_trade_result"] = df.apply(get_acum_trade_result, axis=1)  # 每次交易平仓后的可用资金（后-前 为pnl）
        
        def get_acum_trade_volume(df):
            if df["net_pos"] == 0:
                return df["acum_pos"]
        df["acum_trade_volume"] = df.apply(get_acum_trade_volume, axis=1) # 总交易手数
        def get_acum_trade_amount(df):
            if df["net_pos"] == 0:
                return df["acum_amount"]
        df["acum_trade_amount"] = df.apply(get_acum_trade_amount, axis=1) # 总交易额
        df = df.dropna()
        return df

    def generate_trade_df(self, raw_df,rate,slippage):
        if raw_df.empty:
            return pd.DataFrame()
        trade_df = pd.DataFrame()
        df = raw_df
        trade_df["close_direction"] = df["direction"]
        trade_df["close_time"] = df["current_time"]
        trade_df["entry_time"] = df["last_time"]
        trade_df["close_price"] = df["price"]
        trade_df["entry_price"] = df["entry_price"]
        trade_df["pnl"] = df["acum_trade_result"] - df["acum_trade_result"].shift(1).fillna(0)

        trade_df["volume"] = df["acum_trade_volume"] - df["acum_trade_volume"].shift(1).fillna(0)
        trade_df["duration"] = df["current_time"] - df["last_time"]
        trade_df["turnover"] = df["acum_trade_amount"] - df["acum_trade_amount"].shift(1).fillna(0)
        trade_df['size'] = df['size']
        trade_df["commission"] = trade_df["turnover"] * rate
        trade_df["slipping"] = trade_df["volume"] * trade_df["size"] * slippage
        
        trade_df["net_pnl"] = trade_df["pnl"] - trade_df["commission"] - trade_df["slipping"]
        return trade_df

    def calculate_base_net_pnl(self, df, capital):
        """
        Calculate statistic base on net pnl
        """
        df["acum_pnl"] = df["net_pnl"].cumsum()
        df["balance"] = df["acum_pnl"] + capital
        df["return"] = np.log(
            df["balance"] / df["balance"].shift(1)
        ).fillna(0)
        df["highlevel"] = (df["balance"].rolling(min_periods=1, window=len(df), center=False).max())
        df.loc[df["highlevel"]<capital, 'highlevel'] = capital
        # print(df['highlevel'])
        df["drawdown"] = df["balance"] - df["highlevel"]
        df["ddpercent"] = df["drawdown"] / df["highlevel"] * 100
        df.reset_index(drop=True, inplace=True)
        return df

    def statistics_trade_result(self, df, capital, show_chart=True):
        """"""
        if df.empty:
            end_balance = capital
            max_drawdown = 0
            max_ddpercent = 0
            pnl_medio = 0
            trade_count = 0
            duration_medio = 0
            commission_medio = 0
            slipping_medio = 0
            win_amount = 0
            win_pnl_medio = 0
            win_duration_medio = 0
            win_count = 0
            loss_amount = 0
            loss_pnl_medio = 0
            loss_duration_medio = 0
            loss_count = 0
            winning_rate = 0
            win_loss_pnl_ratio = 0
            total_return = 0
            return_drawdown_ratio = 0
        else:
            end_balance = df["balance"].iloc[-1]
            max_drawdown = df["drawdown"].min()
            max_ddpercent = df["ddpercent"].min()

            pnl_medio = df["net_pnl"].mean()
            trade_count = len(df)
            duration_medio = df["duration"].mean().total_seconds()/3600
            commission_medio = df["commission"].mean()
            slipping_medio = df["slipping"].mean()

            win = df[df["net_pnl"] > 0]
            win_amount = win["net_pnl"].sum()
            win_pnl_medio = win["net_pnl"].mean()
            win_duration_medio = win["duration"].mean().total_seconds()/3600
            win_count = len(win)

            loss = df[df["net_pnl"] < 0]
            loss_amount = loss["net_pnl"].sum()
            loss_pnl_medio = loss["net_pnl"].mean()
            loss_duration_medio = loss["duration"].mean().total_seconds()/3600
            loss_count = len(loss)

            winning_rate = win_count / trade_count
            win_loss_pnl_ratio = - win_pnl_medio / loss_pnl_medio

            total_return = (end_balance / capital - 1) * 100
            return_drawdown_ratio = -total_return / max_ddpercent
        
        date_str = datetime.now().strftime('%Y%m%d')
        file_name = f"{self.strategy_name}_{date_str}.txt"
        
        self.output(f"起始资金:\t{capital:,.2f}",file_name)
        self.output(f"结束资金:\t{end_balance:,.2f}",file_name)
        self.output(f"总收益率:\t{total_return:,.2f}%",file_name)
        self.output(f"最大回撤: \t{max_drawdown:,.2f}",file_name)
        self.output(f"百分比最大回撤: {max_ddpercent:,.2f}%",file_name)
        self.output(f"收益回撤比:\t{return_drawdown_ratio:,.2f}",file_name)

        self.output(f"总成交次数:\t{trade_count}",file_name)
        self.output(f"盈利成交次数:\t{win_count}",file_name)
        self.output(f"亏损成交次数:\t{loss_count}",file_name)
        self.output(f"胜率:\t\t{winning_rate:,.2f}",file_name)
        self.output(f"盈亏比:\t\t{win_loss_pnl_ratio:,.2f}",file_name)

        self.output(f"平均每笔盈亏:\t{pnl_medio:,.2f}",file_name)
        self.output(f"平均持仓小时:\t{duration_medio:,.2f}",file_name)
        self.output(f"平均每笔手续费:\t{commission_medio:,.2f}",file_name)
        self.output(f"平均每笔滑点:\t{slipping_medio:,.2f}",file_name)

        self.output(f"总盈利金额:\t{win_amount:,.2f}",file_name)
        self.output(f"盈利交易均值:\t{win_pnl_medio:,.2f}",file_name)
        self.output(f"盈利持仓小时:\t{win_duration_medio:,.2f}",file_name)

        self.output(f"总亏损金额:\t{loss_amount:,.2f}",file_name)
        self.output(f"亏损交易均值:\t{loss_pnl_medio:,.2f}",file_name)
        self.output(f"亏损持仓小时:\t{loss_duration_medio:,.2f}",file_name)

        if not show_chart:
            return

        if df.empty:
            return
        self.statistics =   {"起始资金":capital, \
                            "结束资金":end_balance, \
                            "总收益率":total_return, \
                            "最大回撤": max_drawdown, \
                            "百分比最大回撤": max_ddpercent,\
                            "收益回撤比":return_drawdown_ratio,\
                            "总成交次数":trade_count,\
                            "盈利成交次数":win_count,\
                            "亏损成交次数":loss_count,\
                            "胜率":winning_rate,\
                            "盈亏比":win_loss_pnl_ratio,\
                            "平均每笔盈亏":pnl_medio,\
                            "平均持仓小时":duration_medio,\
                            "平均每笔手续费":commission_medio,\
                            "平均每笔滑点":slipping_medio,\
                            "总盈利金额":win_amount,\
                            "盈利交易均值":win_pnl_medio,\
                            "盈利持仓小时":win_duration_medio,\
                            "总亏损金额":loss_amount,\
                            "亏损交易均值":loss_pnl_medio,\
                            "亏损持仓小时":loss_duration_medio,\
                                        }

        plt.figure(figsize=(10, 12))
        acum_pnl_plot = plt.subplot(3, 1, 1)
        acum_pnl_plot.set_title("Balance Plot")
        df["balance"].plot(legend=True,grid=True)

        pnl_plot = plt.subplot(3, 1, 2)
        pnl_plot.set_title("Pnl Per Trade")
        df["net_pnl"].plot(legend=True,grid=True,kind="bar")

        distribution_plot = plt.subplot(3, 1, 3)
        distribution_plot.set_title("Trade Pnl Distribution")
        df["net_pnl"].hist(bins=100,grid=True)

        # plt.show()
        plt.savefig(f"{date_str}.png")

    def write_to_file(self, filename, text):
        with open(filename, 'a', encoding='utf-8') as file:
            file.write(text)

    def output(self, msg,outputfile=""):
        """
        Output message with datetime.
        """
        print(f"{datetime.now()}\t{msg}")
        if outputfile!="":
            self.write_to_file(outputfile,msg+"\n")

    def exhaust_trade_result(
        self,
        trades,
        capital: int = 1000000
    ):
        """
        Exhaust all trade result.
        """

        self.output("总体情况")
        self.output("-----------------------")
        total_trades = trades
        self.statistics_trade_result(total_trades, capital)

    def summary(self, strategy_name, author, rate, slippage, capital, symbol='all'):
        self.strategy_name = strategy_name
        trades = self.get_trades(strategy_name, author,symbol)
        trade_df = pd.DataFrame()
        for symbol in trades.symbol.unique():
            trades_symbol = trades[trades.symbol==symbol]
            df = self.get_raw_trade(trades_symbol, symbol)
            if df.empty:
                return
            trade_df_tmp = self.generate_trade_df(df, rate, slippage)
            trade_df = pd.concat([trade_df, trade_df_tmp])
            trade_df = trade_df.sort_values('entry_time')
        # print(trade_df)
        trade_df = self.calculate_base_net_pnl(trade_df, capital)
        self.exhaust_trade_result(trade_df, capital)

        # 保存数据
        date_str = datetime.now().strftime('%Y%m%d')
        #outfile_name = f"{date_str}.xlsx"
        outfile_name = f"{self.strategy_name}_{date_str}.xlsx"

        writer=pd.ExcelWriter(outfile_name)
        sub_df = pd.DataFrame([self.statistics]).T
        sub_df.to_excel(writer,sheet_name = date_str, header=False)
        # writer.save()
        writer.close()

        # 插入图片
        workbook  = load_workbook(outfile_name)
        ws = workbook[date_str]
        img = Image(f"{date_str}.png")
        ws.add_image(img,"E2")
        workbook.save(outfile_name)


if __name__ == '__main__':
    strategy_name = ['liaoyuan_DXJC']
    author = '廖'
    symbol = ['FG888','SR888']
    # rate = 2.5e-4
    # slippage = 1
    rate = 0
    slippage = 0
    capital = 100e4

    pf = Portfolio()
    pf.summary(strategy_name, author, rate, slippage, capital, symbol)