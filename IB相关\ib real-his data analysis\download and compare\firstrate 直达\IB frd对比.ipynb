{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2028ff91b0fff30a", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-08-29T09:59:53.145152400Z", "start_time": "2023-08-29T09:59:47.745512600Z"}}, "outputs": [{"data": {"text/plain": "           id               symbol exchange            datetime interval  \\\n0      173362          PFE-USD-STK    SMART 2023-08-28 21:30:00       1m   \n1      173363          PFE-USD-STK    SMART 2023-08-28 21:31:00       1m   \n2      173364          PFE-USD-STK    SMART 2023-08-28 21:32:00       1m   \n3      173365          PFE-USD-STK    SMART 2023-08-28 21:33:00       1m   \n4      173366          PFE-USD-STK    SMART 2023-08-28 21:34:00       1m   \n...       ...                  ...      ...                 ...      ...   \n10135  191629  NQ-20230915-USD-FUT      CME 2023-08-29 03:55:00       1m   \n10136  191630  NQ-20230915-USD-FUT      CME 2023-08-29 03:56:00       1m   \n10137  191631  NQ-20230915-USD-FUT      CME 2023-08-29 03:57:00       1m   \n10138  191632  NQ-20230915-USD-FUT      CME 2023-08-29 03:58:00       1m   \n10139  191633  NQ-20230915-USD-FUT      CME 2023-08-29 03:59:00       1m   \n\n       volume  turnover  open_interest  open_price  high_price  low_price  \\\n0      3118.0       0.0            0.0       36.44       36.49      36.28   \n1       264.0       0.0            0.0       36.40       36.46      36.40   \n2       411.0       0.0            0.0       36.43       36.52      36.42   \n3       309.0       0.0            0.0       36.50       36.50      36.42   \n4       299.0       0.0            0.0       36.44       36.47      36.40   \n...       ...       ...            ...         ...         ...        ...   \n10135  1470.0       0.0            0.0    15079.00    15091.00   15073.20   \n10136  1495.0       0.0            0.0    15090.00    15094.00   15083.80   \n10137   831.0       0.0            0.0    15084.80    15092.00   15083.80   \n10138  1372.0       0.0            0.0    15088.50    15094.50   15087.20   \n10139  8727.0       0.0            0.0    15094.20    15099.50   15085.00   \n\n       close_price  \n0            36.40  \n1            36.42  \n2            36.48  \n3            36.44  \n4            36.41  \n...            ...  \n10135     15090.00  \n10136     15085.00  \n10137     15088.00  \n10138     15094.50  \n10139     15091.50  \n\n[10140 rows x 12 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>datetime</th>\n      <th>interval</th>\n      <th>volume</th>\n      <th>turnover</th>\n      <th>open_interest</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>173362</td>\n      <td>PFE-USD-STK</td>\n      <td>SMART</td>\n      <td>2023-08-28 21:30:00</td>\n      <td>1m</td>\n      <td>3118.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>36.44</td>\n      <td>36.49</td>\n      <td>36.28</td>\n      <td>36.40</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>173363</td>\n      <td>PFE-USD-STK</td>\n      <td>SMART</td>\n      <td>2023-08-28 21:31:00</td>\n      <td>1m</td>\n      <td>264.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>36.40</td>\n      <td>36.46</td>\n      <td>36.40</td>\n      <td>36.42</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>173364</td>\n      <td>PFE-USD-STK</td>\n      <td>SMART</td>\n      <td>2023-08-28 21:32:00</td>\n      <td>1m</td>\n      <td>411.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>36.43</td>\n      <td>36.52</td>\n      <td>36.42</td>\n      <td>36.48</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>173365</td>\n      <td>PFE-USD-STK</td>\n      <td>SMART</td>\n      <td>2023-08-28 21:33:00</td>\n      <td>1m</td>\n      <td>309.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>36.50</td>\n      <td>36.50</td>\n      <td>36.42</td>\n      <td>36.44</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>173366</td>\n      <td>PFE-USD-STK</td>\n      <td>SMART</td>\n      <td>2023-08-28 21:34:00</td>\n      <td>1m</td>\n      <td>299.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>36.44</td>\n      <td>36.47</td>\n      <td>36.40</td>\n      <td>36.41</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>10135</th>\n      <td>191629</td>\n      <td>NQ-20230915-USD-FUT</td>\n      <td>CME</td>\n      <td>2023-08-29 03:55:00</td>\n      <td>1m</td>\n      <td>1470.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>15079.00</td>\n      <td>15091.00</td>\n      <td>15073.20</td>\n      <td>15090.00</td>\n    </tr>\n    <tr>\n      <th>10136</th>\n      <td>191630</td>\n      <td>NQ-20230915-USD-FUT</td>\n      <td>CME</td>\n      <td>2023-08-29 03:56:00</td>\n      <td>1m</td>\n      <td>1495.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>15090.00</td>\n      <td>15094.00</td>\n      <td>15083.80</td>\n      <td>15085.00</td>\n    </tr>\n    <tr>\n      <th>10137</th>\n      <td>191631</td>\n      <td>NQ-20230915-USD-FUT</td>\n      <td>CME</td>\n      <td>2023-08-29 03:57:00</td>\n      <td>1m</td>\n      <td>831.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>15084.80</td>\n      <td>15092.00</td>\n      <td>15083.80</td>\n      <td>15088.00</td>\n    </tr>\n    <tr>\n      <th>10138</th>\n      <td>191632</td>\n      <td>NQ-20230915-USD-FUT</td>\n      <td>CME</td>\n      <td>2023-08-29 03:58:00</td>\n      <td>1m</td>\n      <td>1372.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>15088.50</td>\n      <td>15094.50</td>\n      <td>15087.20</td>\n      <td>15094.50</td>\n    </tr>\n    <tr>\n      <th>10139</th>\n      <td>191633</td>\n      <td>NQ-20230915-USD-FUT</td>\n      <td>CME</td>\n      <td>2023-08-29 03:59:00</td>\n      <td>1m</td>\n      <td>8727.0</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>15094.20</td>\n      <td>15099.50</td>\n      <td>15085.00</td>\n      <td>15091.50</td>\n    </tr>\n  </tbody>\n</table>\n<p>10140 rows × 12 columns</p>\n</div>"}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime, timedelta, time\n", "from urllib.parse import quote as urlquote\n", "\n", "# 录制数据库配置\n", "username = 'root'\n", "password = 'p0o9i8u7'\n", "dbHost = '**************'\n", "dbPort = 3306\n", "dbName = 'vnpyibhis'\n", "DATABASE_URI = f'mysql+pymysql://{username}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}'\n", "\n", "# 使用pandas和sql语句，从数据库中读取数据保存到csv文件中\n", "import pandas as pd\n", "from sqlalchemy import create_engine\n", "engine = create_engine(DATABASE_URI)\n", "# 前一天下午9点到今天上午6点的数据\n", "today = datetime.now().date()\n", "yesterday = today - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "yesterday_9pm_str = datetime.combine(yesterday, time(21, 30)).strftime('%Y-%m-%d %H:%M:%S')\n", "today_4am_str = datetime.combine(today, time(4, 0)).strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "sql = f\"select * from dbbardata where datetime >= '{yesterday_9pm_str}' and datetime <= '{today_4am_str}'\"\n", "df = pd.read_sql(sql, engine)\n", "# df.to_csv(f'{today}.csv', index=False)\n", "# from IPython.display import display\n", "# display(df)\n", "df"]}, {"cell_type": "code", "execution_count": 26, "outputs": [{"data": {"text/plain": "            symbol             datetime  open_price  high_price  low_price  \\\n7800  AAPL-USD-STK  2023-08-28 21:30:00      180.09      180.20     179.40   \n7801  AAPL-USD-STK  2023-08-28 21:31:00      179.48      179.77     179.46   \n7802  AAPL-USD-STK  2023-08-28 21:32:00      179.66      179.71     179.43   \n7803  AAPL-USD-STK  2023-08-28 21:33:00      179.46      179.89     179.45   \n7804  AAPL-USD-STK  2023-08-28 21:34:00      179.89      180.03     179.82   \n...            ...                  ...         ...         ...        ...   \n9355  AMZN-USD-STK  2023-08-29 03:55:00      133.04      133.19     132.91   \n9356  AMZN-USD-STK  2023-08-29 03:56:00      133.18      133.26     133.06   \n9357  AMZN-USD-STK  2023-08-29 03:57:00      133.07      133.26     133.07   \n9358  AMZN-USD-STK  2023-08-29 03:58:00      133.20      133.21     133.12   \n9359  AMZN-USD-STK  2023-08-29 03:59:00      133.18      133.27     133.06   \n\n      close_price  volume  \n7800       179.48  7035.0  \n7801       179.65  1492.0  \n7802       179.46  1027.0  \n7803       179.88  1276.0  \n7804       179.99  1616.0  \n...           ...     ...  \n9355       133.17  1871.0  \n9356       133.07  1672.0  \n9357       133.20  2034.0  \n9358       133.17  2091.0  \n9359       133.15  5684.0  \n\n[1560 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>7800</th>\n      <td>AAPL-USD-STK</td>\n      <td>2023-08-28 21:30:00</td>\n      <td>180.09</td>\n      <td>180.20</td>\n      <td>179.40</td>\n      <td>179.48</td>\n      <td>7035.0</td>\n    </tr>\n    <tr>\n      <th>7801</th>\n      <td>AAPL-USD-STK</td>\n      <td>2023-08-28 21:31:00</td>\n      <td>179.48</td>\n      <td>179.77</td>\n      <td>179.46</td>\n      <td>179.65</td>\n      <td>1492.0</td>\n    </tr>\n    <tr>\n      <th>7802</th>\n      <td>AAPL-USD-STK</td>\n      <td>2023-08-28 21:32:00</td>\n      <td>179.66</td>\n      <td>179.71</td>\n      <td>179.43</td>\n      <td>179.46</td>\n      <td>1027.0</td>\n    </tr>\n    <tr>\n      <th>7803</th>\n      <td>AAPL-USD-STK</td>\n      <td>2023-08-28 21:33:00</td>\n      <td>179.46</td>\n      <td>179.89</td>\n      <td>179.45</td>\n      <td>179.88</td>\n      <td>1276.0</td>\n    </tr>\n    <tr>\n      <th>7804</th>\n      <td>AAPL-USD-STK</td>\n      <td>2023-08-28 21:34:00</td>\n      <td>179.89</td>\n      <td>180.03</td>\n      <td>179.82</td>\n      <td>179.99</td>\n      <td>1616.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>9355</th>\n      <td>AMZN-USD-STK</td>\n      <td>2023-08-29 03:55:00</td>\n      <td>133.04</td>\n      <td>133.19</td>\n      <td>132.91</td>\n      <td>133.17</td>\n      <td>1871.0</td>\n    </tr>\n    <tr>\n      <th>9356</th>\n      <td>AMZN-USD-STK</td>\n      <td>2023-08-29 03:56:00</td>\n      <td>133.18</td>\n      <td>133.26</td>\n      <td>133.06</td>\n      <td>133.07</td>\n      <td>1672.0</td>\n    </tr>\n    <tr>\n      <th>9357</th>\n      <td>AMZN-USD-STK</td>\n      <td>2023-08-29 03:57:00</td>\n      <td>133.07</td>\n      <td>133.26</td>\n      <td>133.07</td>\n      <td>133.20</td>\n      <td>2034.0</td>\n    </tr>\n    <tr>\n      <th>9358</th>\n      <td>AMZN-USD-STK</td>\n      <td>2023-08-29 03:58:00</td>\n      <td>133.20</td>\n      <td>133.21</td>\n      <td>133.12</td>\n      <td>133.17</td>\n      <td>2091.0</td>\n    </tr>\n    <tr>\n      <th>9359</th>\n      <td>AMZN-USD-STK</td>\n      <td>2023-08-29 03:59:00</td>\n      <td>133.18</td>\n      <td>133.27</td>\n      <td>133.06</td>\n      <td>133.15</td>\n      <td>5684.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>1560 rows × 7 columns</p>\n</div>"}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# 筛选symbol为AMZN-USD-STK、AAPL-USD-STK、TSLA-USD-STK、MSFT-USD-STK的数据\n", "df = df[df['symbol'].isin(['AMZN-USD-STK', 'AAPL-USD-STK', 'TSLA-USD-STK', 'MSFT-USD-STK'])]\n", "# 只保留symbol、datetime、open_price、high_price、low_price、close_price、volume列\n", "df = df[['symbol', 'datetime', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']]\n", "# 将df的datetime列的格式转为2023-08-28 15:00:00\n", "df['datetime'] = pd.to_datetime(df['datetime']).dt.strftime('%Y-%m-%d %H:%M:%S')\n", "df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T06:58:10.667261500Z", "start_time": "2023-08-29T06:58:10.609257400Z"}}, "id": "e3e18f1dfb591a5e"}, {"cell_type": "code", "execution_count": 27, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_768\\517537922.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_768\\517537922.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_768\\517537922.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_768\\517537922.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n"]}, {"data": {"text/plain": "                 datetime  open_price  high_price  low_price  close_price  \\\n7379  2023-08-28 21:30:00    133.7900     133.825    133.165     133.2300   \n7380  2023-08-28 21:31:00    133.2100     133.640    133.210     133.6241   \n7381  2023-08-28 21:32:00    133.6400     133.670    133.470     133.5600   \n7382  2023-08-28 21:33:00    133.5500     133.950    133.550     133.8200   \n7383  2023-08-28 21:34:00    133.8191     133.910    133.690     133.7000   \n...                   ...         ...         ...        ...          ...   \n6551  2023-08-29 03:56:00    323.8200     323.930    323.551     323.5510   \n6552  2023-08-29 03:57:00    323.5600     323.840    323.540     323.7600   \n6553  2023-08-29 03:58:00    323.7800     323.880    323.725     323.8700   \n6554  2023-08-29 03:59:00    323.8700     323.990    323.560     323.7400   \n6555  2023-08-29 04:00:00    323.7000     323.770    323.700     323.7000   \n\n       volume        symbol  \n7379   759125  AMZN-USD-STK  \n7380   189784  AMZN-USD-STK  \n7381   130826  AMZN-USD-STK  \n7382   155831  AMZN-USD-STK  \n7383    87759  AMZN-USD-STK  \n...       ...           ...  \n6551    54275  MSFT-USD-STK  \n6552    41968  MSFT-USD-STK  \n6553    85871  MSFT-USD-STK  \n6554   326450  MSFT-USD-STK  \n6555  2085778  MSFT-USD-STK  \n\n[1564 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>datetime</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n      <th>symbol</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>7379</th>\n      <td>2023-08-28 21:30:00</td>\n      <td>133.7900</td>\n      <td>133.825</td>\n      <td>133.165</td>\n      <td>133.2300</td>\n      <td>759125</td>\n      <td>AMZN-USD-STK</td>\n    </tr>\n    <tr>\n      <th>7380</th>\n      <td>2023-08-28 21:31:00</td>\n      <td>133.2100</td>\n      <td>133.640</td>\n      <td>133.210</td>\n      <td>133.6241</td>\n      <td>189784</td>\n      <td>AMZN-USD-STK</td>\n    </tr>\n    <tr>\n      <th>7381</th>\n      <td>2023-08-28 21:32:00</td>\n      <td>133.6400</td>\n      <td>133.670</td>\n      <td>133.470</td>\n      <td>133.5600</td>\n      <td>130826</td>\n      <td>AMZN-USD-STK</td>\n    </tr>\n    <tr>\n      <th>7382</th>\n      <td>2023-08-28 21:33:00</td>\n      <td>133.5500</td>\n      <td>133.950</td>\n      <td>133.550</td>\n      <td>133.8200</td>\n      <td>155831</td>\n      <td>AMZN-USD-STK</td>\n    </tr>\n    <tr>\n      <th>7383</th>\n      <td>2023-08-28 21:34:00</td>\n      <td>133.8191</td>\n      <td>133.910</td>\n      <td>133.690</td>\n      <td>133.7000</td>\n      <td>87759</td>\n      <td>AMZN-USD-STK</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>6551</th>\n      <td>2023-08-29 03:56:00</td>\n      <td>323.8200</td>\n      <td>323.930</td>\n      <td>323.551</td>\n      <td>323.5510</td>\n      <td>54275</td>\n      <td>MSFT-USD-STK</td>\n    </tr>\n    <tr>\n      <th>6552</th>\n      <td>2023-08-29 03:57:00</td>\n      <td>323.5600</td>\n      <td>323.840</td>\n      <td>323.540</td>\n      <td>323.7600</td>\n      <td>41968</td>\n      <td>MSFT-USD-STK</td>\n    </tr>\n    <tr>\n      <th>6553</th>\n      <td>2023-08-29 03:58:00</td>\n      <td>323.7800</td>\n      <td>323.880</td>\n      <td>323.725</td>\n      <td>323.8700</td>\n      <td>85871</td>\n      <td>MSFT-USD-STK</td>\n    </tr>\n    <tr>\n      <th>6554</th>\n      <td>2023-08-29 03:59:00</td>\n      <td>323.8700</td>\n      <td>323.990</td>\n      <td>323.560</td>\n      <td>323.7400</td>\n      <td>326450</td>\n      <td>MSFT-USD-STK</td>\n    </tr>\n    <tr>\n      <th>6555</th>\n      <td>2023-08-29 04:00:00</td>\n      <td>323.7000</td>\n      <td>323.770</td>\n      <td>323.700</td>\n      <td>323.7000</td>\n      <td>2085778</td>\n      <td>MSFT-USD-STK</td>\n    </tr>\n  </tbody>\n</table>\n<p>1564 rows × 7 columns</p>\n</div>"}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["import pytz\n", "# 从frd_stock_sample文件夹下的AMZN_1min_sample.csv、AAPL_1min_sample.csv、TSLA_1min_sample.csv、MSFT_1min_sample.csv文件中读取数据，AMZN等作为symbol列的值\n", "df_frd = pd.DataFrame()\n", "for symbol in ['AMZN', 'AAPL', 'TSLA', 'MSFT']:\n", "    df_frd_symbol = pd.read_csv(f'frd_stock_sample/{symbol}_1min_sample.csv')\n", "    df_frd_symbol['symbol'] = f'{symbol}-USD-STK'\n", "    df_frd = df_frd.append(df_frd_symbol)\n", "# timestamp列（格式为2023-08-28 15:00:00）设置时区为美东，然后转为上海时区，再格式化为2023-08-28 15:00:00格式\n", "df_frd['timestamp'] = pd.to_datetime(df_frd['timestamp']).dt.tz_localize('US/Eastern').dt.tz_convert('Asia/Shanghai').dt.strftime('%Y-%m-%d %H:%M:%S')\n", "# 筛选timestamp为2023-08-28 21:30:00到2023-08-29 04:00:00的数据\n", "df_frd = df_frd[(df_frd['timestamp'] >= yesterday_9pm_str) & (df_frd['timestamp'] <= today_4am_str)]\n", "# 重命名各列\n", "df_frd = df_frd.rename(columns={'timestamp': 'datetime', 'open': 'open_price', 'high': 'high_price', 'low': 'low_price', 'close': 'close_price', 'volume': 'volume'})\n", "df_frd"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T06:58:17.785030900Z", "start_time": "2023-08-29T06:58:17.308033900Z"}}, "id": "94a36b9b6c9d5c7d"}, {"cell_type": "code", "execution_count": 28, "outputs": [], "source": ["# 比较df与df_frd的数据（以symbol、datetime为索引），输出：df中有，df_frd中没有的数据、df_frd中有，df中没有的数据；df中df_frd中都有的数据则作差，输出不为0的差值\n", "# 以symbol、datetime为索引\n", "df = df.set_index(['symbol', 'datetime'])\n", "df_frd = df_frd.set_index(['symbol', 'datetime'])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T06:58:20.325838200Z", "start_time": "2023-08-29T06:58:20.300834900Z"}}, "id": "fcfbf37d82d294ea"}, {"cell_type": "code", "execution_count": 33, "outputs": [{"data": {"text/plain": "                                  open_price  high_price  low_price  \\\nsymbol       datetime                                                 \nAAPL-USD-STK 2023-08-28 21:30:00      180.09      180.20     179.40   \n             2023-08-28 21:31:00      179.48      179.77     179.46   \n             2023-08-28 21:32:00      179.66      179.71     179.43   \n             2023-08-28 21:33:00      179.46      179.89     179.45   \n             2023-08-28 21:34:00      179.89      180.03     179.82   \n...                                      ...         ...        ...   \nAMZN-USD-STK 2023-08-29 03:55:00      133.04      133.19     132.91   \n             2023-08-29 03:56:00      133.18      133.26     133.06   \n             2023-08-29 03:57:00      133.07      133.26     133.07   \n             2023-08-29 03:58:00      133.20      133.21     133.12   \n             2023-08-29 03:59:00      133.18      133.27     133.06   \n\n                                  close_price    volume  \nsymbol       datetime                                    \nAAPL-USD-STK 2023-08-28 21:30:00       179.48  703500.0  \n             2023-08-28 21:31:00       179.65  149200.0  \n             2023-08-28 21:32:00       179.46  102700.0  \n             2023-08-28 21:33:00       179.88  127600.0  \n             2023-08-28 21:34:00       179.99  161600.0  \n...                                       ...       ...  \nAMZN-USD-STK 2023-08-29 03:55:00       133.17  187100.0  \n             2023-08-29 03:56:00       133.07  167200.0  \n             2023-08-29 03:57:00       133.20  203400.0  \n             2023-08-29 03:58:00       133.17  209100.0  \n             2023-08-29 03:59:00       133.15  568400.0  \n\n[1560 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">AAPL-USD-STK</th>\n      <th>2023-08-28 21:30:00</th>\n      <td>180.09</td>\n      <td>180.20</td>\n      <td>179.40</td>\n      <td>179.48</td>\n      <td>703500.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <td>179.48</td>\n      <td>179.77</td>\n      <td>179.46</td>\n      <td>179.65</td>\n      <td>149200.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <td>179.66</td>\n      <td>179.71</td>\n      <td>179.43</td>\n      <td>179.46</td>\n      <td>102700.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <td>179.46</td>\n      <td>179.89</td>\n      <td>179.45</td>\n      <td>179.88</td>\n      <td>127600.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <td>179.89</td>\n      <td>180.03</td>\n      <td>179.82</td>\n      <td>179.99</td>\n      <td>161600.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">AMZN-USD-STK</th>\n      <th>2023-08-29 03:55:00</th>\n      <td>133.04</td>\n      <td>133.19</td>\n      <td>132.91</td>\n      <td>133.17</td>\n      <td>187100.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:56:00</th>\n      <td>133.18</td>\n      <td>133.26</td>\n      <td>133.06</td>\n      <td>133.07</td>\n      <td>167200.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:57:00</th>\n      <td>133.07</td>\n      <td>133.26</td>\n      <td>133.07</td>\n      <td>133.20</td>\n      <td>203400.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:58:00</th>\n      <td>133.20</td>\n      <td>133.21</td>\n      <td>133.12</td>\n      <td>133.17</td>\n      <td>209100.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:59:00</th>\n      <td>133.18</td>\n      <td>133.27</td>\n      <td>133.06</td>\n      <td>133.15</td>\n      <td>568400.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>1560 rows × 5 columns</p>\n</div>"}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# df的volume列乘以100\n", "df['volume'] = df['volume'] * 100\n", "df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T07:00:46.388236600Z", "start_time": "2023-08-29T07:00:46.345254400Z"}}, "id": "4d9d97c28c62ef35"}, {"cell_type": "code", "execution_count": 34, "outputs": [{"data": {"text/plain": "                                  open_price  high_price  low_price  \\\nsymbol       datetime                                                 \nAMZN-USD-STK 2023-08-28 21:30:00    133.7900     133.825    133.165   \n             2023-08-28 21:31:00    133.2100     133.640    133.210   \n             2023-08-28 21:32:00    133.6400     133.670    133.470   \n             2023-08-28 21:33:00    133.5500     133.950    133.550   \n             2023-08-28 21:34:00    133.8191     133.910    133.690   \n...                                      ...         ...        ...   \nMSFT-USD-STK 2023-08-29 03:56:00    323.8200     323.930    323.551   \n             2023-08-29 03:57:00    323.5600     323.840    323.540   \n             2023-08-29 03:58:00    323.7800     323.880    323.725   \n             2023-08-29 03:59:00    323.8700     323.990    323.560   \n             2023-08-29 04:00:00    323.7000     323.770    323.700   \n\n                                  close_price   volume  \nsymbol       datetime                                   \nAMZN-USD-STK 2023-08-28 21:30:00     133.2300   759125  \n             2023-08-28 21:31:00     133.6241   189784  \n             2023-08-28 21:32:00     133.5600   130826  \n             2023-08-28 21:33:00     133.8200   155831  \n             2023-08-28 21:34:00     133.7000    87759  \n...                                       ...      ...  \nMSFT-USD-STK 2023-08-29 03:56:00     323.5510    54275  \n             2023-08-29 03:57:00     323.7600    41968  \n             2023-08-29 03:58:00     323.8700    85871  \n             2023-08-29 03:59:00     323.7400   326450  \n             2023-08-29 04:00:00     323.7000  2085778  \n\n[1564 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">AMZN-USD-STK</th>\n      <th>2023-08-28 21:30:00</th>\n      <td>133.7900</td>\n      <td>133.825</td>\n      <td>133.165</td>\n      <td>133.2300</td>\n      <td>759125</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <td>133.2100</td>\n      <td>133.640</td>\n      <td>133.210</td>\n      <td>133.6241</td>\n      <td>189784</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <td>133.6400</td>\n      <td>133.670</td>\n      <td>133.470</td>\n      <td>133.5600</td>\n      <td>130826</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <td>133.5500</td>\n      <td>133.950</td>\n      <td>133.550</td>\n      <td>133.8200</td>\n      <td>155831</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <td>133.8191</td>\n      <td>133.910</td>\n      <td>133.690</td>\n      <td>133.7000</td>\n      <td>87759</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">MSFT-USD-STK</th>\n      <th>2023-08-29 03:56:00</th>\n      <td>323.8200</td>\n      <td>323.930</td>\n      <td>323.551</td>\n      <td>323.5510</td>\n      <td>54275</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:57:00</th>\n      <td>323.5600</td>\n      <td>323.840</td>\n      <td>323.540</td>\n      <td>323.7600</td>\n      <td>41968</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:58:00</th>\n      <td>323.7800</td>\n      <td>323.880</td>\n      <td>323.725</td>\n      <td>323.8700</td>\n      <td>85871</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:59:00</th>\n      <td>323.8700</td>\n      <td>323.990</td>\n      <td>323.560</td>\n      <td>323.7400</td>\n      <td>326450</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 04:00:00</th>\n      <td>323.7000</td>\n      <td>323.770</td>\n      <td>323.700</td>\n      <td>323.7000</td>\n      <td>2085778</td>\n    </tr>\n  </tbody>\n</table>\n<p>1564 rows × 5 columns</p>\n</div>"}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df_frd"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T07:00:48.116707700Z", "start_time": "2023-08-29T07:00:48.083736500Z"}}, "id": "830cb8b4ae88d87"}, {"cell_type": "code", "execution_count": 35, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["open_price     float64\n", "high_price     float64\n", "low_price      float64\n", "close_price    float64\n", "volume         float64\n", "dtype: object\n", "open_price     float64\n", "high_price     float64\n", "low_price      float64\n", "close_price    float64\n", "volume           int64\n", "dtype: object\n"]}], "source": ["# 打印df、df_frd各列的数据类型\n", "print(df.dtypes)\n", "print(df_frd.dtypes)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T07:00:49.585803800Z", "start_time": "2023-08-29T07:00:49.555800Z"}}, "id": "4cedfcc28f22a34c"}, {"cell_type": "code", "execution_count": 38, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df中有，df_frd中没有的数据的个数：4\n"]}, {"data": {"text/plain": "                                  open_price  high_price  low_price  \\\nsymbol       datetime                                                 \nAMZN-USD-STK 2023-08-29 04:00:00      133.14      133.50     133.14   \nAAPL-USD-STK 2023-08-29 04:00:00      180.19      180.19     180.11   \nTSLA-USD-STK 2023-08-29 04:00:00      238.82      238.89     238.71   \nMSFT-USD-STK 2023-08-29 04:00:00      323.70      323.77     323.70   \n\n                                  close_price   volume  \nsymbol       datetime                                   \nAMZN-USD-STK 2023-08-29 04:00:00       133.39  3111250  \nAAPL-USD-STK 2023-08-29 04:00:00       180.18  5179122  \nTSLA-USD-STK 2023-08-29 04:00:00       238.80  2744371  \nMSFT-USD-STK 2023-08-29 04:00:00       323.70  2085778  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>AMZN-USD-STK</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>133.14</td>\n      <td>133.50</td>\n      <td>133.14</td>\n      <td>133.39</td>\n      <td>3111250</td>\n    </tr>\n    <tr>\n      <th>AAPL-USD-STK</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>180.19</td>\n      <td>180.19</td>\n      <td>180.11</td>\n      <td>180.18</td>\n      <td>5179122</td>\n    </tr>\n    <tr>\n      <th>TSLA-USD-STK</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>238.82</td>\n      <td>238.89</td>\n      <td>238.71</td>\n      <td>238.80</td>\n      <td>2744371</td>\n    </tr>\n    <tr>\n      <th>MSFT-USD-STK</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>323.70</td>\n      <td>323.77</td>\n      <td>323.70</td>\n      <td>323.70</td>\n      <td>2085778</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["df_frd中有，df中没有的数据的个数：0\n"]}, {"data": {"text/plain": "Empty DataFrame\nColumns: [open_price, high_price, low_price, close_price, volume]\nIndex: []", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "                                  open_price  high_price  low_price  \\\ndatetime            symbol                                            \n2023-08-28 21:30:00 AAPL-USD-STK       0.000      0.0000     0.0000   \n2023-08-28 21:31:00 AAPL-USD-STK       0.000      0.0000     0.0000   \n2023-08-28 21:32:00 AAPL-USD-STK       0.005      0.0001     0.0000   \n2023-08-28 21:33:00 AAPL-USD-STK       0.000      0.0000    -0.0001   \n2023-08-28 21:34:00 AAPL-USD-STK       0.000      0.0001     0.0000   \n...                                      ...         ...        ...   \n2023-08-29 03:55:00 AMZN-USD-STK       0.000      0.0000     0.0000   \n2023-08-29 03:56:00 AMZN-USD-STK       0.005      0.0000     0.0000   \n2023-08-29 03:57:00 AMZN-USD-STK       0.000      0.0000     0.0000   \n2023-08-29 03:58:00 AMZN-USD-STK       0.000      0.0000     0.0000   \n2023-08-29 03:59:00 AMZN-USD-STK       0.000      0.0000     0.0000   \n\n                                  close_price  volume  \ndatetime            symbol                             \n2023-08-28 21:30:00 AAPL-USD-STK      -0.0030 -5289.0  \n2023-08-28 21:31:00 AAPL-USD-STK       0.0000   -47.0  \n2023-08-28 21:32:00 AAPL-USD-STK       0.0050   -26.0  \n2023-08-28 21:33:00 AAPL-USD-STK       0.0000   -93.0  \n2023-08-28 21:34:00 AAPL-USD-STK       0.0000   -90.0  \n...                                       ...     ...  \n2023-08-29 03:55:00 AMZN-USD-STK       0.0000   -89.0  \n2023-08-29 03:56:00 AMZN-USD-STK       0.0000    -1.0  \n2023-08-29 03:57:00 AMZN-USD-STK       0.0000   -97.0  \n2023-08-29 03:58:00 AMZN-USD-STK      -0.0048   -17.0  \n2023-08-29 03:59:00 AMZN-USD-STK       0.0000   -69.0  \n\n[1560 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>datetime</th>\n      <th>symbol</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2023-08-28 21:30:00</th>\n      <th>AAPL-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-0.0030</td>\n      <td>-5289.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <th>AAPL-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-47.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <th>AAPL-USD-STK</th>\n      <td>0.005</td>\n      <td>0.0001</td>\n      <td>0.0000</td>\n      <td>0.0050</td>\n      <td>-26.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <th>AAPL-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>-0.0001</td>\n      <td>0.0000</td>\n      <td>-93.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <th>AAPL-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0001</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-90.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:55:00</th>\n      <th>AMZN-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-89.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:56:00</th>\n      <th>AMZN-USD-STK</th>\n      <td>0.005</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-1.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:57:00</th>\n      <th>AMZN-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-97.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:58:00</th>\n      <th>AMZN-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-0.0048</td>\n      <td>-17.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:59:00</th>\n      <th>AMZN-USD-STK</th>\n      <td>0.000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>0.0000</td>\n      <td>-69.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>1560 rows × 5 columns</p>\n</div>"}, "metadata": {}, "output_type": "display_data"}], "source": ["# 比较df与df_frd的数据（以symbol、datetime为索引），输出：df中有，df_frd中没有的数据、df_frd中有，df中没有的数据；df中df_frd中都有的数据则作差，输出不为0的差值\n", "# df中有，df_frd中没有的数据\n", "df_diff_df_frd = df_frd[~df_frd.index.isin(df.index)]\n", "# 打印df中有，df_frd中没有的数据的个数\n", "print(f'df中有，df_frd中没有的数据的个数：{len(df_diff_df_frd)}')\n", "# 打印df中有，df_frd中没有的数据\n", "display(df_diff_df_frd)\n", "# df_frd中有，df中没有的数据\n", "df_frd_diff_df = df[~df.index.isin(df_frd.index)]\n", "# 打印df_frd中有，df中没有的数据的个数\n", "print(f'df_frd中有，df中没有的数据的个数：{len(df_frd_diff_df)}')\n", "# 打印df_frd中有，df中没有的数据\n", "display(df_frd_diff_df)\n", "# df中df_frd中都有的数据的索引\n", "df_diff_same_index = df.index.intersection(df_frd.index)\n", "# 不为0的差值\n", "df_diff_same = df.loc[df_diff_same_index] - df_frd.loc[df_diff_same_index]\n", "# 以datetime、symbol为两层索引\n", "df_diff_same = df_diff_same.reset_index().set_index(['datetime', 'symbol'])\n", "# 打印不为0的差值\n", "display(df_diff_same)\n", "# 保存三个结果到同一个excel文件的不同sheet中，注意为空的情况\n", "with pd.ExcelWriter(f'{today}_ib_frd_diff.xlsx') as writer:\n", "    # 如果df不为空，则保存到excel中\n", "    if len(df) > 0:\n", "        df.to_excel(writer, sheet_name='df_ib')\n", "    # 如果df_frd不为空，则保存到excel中\n", "    if len(df_frd) > 0:\n", "        df_frd.to_excel(writer, sheet_name='df_frd')\n", "    # 如果df_diff_df_frd不为空，则保存到excel中\n", "    if len(df_diff_df_frd) > 0:\n", "        df_diff_df_frd.to_excel(writer, sheet_name='df_diff_df_frd')\n", "    # 如果df_frd_diff_df不为空，则保存到excel中\n", "    if len(df_frd_diff_df) > 0:\n", "        df_frd_diff_df.to_excel(writer, sheet_name='df_frd_diff_df')\n", "    # 如果df_diff_same不为空，则保存到excel中\n", "    if len(df_diff_same) > 0:\n", "        df_diff_same.to_excel(writer, sheet_name='df_diff_same')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T07:03:38.893057300Z", "start_time": "2023-08-29T07:03:38.499056500Z"}}, "id": "e1185697529d5662"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}