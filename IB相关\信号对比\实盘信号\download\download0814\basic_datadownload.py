from dataclasses import dataclass
from datetime import datetime, timedelta, date
from typing import List, Optional
from functools import lru_cache
import json
import asyncio
import traceback
import pandas as pd
import math
from zoneinfo import ZoneInfo
SH_TZ = ZoneInfo('Asia/Shanghai')
NY_TZ = ZoneInfo('America/New_York')  # 美东时区
import pandas_market_calendars as mcal

from ib_async import IB, Contract, util
from ib_async.wrapper import RequestError
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.object import BarData
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.utility import load_json
from vnpy_ib.ib_gateway import INTERVAL_VT2IB

# 定义最大并发下载数
MAX_CONCURRENT_DOWNLOADS = 50

import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

@dataclass
class SymbolDownloadResult:
    """单个合约下载结果"""
    vt_symbol: str
    success: bool
    message: str

@dataclass
class OverallDownloadResult:
    """整体下载结果"""
    success: bool
    message: str
    failed_vt_symbols: List[str]

@lru_cache(maxsize=128)
def get_nyse_trading_days(start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """获取NYSE交易日历，使用lru_cache缓存结果
    
    Args:
        start_date (datetime): 开始时间，支持任意时区
        end_date (datetime): 结束时间，支持任意时区
        
    Returns:
        pd.DataFrame: 交易日历数据
    """
    # 转换为美东时间并提取日期
    ny_start_date = start_date.astimezone(NY_TZ).date()
    ny_end_date = end_date.astimezone(NY_TZ).date()
    
    nyse = mcal.get_calendar('NYSE')
    return nyse.schedule(start_date=ny_start_date, end_date=ny_end_date)

class IBDataDownloader:
    """IB数据下载器"""
    def __init__(self):
        """
        初始化下载器
        """
        self.database = get_database()
        
        # 加载配置
        config = load_json("connect_ib.json")
        self.host = config['TWS地址']
        self.port = config['TWS端口']
        self.client_id = config['客户号']

        self.ib: IB = IB() # 初始化IB实例
        self.ib.RaiseRequestErrors = True # 启用请求错误抛出

    async def connect(self) -> None:
        """连接到IB TWS/Gateway"""
        if self.ib.isConnected():
            logger.info("已连接到IB (重复连接)")
            return
        
        await self.ib.connectAsync(self.host, self.port, clientId=self.client_id)
        logger.info("已连接到IB")

    async def download_bars(self, vt_symbol: str, start_date: datetime, end_date: datetime, interval: Interval) -> SymbolDownloadResult:
        """下载单个合约数据"""
        try:
            whatToShow = "TRADES"
            # 解析vt_symbol，支持conId.exchange格式
            if '.' in vt_symbol:
                conid_str, exchange_str = vt_symbol.split(".")
                contract = Contract(
                    conId=int(conid_str),
                    exchange=exchange_str
                )
                # 如果exchange_str是IDEALPRO则直接用MIDPOINT免得请求ib
                if exchange_str == "IDEALPRO":
                    whatToShow = "MIDPOINT"
                elif exchange_str == "SMART":
                    pass
                else:
                    await self.ib.qualifyContractsAsync(contract)
                    if contract.secType in ('CASH', 'CMDTY'):
                        whatToShow = "MIDPOINT"
            else:
                message = "vt_symbol格式不正确，应为conId.exchange"
                logger.error(f"{vt_symbol}: {message}")
                return SymbolDownloadResult(vt_symbol, False, message)

            # 根据interval调整end_date，确保包含最后一根K线
            if interval == Interval.MINUTE:
                adjusted_end_date = end_date + timedelta(minutes=1)
            elif interval == Interval.HOUR:
                adjusted_end_date = end_date + timedelta(hours=1)
            else:  # Interval.DAILY
                adjusted_end_date = end_date + timedelta(days=1)

            # 计算durationStr
            if exchange_str == "SMART" and interval != Interval.DAILY:
                # 使用缓存的函数获取交易日历，直接传入datetime对象
                trading_days = get_nyse_trading_days(start_date, adjusted_end_date)
                # 计算交易日数量（向上取整）
                total_days = len(trading_days) + 1  # 加1是为了确保获取最后一天数据
                if total_days < 365:
                    duration_str = f"{total_days} D"
                else:
                    duration_str = f"{math.ceil(total_days/365)} Y"
            else:
                # 非美股市场使用日历日
                delta = adjusted_end_date - start_date
                total_days = delta.total_seconds() / 86400
                if total_days < 365:
                    duration_str = f"{max(1, math.ceil(total_days))} D"  # 用math.ceil向上取整
                else:
                    duration_str = f"{math.ceil(total_days/365)} Y"
                
            bar_size_setting = INTERVAL_VT2IB[interval]

            logger.info(f"开始下载 {vt_symbol} 的历史数据，从 {start_date} 到 {end_date}，周期 {interval.value}，持续时间 {duration_str}")

            try:
                bars = await self.ib.reqHistoricalDataAsync(
                    contract,
                    endDateTime=adjusted_end_date,
                    durationStr=duration_str,
                    barSizeSetting=bar_size_setting,
                    whatToShow=whatToShow,
                    useRTH=True,
                    timeout=0
                )
                
                if not bars:
                    message = "没有数据"
                    logger.info(f"{vt_symbol}: {message}")
                    return SymbolDownloadResult(vt_symbol, False, message)

                # 转换数据
                df = util.df(bars)
                
                # 记录过滤前的数据范围
                if not df.empty:
                    logger.info(f"{vt_symbol}: 过滤前数据范围 {df['date'].min()} - {df['date'].max()}, 总数据量: {len(df)}")
                
                # 根据时间过滤DataFrame
                if interval == Interval.DAILY:
                    df['date'] = pd.to_datetime(df['date'])
                    df['date'] = df['date'].dt.tz_localize(NY_TZ)

                df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                
                # 记录过滤后的数据范围
                if not df.empty:
                    logger.info(f"{vt_symbol}: 过滤后数据范围 {df['date'].min()} - {df['date'].max()}, 总数据量: {len(df)}")
                
                bar_data = []
                for _, row in df.iterrows():
                    # IB返回的时区是TWS登录界面选择的时区，需转换成DB_TZ
                    bar = BarData(
                        symbol=conid_str, # Use conId as symbol
                        exchange=Exchange(exchange_str),
                        datetime=row['date'],
                        interval=interval, # Use Interval enum directly
                        volume=float(row['volume']),
                        open_price=float(row['open']),
                        high_price=float(row['high']),
                        low_price=float(row['low']),
                        close_price=float(row['close']),
                        gateway_name='IB'
                    )
                    bar_data.append(bar)

                if not bar_data:
                    message = "数据为空"
                    logger.info(f"{vt_symbol}: {message}")
                    return SymbolDownloadResult(vt_symbol, False, message)

                # 保存数据
                self.database.save_bar_data(bar_data)
                
                message = f"成功保存{len(bar_data)}条数据，{bar_data[0].datetime.replace(tzinfo=DB_TZ)}-{bar_data[-1].datetime.replace(tzinfo=DB_TZ)}"
                logger.info(f"{vt_symbol}: {message}")
                return SymbolDownloadResult(vt_symbol, True, message)
                
            except RequestError as e:
                error_message = f"IB请求错误: [{e.code}] {e.message}"
                logger.error(f"{vt_symbol}: {error_message}")
                return SymbolDownloadResult(vt_symbol, False, error_message)
                
        except Exception as e:
            error_message = f"处理错误: {str(e)} - {traceback.format_exc()}" # 增加traceback信息
            logger.error(f"{vt_symbol}: {error_message}")
            return SymbolDownloadResult(vt_symbol, False, error_message)

    async def download(self, vt_symbols: List[str], start_date: datetime, end_date: datetime, interval: Interval) -> OverallDownloadResult:
        """批量下载数据"""
        
        overall_success = True
        all_failed_vt_symbols = []

        try:
            await self.connect()
            
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
            download_tasks = []

            async def download_single_symbol(vt_symbol: str) -> SymbolDownloadResult:
                async with semaphore:
                    return await self.download_bars(vt_symbol, start_date, end_date, interval)

            for vt_symbol in vt_symbols:
                task = asyncio.create_task(download_single_symbol(vt_symbol))
                download_tasks.append(task)

            # 执行所有任务并收集结果
            symbol_results: List[SymbolDownloadResult] = await asyncio.gather(*download_tasks)

            # 汇总结果
            for res in symbol_results:
                if not res.success:
                    overall_success = False
                    all_failed_vt_symbols.append(res.vt_symbol)

            if overall_success:
                return OverallDownloadResult(True, "所有合约下载成功", [])
            else:
                return OverallDownloadResult(False, "部分合约下载失败", all_failed_vt_symbols)
            
        except Exception as e:
            # 连接错误发生在connect()中，这里捕获的是connect()或download_bars()中的其他非RequestError
            error_message = f"批量下载过程中发生错误: {str(e)} - {traceback.format_exc()}" # 增加traceback信息
            logger.error(error_message)
            # 如果是连接错误导致整体失败，则所有请求的vt_symbols都算作失败
            return OverallDownloadResult(False, error_message, vt_symbols)
        finally:
            if self.ib and self.ib.isConnected(): # 确保连接存在且处于连接状态才断开
                self.ib.disconnect()
                logger.info("已断开IB连接")

async def sync_download(vt_symbols: List[str], start_date: datetime, end_date: datetime, interval: Interval) -> OverallDownloadResult:
    """同步下载接口"""
    downloader = IBDataDownloader()
    return await downloader.download(vt_symbols, start_date, end_date, interval)

if __name__ == "__main__":
    # 示例用法，不再使用命令行参数解析
    # 请自行修改以下参数进行测试
    from datetime import datetime, timedelta # Ensure timedelta is imported
    import sys # Keep sys for exit

    test_vt_symbols = ["265598.SMART", "4815747.SMART", "12087792.IDEALPRO"] # 示例：TSLA和AAPL在SMART交易所的ConId
    # 注意：这里的时间必须是美东时间，否则IB可能返回错误或空数据
    test_start_date = datetime(2025, 6, 1, 21, 30, 0, tzinfo=SH_TZ) # 示例开始时间，带时区
    test_end_date = datetime(2025, 7, 3, 19, 0, 0, tzinfo=SH_TZ) # 示例结束时间，带时区
    test_interval = Interval.MINUTE # 使用Interval枚举
    
    result = util.run(sync_download(test_vt_symbols, test_start_date, test_end_date, test_interval))
    
    # 打印下载结果
    if result.success:
        logger.info(f"下载成功: {result.message}")
    else:
        logger.error(f"下载失败: {result.message}")
        if result.failed_vt_symbols:
            logger.error(f"失败的合约: {', '.join(result.failed_vt_symbols)}")
