import pymysql
import pandas as pd
from datetime import datetime,timedelta

vnpy_stk_us_frt_d_data = pd.read_csv('vnpy_stk_us_frt_d_2206_250814_gap_analysis.csv')
vnpy_stk_us_ib_d_data = pd.read_csv('vnpy_stk_us_ib_d_2206_250814_gap_analysis.csv')

vnpy_stk_us_frt_d_data['datetime'] =  pd.to_datetime(vnpy_stk_us_frt_d_data['datetime']).dt.strftime('%Y-%m-%d')
vnpy_stk_us_ib_d_data['datetime'] =  pd.to_datetime(vnpy_stk_us_ib_d_data['datetime']).dt.strftime('%Y-%m-%d')
merged_data = vnpy_stk_us_ib_d_data.merge(
    vnpy_stk_us_frt_d_data,
    on=['conid', 'datetime'],
    how='outer',
    suffixes=('_ib', '_frt'),
    indicator=True
)

# 筛选左侧独有数据并清理
ib_only = merged_data[merged_data['_merge'] == 'left_only'].drop(columns=['_merge'])


ib_only = ib_only.reset_index(drop=True)
ib_only.to_csv('ib_only_data.csv', index=False)

df = pd.read_csv('ib_only_data.csv')
only = []
remote_conf = {
    "host": "********",
    "port": 3312,
    "user": "zh",
    "password": "zhP@55word",
    "db": "vnpy_stk_us_frt_d_2206_250814"
}
conn = pymysql.connect(**remote_conf)

cursor = conn.cursor()
for i,row in df.iterrows():
    cursor.execute(f"SELECT * FROM  vnpy_stk_us_frt_d_2206_250814.dbbaroverview where symbol={row['conid']}")
    data = cursor.fetchall()
    if len(data)==0:
        only.append(True)
    else:
        only.append(False)
        print(data)
df['common'] = only

df1 =df[ df['common']==True]
df2 = df[ df['common']==False]
df1.to_csv('frt无数据.csv')
df2.to_csv('frt有数据.csv')
df = pd.read_csv('frt有数据.csv')
df['datetime'] = pd.to_datetime(df['datetime']).dt.strftime('%Y-%m-%d')
df['prev_datetime_ib'] = pd.to_datetime(df['prev_datetime_ib']).dt.strftime('%Y-%m-%d')
volume_frt = []
prev_volume_frt  = []
open_price_frt  = []
prev_close_frt  = []
remote_conf = {
    "host": "********",
    "port": 3312,
    "user": "zh",
    "password": "zhP@55word",
    "db": "vnpy_stk_us_frt_d_2206_250814"
}
conn = pymysql.connect(**remote_conf)
cursor = conn.cursor()
for i,row in df.iterrows():
    print(row)

    query = f"""
        SELECT * 
        FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
        WHERE symbol = {row['conid']} AND Date(`datetime`) = '{row['datetime']}'
    """


    cursor.execute(query)
    data = cursor.fetchall()
    if data:
        volume_frt.append(data[0][5])
        open_price_frt.append(data[0][8])
    else:
        volume_frt.append('无数据')
        open_price_frt.append('无数据')


    query2 = f"""
        SELECT * 
        FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
        WHERE symbol = {row['conid']} AND Date(`datetime`) = '{row['prev_datetime_ib']}'
    """


    cursor.execute(query2)
    data2 = cursor.fetchall()
    if data2:
        prev_volume_frt.append(data2[0][5])
        prev_close_frt.append(data2[0][11])
    else:
        prev_volume_frt.append('无数据')
        prev_close_frt.append('无数据')
df['prev_volume_frt'] = prev_volume_frt
df['prev_close_frt'] = prev_close_frt
df['volume_frt'] = volume_frt
df['open_price_frt'] = open_price_frt
df.to_csv('数据整合.csv')



