# IB Gateway 监控脚本

## 功能说明

这个脚本用于监控3个IB Gateway的连接状态，确保交易系统的稳定运行。

### 主要功能

1. **多Gateway监控**: 同时监控端口4002、4012、4022的3个Gateway
2. **定时健康检查**: 每5分钟执行一次健康检查
3. **智能告警**: Gateway状态变化时发送企业微信通知
4. **24小时运行**: 支持长时间不间断运行
5. **异常恢复**: 自动处理连接异常并重试

### 健康检查机制

- 使用`basic_datadownload.py`下载测试数据
- 测试合约: `265598.SMART` (AAPL)
- 测试时间范围: 2025-01-02 到 2025-01-03 日线数据
- 客户号统一使用: 502

### 告警策略

1. **状态变化告警**: Gateway从正常变为异常时立即告警
2. **恢复通知**: Gateway从异常恢复正常时发送通知
3. **持续异常告警**: 异常持续半小时后重复告警
4. **定时状态摘要**: 每小时发送一次状态摘要

## 使用方法

```bash
python run_gateway_sentinel.py
```

## 配置说明

### 主要配置参数 (在脚本顶部)

```python
GATEWAY_PORTS = [4002, 4012, 4022]  # 监控的Gateway端口
CLIENT_ID = 502                     # 统一客户号
CHECK_INTERVAL = 300                 # 检查间隔(秒)
TEST_SYMBOL = "265598.SMART"         # 测试合约
```

### 依赖的配置文件

- `connect_ib.json`: IB连接配置模板
- `wecom_alert.py`: 企业微信告警配置

## 日志文件

- 日志文件位置: `logs/gateway_sentinel_YYYYMMDD.log`
- 日志轮转: 每天0点自动轮转

## 停止脚本

- **Ctrl+C**: 优雅停止
- **发送SIGTERM信号**: 程序会自动清理并发送停止通知

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Gateway是否正常运行
   - 确认端口号配置正确
   - 检查网络连接

2. **企业微信通知失败**
   - 检查`wecom_alert.py`中的webhook key
   - 确认网络可以访问企业微信API

3. **数据下载失败**
   - 检查测试合约是否有效
   - 确认时间范围设置正确
   - 检查IB账户权限