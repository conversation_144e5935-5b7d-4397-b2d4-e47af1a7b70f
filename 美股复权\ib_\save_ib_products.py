from time import sleep
from datetime import datetime
from typing import List
import os

import requests
from peewee import (
    chunked,
    Model
)
import pandas as pd
from loguru import logger

# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, IbProduct, IbContractDetail, ContractTime, IbFundamentals
from utils.proxy_utils import get_system_proxy
from update_ib_contract_details import IbContractDetailUpdater

# 配置loguru
# logger.remove()  # 移除默认的处理器
logger.add(
    'logs/save_ib_products_{time:YYYYMMDD}.log',
    level=0,
    format='{time} | {level: <8} | {name}:{function}:{line} - {message}',
    rotation='100 MB',
    retention=None
)

# 在文件开头添加字段映射
FIELD_MAPPING = {
    # API字段名: 数据库字段名
    "type": "type",
    "symbol": "symbol",
    "exchangeId": "exchange_id",
    "localSymbol": "local_symbol",
    "description": "description",
    "conid": "conid",
    "underConid": "under_conid",
    "isin": "isin",
    "cusip": "cusip",
    "currency": "currency",
    "country": "country",
    "isPrimeExchId": "is_prime_exch",
    "isNewPdt": "is_new_product",
    "assocEntityId": "assoc_entity_id"
}

def get_field(record: dict | Model, api_field: str) -> any:
    """统一获取字段值，支持字典和ORM对象"""
    db_field = FIELD_MAPPING[api_field]
    if isinstance(record, dict):
        # API返回的数据，尝试两种字段名
        return record.get(api_field, record.get(db_field))
    # 数据库记录，使用数据库字段名
    return getattr(record, db_field)

def get_product_key(product_data: dict | Model) -> tuple:
    """获取产品数据的唯一键(除created_time外的所有字段)"""
    return tuple(get_field(product_data, field) for field in FIELD_MAPPING.keys())

def init_database():
    """初始化数据库"""
    db_manager.common_db.create_tables([IbProduct, IbContractDetail, ContractTime, IbFundamentals], safe=True)

def fetch_products(page_number: int = 1, page_size: int = 500) -> dict:
    """获取IB产品信息"""
    url = "https://www.interactivebrokers.com/webrest/search/products-by-filters"

    data = {
        "domain": "com",
        "newProduct": "all",
        "pageNumber": page_number,
        "pageSize": page_size,
        "productCountry": ["US"],
        "productSymbol": "",
        "productType": ["STK"],
        "sortDirection": "asc",
        "sortField": "symbol"
    }

    # 获取系统代理设置
    proxies = get_system_proxy()
    
    # 使用代理发送请求
    response = requests.post(url, json=data, proxies=proxies)
    return response.json()


def fetch_all_products() -> List[dict]:
    """获取所有IB产品信息"""
    all_products = []
    page = 1
    
    while True:
        logger.info(f"正在获取第{page}页数据...")
        result = fetch_products(page)
        
        products = result.get("products", [])
        if not products:
            break
            
        all_products.extend(products)
        page += 1
        
    logger.info(f"数据获取完成，共获取{len(all_products)}条记录")
    return all_products

def find_root(parent: dict, x: str) -> str:
    """查找并查集中元素的根节点，同时进行路径压缩"""
    if x not in parent:
        parent[x] = x
        return x
    if parent[x] != x:
        parent[x] = find_root(parent, parent[x])
    return parent[x]

def union(parent: dict, x: str, y: str):
    """合并两个元素所在的集合"""
    root_x = find_root(parent, x)
    root_y = find_root(parent, y)
    if root_x != root_y:
        parent[root_y] = root_x

def assign_stable_id_to_records(records: List[dict], existing_stable_ids: dict = None) -> List[dict]:
    """为记录分配stable_id的通用函数，使用并查集确保相关联的记录获得相同的stable_id
    
    Args:
        records: 需要分配stable_id的记录列表，每个记录必须包含conid、symbol和isin字段
        existing_stable_ids: 已存在的stable_id映射，格式为{(conid/symbol/isin): stable_id}
        
    Returns:
        处理后的记录列表，每个记录增加stable_id字段
    """
    if existing_stable_ids is None:
        existing_stable_ids = {}

    # 初始化并查集
    parent = {}
    
    # 第一步：处理现有的stable_id映射
    existing_groups = {}  # stable_id -> 代表元素
    for key, stable_id in existing_stable_ids.items():
        if stable_id not in existing_groups:
            existing_groups[stable_id] = str(key)
        root = find_root(parent, str(key))
        union(parent, existing_groups[stable_id], root)

    # 第二步：将每条记录的所有标识符连接起来
    for record in records:
        # 获取记录的标识符
        if isinstance(record, dict):
            conid = str(record.get('conid', ''))
            symbol = str(record.get('symbol', ''))
            isin = str(record.get('isin', ''))
        else:
            conid = str(record.conid if record.conid else '')
            symbol = str(record.symbol if record.symbol else '')
            isin = str(record.isin if record.isin else '')

        # 记录特殊记录的处理
        if symbol in ['APETF', 'APETD'] or (conid and conid == '773088602'):
            logger.info(f"处理特殊记录: symbol={symbol}, conid={conid}, isin={isin}")

        # 将所有非空标识符连接起来
        identifiers = [x for x in [conid, symbol, isin] if x]
        if not identifiers:
            continue

        # 将所有标识符连接到第一个标识符
        first = identifiers[0]
        for other in identifiers[1:]:
            union(parent, first, other)
            if symbol in ['APETF', 'APETD'] or (conid and conid == '773088602'):
                logger.info(f"连接标识符: {first} -> {other}")

    # 第三步：为每个根节点分配stable_id
    root_to_stable_id = {}
    max_stable_id = max(existing_stable_ids.values()) if existing_stable_ids else 0

    # 首先处理已有stable_id的根节点
    for key, stable_id in existing_stable_ids.items():
        root = find_root(parent, str(key))
        if root not in root_to_stable_id:
            root_to_stable_id[root] = stable_id
            if symbol in ['APETF', 'APETD'] or (conid and conid == '773088602'):
                logger.info(f"为已存在的根节点{root}分配stable_id: {stable_id}")

    # 第四步：为记录分配stable_id
    for record in records:
        # 获取记录的标识符
        if isinstance(record, dict):
            conid = str(record.get('conid', ''))
            symbol = str(record.get('symbol', ''))
            isin = str(record.get('isin', ''))
        else:
            conid = str(record.conid if record.conid else '')
            symbol = str(record.symbol if record.symbol else '')
            isin = str(record.isin if record.isin else '')

        # 找到记录的根节点
        identifiers = [x for x in [conid, symbol, isin] if x]
        if not identifiers:
            continue

        root = find_root(parent, identifiers[0])

        # 如果根节点还没有stable_id，分配一个新的
        if root not in root_to_stable_id:
            max_stable_id += 1
            root_to_stable_id[root] = max_stable_id
            if symbol in ['APETF', 'APETD'] or (conid and conid == '773088602'):
                logger.info(f"为新的根节点{root}分配stable_id: {max_stable_id}")

        # 为记录设置stable_id
        stable_id = root_to_stable_id[root]
        if isinstance(record, dict):
            record['stable_id'] = stable_id
        else:
            record.stable_id = stable_id

        # 记录特殊记录的stable_id分配
        if symbol in ['APETF', 'APETD'] or (conid and conid == '773088602'):
            logger.info(f"为记录设置stable_id: symbol={symbol}, conid={conid}, isin={isin}, stable_id={stable_id}, root={root}")

    return records


def init_existing_stable_ids():
    """初始化数据库中已有记录的stable_id"""
    logger.info("正在初始化数据库中已有记录的stable_id...")
    
    # 获取所有没有stable_id的记录，按id排序
    records_without_stable_id = list(IbProduct.select().where(IbProduct.stable_id.is_null(True)).order_by(IbProduct.id))
    if not records_without_stable_id:
        logger.info("所有记录都已有stable_id")
        return
        
    logger.info(f"发现{len(records_without_stable_id)}条记录没有stable_id")
    
    # 获取已有的stable_id映射，按id排序获取记录
    existing_stable_ids = {}
    for record in IbProduct.select().where(IbProduct.stable_id.is_null(False)).order_by(IbProduct.id):
        existing_stable_ids[record.conid] = record.stable_id
        existing_stable_ids[record.symbol] = record.stable_id
        existing_stable_ids[record.isin] = record.stable_id
    
    # 为没有stable_id的记录分配stable_id
    updated_records = assign_stable_id_to_records(records_without_stable_id, existing_stable_ids)
    
    # 批量更新数据库
    with db_manager.common_db.atomic():
        for record in updated_records:
            IbProduct.update(stable_id=record.stable_id).where(IbProduct.id == record.id).execute()
    
    logger.info(f"已完成{len(updated_records)}条记录的stable_id初始化")


def save_products(products: List[dict]):
    """保存产品信息到数据库"""
    now = datetime.now()
    
    # 1. 获取数据库中现有记录的键集合
    existing_keys = {}  # 改为字典，记录key到record的映射
    existing_stable_ids = {}
    for record in IbProduct.select():
        key = get_product_key(record)
        existing_keys[key] = record
        if record.stable_id:
            existing_stable_ids[record.conid] = record.stable_id
            existing_stable_ids[record.symbol] = record.stable_id
            existing_stable_ids[record.isin] = record.stable_id
    
    # 2. 分类处理记录
    new_products = []
    records_to_update = []
    
    for product in products:
        key = get_product_key(product)
        if key in existing_keys:
            # 已存在的记录，更新created_time
            records_to_update.append(existing_keys[key])
        else:
            # 新记录
            new_products.append(product)
    
    # 3. 更新已存在记录的created_time
    if records_to_update:
        logger.info(f"发现{len(records_to_update)}条已存在的记录需要更新时间戳")
        record_ids = [record.id for record in records_to_update]
        with db_manager.common_db.atomic():
            (IbProduct
             .update(created_time=now)
             .where(IbProduct.id.in_(record_ids))
             .execute())
    
    # 4. 处理新记录
    if not new_products:
        logger.info("没有发现新记录")
    else:
        logger.info(f"发现{len(new_products)}条新记录")
        assert existing_stable_ids
        # 为新记录分配stable_id
        new_products = assign_stable_id_to_records(new_products, existing_stable_ids)
        
        # 保存新记录
        rows_to_save = []
        for product in new_products:
            product_data = {
                db_field: get_field(product, api_field)
                for api_field, db_field in FIELD_MAPPING.items()
            }
            product_data.update({
                "created_time": now,
                "is_latest": False,  # 默认设为False，稍后会更新
                "stable_id": product["stable_id"]
            })
            rows_to_save.append(product_data)
        
        # 保存到数据库
        with db_manager.common_db.atomic():
            for batch in chunked(rows_to_save, 100):
                IbProduct.insert_many(batch).on_conflict_replace().execute()
    
    return len(new_products)


def update_latest_records():
    """更新最新记录标志"""
    logger.info("开始更新最新记录标识...")
    # 按stable_id分组，找出每组最新的记录并更新
    update_sql = """
        UPDATE ib_product 
        SET is_latest = CASE WHEN id IN (
            SELECT id FROM (
                SELECT id,
                       ROW_NUMBER() OVER (
                           PARTITION BY stable_id 
                           ORDER BY created_time DESC, id DESC
                       ) as rn
                FROM ib_product
                WHERE stable_id IS NOT NULL
            ) ranked 
            WHERE rn = 1
        ) THEN TRUE ELSE FALSE END
    """
    
    db_manager.common_db.execute_sql(update_sql)
    logger.info("已完成最新标识的更新")

def check_detail_update_needed() -> bool:
    """检查是否需要更新合约详情
    
    Returns:
        bool: 如果需要更新返回True，否则返回False
    """
    detail_file = "detail_time.txt"
    today = datetime.now().strftime("%Y%m%d")
    
    # 如果文件不存在，需要更新
    if not os.path.exists(detail_file):
        with open(detail_file, "w") as f:
            f.write(today)
        return True
    
    # 读取文件中的日期
    with open(detail_file, "r") as f:
        last_update = f.read().strip()
    
    # 如果日期不同，需要更新
    if last_update != today:
        with open(detail_file, "w") as f:
            f.write(today)
        return True
    
    return False

def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    init_database()

    # 1. 初始化已有记录的stable_id
    init_existing_stable_ids()

    # 2. 获取所有数据
    all_products = fetch_all_products()
    
    # 3. 统一保存处理
    old_count = IbProduct.select().count()
    new_count = save_products(all_products)
    
    # 4. 更新最新记录标识
    update_latest_records()

    # 检查当前时间，如果未到5:15则等待
    current_time = datetime.now()
    target_time = current_time.replace(hour=5, minute=15, second=0, microsecond=0)
    
    if current_time < target_time:
        sleep_seconds = (target_time - current_time).total_seconds()
        logger.info(f"当前时间{current_time.strftime('%H:%M:%S')}，等待到5:15再继续...")
        sleep(sleep_seconds)
    else:
        logger.info(f"当前时间{current_time.strftime('%H:%M:%S')}已超过5:15，继续执行...")

    # 5. 检查是否需要更新合约详情
    if check_detail_update_needed():
        logger.info("开始更新合约详情...")
        updater = IbContractDetailUpdater()
        updater.update_contract_details() # 不再传入conids，让其使用内部latest_product_conids

        # 更新ContractTime表中的conid
        logger.info("开始更新ContractTime表中的conid...")
        from update_ib_contract_details import update_contract_time_conids_command
        update_contract_time_conids_command()

        # 更新head_time，只使用4012端口
        # logger.info("开始更新合约head_time...")
        # from update_ib_contract_details import update_head_times_command
        # update_head_times_command(ports_str="4012")
    else:
        logger.info("今日已更新过合约详情，跳过更新步骤")
    
    # 6. 统计结果
    latest_count = IbProduct.select().where(IbProduct.is_latest == True).count()
    total_count = IbProduct.select().count()
    
    logger.info(f"处理完成，原有{old_count}条记录，新增{new_count}条记录，当前共{total_count}条记录")
    logger.info(f"其中标记为最新的记录有{latest_count}条")


if __name__ == "__main__":
    main()
