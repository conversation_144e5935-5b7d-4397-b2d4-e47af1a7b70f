from vnpy.trader.setting import SETTINGS
from datetime import datetime
from enum import Enum
from typing import List, Optional, Tuple
import typer
from zoneinfo import ZoneInfo
from types import MethodType
import pandas as pd

from futu import (
    OpenQuoteContext, 
    KLType,
    AuType,
    RET_OK,
    Market
)

from vnpy.event import EventEngine
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import HistoryRequest, BarData
from vnpy.trader.utility import load_json

# 创建CLI应用
app = typer.Typer()

# 设置合约品种
symbols = {
    # "SEHK": ["00700", "09988", "03690", "01810", "02318", "02020", "00941", "00388", "03968", "02269"],
    "NYSE": ["BABA", "PDD", "NIO"],
    "NASDAQ": ["TSLA", "AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "AMD", "INTC", "NFLX"]
}

# 设置时区
CHINA_TZ = ZoneInfo("Asia/Shanghai")
NY_TZ = ZoneInfo("America/New_York")

# 设置下载时间段
start_date = datetime(2024, 12, 1, tzinfo=DB_TZ)
end_date = datetime(2099, 1, 1, tzinfo=DB_TZ)

# 设置数据库实例
database = get_database()

# 交易所映射
EXCHANGE_MAP = {
    Exchange.NYSE: (Market.US, NY_TZ),
    Exchange.NASDAQ: (Market.US, NY_TZ),
    Exchange.SEHK: (Market.HK, CHINA_TZ),
    Exchange.SSE: (Market.SH, CHINA_TZ),
    Exchange.SZSE: (Market.SZ, CHINA_TZ),
}

# K线周期映射
INTERVAL_MAP_FUTU = {
    Interval.MINUTE: KLType.K_1M,
    Interval.HOUR: KLType.K_60M,
    Interval.DAILY: KLType.K_DAY,
    Interval.WEEKLY: KLType.K_WEEK,
}

# K线周期的时间调整映射
TIMEDELTA_MAP = {
    Interval.MINUTE: pd.Timedelta(minutes=1),
    Interval.HOUR: pd.Timedelta(hours=1),
}

class IntervalChoice(str, Enum):
    MINUTE = "1m"
    HOUR = "1h" 
    DAILY = "d"
    WEEKLY = "w"

INTERVAL_MAP = {
    IntervalChoice.MINUTE: Interval.MINUTE,
    IntervalChoice.HOUR: Interval.HOUR,
    IntervalChoice.DAILY: Interval.DAILY,
    IntervalChoice.WEEKLY: Interval.WEEKLY
}

class FutuDataService:
    """富途数据服务"""
    
    def __init__(self):
        """构造函数"""
        setting = load_json("connect_futu.json")
        self.quote_ctx = OpenQuoteContext(
            host=setting["host"],
            port=setting["port"]
        )
        
    def query_history(self, req: HistoryRequest) -> List[BarData]:
        """查询历史数据"""
        # 获取合约对应的市场和时区
        market, tz = EXCHANGE_MAP[req.exchange]
        
        # 转换时间为本地时间字符串
        start = req.start.astimezone(tz).strftime("%Y-%m-%d")
        end = req.end.astimezone(tz).strftime("%Y-%m-%d")
        
        # 查询数据
        ret, data, page_req_key = self.quote_ctx.request_history_kline(
            code=f"{market}.{req.symbol}",
            start=start,
            end=end,
            ktype=INTERVAL_MAP_FUTU[req.interval],
            autype=AuType.QFQ,
            max_count=1000,
            page_req_key=None,
            extended_time=False
        )
        
        if ret != RET_OK:
            print(f"查询失败: {data}")
            return []
            
        bars: List[BarData] = []
        all_data = []
        
        # 循环处理数据
        while True:
            all_data.append(data)
                
            # 如果还有更多数据，继续查询
            if page_req_key:
                ret, data, page_req_key = self.quote_ctx.request_history_kline(
                    code=f"{market}.{req.symbol}",
                    start=start,
                    end=end,
                    ktype=INTERVAL_MAP_FUTU[req.interval],
                    autype=AuType.QFQ,
                    max_count=1000,
                    page_req_key=page_req_key,
                    extended_time=False
                )
                
                if ret != RET_OK:
                    print(f"查询失败: {data}")
                    break
            else:
                break
        
        # 合并所有数据
        if not all_data:
            return []
            
        df = pd.concat(all_data, ignore_index=True)
        
        # 处理时间戳
        # 将time_key转换为datetime
        df["time_key"] = pd.to_datetime(df["time_key"])
        
        # 对需要调整的周期进行时间调整(富途返回的是该周期的结束时间)
        if req.interval in TIMEDELTA_MAP:
            df["time_key"] = df["time_key"] - TIMEDELTA_MAP[req.interval]
            
        # 转换为BarData对象
        for _, row in df.iterrows():
            # 处理时间戳并保留毫秒精度
            dt = row["time_key"].to_pydatetime()
            
            # 确保时区信息正确
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=tz)
            else:
                dt = dt.astimezone(tz)
                
            bar = BarData(
                symbol=req.symbol,
                exchange=req.exchange,
                datetime=dt,
                interval=req.interval,
                volume=float(row["volume"]),
                turnover=float(row["turnover"]),
                open_price=row["open"],
                high_price=row["high"],
                low_price=row["low"],
                close_price=row["close"]
            )
            bars.append(bar)
                
        return bars
        
    def close(self):
        """关闭接口"""
        self.quote_ctx.close()

def query_save_data(service: FutuDataService, req: HistoryRequest) -> None:
    """查询并保存数据"""
    data = service.query_history(req)
    if data:
        database.save_bar_data(data)
        print(f"{req.symbol} - {len(data)}条数据下载完成")
    else:
        print(f"{req.symbol} - 无数据")

def download_data(service: FutuDataService, interval: Interval, symbol_type: str = "") -> None:
    """下载数据"""
    # 获取已有数据的合约列表
    overview_symbols: set = {(x.symbol, x.exchange, x.interval) for x in database.get_bar_overview()}
    
    # 构建下载任务
    symbols_to_download: List[Tuple[str, Exchange, Interval]] = []
    for exchange, symbols_list in symbols.items():
        for symbol in symbols_list:
            if (symbol + symbol_type, Exchange(exchange), interval) not in overview_symbols:
                symbols_to_download.append((symbol + symbol_type, Exchange(exchange), interval))
    print(f'symbols_to_download:{symbols_to_download}')

    for symbol, exchange, _ in symbols_to_download:
        req = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            start=start_date,
            interval=interval,
            end=end_date
        )
        query_save_data(service, req)

def update_data(service: FutuDataService, interval: Interval) -> None:
    """更新数据"""
    end = datetime.now().astimezone(DB_TZ)
    
    # 遍历数据库中的合约
    for overview in database.get_bar_overview():
        if not overview.interval == interval:
            continue
            
        symbol = overview.symbol
        exchange = overview.exchange
        start = overview.end

        req = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            start=start,
            interval=interval,
            end=end
        )
        query_save_data(service, req)

@app.command()
def main(
    interval: IntervalChoice = typer.Option(IntervalChoice.DAILY, "--interval", "-i", help="K线周期"),
    download: bool = typer.Option(True, "--download/--no-download", help="是否下载新数据"),
    update: bool = typer.Option(True, "--update/--no-update", help="是否更新数据")
) -> None:
    """富途数据下载工具"""
    print(f"开始执行{interval.value}数据更新任务")
    
    # 创建数据服务实例
    service = FutuDataService()
    
    try:
        # 转换interval枚举
        interval_vt = INTERVAL_MAP[interval]
        
        # 根据参数执行任务
        if download:
            download_data(service, interval_vt)
        if update:
            update_data(service, interval_vt)
            
    except Exception as e:
        print(f"任务执行出错: {e}")
        raise
    finally:
        service.close()

if __name__ == "__main__":
    app()

