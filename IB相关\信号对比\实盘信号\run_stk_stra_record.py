import multiprocessing
import sys
from datetime import time, datetime, timedelta
from logging import INFO
from time import sleep
import signal  # 导入signal模块
import platform  # 导入platform模块

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine as BaseMainEngine
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import SubscribeRequest
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, ZoneInfo
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG

from data_recorder_fake_bar.utils.barGen_redis_engine import BarGenEngineIb
from data_recorder_fake_bar.utils.recorder_engine import RecorderEngine
from data_recorder_fake_bar.utils.ib_gateway import IbGateway

# 强制使用'spawn'启动方式。
# 在Windows上这是默认且唯一选择；在Linux上会模拟Windows行为，子进程不继承父进程的信号处理器。
multiprocessing.set_start_method('spawn', force=True)

# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True

# Trading hours configuration (ET)
START_TIME = time(9, 25)
US_DAY_START = time(9, 30)
US_DAY_END = time(16, 0)  # 收盘后半小时再关闭
CLOSE_TIME = time(16, 30)

# 盘后定时段生成：每半小时启动10分钟，从16:35-16:45, 17:05-17:15, ..., 23:35-23:45, 00:05-00:15, ..., 08:35-08:45, 09:05-09:15
def generate_preclose_periods():
    periods = []
    base_date = datetime.today()
    # 第一段：16:35-16:45, 17:05-17:15, ..., 23:35-23:45, 23:05-23:15
    start_dt = datetime.combine(base_date, time(16, 35))
    end_dt = datetime.combine(base_date, time(23, 45))
    while start_dt <= end_dt:
        periods.append((start_dt.time(), (start_dt + timedelta(minutes=10)).time()))
        start_dt += timedelta(minutes=30)
    # 00:05-00:15, 00:35-00:45, ..., 08:05-08:15, 08:35-08:45, 09:05-09:15
    start_dt = datetime.combine(base_date, time(0, 5))
    end_dt = datetime.combine(base_date, time(9, 15))
    while start_dt <= end_dt:
        periods.append((start_dt.time(), (start_dt + timedelta(minutes=10)).time()))
        start_dt += timedelta(minutes=30)
    return periods

PRE_CLOSE_PERIODS = generate_preclose_periods()

# 配置文件
connect_trade_filename = 'connect_ib.json'
connect_quote_filename = 'connect_ib_quote.json'
# data_recorder_filename = 'data_recorder_setting_ib_record.json'
data_recorder_filename = 'data_recorder_setting_ib_bargen.json'

def rewrite_status(data_recorder_filename):
    """
    Convert data recorder settings to Status table entries with '_A' suffix

    Args:
        data_recorder_filename: Path to the data recorder settings file
    """
    import datetime
    from vnpy.trader.utility import load_json
    from data_recorder_fake_bar.utils.db_status_manager import Status, db

    data_recorder_setting = load_json(data_recorder_filename)
    current_time = datetime.datetime.now()

    # 开始数据库事务
    with db.atomic():
        # 先清空Status表
        Status.delete().execute()
        print("Status表已清空")
        # 准备批量插入的数据
        status_data = []

        # 遍历所有配置的合约
        for bar_type, bar_setting in data_recorder_setting.items():
            for vt_symbol in bar_setting.keys():
                # 添加 _A 后缀
                content = f"{vt_symbol}_A"
                status_data.append({
                    "content": content,
                    "creation_time": current_time,
                    "running_status": 2
                })

        # 批量插入数据，如有冲突则替换
        if status_data:
            Status.insert_many(status_data).on_conflict_replace().execute()
            print(f"批量添加了 {len(status_data)} 个合约到Status表")
    print("Status表更新完成")

def check_trading_period(is_us_market: bool = True, extended: bool = True):
    """
    检查当前时间是否在交易时段内
    :param is_us_market: 是否美股市场，保留参数以兼容
    :param extended: 是否使用扩展交易时段（默认True，使用CLOSE_TIME，否则用US_DAY_START/US_DAY_END）
    """
    eastern = ZoneInfo('US/Eastern')
    current_time = datetime.now(eastern).time()
    if extended:
        in_main = START_TIME <= current_time <= CLOSE_TIME
        in_preclose = any(start <= current_time <= end for start, end in PRE_CLOSE_PERIODS)
        return in_main or in_preclose
    else:
        return US_DAY_START <= current_time <= US_DAY_END

class MainEngine(BaseMainEngine):
    """
    扩展MainEngine，确保所有行情订阅都通过quote网关进行
    """
    def subscribe(self, req: SubscribeRequest, gateway_name: str = "") -> None:
        """始终使用quote网关订阅行情"""
        super().subscribe(req, "quote")

    def unsubscribe(self, req: SubscribeRequest, gateway_name: str = "") -> None:
        """始终使用quote网关取消订阅行情"""
        gateway: BaseGateway = self.get_gateway("quote")
        if gateway:
            gateway.unsubscribe(req)

class StockTradingApp:
    """
    负责运行CTA策略和数据记录的交易应用类。
    这个类的实例将在子进程中运行。
    """

    def __init__(self, stop_event: multiprocessing.Event):
        """
        初始化交易应用实例。
        Args:
            stop_event: 用于父进程通知子进程停止的Event对象。
        """
        self.event_engine: EventEngine = None
        self.main_engine: MainEngine = None
        self.trade_gateway: IbGateway = None  # 交易网关
        self.quote_gateway: IbGateway = None  # 行情网关
        self.cta_engine: CtaStrategyApp = None
        self.bargen_engine: BarGenEngineIb = None
        self.recorder_engine: RecorderEngine = None
        self.log_engine = None

        self.stop_event = stop_event  # 接收父进程的停止信号

    def _signal_handler(self, signum, frame):
        """
        子进程内的信号处理函数。
        当子进程收到 SIGINT (Ctrl+C) 或 SIGTERM 信号时调用。
        """
        if self.main_engine:
            self.main_engine.write_log(f"子进程收到信号: {signal.Signals(signum).name}，准备关闭应用...")
        else:
            print(f"子进程收到信号: {signal.Signals(signum).name}，MainEngine未初始化，直接退出...")

        # 设置停止事件，让主循环能够检测到并退出
        self.stop_event.set()

    def process_position_event(self, event):
        """处理持仓更新事件"""
        self.main_engine.write_log(f"持仓更新： {event.data}")

    def start_engines_and_strategies(self):
        """初始化并启动交易应用的所有引擎和策略"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)

        # 添加数据记录引擎
        self.recorder_engine = self.main_engine.add_engine(RecorderEngine)
        self.recorder_engine.start()
        self.main_engine.write_log("添加数据记录引擎")

        # 添加行情网关
        self.quote_gateway = self.main_engine.add_gateway(IbGateway, "quote")
        self.main_engine.write_log("行情接口添加成功")
        setting = load_json(connect_quote_filename)
        self.quote_gateway.quote_only = 1  # 设置为仅行情模式
        # self.quote_gateway.use_5s_bar = False # 默认True
        self.main_engine.write_log(f"行情网关配置: 地址={setting['TWS地址']}, 端口={setting['TWS端口']}, 客户号={setting['客户号']}")
        self.quote_gateway.connect(setting)
        self.main_engine.write_log("行情网关连接成功 [仅行情模式]")

        # 添加交易网关
        self.trade_gateway = self.main_engine.add_gateway(IbGateway)
        self.main_engine.write_log("交易接口添加成功")
        setting = load_json(connect_trade_filename)
        self.trade_gateway.quote_only = -1  # 设置为仅交易模式
        self.main_engine.write_log(f"交易网关配置: 地址={setting['TWS地址']}, 端口={setting['TWS端口']}, 客户号={setting['客户号']}")
        self.trade_gateway.connect(setting)
        self.main_engine.write_log("交易网关连接成功 [仅交易模式]")

        # 添加CTA策略引擎
        self.cta_engine = self.main_engine.add_app(CtaStrategyApp)
        self.main_engine.write_log("主引擎创建成功")

        # 注册CTA日志事件
        self.log_engine = self.main_engine.get_engine("log")
        self.event_engine.register(EVENT_CTA_LOG, self.log_engine.process_log_event)
        self.main_engine.write_log("注册日志事件监听")

        # 添加barGen Engine
        self.main_engine.write_log("添加barGen Engine")
        self.bargen_engine = self.main_engine.add_engine(BarGenEngineIb)
        self.bargen_engine.start()
        self.main_engine.write_log("Bargen for redis 启动")

        # 查询资金和持仓
        sleep(10)
        self.main_engine.write_log("***查询资金和持仓***")
        self.main_engine.write_log(self.main_engine.get_all_accounts())
        self.main_engine.write_log(self.main_engine.get_all_positions())
        self.main_engine.event_engine.register("EVENT_POSITION", self.process_position_event)

        # 初始化 CTA 策略
        self.cta_engine.init_engine()
        self.main_engine.write_log("CTA策略初始化完成")

        # 加载策略
        self.main_engine.write_log("***从数据库读取准备数据, 实盘运行***")
        old_strategies = list(self.cta_engine.strategies.keys())
        data_recorder_setting = load_json(data_recorder_filename)
        for bar_type, bar_setting in data_recorder_setting.items():
            for vt_symbol, setting in bar_setting.items():
                strategy_symbol_name3 = f'DoubleMa3Strategy_IB_{vt_symbol}_v1'
                if strategy_symbol_name3 in old_strategies:
                    self.cta_engine.edit_strategy(strategy_symbol_name3, {'fast_window': 10, 'slow_window': 20})
                else:
                    self.cta_engine.add_strategy("DoubleMa3Strategy", strategy_symbol_name3, vt_symbol,
                                                 {"fast_window": 10, "slow_window": 20})

                strategy_symbol_name5 = f'DoubleMa5Strategy_IB_{vt_symbol}_v1'
                if strategy_symbol_name5 in old_strategies:
                    self.cta_engine.edit_strategy(strategy_symbol_name5, {'fast_window': 10, 'slow_window': 20})
                else:
                    self.cta_engine.add_strategy("DoubleMa5Strategy", strategy_symbol_name5, vt_symbol,
                                                 {"fast_window": 10, "slow_window": 20})
        sleep(5)

        # 启动策略
        self.cta_engine.init_all_strategies()
        time1 = datetime.now()
        while not all([strategy.inited for strategy in self.cta_engine.strategies.values()]):
            sleep(1)
        time_used = datetime.now() - time1
        self.main_engine.write_log(
            f"CTA策略全部初始化，耗时：{time_used}，平均每个策略耗时：{time_used / len(self.cta_engine.strategies)}")

        self.cta_engine.start_all_strategies()
        self.main_engine.write_log("CTA策略全部启动")

    def run(self):
        """
        子进程的入口函数，执行交易应用的主循环。
        这个方法将作为 multiprocessing.Process 的 target。
        """
        self.start_engines_and_strategies()
        self.main_engine.write_log("子进程交易应用已启动")

        # 在子进程中重新注册信号处理器
        # 由于使用了'spawn'启动方式，子进程不会继承父进程的信号处理器，所以需要在这里重新注册。
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        # Windows特有的Ctrl+Break信号
        if platform.system() == "Windows":
            signal.signal(signal.SIGBREAK, self._signal_handler)
        self.main_engine.write_log("子进程信号处理器已注册。")

        while not self.stop_event.is_set():
            sleep(1)
        self.close() # 无论正常退出还是异常，都执行关闭逻辑
        sys.exit(0) # 确保子进程彻底退出

    def close(self):
        """
        优雅地关闭交易应用。
        包括撤销所有挂单，关闭主引擎等。
        """
        # 优雅关闭时的日志
        if self.trade_gateway:
            self.main_engine.write_log("开始撤销所有活动委托...")
            self.trade_gateway.cancel_all()
            self.main_engine.write_log("所有活动委托已撤销")
            self.trade_gateway.close()

        if self.quote_gateway:
            self.main_engine.write_log("正在关闭行情网关连接...")
            self.quote_gateway.close()
            self.main_engine.write_log("行情网关已关闭")
        sleep(10) # 等待撤单操作完成
        self.main_engine.close()


def parent_signal_handler(signum, frame, child_process_ref: multiprocessing.Process,
                          stop_event_ref: multiprocessing.Event):
    """
    父进程的信号处理函数。
    当父进程收到 SIGINT (Ctrl+C) 或 SIGTERM 信号时调用。
    """
    print(f"父进程收到信号: {signal.Signals(signum).name}，准备关闭子进程...")
    stop_event_ref.set()  # 通知子进程停止

    if child_process_ref and child_process_ref.is_alive():
        print("等待子进程优雅关闭 (最长20秒)...")
        # 等待子进程执行其关闭逻辑并退出
        child_process_ref.join(timeout=20)

    if child_process_ref and child_process_ref.is_alive():
        print("子进程未能优雅关闭，强制终止。")
        child_process_ref.terminate()  # 强制终止子进程
        child_process_ref.join()  # 确保子进程资源被回收
    print("子进程已处理完毕，父进程退出。")
    sys.exit(0)  # 父进程退出


def main_parent():
    """
    运行父进程来管理子进程的生命周期。
    负责根据交易时段启动和停止子进程。
    """
    print("启动CTA策略守护父进程")
    child_process = None
    stop_event = multiprocessing.Event()  # 用于父进程通知子进程停止的事件

    # 在父进程中注册信号处理器
    # 注意：这里使用 lambda 表达式来传递额外的参数给信号处理函数。
    signal.signal(signal.SIGINT, lambda signum, frame: parent_signal_handler(signum, frame, child_process, stop_event))
    signal.signal(signal.SIGTERM, lambda signum, frame: parent_signal_handler(signum, frame, child_process, stop_event))
    # Windows特有的Ctrl+Break信号
    if platform.system() == "Windows":
        signal.signal(signal.SIGBREAK,
                      lambda signum, frame: parent_signal_handler(signum, frame, child_process, stop_event))
    print("父进程信号处理器已注册。")

    while True:
        trading = check_trading_period()

        # 在交易时段启动子进程
        if trading and child_process is None:
            print("启动子进程...")
            stop_event.clear()  # 启动前清除停止事件，确保子进程知道要运行

            # 创建 StockTradingApp 实例，并将其 run 方法作为子进程的目标
            app_instance = StockTradingApp(stop_event)
            child_process = multiprocessing.Process(target=app_instance.run)
            child_process.start()
            print(f"子进程 {child_process.pid} 启动成功。")

        # 非交易时间且子进程仍在运行，则通知子进程退出
        if not trading and child_process is not None and child_process.is_alive():
            print("非交易时间，通知子进程关闭...")
            stop_event.set()  # 通知子进程停止
            child_process.join(timeout=20)  # 等待子进程优雅关闭
            
            # 如果超时后子进程仍在运行，强制终止它
            if child_process.is_alive():
                print("子进程未能在20秒内关闭，强制终止...")
                child_process.kill()  # 使用kill()而不是terminate()，确保彻底终止
                child_process.join(timeout=5)  # 再等待5秒确保资源释放
                if child_process.is_alive():
                    print("警告：子进程仍然无法终止，可能需要手动处理")

        # 如果子进程已经退出，重置 child_process 为 None，以便下次交易时间重新启动
        if child_process is not None and not child_process.is_alive():
            child_process = None
            print("子进程关闭成功。")

        sleep(5)  # 每5秒检查一次状态


if __name__ == "__main__":
    rewrite_status(data_recorder_filename)  # 执行一次设置重写
    main_parent()  # 启动父进程守护程序
