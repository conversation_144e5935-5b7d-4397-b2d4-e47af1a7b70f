# Portfolio_trades脚本: 

################### TradeAnalyse.py ##########################
## 安装步骤
如果没有安装openxl，请执行：
pip install openxl
pip install pymysql

portfolio_trades.py放在工作目录下

## 脚本运行
python portfolio_trades.py

## 使用指南
strategy_name：list, 数据库里的strategy_name
author： 数据库里的author
rate：    手续费
slippage： 滑点（和vnpy里的slippage一样）
capital：   总资金
symbol: list, 数据库里对应的symbol，可以不填，默认为'all',symbol='all'表示取所有制定策略下symbol的数据

from portfolio_trades import Portfolio
pf = Portfolio()
pf.summary(strategy_name, author, rate, slippage, capital, symbol)

实际测试下：
1. 策略因为参数异常无交易时，该模块不会报错中断程序，可以在大规模测试中使用
2. 策略正常时，会保存分析文件：$strategy_name_$date.xlsx


