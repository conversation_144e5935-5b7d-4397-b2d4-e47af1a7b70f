[{"title": "Subscribe and Unsubscribe | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/sub.html", "html": " Futu API Doc v8.8\nProgramming Language \n简体中文 \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nSubscription \n\nSubscribe and Unsubscribe\nGet Subscription Status\n\nPush and Callback \n\nGet \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nSubscribe and Unsubscribe\n#\nSubscribe to Real-Time Market Data\n\nsubscribe(code_list, subtype_list, is_first_push=True, subscribe_push=True, is_detailed_orderbook=False, extended_time=False)\n\nDescription\n\nTo subscribe to the real-time information required for registration, specify the stock and subscription data types. HK market (including underlying stocks, warrants, CBBCs, options, futures) subscriptions require LV1 and above permissions. Subscriptions are not supported under BMP permissions.\n\nParameters\n\nParameter\tType\tDescription\ncode_list\tlist\tA list of stock codes that need to be subscribed. \n\nsubtype_list\tlist\tList of data types that need to be subscribed. \n\nis_first_push\tbool\tWhether to push the cached data immediately after a successful subscription. \n\nsubscribe_push\tbool\tWhether to push data after subscription.\n\nis_detailed_orderbook\tbool\tWhether to subscribe to the detailed order book. \n\nextended_time\tbool\tWhether to allow pre-market and after-hours data of US stocks. \n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\nerr_message\tNoneType\tIf ret == RET_OK, None is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nExample\n\nimport time\nfrom futu import *\nclass OrderBookTest(OrderBookHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, data = super(OrderBookTest,self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"OrderBookTest: error, msg: %s\"% data)\n            return RET_ERROR, data\n        print(\"OrderBookTest \", data) # OrderBookTest's own processing logic\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = OrderBookTest()\nquote_ctx.set_handler(handler) # Set real-time swing callback\nquote_ctx.subscribe(['HK.00700'], [SubType.ORDER_BOOK]) # Subscribe to the order type, OpenD starts to receive continuous push from the server\ntime.sleep(15) # Set the script to receive OpenD push duration to 15 seconds\nquote_ctx.close() # Close the current link, OpenD will automatically cancel the corresponding type of subscription for the corresponding stock after 1 minute\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\nOrderBookTest  {'code': 'HK.00700', 'svr_recv_time_bid': '2020-04-29 10:40:03.147', 'svr_recv_time_ask': '2020-04-29 10:40:03.147', 'Bid': [(416.8, 2600, 11, {}), (416.6, 13100, 17, {}), (416.4, 24600, 17, {}), (416.2, 28000, 13, {}), (416.0, 46900, 30, {}), (415.8, 10900, 7, {}), (415.6, 7100, 9, {}), (415.4, 13300, 3, {}), (415.2, 300, 3, {}), (415.0, 11200, 36, {})], 'Ask': [(417.0, 17600, 31, {}), (417.2, 17800, 24, {}), (417.4, 15300, 10, {}), (417.6, 28800, 17, {}), (417.8, 20700, 11, {}), (418.0, 114200, 155, {}), (418.2, 20600, 19, {}), (418.4, 24100, 28, {}), (418.6, 42700, 45, {}), (418.8, 181900, 76, {})]}\n\n1\n\n#\nCancel Market Data Subscription\n\nunsubscribe(code_list, subtype_list, unsubscribe_all=False)\n\nDescription\n\nunsubscribe\n\nParameters\n\nParameter\tType\tDescription\ncode_list\tlist\tA list of stock codes to unsubscribe. \n\nsubtype_list\tlist\tList of data types that need to be subscribed. \n\nunsubscribe_all\tbool\tCancel all subscriptions. \n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\nerr_message\tNoneType\tIf ret == RET_OK, None is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nprint('current subscription status :', quote_ctx.query_subscription()) # Query the initial subscription status\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE, SubType.TICKER], subscribe_push=False)\n# First subscribed to the two types of QUOTE and TICKER. After the subscription is successful, OpenD will continue to receive pushes from the server, False means that there is no need to push to the script temporarily\nif ret_sub == RET_OK: # Subscription successful\n    print('subscribe successfully! current subscription status :', quote_ctx.query_subscription()) # Query subscription status after successful subscription\n    time.sleep(60) # You can unsubscribe at least 1 minute after subscribing\n    ret_unsub, err_message_unsub = quote_ctx.unsubscribe(['HK.00700'], [SubType.QUOTE])\n    if ret_unsub == RET_OK:\n        print('unsubscribe successfully! current subscription status:', quote_ctx.query_subscription()) # Query the subscription status after canceling the subscription\n    else:\n        print('unsubscription failed!', err_message_unsub)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\ncurrent subscription status : (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\nsubscribe successfully！current subscription status : (0, {'total_used': 2, 'remain': 998, 'own_used': 2, 'sub_list': {'QUOTE': ['HK.00700'], 'TICKER': ['HK.00700']}})\nunsubscribe successfully！current subscription status: (0, {'total_used': 1, 'remain': 999, 'own_used': 1, 'sub_list': {'TICKER': ['HK.00700']}})\n\n1\n2\n3\n\n#\nCancel All Market Data Subscriptions\n\nunsubscribe_all()\n\nDescription\n\nUnsubscribe all subscriptions\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\nerr_message\tNoneType\tIf ret == RET_OK, None is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nprint('current subscription status :', quote_ctx.query_subscription()) # Query the initial subscription status\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE, SubType.TICKER], subscribe_push=False)\n# First subscribed to the two types of QUOTE and TICKER. After the subscription is successful, OpenD will continue to receive pushes from the server, False means that there is no need to push to the script temporarily\nif ret_sub == RET_OK: # Subscription successful\n    print('subscribe successfully! current subscription status :', quote_ctx.query_subscription()) # Query subscription status after successful subscription\n    time.sleep(60) # You can unsubscribe at least 1 minute after subscribing\n    ret_unsub, err_message_unsub = quote_ctx.unsubscribe_all() # Cancel all subscriptions\n    if ret_unsub == RET_OK:\n        print('unsubscribe all successfully! current subscription status:', quote_ctx.query_subscription()) # Query the subscription status after canceling the subscription\n    else:\n        print('Failed to cancel all subscriptions！', err_message_unsub)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\ncurrent subscription status : (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\nsubscribe successfully！current subscription status : (0, {'total_used': 2, 'remain': 998, 'own_used': 2, 'sub_list': {'QUOTE': ['HK.00700'], 'TICKER': ['HK.00700']}})\nunsubscribe all successfully！current subscription status: (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\n\n1\n2\n3\n\n\nInterface Limitations\n\nSupports subscriptions of multiple real-time data types, refer to SubType, each stock subscription for one one quota.\nPlease refer to Subscription Quota & Historical Candlestick Quota for Subscription Quota rules.\nYou can unsubscribe after subscribing after at least one minute.\nDue to the large amount of SF market data in Hong Kong stocks, in order to ensure the speed of the SF market and the processing performance of OpenD, currently SF authorized users are limited to subscribing to 50 security products (including hkex stocks, warrants, bulls and bears) at the same time, the remaining subscription quota can still be used to subscribe to other types, such as: tickers and brokerage etc.\nHK options and futures do not support subscription to TICKER type under LV1 authority.\n\n← Quote Object\nGet Subscription Status →\n\nSubscribe and Unsubscribe"}, {"title": "Get Historical Orders | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/trade/get-history-order-list.html", "html": " Futu API Doc v8.8\nProgramming Language\n简体中文\nProgramming Language\n简体中文\n\nIntroduction\n\nQuick Start\n\nOpenD\n\nQuote API\n\nTrade API\n\nOverview\nTransaction Objects\n\nAccounts\n\nAssets and Positions\n\nOrders\n\nPlace Orders\nModify or Cancel Orders\nGet open Orders\nGet Historical Orders\nOrders Push Callback\nGet Order Fee\nSubscribe to Transaction Push\n\nDeals\n\nTrading Definitions\n\nBasic API\n\nQ&A\n\n# Get Historical Orders\n\nhistory_order_list_query(status_filter_list=[], code='', start='', end='', trd_env=TrdEnv.REAL, acc_id=0, acc_index=0)\n\nDescription\n\nQuery the historical order list of a specified trading account\n\nParameters\n\nParameter\tType\tDescription\nstatus_filter_list\tlist\tOrder status filter conditions.\n\ncode\tstr\tSecurity symbol.\n\nstart\tstr\tStart time.\n\nend\tstr\tEnd time\n\ntrd_env\tTrdEnv\tTrading environment.\nacc_id\tint\tTrading account ID.\n\nacc_index\tint\tThe account number in the trading account list.\nThe combination of start and end is as follows\nStart type\tEnd type\tDescription\nstr\tstr\tstart and end are the specified dates respectively.\nNone\tstr\tstart is 90 days before end.\nstr\tNone\tend is 90 days after start.\nNone\tNone\tstart is 90 days before, end is the current date.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, order list is returned.\nstr\tIf ret != RET_OK, error description is returned.\nOrder list format as follows:\nField\tType\tDescription\ntrd_side\tTrdSide\tTrading direction.\norder_type\tOrderType\tOrder type.\norder_status\tOrderStatus\tOrder status.\norder_id\tstr\tOrder ID.\ncode\tstr\tSecurity code.\nstock_name\tstr\tSecurity name.\nqty\tfloat\tOrder quantity.\n\nprice\tfloat\tOrder price.\n\ncurrency\tCurrency\tTransaction currency.\ncreate_time\tstr\tCreate time.\n\nupdated_time\tstr\tLast update time.\n\ndealt_qty\tfloat\tDeal quantity\n\ndealt_avg_price\tfloat\tAverage deal price.\n\nlast_err_msg\tstr\tThe last error description.\n\nremark\tstr\tIdentification of remarks when placing an order.\n\ntime_in_force\tTimeInForce\tValid period.\nfill_outside_rth\tbool\tWhether pre-market and after-hours are needed.\n\naux_price\tfloat\tTraget price.\ntrail_type\tTrailType\tTrailing type.\ntrail_value\tfloat\tTrailing amount/ratio.\ntrail_spread\tfloat\tSpecify spread.\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.history_order_list_query()\nif ret == RET_OK:\n    print(data)\n    if data.shape[0] > 0:  # If the order list is not empty\n        print(data['order_id'][0])  # Get Order ID of the first holding position\n        print(data['order_id'].values.tolist())  # Convert to list\nelse:\n    print('history_order_list_query error: ', data)\ntrd_ctx.close()\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n        code stock_name trd_side           order_type   order_status             order_id    qty  price              create_time             updated_time  dealt_qty  dealt_avg_price last_err_msg      remark time_in_force fill_outside_rth aux_price trail_type trail_value trail_spread currency\n0   HK.00700                 BUY           NORMAL  CANCELLED_ALL  6644468615272262086  100.0  520.0  2021-09-06 10:17:52.465  2021-09-07 16:10:22.806        0.0              0.0               asdfg+=@@@           GTC              N/A       560        N/A         N/A          N/A      HKD\n6644468615272262086\n['6644468615272262086']\n\n1\n2\n3\n4\n\n\nInterface Limitations\n\nA maximum of 10 requests per 30 seconds\n\nTips\n\nHistorical orders are arranged in reverse chronological order: later orders return first, followed by earlier orders.\n\n← Get open Orders Orders Push Callback →\n\nGet Historical Orders"}, {"title": "查询未完成订单 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/get-order-list.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n订单 \n\n下单\n改单撤单\n查询未完成订单\n查询历史订单\n响应订单推送回调\n查询订单费用\n订阅交易推送\n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n查询未完成订单\n\norder_list_query(order_id=\"\", status_filter_list=[], code='', start='', end='', trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, refresh_cache=False)\n\n介绍\n\n查询指定交易业务账户的未完成订单列表\n\n参数\n\n参数\t类型\t说明\norder_id\tstr\t订单号过滤 \n\nstatus_filter_list\tlist\t订单状态过滤 \n\ncode\tstr\t代码过滤 \n\nstart\tstr\t开始时间 \n\nend\tstr\t结束时间 \n\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\nrefresh_cache\tbool\t是否刷新缓存 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回订单列表\nstr\t当 ret != RET_OK 时，返回错误描述\n订单列表格式如下：\n字段\t类型\t说明\ntrd_side\tTrdSide\t交易方向\norder_type\tOrderType\t订单类型\norder_status\tOrderStatus\t订单状态\norder_id\tstr\t订单号\ncode\tstr\t股票代码\nstock_name\tstr\t股票名称\nqty\tfloat\t订单数量 \n\nprice\tfloat\t订单价格 \n\ncurrency\tCurrency\t交易货币\ncreate_time\tstr\t创建时间 \n\nupdated_time\tstr\t最后更新时间 \n\ndealt_qty\tfloat\t成交数量 \n\ndealt_avg_price\tfloat\t成交均价 \n\nlast_err_msg\tstr\t最后的错误描述 \n\nremark\tstr\t下单时备注的标识 \n\ntime_in_force\tTimeInForce\t有效期限\nfill_outside_rth\tbool\t是否允许盘前盘后（用于港股盘前竞价与美股盘前盘后） \n\naux_price\tfloat\t触发价格\ntrail_type\tTrailType\t跟踪类型\ntrail_value\tfloat\t跟踪金额/百分比\ntrail_spread\tfloat\t指定价差\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.order_list_query()\nif ret == RET_OK:\n    print(data)\n    if data.shape[0] > 0:  # 如果订单列表不为空\n        print(data['order_id'][0])  # 获取未完成订单的第一个订单号\n        print(data['order_id'].values.tolist())  # 转为 list\nelse:\n    print('order_list_query error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n        code stock_name trd_side           order_type   order_status             order_id    qty  price              create_time             updated_time  dealt_qty  dealt_avg_price last_err_msg      remark time_in_force fill_outside_rth aux_price trail_type trail_value trail_spread currency\n0   HK.00700                 BUY           NORMAL  CANCELLED_ALL  6644468615272262086  100.0  520.0  2021-09-06 10:17:52.465  2021-09-07 16:10:22.806        0.0              0.0               asdfg+=@@@           GTC              N/A       560        N/A         N/A          N/A      HKD\n6644468615272262086\n['6644468615272262086']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n\n接口限制\n\n每 30 秒内最多请求 10 次查询未完成订单接口\n调用此接口，只有在刷新缓存时，才受到限频限制\n\n提示\n\n未完成订单，按照时间的“顺序”进行排列，即：先提交的订单在前，后提交的订单在后\n\n← 改单撤单\n查询历史订单 →\n\n查询未完成订单"}, {"title": "改单撤单 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/modify-order.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n订单 \n\n下单\n改单撤单\n查询未完成订单\n查询历史订单\n响应订单推送回调\n查询订单费用\n订阅交易推送\n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n改单撤单\n\nmodify_order(modify_order_op, order_id, qty, price, adjust_limit=0, trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, aux_price=None, trail_type=None, trail_value=None, trail_spread=None)\n\n介绍\n\n修改订单的价格和数量、撤单、操作订单的失效和生效、删除订单等。\n如果是 OpenHKCCTradeContext，将不支持改单。可撤单。删除订单是 OpenD 本地操作。\n\n参数\n\n参数\t类型\t说明\nmodify_order_op\tModifyOrderOp\t改单操作类型\norder_id\tstr\t订单号\nqty\tfloat\t订单改单后的数量 \n\nprice\tfloat\t订单改单后的价格 \n\nadjust_limit\tfloat\t价格微调幅度 \n\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\naux_price\tfloat\t触发价格 \n\ntrail_type\tTrailType\t跟踪类型 \n\ntrail_value\tfloat\t跟踪金额/百分比 \n\ntrail_spread\tfloat\t指定价差 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回改单信息\nstr\t当 ret != RET_OK 时，返回错误描述\n改单信息格式如下：\n字段\t类型\t说明\ntrd_env\tTrdEnv\t交易环境\norder_id\tstr\t订单号\n\nExample\n\nfrom futu import *\npwd_unlock = '123456'\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.unlock_trade(pwd_unlock)  # 若使用真实账户改单/撤单，需先对账户进行解锁。此处示例为模拟账户撤单，也可省略解锁。\nif ret == RET_OK:\n    order_id = \"8851102695472794941\"\n    ret, data = trd_ctx.modify_order(ModifyOrderOp.CANCEL, order_id, 0, 0)\n    if ret == RET_OK:\n        print(data)\n        print(data['order_id'][0])  # 获取改单的订单号\n        print(data['order_id'].values.tolist())  # 转为 list\n    else:\n        print('modify_order error: ', data)\nelse:\n    print('unlock_trade failed: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\n    trd_env             order_id\n0    REAL      8851102695472794941\n8851102695472794941\n['8851102695472794941']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n\ncancel_all_order(trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, trdmarket=TrdMarket.NONE)\n\n介绍\n\n撤消全部订单。模拟交易以及 A 股通账户暂不支持全部撤单。\n\n参数\n\n参数\t类型\t说明\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\ntrdmarket\tTrdMarket\t指定交易市场 \n\n返回\n\n参数\t类型\t说明\nret\tstr\t接口调用结果。ret == RET_OK 代表接口调用正常，ret != RET_OK 代表接口调用失败\ndata\tstr\t当 ret == RET_OK，返回\"success\"\n当 ret != RET_OK，返回错误描述\n全部撤单信息格式如下：\n字段\t类型\t说明\ntrd_env\tTrdEnv\t交易环境\norder_id\tstr\t订单号\n\nExample\n\nfrom futu import *\npwd_unlock = '123456'\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.unlock_trade(pwd_unlock)  # 若使用真实账户改单/撤单，需先对账户进行解锁。此处示例为模拟账户全部撤单，也可省略解锁。\nif ret == RET_OK:\n    ret, data = trd_ctx.cancel_all_order()\n    if ret == RET_OK:\n        print(data)\n    else:\n        print('cancel_all_order error: ', data)\nelse:\n    print('unlock_trade failed: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\nOutput\nsuccess\n\n \n\n        Copied!\n    \n1\n\n\n接口限制\n\n每 30 秒内最多请求 20 次改单撤单接口，且连续两次请求的间隔不可小于 0.04 秒。\n真实账户调用改单撤单接口前，需要先进行 解锁；模拟账户无需解锁。\n\n提示\n\n若执行 修改订单 操作，各类订单类型对应的必传参数，可 点击这里 了解更多。\n如果希望执行 改单操作 去 修改订单数量，此接口入参的订单数量 qty，应该等于期望成交的总数量。\n举例： 一笔订单数量是 N 股，已部分成交 n 股。对于暂未成交的 (N-n) 股，如果您希望撤掉其中的 x 股，modify_order_op 应选择 NORMAL，qty 应传 (N-x)。 \n如果希望执行 撤单操作，此接口入参的 modify_order_op 应该选择 CANCEL。\n举例： 一笔订单数量是 N 股，已部分成交 n 股。如果希望将未成交的 (N-n) 股全部撤掉，modify_order_op 应选择 CANCEL，此时 qty 和 price 的入参会被忽略。\n\n← 下单\n查询未完成订单 →\n\n改单撤单"}, {"title": "查询持仓 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/get-position-list.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n查询账户资金\n查询最大可买可卖\n查询持仓\n获取融资融券数据\n\n订单 \n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n查询持仓\n\nposition_list_query(code='', pl_ratio_min=None, pl_ratio_max=None, trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, refresh_cache=False)\n\n介绍\n\n查询交易业务账户的持仓列表\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t代码过滤 \n\npl_ratio_min\tfloat\t当前盈亏比例下限过滤，仅返回高于此比例的持仓 \n\npl_ratio_max\tfloat\t当前盈亏比例上限过滤，低于此比例的会返回 \n\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\nrefresh_cache\tbool\t是否刷新缓存 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回持仓列表\nstr\t当 ret != RET_OK 时，返回错误描述\n持仓列表\n字段\t类型\t说明\nposition_side\tPositionSide\t持仓方向\ncode\tstr\t股票代码\nstock_name\tstr\t股票名称\nqty\tfloat\t持有数量 \n\ncan_sell_qty\tfloat\t可用数量 \n\ncurrency\tCurrency\t交易货币\nnominal_price\tfloat\t市价 \n\ncost_price\tfloat\t摊薄成本价（证券账户），平均开仓价（期货账户）\ncost_price_valid\tbool\t成本价是否有效 \n\nmarket_val\tfloat\t市值 \n\npl_ratio\tfloat\t盈亏比例 \n\npl_ratio_valid\tbool\t盈亏比例是否有效 \n\npl_val\tfloat\t盈亏金额 \n\npl_val_valid\tbool\t盈亏金额是否有效 \n\ntoday_pl_val\tfloat\t今日盈亏金额 \n\ntoday_trd_val\tfloat\t今日交易金额 \n\ntoday_buy_qty\tfloat\t今日买入总量 \n\ntoday_buy_val\tfloat\t今日买入总额 \n\ntoday_sell_qty\tfloat\t今日卖出总量 \n\ntoday_sell_val\tfloat\t今日卖出总额 \n\nunrealized_pl\tfloat\t未实现盈亏 \n\nrealized_pl\tfloat\t已实现盈亏 \n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.position_list_query()\nif ret == RET_OK:\n    print(data)\n    if data.shape[0] > 0:  # 如果持仓列表不为空\n        print(data['stock_name'][0])  # 获取持仓第一个股票名称\n        print(data['stock_name'].values.tolist())  # 转为 list\nelse:\n    print('position_list_query error: ', data)\ntrd_ctx.close()  # 关闭当条连接\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n         code                    stock_name      qty  can_sell_qty  cost_price  cost_price_valid  market_val  nominal_price  pl_ratio  pl_ratio_valid    pl_val  pl_val_valid  today_buy_qty  today_buy_val  today_pl_val  today_trd_val  today_sell_qty  today_sell_val position_side unrealized_pl realized_pl currency\n0   HK.00943                          中证国际   8000.0        8000.0      0.0320              True      200.00         0.0250    -21.87            True  -56.0000          True            0.0            0.0        0.0000         0.0000             0.0             0.0          LONG           N/A         N/A      HKD\n中证国际\n['中证国际']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n\n接口限制\n\n每 30 秒内最多请求 10 次查询持仓接口\n调用此接口，只有在刷新缓存时，才受到限频限制\n\n← 查询最大可买可卖\n获取融资融券数据 →\n\n查询持仓"}, {"title": "查询最大可买可卖 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/get-max-trd-qtys.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n查询账户资金\n查询最大可买可卖\n查询持仓\n获取融资融券数据\n\n订单 \n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n查询最大可买可卖\n\nacctradinginfo_query(order_type, code, price, order_id=None, adjust_limit=0, trd_env=TrdEnv.REAL, acc_id=0, acc_index=0)\n\n介绍\n\n查询指定交易业务账户下的最大可买卖数量，亦可查询指定交易业务账户下指定订单的最大可改成的数量。\n\n现金账户请求期权不适用。\n\n参数\n\n参数\t类型\t说明\norder_type\tOrderType\t订单类型\ncode\tstr\t证券代码 \n\nprice\tfloat\t报价 \n\norder_id\tstr\t订单号 \n\nadjust_limit\tfloat\t价格微调幅度 \n\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回账号列表\nstr\t当 ret != RET_OK 时，返回错误描述\n账号列表格式如下：\n字段\t类型\t说明\nmax_cash_buy\tfloat\t现金可买 \n\nmax_cash_and_margin_buy\tfloat\t最大可买 \n\nmax_position_sell\tfloat\t持仓可卖 \n\nmax_sell_short\tfloat\t可卖空 \n\nmax_buy_back\tfloat\t平仓需买入 \n\nlong_required_im\tfloat\t买 1 张合约所带来的初始保证金变动 \n\nshort_required_im\tfloat\t卖 1 张合约所带来的初始保证金变动 \n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.acctradinginfo_query(order_type=OrderType.NORMAL, code='HK.00700', price=400)\nif ret == RET_OK:\n    print(data)\n    print(data['max_cash_and_margin_buy'][0])  # 最大融资可买数量\nelse:\n    print('acctradinginfo_query error: ', data)\ntrd_ctx.close()  # 关闭当条连接\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\nOutput\n    max_cash_buy  max_cash_and_margin_buy  max_position_sell  max_sell_short  max_buy_back long_required_im short_required_im\n0           0.0                   1500.0                0.0             0.0           0.0              N/A               N/A\n1500.0\n\n \n\n        Copied!\n    \n1\n2\n3\n\n\n接口限制\n\n每 30 秒内最多请求 10 次查询最大可买可卖接口\n\n提示\n\n现金业务账户不支持交易衍生品，因此不支持通过现金业务账户查询期权的最大可买可卖。\n\n← 查询账户资金\n查询持仓 →\n\n查询最大可买可卖"}, {"title": "查询账户资金 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/get-funds.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n查询账户资金\n查询最大可买可卖\n查询持仓\n获取融资融券数据\n\n订单 \n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n查询账户资金\n\naccinfo_query(trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, refresh_cache=False, currency=Currency.HKD)\n\n介绍\n\n查询交易业务账户的资产净值、证券市值、现金、购买力等资金数据。\n\n参数\n\n参数\t类型\t说明\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\nrefresh_cache\tbool\t是否刷新缓存 \n\ncurrency\tCurrency\t计价货币 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回资金数据\nstr\t当 ret != RET_OK 时，返回错误描述\n资金数据格式如下：\n字段\t类型\t说明\npower\tfloat\t最大购买力 \n\nmax_power_short\tfloat\t卖空购买力 \n\nnet_cash_power\tfloat\t现金购买力\n\ntotal_assets\tfloat\t总资产净值\n\nsecurities_assets\tfloat\t证券资产净值\n\nfunds_assets\tfloat\t基金资产净值\n\nbonds_assets\tfloat\t债券资产净值\n\ncash\tfloat\t现金\n\nmarket_val\tfloat\t证券市值 \n\nlong_mv\tfloat\t多头市值\nshort_mv\tfloat\t空头市值\npending_asset\tfloat\t在途资产\ninterest_charged_amount\tfloat\t计息金额\nfrozen_cash\tfloat\t冻结资金\navl_withdrawal_cash\tfloat\t现金可提 \n\nmax_withdrawal\tfloat\t最大可提 \n\ncurrency\tCurrency\t计价货币 \n\navailable_funds\tfloat\t可用资金 \n\nunrealized_pl\tfloat\t未实现盈亏 \n\nrealized_pl\tfloat\t已实现盈亏 \n\nrisk_level\tCltRiskLevel\t风控状态 \n\nrisk_status\tCltRiskStatus\t风险状态 \n\ninitial_margin\tfloat\t初始保证金\nmargin_call_margin\tfloat\tMargin Call 保证金\nmaintenance_margin\tfloat\t维持保证金\nhk_cash\tfloat\t港元现金 \n\nhk_avl_withdrawal_cash\tfloat\t港元可提 \n\nus_cash\tfloat\t美元现金 \n\nus_avl_withdrawal_cash\tfloat\t美元可提 \n\njp_cash\tfloat\t日元现金 \n\njp_avl_withdrawal_cash\tfloat\t日元可提 \n\npdt_seq\tstring\t剩余日内交易次数 \n\nis_pdt\tbool\t是否为 PDT 账户 \n\nbeginning_dtbp\tfloat\t初始日内交易购买力 \n\nremaining_dtbp\tfloat\t剩余日内交易购买力 \n\ndt_call_amount\tfloat\t日内交易待缴金额 \n\ndt_status\tDtStatus\t日内交易限制情况 \n\ncn_cash\tfloat\t人民币现金 \n\ncn_avl_withdrawal_cash\tfloat\t人民币可提 \n\nsg_cash\tfloat\t新元现金 \n\nsg_avl_withdrawal_cash\tfloat\t新元可提 \n\nau_cash\tfloat\t澳元现金 \n\nau_avl_withdrawal_cash\tfloat\t澳元可提 \n\nhkd_net_cash_power\tfloat\t港元现金购买力 \n\nusd_net_cash_power\tfloat\t美元现金购买力 \n\njpy_net_cash_power\tfloat\t日元现金购买力 \n\ncnh_net_cash_power\tfloat\t人民币现金购买力 \n\nsgd_net_cash_power\tfloat\t新元现金购买力 \n\naud_net_cash_power\tfloat\t澳元现金购买力 \n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.accinfo_query()\nif ret == RET_OK:\n    print(data)\n    print(data['power'][0])  # 取第一行的购买力\n    print(data['power'].values.tolist())  # 转为 list\nelse:\n    print('accinfo_query error: ', data)\ntrd_ctx.close()  # 关闭当条连接\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\nOutput\npower  max_power_short  net_cash_power  total_assets  securities_assets  fund_assets  bond_assets   cash   market_val      long_mv   short_mv  pending_asset  interest_charged_amount  frozen_cash  avl_withdrawal_cash  max_withdrawal currency available_funds unrealized_pl realized_pl risk_level risk_status  initial_margin  margin_call_margin  maintenance_margin  hk_cash  hk_avl_withdrawal_cash  hkd_net_cash_power  us_cash  us_avl_withdrawal_cash  usd_net_cash_power  cn_cash  cn_avl_withdrawal_cash  cnh_net_cash_power  jp_cash  jp_avl_withdrawal_cash  jpy_net_cash_power sg_cash sg_avl_withdrawal_cash sgd_net_cash_power au_cash au_avl_withdrawal_cash aud_net_cash_power is_pdt pdt_seq beginning_dtbp remaining_dtbp dt_call_amount dt_status\n0  465453.903307    465453.903307             0.0   289932.0404        197028.2204     92903.82          0.0  25.18  289906.8648  211960.7568 -14957.712            0.0                      0.0    25.930845                  0.0             0.0      HKD             N/A           N/A         N/A        N/A      LEVEL3   219346.648525       288656.787955       181250.967601      0.0                     0.0          13225.7955     3.24                     0.0           9656.4365      0.0                     0.0                 0.0      0.0                     0.0                 0.0     N/A                    N/A                N/A     N/A                    N/A                N/A    N/A     N/A            N/A            N/A            N/A       N/A\n465453.9033072\n[465453.9033072]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n\n接口限制\n\n每 30 秒内最多请求 10 次查询账户资金接口\n调用此接口，只有在刷新缓存时，才受到限频限制\n\n← 解锁交易\n查询最大可买可卖 →\n\n查询账户资金"}, {"title": "交易对象 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/base.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n订单 \n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n交易对象\n#\n创建连接\n\nOpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\n\nOpenFutureTradeContext(host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\n\n介绍\n\n根据交易品类，选择账户，并创建对应的交易对象。\n\n实例\t账户\nOpenSecTradeContext\t证券账户 \n\nOpenFutureTradeContext\t期货账户 \n\n参数\n\n参数\t类型\t说明\nfilter_trdmarket\tTrdMarket\t筛选对应交易市场权限的账户 \n\nhost\tstr\tOpenD 监听的 IP 地址\nport\tint\tOpenD 监听的 IP 端口\nis_encrypt\tbool\t是否启用加密 \n\nsecurity_firm\tSecurityFirm\t所属券商\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\ntrd_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\n关闭连接\n\nclose()\n\n介绍\n\n关闭交易对象。默认情况下，Futu API 内部创建的线程会阻止进程退出，只有当所有 Context 都 close 后，进程才能正常退出。但通过 set_all_thread_daemon 可以设置所有内部线程为 daemon 线程，这时即使没有调用 Context 的 close，进程也可以正常退出。\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)\ntrd_ctx.close()  # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n\n\n← 交易接口总览\n获取交易业务账户列表 →\n\n交易对象"}, {"title": "Set Price Reminder | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/set-price-reminder.html", "html": " Futu API Doc v8.8\nProgramming Language \n简体中文 \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nGet Details of Historical Candlestick Quota\nSet Price Reminder\nGet Price Reminder List\nGet The Watchlist\nGet Groups From Watchlist\nModify the Watchlist\nPrice Reminder Callback\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nSet Price Reminder\n\nset_price_reminder(code, op, key=None, reminder_type=None, reminder_freq=None, value=None, note=None)\n\nDescription\n\nAdd, delete, modify, enable, and disable price reminders for specified stocks\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code\nop\tSetPriceReminderOp\tOperation type.\nkey\tint\tIdentification, do not need to fill in the case of adding all or deleting all.\nreminder_type\tPriceReminderType\tThe type of price reminder, this input parameter will be ignored when delete, enable, or disable.\nreminder_freq\tPriceReminderFreq\tThe frequency of price reminder, this input parameter will be ignored when delete, enabled, or disable.\nvalue\tfloat\tReminder value, the input parameter will be ignored when delete, enable, or disable. \n\nnote\tstr\tThe note set by the user, note supports no more than 20 Chinese characters, the input parameter will be ignored when delete, enable, or disable.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\nkey\tint\tIf ret == RET_OK, The price reminder key of the operation is returned. When deleting all reminders of a specific stock, 0 is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nExample\n\nfrom futu import *\nimport time\nclass PriceReminderTest(PriceReminderHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, content = super(PriceReminderTest,self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"PriceReminderTest: error, msg: %s\" % content)\n            return RET_ERROR, content\n        print(\"PriceReminderTest \", content)\n        return RET_OK, content\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = PriceReminderTest()\nquote_ctx.set_handler(handler)\nret, data = quote_ctx.get_market_snapshot(['HK.HSImain'])\nif ret == RET_OK:\n    bid_price = data['bid_price'][0] # Get real-time bid price\n    ask_price = data['ask_price'][0] # Get real-time selling price\n    # Set a reminder when the selling price is lower than (ask_price-1)\n    ret_ask, ask_data = quote_ctx.set_price_reminder(code='HK.HSImain', op=SetPriceReminderOp.ADD, key=None, reminder_type=PriceReminderTypeASK_PRICE_DOWN, reminder_freq=PriceReminderFreq.ALWAYS, value=(ask_price-1) ')\n    if ret_ask == RET_OK:\n        print('When the selling price is lower than (ask_price-1), remind that the setting is successful:', ask_data)\n    else:\n        print('error:', ask_data)\n    # Set a reminder when the bid price is higher than (bid_price+1)\n    ret_bid, bid_data = quote_ctx.set_price_reminder(code='HK.HSImain', op=SetPriceReminderOp.ADD, key=None, reminder_type=PriceReminderType.BID_PRICE_UP, reminder_freq=PriceReminderFreq.ALWAYS, value=(bid_price+1), note='456')\n    if ret_bid == RET_OK:\n        print('When the bid price is higher than (bid_price+1), the reminder is set successfully: ', bid_data)\n    else:\n        print('error:', bid_data)\ntime.sleep(15)\nquote_ctx.close()\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n\nOutput\nWhen the selling price is lower than (ask_price-1), the reminder is set successfully: 158815356110052101\nWhen the bid price is higher than (bid_price+1), the reminder is set successfully: 158815356129980801\nPriceReminderTest  {'code': 'HK.HSImain', 'price': 24532.0, 'change_rate': 0.122, 'market_status': 'OPEN', 'content': '买一价高于24533.000', 'note': '456', 'key': 158815356129980801, 'reminder_type': 'BID_PRICE_UP', 'set_value': 24533.0, 'cur_value': 24533.0}\nPriceReminderTest  {'code': 'HK.HSImain', 'price': 24532.0, 'change_rate': 0.122, 'market_status': 'OPEN', 'content': '卖一价低于24533.000', 'note': '123', 'key': 158815356110052101, 'reminder_type': 'ASK_PRICE_DOWN', 'set_value': 24533.0, 'cur_value': 24533.0}\n\n1\n2\n3\n4\n\n\nTips\n\nTrading volume in API is based on shares. A-shares are shown in lots in Futubull Client.\n\nThe type of price alert has minimum precision, as follows:\n\nTURNOVER_UP: The minimum precision of the turnover is 10 (Yuan, Hong Kong dollar, US dollar). The value passed in will be automatically rounded down to an integer multiple of the minimum precision. If you set 00700 transaction volume 102 yuan reminder, you will get 00700 transaction volume 100 yuan reminder. After setting; if you set 00700 transaction volume 8 yuan reminder, you will get 00700 transaction volume 0 yuan reminder after setting.\n\nVOLUME_UP: The minimum accuracy of A-share trading volume is 1000 shares, and the minimum accuracy of other market stock trading volume is 10 shares. The value passed in will be automatically rounded down to an integer multiple of the minimum precision.\n\nBID_VOL_UP, ASK_VOL_UP: The minimum precision for buying and selling of A-shares is 100 shares. The value passed in will be automatically rounded down to an integer multiple of the minimum precision.\n\nThe precision of the remaining price alert types supports up to 3 decimal places\n\nInterface Limitations\n\nA maximum of 60 requests per 30 seconds\nThe upper limit of reminders that can be set for each type of each stock is 10\n\n← Get Details of Historical Candlestick Quota\nGet Price Reminder List →\n\nSet Price Reminder"}, {"title": "底层协议介绍 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/ftapi/protocol.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\n基础功能\n通用定义\n底层协议介绍\n\nQ&A \n\n#\n底层协议介绍\n\nFutu API 是富途为主流的编程语言（Python、Java、C#、C++、JavaScript）封装的 API SDK，以方便您调用，降低策略开发难度。\n这部分主要介绍策略脚本与 OpenD 服务之间通信的底层协议，适用于非上述 5 种编程语言用户，自行对接实现底层裸协议。\n\n提示\n\n如果您使用的编程语言在上述的 5 种主流编程语言之内，可以直接跳过这部分内容。\n#\n协议请求流程\n建立连接\n初始化连接\n请求数据或接收推送数据\n定时发送 KeepAlive 保持连接\n\n#\n协议设计\n\n协议数据包括协议头以及协议体，协议头固定字段，协议体根据具体协议决定。\n\n#\n协议头\nstruct APIProtoHeader\n{\n    u8_t szHeaderFlag[2];\n    u32_t nProtoID;\n    u8_t nProtoFmtType;\n    u8_t nProtoVer;\n    u32_t nSerialNo;\n    u32_t nBodyLen;\n    u8_t arrBodySHA1[20];\n    u8_t arrReserved[8];\n};\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n字段\t说明\nszHeaderFlag\t包头起始标志，固定为“FT”\nnProtoID\t协议 ID\nnProtoFmtType\t协议格式类型，0 为 Protobuf 格式，1 为 Json 格式\nnProtoVer\t协议版本，用于迭代兼容，目前填 0\nnSerialNo\t包序列号，用于对应请求包和回包，要求递增\nnBodyLen\t包体长度\narrBodySHA1\t包体原始数据(解密后)的 SHA1 哈希值\narrReserved\t保留 8 字节扩展\n\n提示\n\nu8_t 表示 8 位无符号整数，u32_t 表示 32 位无符号整数\nOpenD 内部处理使用 Protobuf，因此协议格式建议使用 Protobuf，减少 Json 转换开销\nnProtoFmtType 字段指定了包体的数据类型，回包会回对应类型的数据；推送协议数据类型由 OpenD 配置文件指定\narrBodySHA1 用于校验请求数据在网络传输前后的一致性，必须正确填入\n协议头的二进制流使用的是小端字节序，即一般不需要使用 ntohl 等相关函数转换数据\n#\n协议体\n#\nProtobuf 协议请求包体结构\nmessage C2S\n{\n    required int64 req = 1;\n}\n\nmessage Request\n{\n    required C2S c2s = 1;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n#\nProtobuf 协议回应包体结构\nmessage S2C\n{\n    required int64 data = 1;\n}\n\nmessage Response\n{\n    required int32 retType = 1 [default = -400]; //RetType，返回结果\n    optional string retMsg = 2;\n    optional int32 errCode = 3;\n    optional S2C s2c = 4;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n字段\t说明\nc2s\t请求参数结构\nreq\t请求参数，实际根据协议定义\nretType\t请求结果\nretMsg\t若请求失败，说明失败原因\nerrCode\t若请求失败对应错误码\ns2c\t回应数据结构，部分协议不返回数据则无该字段\ndata\t回应数据，实际根据协议定义\n\n提示\n\n包体格式类型请求包由协议头 nProtoFmtType 指定，OpenD 主动推送格式在 InitConnect 设置。\n原始协议文件格式是以 Protobuf 格式定义，若需要 json 格式传输，建议使用 protobuf3 的接口直接转换成 json。\n枚举值字段定义使用有符号整形，注释指明对应枚举，枚举一般定义于 Common.proto，Qot_Common.proto，Trd_Common.proto 文件中。\n协议中价格、百分比等数据用浮点类型来传输，直接使用会有精度问题，需要根据精度（如协议中未指明，默认小数点后三位）做四舍五入之后再使用。\n#\n心跳保活\nsyntax = \"proto2\";\npackage KeepAlive;\noption java_package = \"com.futu.openapi.pb\";\noption go_package = \"github.com/futuopen/ftapi4go/pb/keepalive\";\n\nimport \"Common.proto\";\n\nmessage C2S\n{\n\trequired int64 time = 1; //客户端发包时的格林威治时间戳，单位秒\n}\n\nmessage S2C\n{\n\trequired int64 time = 1; //服务器回包时的格林威治时间戳，单位秒\n}\n\nmessage Request\n{\n\trequired C2S c2s = 1;\n}\n\nmessage Response\n{\n\trequired int32 retType = 1 [default = -400]; //RetType,返回结果\n\toptional string retMsg = 2;\n\toptional int32 errCode = 3;\n\t\n\toptional S2C s2c = 4;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n\n\n介绍\n\n心跳保活\n\n协议 ID\n\n1004\n\n使用\n\n根据初始化链接返回的心跳保活间隔时间，向 OpenD 发送保活协议\n\n#\n加密通信流程\n若 OpenD 配置了加密，InitConnect 初始化连接协议必须使用 RSA 公钥加密，后续其他协议使用 InitConnect 返回的随机密钥进行 AES 加密通信。\nOpenD 的加密流程借鉴了 SSL 协议，但考虑到一般是本地部署服务和应用，简化了相关流程，OpenD 与接入 Client 共用了同一个 RSA 私钥文件，请妥善保存和分发私钥文件。\n可到这个 网址 在线生成随机 RSA 密钥对，密钥格式必须为 PCKS#1，密钥长度 512，1024 都可以，不要设置密码，将生成的私钥复制保存到文件中，然后将私钥文件路径配置到 OpenD 配置 约定的 rsa_private_key 配置项中。\n建议有实盘交易的用户配置加密，避免账户和交易信息泄露。\n\n#\nRSA 加解密\nOpenD 配置 约定 rsa_private_key 为私钥文件路径\nOpenD 与接入客户端共用相同的私钥文件\nRSA 加解密仅用于 InitConnect 请求，用于安全获取其它请求协议的对称加密 Key\nOpenD 的 RSA 密钥为 1024 位，填充方式 PKCS1，公钥加密，私钥解密，公钥可通过私钥生成\nPython API 参考实现：RsaCrypt 类的 encrypt / decrypt 接口\n#\n发送数据加密\nRSA 加密规则:若密钥位数是 key_size，单次加密串的最大长度为 (key_size)/8 - 11，目前位数 1024，一次加密长度可定为 100。\n将明文数据分成一个或数个最长 100 字节的小段进行加密，拼接分段加密数据即为最终的 Body 加密数据。\n#\n接收数据解密\nRSA 解密同样遵循分段规则，对于 1024 位密钥，每小段待解密数据长度为 128 字节。\n将密文数据分成一个或数个 128 字节长的小段进行解密，拼接分段解密数据即为最终的 Body 解密数据。\n#\nAES 加解密\n加密 key 由 InitConnect 协议返回\n默认使用的是 AES 的 ecb 加密模式。\nPython API 参考实现: ConnMng 类的 encrypt_conn_data / decrypt_conn_data 接口\n#\n发送数据加密\nAES 加密要求源数据长度必须是 16 的整数倍，故需补‘0’对齐后再加密，记录 mod_len 为源数据长度与 16 取模值。\n因加密前有可能对源数据作修改，故需在加密后的数据尾再增加一个 16 字节的填充数据块，其最后一个字节赋值 mod_len，其余字节赋值‘0’，将加密数据和额外的填充数据块拼接作为最终要发送协议的 body 数据。\n#\n接收数据解密\n协议 body 数据，先将最后一个字节取出，记为 mod_len，然后将 body 截掉尾部 16 字节填充数据块后再解密（与加密填充额外数据块逻辑对应）。\nmod_len 为 0 时，上述解密后的数据即为协议返回的 body 数据，否则需截掉尾部(16 - mod_len)长度的用于填充对齐的数据。\n\n← 通用定义\nOpenD 相关 →\n\n底层协议介绍\n协议请求流程\n协议设计\n心跳保活\n加密通信流程\nRSA 加解密\nAES 加解密"}, {"title": "Get Subscription Status | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/query-subscription.html", "html": " Futu API Doc v8.8\nProgramming Language\n简体中文\nProgramming Language\n简体中文\n\nIntroduction\n\nQuick Start\n\nOpenD\n\nQuote API\n\nOverview\nQuote Object\n\nReal-Time Data\n\nSubscription\n\nSubscribe and Unsubscribe\nGet Subscription Status\n\nPush and Callback\n\nGet\n\nBasic Data\n\nRelated Derivatives\n\nMarket Filter\n\nCustomization\n\nQuotation Definitions\n\nTrade API\n\nBasic API\n\nQ&A\n\n# Get Subscription Status\n\nquery_subscription(is_all_conn=True)\n\nDescription\n\nGet subscription information\n\nParameters\n\nParameter\tType\tDescription\nis_all_conn\tbool\tWhether to return the subscription status of all connections.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tdict\tIf ret == RET_OK, subscription information data is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nsubscription information data format as follows:\n\n  {\n      'total_used': subscription quota used by all connections,\n      'own_used': The subscription quota used by the current connection,\n      'remain': remaining subscription quota,\n      'sub_list': The stock list corresponding to each subscription type,\n      {\n          'Subscription type': A list of all subscribed stocks under this subscription type,\n          …\n      }\n  }\n\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nquote_ctx.subscribe(['HK.00700'], [SubType.QUOTE])\nret, data = quote_ctx.query_subscription()\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\nOutput\n{'total_used': 1, 'remain': 999, 'own_used': 1, 'sub_list': {'QUOTE': ['HK.00700']}}\n\n1\n\n\n← Subscribe and Unsubscribe Real-time Quote Callback →\n\nGet Subscription Status"}, {"title": "交易接口总览 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/overview.html", "html": " Futu API 文档 v8.8\n编程语言\n简体中文\n编程语言\n简体中文\n\n介绍\n\n快速上手\n\nOpenD\n\n行情接口\n\n交易接口\n\n交易接口总览\n交易对象\n\n账户\n\n资产持仓\n\n订单\n\n成交\n\n交易定义\n\n基础接口\n\nQ&A\n\n# 交易接口总览\n模块\t接口名\t功能简介\n账户\tGet Account List\t获取交易业务账户列表\nUnlock Trading\t解锁交易\n资产持仓\tGet Account Financial Information\t获取账户资金数据\nGet Maximum Tradable Quantity\t查询账户最大可买卖数量\nGet Positions List\t获取持仓列表\nGet Margin Trading Data\t获取融资融券数据\n订单\tPlace Order\t下单\nModify or Cancel Order\t改单撤单\nGet Order list\t查询未完成订单\nGet Order Fees\t查询订单费用\n\nGet Historical Order List\t查询历史订单\nOrder Callback\t订单回调\nTrade Data Callback\t订阅交易推送\n成交\tGet Today's Executed Trades\t查询当日成交\nGet Historical Executed Trades\t查询历史成交\nTrade Execution Callback\t成交回调\n\n← 行情定义 交易对象 →\n\n交易接口总览"}, {"title": "Transaction Objects | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/trade/base.html#8155", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nOverview\nTransaction Objects\n\nAccounts \n\nAssets and Positions \n\nOrders \n\nDeals \n\nTrading Definitions\n\nBasic API \n\nQ&A \n\n#\nTransaction Objects\n#\nCreate the connection\n\nOpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\n\nOpenFutureTradeContext(host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\n\nDescription\n\nAccording to the transaction variaties, select a correct account, and create its transaction object.\n\nTransaction Objects\tAccounts\nOpenSecTradeContext\tSecurities account \n\nOpenFutureTradeContext\tFutures account \n\nParameters\n\nParameter\tType\tDescription\nfilter_trdmarket\tTrdMarket\tFilter accounts according to the transaction market authority. \n\nhost\tstr\tThe IP listened by OpenD.\nport\tint\tThe port listened by OpenD.\nis_encrypt\tbool\tWhether to enable encryption. \n\nsecurity_firm\tSecurityFirm\tSpecified security firm\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\ntrd_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\nClose the connection\n\nclose()\n\nDescription\n\nClose the trading object. By default, the threads created inside the Futu API will prevent the process from exiting, and the process can exit normally only after all Contexts are closed. But through set_all_thread_daemon, all internal threads can be set as daemon threads. At this time, even if close of Context is not called, the process can exit normally.\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)\ntrd_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n\n\n← Overview\nGet the List of Trading Accounts →\n\nTransaction Objects"}, {"title": "OpenD 相关 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/qa/opend.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\nOpenD 相关\n行情相关\n交易相关\n其他\n#\nOpenD 相关\n#\nQ1：OpenD 因未完成“问卷评估及协议确认”自动退出\n\nA: 您需要进行相关问卷评估及协议确认，才可以使用 OpenD，请先 前往完成。\n\n#\nQ2：OpenD 因”程序自带数据不存在“退出\n\nA: 一般因权限问题导致自带数据拷贝失败，可以尝试将程序目录下 Appdata.dat 解压后的文件拷贝到程序数据目录下。\n\nwindows 程序数据目录:%appdata%/com.futunn.FutuOpenD/F3CNN\n非 windows 程序数据目录:~/.com.futunn.FutuOpenD/F3CNN\n#\nQ3：OpenD 服务启动失败\n\nA: 请检查：\n\n是否有其他程序占用所配置的端口；\n是否已经有配置了相同端口的 OpenD 在运行。\n#\nQ4：如何验证手机验证码？\n\nA: 在 OpenD 界面上或远程到 Telnet 端口，输入命令input_phone_verify_code -code=123456。\n\n提示\n\n123456 是收到的手机验证码\n-code=123456 前有空格\n#\nQ5：是否支持其他编程语言？\n\nA: OpenD 有对外提供基于 socket 的协议，目前我们提供并维护 Python，C++，Java，C# 和 JavaScript 接口，下载入口。\n\n如果上述语言仍不能满足您的需求，您可以自行对接 Protobuf 协议。\n\n#\nQ6：在同一设备多次验证设备锁\n\nA: 设备标识随机生成并存放于\n\nwindows: %appdata%/com.futunn.FutuOpenD/F3CNN/Device.dat 文件中。 非windows: ~/.com.futunn.FutuOpenD/F3CNN/Device.dat\n\n提示\n\n如果文件被删除或损坏，OpenD 会重新生成新设备标识，然后验证设备锁。\n另外镜像拷贝部署的用户需要注意，如果多台机器的 Device.dat 内容相同，也会导致这些机器多次验证设备锁。删除 Device.dat 文件即可解决。\n#\nQ7：OpenD 是否有提供 Docker 镜像？\n\nA: 目前没有提供。\n\n#\nQ8：一个账号可以登录多个 OpenD 吗？\n\nA: 一个账号可以在多台机器上登录 OpenD 或者其他客户终端，最多允许 10 个 OpenD 终端同时登录。同时有“行情互踢”的限制，只能有一个 OpenD 获得最高权限行情。例如：两个终端登录同一个账号，只能有一个港股 LV2 行情，另一个是港股 BMP 行情。\n\n#\nQ9：如何控制 OpenD 和其他客户端（桌面端和移动端）的行情权限？\n\nA: 应交易所的规定，多个终端同时在线会有“行情互踢”的限制，只能有一个终端获得最高权限行情。OpenD 命令行版本的启动参数中，内置了 auto_hold_quote_right 参数，用于灵活配置行情权限。当该参数选项开启时，OpenD 在行情权限被抢后，会自动抢回。如果 10 秒内再次被抢，则其他终端获得最高行情权限（OpenD 不会再抢）。\n\n#\nQ10：如何优先保证 OpenD 行情权限？\n\nA:\n\n将 OpenD 启动参数 auto_hold_quote_right 配置为 1；\n保证不要在移动端或桌面端富途牛牛上在 10 秒内连续两次抢最高权限（登录算一次，点击“重启行情”算第二次）。\n\n#\nQ11：如何优先保证移动端（或桌面端）的行情权限？\n\nA: OpenD 启动参数 auto_hold_quote_right 设置为 0，移动端或桌面端富途牛牛在 OpenD 之后登录即可。\n\n#\nQ12：使用可视化 OpenD 记住密码登录，长时间挂机后提示连接断开，需要重新登录？\n\nA: 使用可视化 OpenD，如果选择记住密码登录，用的是记录在本地的令牌。由于令牌有时间限制，当令牌过期后，如果出现网络波动或富途后台发布，就可能导致与后台断开连接后无法自动连接上的情况。因此，可视化 OpenD 如果希望长时间挂机，建议手动输入密码登录，由 OpenD 自动处理该情况。\n\n#\nQ13：使用遇到无法解决的问题时，如何请富途的研发人员协助排查？\n\nA:\n\n通过 QQ/微信，联系 OpenAPI 研发人员，方便即时沟通和传输文件。\n\n详述：发生错误的时间、OpenD 版本号、 API 版本号、脚本语言名、接口名或协议号、含详细入参和返回的短代码或截图。\n\n必要时，须提供 OpenD 日志，方便定位确认问题。交易类问题需要 info 日志级别，行情类问题需要 debug 日志级别。日志级别 log_level 可以在 OpenD.xml 中 配置 ，配置后需要重启 OpenD 方能生效，待问题复现后，将该段日志打包发给富途研发人员。\n\n提示\n\n日志路径如下：\nwindows：%appdata%/com.futunn.FutuOpenD/Log\n\n非 windows：~/.com.futunn.FutuOpenD/Log\n\n#\nQ14：脚本连接不上 OpenD\n\nA: 请先尝试检查：\n\n脚本连接的端口与 OpenD 配置的端口是否一致。\n由于 OpenD 连接上限为 128，是否有无用连接未关闭。\n检查监听地址是否正确，如果脚本和 OpenD 不在同一机器，OpenD 监听地址需要设置成 0.0.0.0 。\n#\nQ15：连接上一段时间后断开\n\nA: 如果是自己对接协议，检查下是否有定时发送心跳维持连接。\n\n#\nQ16：Linux 下通过 multiprocessing 模块以多进程方式运行 Python 脚本，连不上 OpenD？\n\nA: Linux/Mac 环境下以默认方式创建进程后，父进程中 py-futu-api 内部创建的线程将会在子进程中消失，导致程序内部状态错误。\n可以用 spawn 方式来启动进程：\n\nimport multiprocessing as mp\nmp.set_start_method('spawn')\np = mp.Process(target=func)\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\nQ17：如何在一台电脑同时登录两个 OpenD?\n\nA: 可视化 OpenD 不支持，命令行 OpenD 支持。\n\n解压从官网下载的文件，复制整个命令行 OpenD 文件夹（如 OpenD_5.2.1408_Windows）得到副本（此处以 Windows 为例，其他系统可采取相同操作）。\n\n分别打开两个命令行 OpenD 文件夹配置好两份 OpenD.xml 文件。\n\n第一份配置文件参数：api_port = 11111，login_account = 登录账号1，login_pwd = 登录密码1\n\n第二份配置文件参数：api_port = 11112，login_account = 登录账号2，login_pwd = 登录密码2\n\n配置完成后，分别打开两个 OpenD 程序运行。\n\n调用接口时，注意接口的参数port（OpenD 监听端口）与 OpenD.xml 文件中的参数api_port为对应关系\n例如：\nfrom futu import *\n\n# 向账号1登录的 OpenD 进行请求\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111, is_encrypt=False)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n# 向账号2登录的 OpenD 进行请求\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11112, is_encrypt=False)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n#\nQ18：行情权限被其他客户端踢掉，如何通过脚本执行抢权限的运维命令？\n\nA：\n\n在OpenD启动参数中，配置好 Telnet 地址和 Telnet 端口。  \n启动 OpenD（会同时启动 Telnet）。\n当发现行情权限被抢之后，您可以参考如下代码示例，通过 Telnet，向 OpenD 发送 request_highest_quote_right 命令。\nfrom telnetlib import Telnet\nwith Telnet('127.0.0.1', 22222) as tn:  # Telnet 地址为：127.0.0.1，Telnet 端口为：22222\n    tn.write(b'request_highest_quote_right\\r\\n')\n    reply = b''\n    while True:\n        msg = tn.read_until(b'\\r\\n', timeout=0.5)\n        reply += msg\n        if msg == b'':\n            break\n    print(reply.decode('gb2312'))\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\n#\nQ19：OpenD 自动升级失败\n\nA： 通过update命令执行 OpenD 自动更新失败，可能的原因：\n\n文件被其他进程占用：可以尝试关闭其他 OpenD 进程，或者重启系统后，再次执行 update 如果以上仍无法解决，可以通过官网自行下载更新。\n#\nQ20：ubuntu22无法启动可视化 OpenD？\n\nA： 在有些Linux发行版（例如Ubuntu 22.04）运行可视化OpenD时，可能会提示：dlopen(): error loading libfuse.so.2。 这是因为这些系统没有默认安装libfuse。通常可以手动安装来解决，例如对于Ubuntu22.04，可以在命令行运行：\n\nsudo apt update\nsudo apt install -y libfuse2\n\n \n\n        Copied!\n    \n1\n2\n\n\n安装成功后就可以正常运行可视化OpenD了。详细信息请参考：https://docs.appimage.org/user-guide/troubleshooting/fuse.html。\n\n← 底层协议介绍\n行情相关 →\n\nOpenD 相关\nQ1：OpenD 因未完成“问卷评估及协议确认”自动退出\nQ2：OpenD 因”程序自带数据不存在“退出\nQ3：OpenD 服务启动失败\nQ4：如何验证手机验证码？\nQ5：是否支持其他编程语言？\nQ6：在同一设备多次验证设备锁\nQ7：OpenD 是否有提供 Docker 镜像？\nQ8：一个账号可以登录多个 OpenD 吗？\nQ9：如何控制 OpenD 和其他客户端（桌面端和移动端）的行情权限？\nQ10：如何优先保证 OpenD 行情权限？\nQ11：如何优先保证移动端（或桌面端）的行情权限？\nQ12：使用可视化 OpenD 记住密码登录，长时间挂机后提示连接断开，需要重新登录？\nQ13：使用遇到无法解决的问题时，如何请富途的研发人员协助排查？\nQ14：脚本连接不上 OpenD\nQ15：连接上一段时间后断开\nQ16：Linux 下通过 multiprocessing 模块以多进程方式运行 Python 脚本，连不上 OpenD？\nQ17：如何在一台电脑同时登录两个 OpenD?\nQ18：行情权限被其他客户端踢掉，如何通过脚本执行抢权限的运维命令？\nQ19：OpenD 自动升级失败\nQ20：ubuntu22无法启动可视化 OpenD？"}, {"title": "Transaction related | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/qa/trade.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\nOpenD Related\nQuote related\nTransaction related\nOthers\n#\nTransaction related\n#\nQ1: How to use paper trading?\n\nA:\n\n#\nOverview\n\nPaper trading is a simulation that allows you to practice trading without the risk of using real money.\n\n#\nTrading time\n\nPaper trading only supports trading during regular trading hours, and does not support trading outside regular trading hours, US market pre-market and after-hours, HK market and China A-shares market Opening and Closing Auction. For details, please click Rules of paper trading.\n\n#\nCategories supported\n\nFor categories that OpenAPI supports by paper trading, please click here.\n\n#\nUnlock Trade\n\nDifferent from live trading, you do not need to unlock the account to place orders or modify or cancel orders when using paper trading.\n\n#\nOrders\nOrder Types: limit order and market order.\nModify Order Operation: Paper trading does not support enabling, disabling, and deleting the order, but supports modifying and canceling the order.\nDeals: Paper trading does not support deals related operations, including Get today's deals, Get historical deals, and Respond to the transaction push.\nValid Period: Paper trading only supports good for day order when setting valid period.\nShort Selling: Options and futures support short selling. Only US stocks support short selling.\n#\nPlatform\nMobile clients: Me — Paper Trading.\n\nDesktop clients: Left side tab Paper .\n\nWeb clients: Paper Trading Website.\n\nOpenAPI: When calling the interface, set the parameter trading environment to the simulated environment. Click How to use paper trading through OpenAPI for detail.\n\nTips\n\nThe four platforms shown above use the same paper trading accounts.\n#\nHow to use paper trading through OpenAPI?\n#\nCreate Connection\n\nFirstly, create the corresponding connection. When the underlyings are stocks or options, please use OpenSecTradeContext. When the underlyings are futures, please use OpenFutureTradeContext.\n\n#\nGet the List of Trading Accounts\n\nUse the interface Get the List of Trading Accounts to view trading accounts (including paper trading accounts and live trading accounts). Take Python as an example: When the returned parameter trd_env is SIMULATE, it means the corresponding account is a paper trading account.\n\nExample：Stocks and Options\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\n#trd_ctx = OpenFutureTradeContext(host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.get_acc_list()\nif ret == RET_OK:\n    print(data)\n    print(data['acc_id'][0])  # get the first account id\n    print(data['acc_id'].values.tolist())  # convert to list format\nelse:\n    print('get_acc_list error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n               acc_id   trd_env acc_type          card_num   security_firm  \\\n0  281756480572583411      REAL   MARGIN  ****************  FUTUSECURITIES   \n1             9053218  SIMULATE     CASH               N/A             N/A   \n2             9048221  SIMULATE   MARGIN               N/A             N/A   \n\n  sim_acc_type  trdmarket_auth  \n0          N/A  [HK, US, HKCC]  \n1        STOCK            [HK]  \n2       OPTION            [HK] \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n\nTips\n\nIn paper trading, stock accounts and options accounts are distinguished. Stock accounts can only trade stocks, and options accounts can only trade options; take Python as an example: sim_acc_type in the returned field is STOCK, which means stock account; OPTION means option account.\nExample: Futures\nfrom futu import *\n#trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\ntrd_ctx = OpenFutureTradeContext(host='127.0.0.1', port=11111, is_encrypt=None, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.get_acc_list()\nif ret == RET_OK:\n    print(data)\n    print(data['acc_id'][0])  # get the first account id\n    print(data['acc_id'].values.tolist())  # convert to list format\nelse:\n    print('get_acc_list error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    acc_id   trd_env acc_type card_num security_firm sim_acc_type  \\\n0  9497808  SIMULATE   MARGIN      N/A           N/A      FUTURES   \n1  9497809  SIMULATE   MARGIN      N/A           N/A      FUTURES   \n2  9497810  SIMULATE   MARGIN      N/A           N/A      FUTURES   \n3  9497811  SIMULATE   MARGIN      N/A           N/A      FUTURES   \n\n          trdmarket_auth  \n0  [FUTURES_SIMULATE_HK]  \n1  [FUTURES_SIMULATE_US]  \n2  [FUTURES_SIMULATE_SG]  \n3  [FUTURES_SIMULATE_JP]  \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n#\nPlace Orders\n\nWhen using the Interface Place Orders, set the trading environment to the simulated environment. Take Python as an example: trd_env = TrdEnv.SIMULATE.\n\nExample\nfrom futu import *\ntrd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.place_order(price=510.0, qty=100, code=\"HK.00700\", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE)\nif ret == RET_OK:\n    print(data)\nelse:\n    print('place_order error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\nOutput\n\tcode\tstock_name\ttrd_side\torder_type\torder_status\torder_id\tqty\tprice\tcreate_time\tupdated_time\tdealt_qty\tdealt_avg_price\tlast_err_msg\tremark\ttime_in_force\tfill_outside_rth\n0\tHK.00700\tTencent\tBUY\tNORMAL\tSUBMITTING\t4642000476506964749\t100.0\t510.0\t2021-10-09 11:34:54\t2021-10-09 11:34:54\t0.0\t0.0\t\t\tDAY\tN/A\n\n \n\n        Copied!\n    \n1\n2\n\n#\nModify or Cancel Orders\n\nWhen using the Interface Modify or Cancel Orders, set the trading environment to the simulated environment. Take Python as an example: trd_env = TrdEnv.SIMULATE.\n\nExample\nfrom futu import *\ntrd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\norder_id = \"4642000476506964749\"\nret, data = trd_ctx.modify_order(ModifyOrderOp.CANCEL, order_id, 0, 0, trd_env=TrdEnv.SIMULATE)\nif ret == RET_OK:\n    print(data)\nelse:\n    print('modify_order error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\nOutput\n    trd_env             order_id\n0  SIMULATE  4642000476506964749\n\n \n\n        Copied!\n    \n1\n2\n\n#\nGet Historical Orders\n\nWhen using the Interface Get Historical Orders, set the trading environment to the simulated environment. Take Python as an example: trd_env = TrdEnv.SIMULATE.\n\nExample\nfrom futu import *\ntrd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.history_order_list_query(trd_env=TrdEnv.SIMULATE)\nif ret == RET_OK:\n    print(data)\nelse:\n    print('history_order_list_query error: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\nOutput\n\tcode\tstock_name\ttrd_side\torder_type\torder_status\torder_id\tqty\tprice\tcreate_time\tupdated_time\tdealt_qty\tdealt_avg_price\tlast_err_msg\tremark\ttime_in_force\tfill_outside_rth\n0\tHK.00700\tTencent\tBUY\tABSOLUTE_LIMIT\tCANCELLED_ALL\t4642000476506964749\t100.0\t510.0\t2021-10-09 11:34:54\t2021-10-09 11:37:08\t0.0\t0.0\t\t\tDAY\tN/A\n\n \n\n        Copied!\n    \n1\n2\n\n#\nHow to reset the paper trading account?\n\nCurrently, OpenAPI does not support resetting the paper trading account. You can use the reset card on the mobile clients. After the reset, net assets would be restored to the initial value and the historical orders would be emptied.\n\n#\nSpecific process\n\nModify clients: Me — Paper Trading — My Icon — My Card — Reset Card \n\n#\nHow to reset the paper trading account?\n\nCurrently, OpenAPI does not support resetting the paper trading account. You can use the reset card on the mobile clients. After the reset, net assets would be restored to the initial value and the historical orders would be emptied.\n\n#\nSpecific process\n\nModify clients: Me — Paper Trading — My Icon — My Card — Reset Card \n\n#\nQ2: If support A-share trading or not?\n\nA: Paper trading supports A-share trading. However, real trade can only be used to trade some A-shares through A-shares connect. For details, please refer to List of HKCC.\n\n#\nQ3: Trading directions supported by each market\n\nA: Except for futures, other stocks only support the two trading directions of BUY and SELL. In the case of a short position, SELL is passed in, and the direction of the resulting order is short selling.\n\n#\nQ4: Order types supported in each market in real environment\n\nA:\nMarket\tVariety\tLimit Orders\tMarket Orders\tAt-auction Limit Orders\tAt-auction Market Orders\tAbsolute Limit Orders\tSpecial Limit Orders\tAON Special Limit Orders\tStop Orders\tStop Limit Orders\tMarket if Touched Orders\tLimit if Touched Orders\tTrailing Stop Orders\tTrailing Stop Limit Orders\nHK\tSecurities (including stocks, ETFs, warrants, CBBCs, Inline Warrants)\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\nOptions\t✓\tX\t-\t-\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\nFutures\t✓\t✓\t-\t✓\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\nUS\tSecurities (including stocks, ETFs)\t✓\t✓\t-\t-\t-\t-\t-\t✓\t✓\t✓\t✓\t✓\t✓\nOptions\t✓\t✓\t-\t-\t-\t-\t-\t✓\t✓\t✓\t✓\t✓\t✓\nFutures\t✓\t✓\t-\t-\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\nHKCC\tSecurities (including stocks, ETFs)\t✓\tX\t-\t-\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\nSingapore\tFutures\t✓\tX\t-\t-\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\nJapanese\tFutures\t✓\tX\t-\t-\t-\t-\t-\tX\t✓\tX\t✓\tX\t✓\n\n#\nQ5: Order operations supported by each market\n\nA:\n\nHK stocks support order modification, cancellation, entry into force, invalidation, and deletion\nUS stocks only support order modification and cancellation\nHKCC only supports cancellation of orders\nFutures supports order modification, cancellation, and deletion\n#\nQ6: How to use OpenD startup parameter future_trade_api_time_zone?\n\nA: Since the types of futures supported for trading account are distributed in multiple exchanges around the world, and the time zones of the exchanges are different, the time display of the futures trading API has become a problem. The future_trade_api_time_zone parameter has been added to the OpenD startup parameters, allowing futures traders in different regions of the world to flexibly specify the time zone. The default time zone is UTC+8. If you are more accustomed to Eastern Time, you only need to configure this parameter to UTC-5.\n\nTips\n\nThis parameter is only valid for futures trading interface objects. The time zone of HK stock trading, US stock trading, and HKCC trading interface objects is still displayed in accordance with the time zone of the exchange.\nThe interfaces affected by this parameter include: responding to order push callbacks, responding to transaction push callbacks, querying today's orders, querying historical orders, querying current transactions, querying historical transactions, and placing orders.\n#\nQ7: Can I see the order placed through OpenAPI, in APP?\n\nA：Yes, you can.\nAfter the order is successfully placed through OpenAPI, you can view today's orders, order status change in the trade page of APP, and you can also receive Order Notice in the APP.\n\n#\nQ8: Which trading targets support Off-Market order?\n\nA：All orders can only be filled during the market opening period.\nOrders made outside market hours and extended hours trading are queued and fulfilled either at or near the beginning of extended hours trading or at or near the market open, according to your instructions. These orders may be named as off market orders or overnight order. OpenAPI supports Off-Market order for a part of trading targets (APP supports much more trading targets' Off-Market order), as follows:\nMarket\tContracts\tPaper Trading\tLive Trading\nFUTU HK\tMoomoo Financial Inc.\tMoomoo Financial Singapore Pte. Ltd.\tFUTU AU\nHK Market\tSecurities\n(including stocks, ETFs, warrants, CBBCs, Inline Warrants)\t✓\t✓\t✓\t✓\t✓\nOptions\t✓\tX\tX\tX\tX\nFutures\tX\tX\tX\tX\tX\nUS Market\tSecurities (including stocks, ETFs)\t✓\tX\tX\tX\tX\nOptions\t✓\tX\tX\tX\tX\nFutures\tX\tX\tX\tX\tX\nA-share Market\tHKCC stocks\t✓\tX\tX\tX\tX\nNon-HKCC stocks\t✓\tX\tX\tX\tX\nSingapore Market\tFutures\tX\tX\tX\tX\tX\nJapanese Market\tFutures\tX\tX\tX\tX\tX\n\nTip\n\n✓：support Off-Market order\nX：do not support Off-Market order（or non-tradable）\n#\nQ9: For each order type，mandatory parameters of PlaceOrder as follows.\n\nA:\n\nParameters\tLimit Orders\tMarket Orders\tAt-auction Limit Orders\tAt-auction Market Orders\tAbsolute Limit Orders\tSpecial Limit Orders\tAON Special Limit Orders\tStop Orders\tStop Limit Orders\tMarket if Touched Orders\tLimit if Touched Orders\tTrailing Stop Orders\tTrailing Stop Limit Orders\nprice\t✓\t\t✓\t\t✓\t✓\t✓\t\t✓\t\t✓\t\t\nqty\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\ncode\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\ntrd_side\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\norder_type\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\ntrd_env\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\naux_price\t\t\t\t\t\t\t\t✓\t✓\t✓\t✓\t\t\ntrail_type\t\t\t\t\t\t\t\t\t\t\t\t✓\t✓\ntrail_value\t\t\t\t\t\t\t\t\t\t\t\t✓\t✓\ntrail_spread\t\t\t\t\t\t\t\t\t\t\t\t\t✓\n\nPython users should note that, place_order does not set a default value for price. For the five types of orders mentioned above, you still need to pass in price, which can be any value.\n\n#\nQ10: For each order type, when modifying the order, mandatory parameters of ModifyOrder as follows.\nParameters\tLimit Orders\tMarket Orders\tAt-auction Limit Orders\tAt-auction Market Orders\tAbsolute Limit Orders\tSpecial Limit Orders\tAON Special Limit Orders\tStop Orders\tStop Limit Orders\tMarket if Touched Orders\tLimit if Touched Orders\tTrailing Stop Orders\tTrailing Stop Limit Orders\nmodify_order_op\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\norder_id\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\nprice\t✓\t\t✓\t\t✓\t✓\t✓\t\t✓\t\t✓\t\t\nqty\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\ntrd_env\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\t✓\naux_price\t\t\t\t\t\t\t\t✓\t✓\t✓\t✓\t\t\ntrail_type\t\t\t\t\t\t\t\t\t\t\t\t✓\t✓\ntrail_value\t\t\t\t\t\t\t\t\t\t\t\t✓\t✓\ntrail_spread\t\t\t\t\t\t\t\t\t\t\t\t\t✓\n\nPython users should note that, modify_order does not set a default value for price. For the five types of orders mentioned above, you still need to pass in price, which can be any value.\n\n#\nQ11: The Trade API returns \"The current securities account has not yet agreed to the disclaimer.\"?\n\nA:\nClick the link below to confirm the agreement, and restart OpenD to use trading functions normally.\n\nSecurities Firm\tAggrement Link\nFUTU HK\tClick here\nMoomoo US\tClick here\nMoomoo SG\tClick here\nMoomoo AU\tClick here\n#\nQ12: Pattern Day Trader (PDT)\n#\nOverview\n\nWhen clients use Moomoo US accounts for intraday trading, they are subject to regulations by the US Financial Industry Regulatory Authority (FINRA). This is a regulatory requirement for US brokers and has nothing to do with the market to which a stock being traded belongs. The trading accounts of brokers in other countries or regions, such as Futu HK and Moomoo SG accounts, are not subject to this restriction. If a client conducts over 3 day trades in any 5 consecutive trading days, the client will be labelled as a pattern day trader (PDT).\nFor more details, refer to Help Center - Day Trade Rules.\n\n#\nDay Trading Flowchart\n\n#\nHow to turn off \"Pattern Day Trade Protection\", if I'm willing to be labelled as a PDT and do not want the quant trading program to be interrupted?\n\nA:\nTo prevent you from being unintentionally labelled as a PDT, the server will automatically intercept your 4th day trade in any 5 consecutive trading days. If you are willing to be labelled as a PDT and do not want the server to intercept your trade, you can take the following step:\nVia Command Line OpenD, modify the value of the startup parameter \"pdt_protection\" to \"0\".\n\n NOTE: You will not be able to establish new positions when you are labelled as a PDT and your account equity is below $25000.\n\n#\nHow to turn off the Day-Trading Call Warning?\n\nA:\nOnce you are labelled as a PDT, you need to pay attention to the day trading buying power (DTBP) of your account. When the DTBP is insufficient, you will receive a DTCall. The server will intercept your order that exceeds the DTBP. If you still want to place the order and do not want the server to intercept it, you can take the following step:\nVia Command Line OpenD, modify the value of the startup parameter \"dtcall_confirmation\" to \"0\".\n\n NOTE: If the market value of a newly established position exceeds your remaining DTBP and you close the position in the same day, you will receive a DTCall, which can only be met by depositing funds.\n\n#\nHow to check my DTBP?\n\nA:\nVia Get Account Funds Interface, you can request values related to day trading, such as Day Trades Left, Beginning DTBP, Remaining DTBP, etc.\n\n#\nQ13: How to track the status of orders?\n\nA:\nThe two interfaces can be uesed to track the status of orders, after which have been placed.\nTrading Enviroment\tInterfaces\nReal\tOrders Push Callback, Deals Push Callback\nSimulate\tOrders Push Callback\n\nNote: Non-python users need to Subscribe to Transaction Push before using the above two interfaces.\n\n#\nOrders Push Callback:\n\nFeedback changes of the entire order. The order push will be triggered when the following 8 fields change:\nOrder status, Order price, Order quantity, Deal quantity, Traget price, Trailing type, Trailing amount/ratio, Specify spread\n\nTherefore, when you place, modify, cancel, enable, or disable the order, or when an advanced order is triggered or an order has transaction changes, it will cause orders push. You just need to call the Orders Push Callback to listen for these messages.\n\n#\nDeals Push Callback:\n\nFeedback changes of a transaction. The order push will be triggered when the following field change:\nDeal status\n\nFot example: Suppose a limit order of 900 shares is divided into 3 transactions before it is completely filled, with each transaction being 200, 300 and 400 shares. \n\n#\nQ14: Why does the order interface return “Invalid price”?\n\nA:\nDifferent exchanges have different rules on order price spreads. If the price of a submitted order does not follow relevant rules, the order will be rejected.\n\n#\nRules on Price Spread\n#\nHong Kong Market\n\nRefer to the official HKEX Spread Table\n\n#\nChina A-Shares\n\nStock price spread: 0.01\n\n#\nUS Market\n\nStock Price Spreads:\nPrice\tSpread\nBelow $1\t$0.0001\n$1 or above\t$0.01\nOption Price Spreads:\nPrice\tSpread\n$0.10 - $3.00\t$0.01 or $0.05\n$3.00+\t$0.05 or $0.10\n\nFutures Price Spreads:\nDifferent contracts have different price spreads, which can be obtained via the Price change step of Get Futures Contract Information interface.\n\n#\nHow to ensure an order price meets spread rules?\n\nMethod 1: Valid order prices can be obtained via the Get Real-time Order Book interface, since the prices of orders on the order book must be valid.\n\nMethod 2: Auto-adjust an order price to a valid value via the Price adjustment range parameter in the Place Orders interface.\n\nHow it works:\n\nSuppose the Adjust Limit is set to 0.0015. A positive value means that OpenD will auto-adjust upward the price of a submitted order to a valid value within +0.15% of the original price.\n\nSuppose the current market price of Tencent Holdings is 359.600, so the spread is 0.200 according to the HKEX Spread Table. Let’s say an order priced at 359.678 is submitted. In this case, the nearest upward valid price is 359.800, which means the order price only needs to be adjusted by 0.034%. The adjustment satisfies the Adjust Limit, so the final price of the submitted order is 359.800.\n\nIf the actual adjustment exceeds the Adjust Limit, OpenD will fail to auto-adjust the price, and the order submission will still return the error prompt \"Invalid price\".\n\n#\nQ15: Why did it say \"Insufficient Buying Power\" when I place a market order with enough buying power in my account?\n\nA:\n\n#\nWhy it indicates insufficient buying power when you place a market order\nFor the sake of risk management, the system poses a higher buying power coefficient on market orders. With the same order parameters, a market order takes up more buying power than a limit order.\nDepending on different product types and market conditions, the risk management system dynamically adjusts the buying power coefficient of market orders. Therefore, when placing a market order, if you calculate the maximum buyable quantity using your maximum buying power, you are likely to get an inaccurate result.\n#\nHow to get the correct buyable quantity\n\nInstead of calculating it, you can obtain the correct buyable quantity through the [Query the Maximum Quantity that Can be Bought or Sold] (../trade/get-max-trd-qtys.html) API.\n\n#\nHow to buy as much as possible\n\nYou can place a limit order at the BBO, instead of a market order. In particular, the BBO means the best bid (or Bid 1) in the case of a sell order, or the best ask (or Ask 1) for a buy order.\n\n← Quote related\nOthers →\n\nTransaction related\nQ1: How to use paper trading?\nQ2: If support A-share trading or not?\nQ3: Trading directions supported by each market\nQ4: Order types supported in each market in real environment\nQ5: Order operations supported by each market\nQ6: How to use OpenD startup parameter futuretradeapitimezone?\nQ7: Can I see the order placed through OpenAPI, in APP?\nQ8: Which trading targets support Off-Market order?\nQ9: For each order type，mandatory parameters of PlaceOrder as follows.\nQ10: For each order type, when modifying the order, mandatory parameters of ModifyOrder as follows.\nQ11: The Trade API returns \"The current securities account has not yet agreed to the disclaimer.\"?\nQ12: Pattern Day Trader (PDT)\nQ13: How to track the status of orders?\nQ14: Why does the order interface return “Invalid price”?\nQ15: Why did it say \"Insufficient Buying Power\" when I place a market order with enough buying power in my account?"}, {"title": "Orders Push Callback | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/trade/update-order.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nOverview\nTransaction Objects\n\nAccounts \n\nAssets and Positions \n\nOrders \n\nPlace Orders\nModify or Cancel Orders\nGet open Orders\nGet Historical Orders\nOrders Push Callback\nGet Order Fee\nSubscribe to Transaction Push\n\nDeals \n\nTrading Definitions\n\nBasic API \n\nQ&A \n\n#\nOrders Push Callback\n\non_recv_rsp(self, rsp_pb)\n\nDescription\n\nIn response to orders push, asynchronously process the order status information pushed by OpenD. After receiving the order status information pushed by OpenD, this function is called.. You need to override on_recv_rsp in the derived class.\n\nParameters\n\nParameter\tType\tDescription\nrsp_pb\tTrd_UpdateOrder_pb2.Response\tThis parameter does not need to be processed in the derived class.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, order list is returned.\nstr\tIf ret != RET_OK, error description is returned.\nOrder list format as follows:\nField\tType\tDescription\ntrd_side\tTrdSide\tTrading direction.\norder_type\tOrderType\tOrder type.\norder_status\tOrderStatus\tOrder status.\norder_id\tstr\tOrder ID.\ncode\tstr\tSecurity code.\nstock_name\tstr\tSecurity name.\nqty\tfloat\tOrder quantity. \n\nprice\tfloat\tOrder price. \n\ncurrency\tCurrency\tTransaction currency.\ncreate_time\tstr\tCreate time. \n\nupdated_time\tstr\tLast update time. \n\ndealt_qty\tfloat\tDeal quantity \n\ndealt_avg_price\tfloat\tAverage deal price. \n\nlast_err_msg\tstr\tThe last error description. \n\nremark\tstr\tIdentification of remarks when placing an order. \n\ntime_in_force\tTimeInForce\tValid period.\nfill_outside_rth\tbool\tWhether pre-market and after-hours are needed. \n\naux_price\tfloat\tTraget price.\ntrail_type\tTrailType\tTrailing type.\ntrail_value\tfloat\tTrailing amount/ratio.\ntrail_spread\tfloat\tSpecify spread.\n\nExample\n\nfrom futu import *\nfrom time import sleep\nclass TradeOrderTest(TradeOrderHandlerBase):\n    \"\"\" order update push\"\"\"\n    def on_recv_rsp(self, rsp_pb):\n        ret, content = super(TradeOrderTest, self).on_recv_rsp(rsp_pb)\n        if ret == RET_OK:\n            print(\"* TradeOrderTest content={}\\n\".format(content))\n        return ret, content\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\ntrd_ctx.set_handler(TradeOrderTest())\nprint(trd_ctx.place_order(price=518.0, qty=100, code=\"HK.00700\", trd_side=TrdSide.SELL))\n\nsleep(15)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n\nOutput\n* TradeOrderTest content=  trd_env      code stock_name  dealt_avg_price  dealt_qty    qty           order_id order_type  price order_status          create_time         updated_time trd_side last_err_msg trd_market remark time_in_force fill_outside_rth aux_price trail_type trail_value trail_spread currency\n0    REAL  HK.00700       Tencent              0.0        0.0  100.0  72625263708670783     NORMAL  518.0   SUBMITTING  2021-11-04 11:26:27  2021-11-04 11:26:27      BUY                      HK                  DAY              N/A       N/A        N/A         N/A          N/A      HKD\n\n \n\n        Copied!\n    \n1\n2\n\n\n← Get Historical Orders\nGet Order Fee →\n\nOrders Push Callback"}, {"title": "Protocol Introduction | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/ftapi/protocol.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nBasic Functions\nGeneral Definitions\nProtocol Introduction\n\nQ&A \n\n#\nProtocol Introduction\n\nFutu API is an API SDK, encapsulated by Futu including mainstream programming languages (Python, Java, C #, C++, JavaScript) to make it easy for you to call and reduce the difficulty of trading strategy development.\nThis part mainly introduces the underlying protocol of communication between script and OpenD service, which is suitable for users who do not use the above five programming languages.\n\nTips\n\nIf you are using a programming language that is one of the five mainstream programming languages mentioned above, you can skip this part.\n#\nProtocol Request Process\nCreate a connection\nInitialize the connection\nRequest data or receive pushed data\nSend KeepAlive protocol periodically to keep connected\n\n#\nProtocol Design\n\nThe protocol data includes the protocol header and the protocol body. The protocol header is fixed, and the protocol body is determined according to the specific protocol.\n\n#\nProtocol Header\nstruct APIProtoHeader\n{\n    u8_t szHeaderFlag[2];\n    u32_t nProtoID;\n    u8_t nProtoFmtType;\n    u8_t nProtoVer;\n    u32_t nSerialNo;\n    u32_t nBodyLen;\n    u8_t arrBodySHA1[20];\n    u8_t arrReserved[8];\n};\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nField\tDescription\nszHeaderFlag\tPacket header start flag, fixed as \"FT\"\nnProtoID\tProtocol ID\nnProtoFmtType\tProtocol type, 0 for Protobuf, 1 for Json\nnProtoVer\tProtocol version, used for iterative compatibility, currently 0\nnSerialNo\tPacket serial number, used to correspond to the request packet and return packet, and it is required to be incremented\nnBodyLen\tBody length\narrBodySHA1\tSHA1 hash value of the original data of the packet body (after decryption)\narrReserved\tReserved 8-byte extension\n\nTips\n\nu8_t refer to 8-bit unsigned integer, u32_t refer to 32-bit unsigned integer\nOpenD internal processing uses Protobuf, so the protocol format recommends using Protobuf, to reduce Json conversion overhead.\nThe nProtoFmtType field specifies the data type of the package body, and the corresponding protocol type will be returned when the package is returned. The data type of the push protocol is specified by the OpenD configuration file\narrBodySHA1 is used to verify the consistency of the requested data before and after network transmission, and must be filled in correctly\nThe binary stream of the protocol header uses little-endian byte order, that is, generally there is no need to use ntohl and other related functions to convert the data\n#\nProtocol Body\n#\nPacket Body Structure of Protobuf Request\nmessage C2S\n{\n    required int64 req = 1;\n}\n\nmessage Request\n{\n    required C2S c2s = 1;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n#\nPacket Body Structure of Protobuf Response\nmessage S2C\n{\n    required int64 data = 1;\n}\n\nmessage Response\n{\n    required int32 retType = 1 [default = -400]; //RetType, result of return\n    optional string retMsg = 2;\n    optional int32 errCode = 3;\n    optional S2C s2c = 4;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\nField\tDescription\nc2s\tRequest parameter structure\nreq\tRequest parameters, actually defined according to the protocol\nretType\tRequest result\nretMsg\tThe reason for the failed request\nerrCode\tThe corresponding error code for failed request\ns2c\tResponse data structure, some protocols do not return data if there is no such field\ndata\tResponse data, actually defined according to the protocol\n\nTips\n\nThe package body format type request package is specified by nProtoFmtType field from protocol header, and the OpenD initiative push format is set in InitConnect.\nThe original protocol file format is defined in Protobuf format. If you need json format transmission, it is recommended to use the protobuf3 interface to directly convert to json.\nThe enumeration value field definition uses signed integer, and the comment indicates the corresponding enumeration. The enumeration is generally defined in Common.proto, Qot_Common.proto, Trd_Common.proto files.\nThe price, percentage and other data in the protocol are transmitted in floating point type. Direct use will cause accuracy problems. It needs to be rounded according to the accuracy (if not specified in the protocol, the default is 3 decimal places) before use.\n#\nHeartbeat Keep Alive\nsyntax = \"proto2\";\npackage KeepAlive;\noption java_package = \"com.futu.openapi.pb\";\noption go_package = \"github.com/futuopen/ftapi4go/pb/keepalive\";\n\nimport \"Common.proto\";\n\nmessage C2S\n{\n\trequired int64 time = 1; //Greenwich timestamp when the client sends the packet, in seconds\n}\n\nmessage S2C\n{\n\trequired int64 time = 1; //Greenwich timestamp when the server returned the packet, in seconds\n}\n\nmessage Request\n{\n\trequired C2S c2s = 1;\n}\n\nmessage Response\n{\n\trequired int32 retType = 1 [default = -400]; //RetType, return result\n\toptional string retMsg = 2;\n\toptional int32 errCode = 3;\n\t\n\toptional S2C s2c = 4;\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n\n\nIntroduction\n\nHeartbeat keep alive\n\nProtocol ID\n\n1004\n\nIntroduction\n\nAccording to the heartbeat keeping alive interval returned by the initialization protocol, send the heartbeeat keep alive protocol to OpenD.\n\n#\nEncrypted Communication Process\nIf OpenD is configured with encryption, InitConnect must use RSA public key encryption to initialize the connection protocol, and other subsequent protocols use the random key returned by InitConnect for AES encrypted communication.\nThe encryption process of OpenD draws on the SSL protocol. Considering that services and applications are generally deployed locally, we simplifies the related processes. OpenD shares the same RSA private key file with the access Client. Please save and distribute the private key file properly.\nGo to this URL to generate a random RSA key pair online. The key format must be PCKS#1, the key length can be 512, 1024, and do not set password. Copy and save the generated private key to a file, and then configure the path of the private key file to the rsa_private_key configuration item agreed upon in OpenD Configuration.\nIt is recommended that users who have real trade configure encryption to avoid leakage of account and trade information.\n\n#\nRSA Encryption and Decryption\nOpenD configuration Convention rsa_private_key is the path of the private key file\nOpenD shares the same private key file with the access client\nRSA encryption and decryption is only used for InitConnect requests, and is used to securely obtain symmetric encryption key of other request protocols\nThe RSA key of OpenD is 1024-bit, the filling method is PKCS1, public key encryption, private key decryption, public key can be generated by private key\n#\nSend Data Encryption\nRSA encryption rules: If the number of key bits is key_size, the maximum length of a single encryption string is (key_size)/8-11. The current number of bits is 1024, and the length of one encryption can be set to 100.\nDivide the plaintext data into one or several segments of up to 100 bytes for encryption, and the final encrypted data is spliced by all segmented encrypted data.\n#\nReceive Data Decryption\nRSA decryption also follows the segmentation rule. For a 1024-bit key, the length of each segment to be decrypted is 128-byte.\nDivide the ciphertext data into one or several segments of up to 128 bytes for decryption, and the final decrypted data is spliced by all segmented decrypted data.\n#\nAES Encryption and Decryption\nThe encryption key is returned by the InitConnect protocol\nThe ecb encryption mode of AES is used by default.\n#\nSend Data Encryption\nAES encryption requires that the length of the source data must be an integer multiple of 16, so it needs to be aligned with ‘0’ before encryption. Record mod_len for source data length and 16 module.\nBecause it is possible to modify the source data before encryption, it is necessary to add a 16-byte padding data block at the end of the encrypted data. The last byte is assigned mod_len, and the remaining bytes are assigned the value '0'. The encrypted data and additional populated data blocks are spliced as the body data to be sent in the end.\n#\nReceive Data Decryption\nFor protocol body data, first take out the last byte and record it as mod_len, then truncate the body to the 16-byte padding data block before decrypting it (corresponding to the encrypted padding extra data block logic).\nWhen mod_len is 0, the above decrypted data is the body data returned by the protocol, otherwise the tail (16-mod_len) length of the data used for filling and alignment needs to be truncated.\n\n← General Definitions\nOpenD Related →\n\nProtocol Introduction\nProtocol Request Process\nProtocol Design\nHeartbeat Keep Alive\nEncrypted Communication Process\nRSA Encryption and Decryption\nAES Encryption and Decryption"}, {"title": "概述 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/opend/opend-intro.html", "html": " Futu API 文档 v8.8\n编程语言 \nEnglish \n\n介绍 \n\n快速上手 \n\nOpenD \n\n概述\n命令行 OpenD\n运维命令\n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n概述\nOpenD 是 Futu API 的网关程序，运行于您的本地电脑或云端服务器，负责中转协议请求到富途服务器，并将处理后的数据返回。是运行 Futu API 程序必要的前提。\nOpenD 支持 Windows、MacOS、CentOS、Ubuntu 四个平台。\nOpenD 集成了登录功能。运行时，需要使用 平台账号（牛牛号）和 登录密码 进行登录。\nOpenD 登录成功后，会启动 Socket 服务以供 Futu API 连接和通信。\n#\n运行方式\n\nOpenD 目前提供两种安装运行方式，您可选择任一方式：\n\n可视化 OpenD：提供界面化应用程序，操作便捷，尤其适合入门用户，安装和运行请参考 可视化 OpenD。\n命令行 OpenD：提供命令行执行程序，需自行进行配置，适合对命令行熟悉或长时间在服务器上挂机的用户，安装和运行请参考 命令行 OpenD。\n#\n运行时操作\n\nOpenD 在运行过程中，可以查看用户额度、行情权限、链接状态、延迟统计，以及操作关闭 API 连接、重登录、退出登录等运维操作。\n具体方法可以查看下表：\n\n方式\t可视化 OpenD\t命令行 OpenD\n直接方式\t界面查看或操作\t命令行发送 运维命令\n间接方式\t通过 Telnet 发送 运维命令\t通过 Telnet 发送 运维命令\n\n← 交易策略搭建示例\n命令行 OpenD →\n\n概述\n运行方式\n运行时操作"}, {"title": "下单 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/place-order.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n订单 \n\n下单\n改单撤单\n查询未完成订单\n查询历史订单\n响应订单推送回调\n查询订单费用\n订阅交易推送\n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n下单\n\nplace_order(price, qty, code, trd_side, order_type=OrderType.NORMAL, adjust_limit=0, trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, remark=None, time_in_force=TimeInForce.DAY, fill_outside_rth=False, aux_price=None, trail_type=None, trail_value=None, trail_spread=None)\n\n介绍\n\n下单\n\n提示\n\nPython API 是同步的，但网络收发是异步的。当 place_order 对应的应答数据包与 响应成交推送回调 或 响应订单推送回调 间隔很短时，就可能出现 place_order 的数据包先返回，但回调函数先被调用的情况。例如：可能先调用了 响应订单推送回调，然后 place_order 这个接口才返回。\n\n参数\n\n参数\t类型\t说明\nprice\tfloat\t订单价格 \n\nqty\tfloat\t订单数量 \n\ncode\tstr\t标的代码 \n\ntrd_side\tTrdSide\t交易方向\norder_type\tOrderType\t订单类型\nadjust_limit\tfloat\t价格微调幅度 \n\ntrd_env\tTrdEnv\t交易环境\nacc_id\tint\t交易业务账户 ID \n\nacc_index\tint\t交易业务账户列表中的账户序号 \n\nremark\tstr\t备注 \n\ntime_in_force\tTimeInForce\t有效期限 \n\nfill_outside_rth\tbool\t是否允许盘前盘后 \n\naux_price\tfloat\t触发价格 \n\ntrail_type\tTrailType\t跟踪类型 \n\ntrail_value\tfloat\t跟踪金额/百分比 \n\ntrail_spread\tfloat\t指定价差 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回订单列表\nstr\t当 ret != RET_OK 时，返回错误描述\n订单列表格式如下：\n字段\t类型\t说明\ntrd_side\tTrdSide\t交易方向\norder_type\tOrderType\t订单类型\norder_status\tOrderStatus\t订单状态\norder_id\tstr\t订单号\ncode\tstr\t股票代码\nstock_name\tstr\t股票名称\nqty\tfloat\t订单数量 \n\nprice\tfloat\t订单价格 \n\ncreate_time\tstr\t创建时间 \n\nupdated_time\tstr\t最后更新时间 \n\ndealt_qty\tfloat\t成交数量 \n\ndealt_avg_price\tfloat\t成交均价 \n\nlast_err_msg\tstr\t最后的错误描述 \n\nremark\tstr\t下单时备注的标识 \n\ntime_in_force\tTimeInForce\t有效期限\nfill_outside_rth\tbool\t是否允许盘前盘后（用于港股盘前竞价与美股盘前盘后） \n\naux_price\tfloat\t触发价格\ntrail_type\tTrailType\t跟踪类型\ntrail_value\tfloat\t跟踪金额/百分比\ntrail_spread\tfloat\t指定价差\n\nExample\n\nfrom futu import *\npwd_unlock = '123456'\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.unlock_trade(pwd_unlock)  # 若使用真实账户下单，需先对账户进行解锁。此处示例为模拟账户下单，也可省略解锁。\nif ret == RET_OK:\n    ret, data = trd_ctx.place_order(price=510.0, qty=100, code=\"HK.00700\", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE)\n    if ret == RET_OK:\n        print(data)\n        print(data['order_id'][0])  # 获取下单的订单号\n        print(data['order_id'].values.tolist())  # 转为 list\n    else:\n        print('place_order error: ', data)\nelse:\n    print('unlock_trade failed: ', data)\ntrd_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n\nOutput\n\n       code stock_name trd_side order_type order_status           order_id    qty  price          create_time         updated_time  dealt_qty  dealt_avg_price last_err_msg remark time_in_force fill_outside_rth aux_price trail_type trail_value trail_spread currency\n0  HK.00700       腾讯控股      BUY     NORMAL   SUBMITTING  38196006548709500  100.0  420.0  2021-11-04 11:38:19  2021-11-04 11:38:19        0.0              0.0                               DAY              N/A       N/A        N/A         N/A          N/A      HKD\n38196006548709500\n['38196006548709500']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n接口限制\n\n每 30 秒内最多请求 15 次下单接口，且连续两次请求的间隔不可小于 0.02 秒。\n真实账户调用下单接口前，需要先进行 解锁；模拟账户无需解锁。\n\n提示\n\n各订单类型对应的必传参数：点击这里 了解更多\n对于 可做空标的，暂不支持锁仓功能，故无法同时持有相同产品的多头头寸和空头头寸。\n如果希望对 可做空标的 进行 平仓 操作，需要自行判断持仓头寸的方向，然后提交一笔反向的相同数量的订单完成平仓操作。\n如果希望对 可做空标的 进行 反手 操作，需要两步：1. 先判断持仓头寸的方向，并提交一笔反向的相同数量的订单完成平仓操作；2. 提交一笔反向的订单，完成反向订单的提交。\n举例：A 当前持有 1 手 HK.HSI2012 期货合约的多单，如果希望反手，必须先 卖出 1 手 HK.HSI2012 完成平仓，再卖出 1 手 HK.HSI2012 完成空单的建立。\n\n← 获取融资融券数据\n改单撤单 →\n\n下单"}, {"title": "Get Real-time Tick-by-Tick | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-ticker.html", "html": " Futu API Doc v8.8\nProgramming Language\n简体中文\nProgramming Language\n简体中文\n\nIntroduction\n\nQuick Start\n\nOpenD\n\nQuote API\n\nOverview\nQuote Object\n\nReal-Time Data\n\nSubscription\n\nPush and Callback\n\nGet\n\nGet Market Snapshot\nGet Real-time Quote\nGet Real-time Order Book\nGet Real-time Candlestick\nGet Real-time Time Frame Data\nGet Real-time Tick-by-Tick\nGet Real-time Broker Queue\n\nBasic Data\n\nRelated Derivatives\n\nMarket Filter\n\nCustomization\n\nQuotation Definitions\n\nTrade API\n\nBasic API\n\nQ&A\n\n# Get Real-time Tick-by-Tick\n\nget_rt_ticker(code, num=500)\n\nDescription\n\nTo get real-time tick-by-tick of subscribed stocks. (Require real-time data subscription.)\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code.\nnum\tint\tNumber of recent tick-by-tick.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, tick-by-tick data is returned.\nstr\tIf ret != RET_OK, error description is returned.\nTick-by-tick data format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\nsequence\tint\tSequence number.\ntime\tstr\tTransaction time.\n\nprice\tfloat\tTransaction price.\nvolume\tint\tVolume.\n\nturnover\tfloat\tTransaction amount.\nticker_direction\tTickerDirect\tTick-By-Tick direction.\ntype\tTickerType\tTick-By-Tick type.\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.TICKER], subscribe_push=False)\n# First subscribe to each type. After the subscription is successful, OpenD will continue to receive pushes from the server, False means that there is no need to push to the script temporarily\nif ret_sub == RET_OK: # Subscription successful\n     ret, data = quote_ctx.get_rt_ticker('HK.00700', 2) # Get the last 2 transactions of Hong Kong stocks 00700\n     if ret == RET_OK:\n         print(data)\n         print(data['turnover'][0]) # Take the first transaction amount\n         print(data['turnover'].values.tolist()) # Convert to list\n     else:\n         print('error:', data)\nelse:\n     print('subscription failed', err_message)\nquote_ctx.close() # Close the current link, OpenD will automatically cancel the corresponding type of subscription for the corresponding stock after 1 minute\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\n   code     name                 time  price   volume     turnover ticker_direction             sequence        type\n0  HK.00700  TENCENT  2023-07-19 15:59:58  332.4      100      33240.0             SELL  7257436441708356760  AUTO_MATCH\n1  HK.00700  TENCENT  2023-07-19 16:08:12  333.0  1667000  555111000.0          NEUTRAL  7257438563422200985     AUCTION\n33240.0\n[33240.0, 555111000.0]\n\n1\n2\n3\n4\n5\n\n\nInterface Limitations\n\nYou can get up to the latest 1000 tick-by-tick data, more historical tick-by-tick data is not yet available\nUnder the authority of LV1 HK futures and options market, tick-by-tick data is not available\n\nTips\n\nThis API provides the function of obtaining real-time data at one time. If you need to obtain pushed data continuously, please refer to the Real-time Tick-By-Tick Callback API.\nFor the difference between get real-time data and real-time data callback, please refer to How to Get Real-time Quotes Through Subscription Interface.\n\n← Get Real-time Time Frame Data Get Real-time Broker Queue →\n\nGet Real-time Tick-by-Tick"}, {"title": "Get Option Chain | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-option-chain.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nGet Option Expiration Date\nGet Option Chain\nGet Filtered Warrant\nGet Related Data of a Specific Security\nGet Futures Contract Information\n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Option Chain\n\nget_option_chain(code, index_option_type=IndexOptionType.NORMAL, start=None, end=None, option_type=OptionType.ALL, option_cond_type=OptionCondType.ALL, data_filter=None)\n\nDescription\n\nQuery the option chain from an underlying stock. This interface only returns the static information of the option chain. If you need to obtain dynamic information such as quotation or trading, please use the security code returned by this interface to subscribe the required security.\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tCode of underlying stock.\nindex_option_type\tIndexOptionType\tIndex option type. \n\nstart\tstr\tStart date, for expiration date. \n\nend\tstr\tEnd date (including this day), for expiration date. \n\noption_type\tOptionType\tOption type for call/put. \n\noption_cond_type\tOptionCondType\tOption type for in/out of the money. \n\ndata_filter\tOptionDataFilter\tData filter condition. \n\nThe combination of start and end is as follows:\n\nStart type\tEnd type\tDescription\nstr\tstr\tstart and end are the specified dates respectively.\nNone\tstr\tstart is 30 days before end.\nstr\tNone\tend is 30 days after start.\nNone\tNone\tstart is the current date, end is 30 days later.\n\nOptionDataFilter fields are as follows\n\nField\tType\tDescription\nimplied_volatility_min\tfloat\tMin value of implied volatility. \n\nimplied_volatility_max\tfloat\tMax value of implied volatility. \n\ndelta_min\tfloat\tMin value of Greek value Delta. \n\ndelta_max\tfloat\tMax value of Greek value Delta. \n\ngamma_min\tfloat\tMin value of Greek value Gamma. \n\ngamma_max\tfloat\tMax value of Greek value Gamma. \n\nvega_min\tfloat\tMin value of Greek value Vega. \n\nvega_max\tfloat\tMax value of Greek value Vega. \n\ntheta_min\tfloat\tMin value of Greek value Theta. \n\ntheta_max\tfloat\tMax value of Greek value Theta. \n\nrho_min\tfloat\tMin value of Greek value Rho. \n\nrho_max\tfloat\tMax value of Greek value Rho. \n\nnet_open_interest_min\tfloat\tMin value of net open contract number. \n\nnet_open_interest_max\tfloat\tMax value of net open contract number. \n\nopen_interest_min\tfloat\tMin value of open contract number. \n\nopen_interest_max\tfloat\tMax value of open contract number. \n\nvol_min\tfloat\tMin value of Volume. \n\nvol_max\tfloat\tMax value of Volume. \n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, option chain data is returned.\nstr\tIf ret != RET_OK, error description is returned.\nOption chain data format as follows:\nField\tType\tDescription\ncode\tstr\tSecurity code.\nname\tstr\tSecurity name.\nlot_size\tint\tNumber of shares per lot, number of shares per contract for options. \n\nstock_type\tSecurityType\tStock type.\noption_type\tOptionType\tOption type.\nstock_owner\tstr\tUnderlying stock.\nstrike_time\tstr\tExercise date. \n\nstrike_price\tfloat\tStrike price.\nsuspension\tbool\tWhether is suspended. \n\nstock_id\tint\tStock ID.\nindex_option_type\tIndexOptionType\tIndex option type.\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret1, data1 = quote_ctx.get_option_expiration_date(code='HK.00700')\n\nfilter1 = OptionDataFilter()\nfilter1.delta_min = 0\nfilter1.delta_max = 0.1\n\nif ret1 == RET_OK:\n    expiration_date_list = data1['strike_time'].values.tolist()\n    for date in expiration_date_list:\n        ret2, data2 = quote_ctx.get_option_chain(code='HK.00700', start=date, end=date, data_filter=filter1)\n        if ret2 == RET_OK:\n            print(data2)\n            print(data2['code'][0])  # Take the first stock code\n            print(data2['code'].values.tolist())  # Convert to list\n        else:\n            print('error:', data2)\n        time.sleep(3)\nelse:\n    print('error:', data1)\nquote_ctx.close()  # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n\nOutput\n                     code                 name  lot_size stock_type option_type stock_owner strike_time  strike_price  suspension  stock_id index_option_type\n0     HK.TCH210429C350000   腾讯 210429 350.00 购       100       DRVT        CALL    HK.00700  2021-04-29         350.0       False  80235167               N/A\n1     HK.TCH210429P350000   腾讯 210429 350.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         350.0       False  80235247               N/A\n2     HK.TCH210429C360000   腾讯 210429 360.00 购       100       DRVT        CALL    HK.00700  2021-04-29         360.0       False  80235163               N/A\n3     HK.TCH210429P360000   腾讯 210429 360.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         360.0       False  80235246               N/A\n4     HK.TCH210429C370000   腾讯 210429 370.00 购       100       DRVT        CALL    HK.00700  2021-04-29         370.0       False  80235165               N/A\n5     HK.TCH210429P370000   腾讯 210429 370.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         370.0       False  80235248               N/A\nHK.TCH210429C350000\n['HK.TCH210429C350000', 'HK.TCH210429P350000', 'HK.TCH210429C360000', 'HK.TCH210429P360000', 'HK.TCH210429C370000', 'HK.TCH210429P370000']\n...\n                   code                name  lot_size stock_type option_type stock_owner strike_time  strike_price  suspension  stock_id index_option_type\n0   HK.TCH220330C490000  腾讯 220330 490.00 购       100       DRVT        CALL    HK.00700  2022-03-30         490.0       False  80235143               N/A\n1   HK.TCH220330P490000  腾讯 220330 490.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         490.0       False  80235193               N/A\n2   HK.TCH220330C500000  腾讯 220330 500.00 购       100       DRVT        CALL    HK.00700  2022-03-30         500.0       False  80233887               N/A\n3   HK.TCH220330P500000  腾讯 220330 500.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         500.0       False  80233912               N/A\n4   HK.TCH220330C510000  腾讯 220330 510.00 购       100       DRVT        CALL    HK.00700  2022-03-30         510.0       False  80233747               N/A\n5   HK.TCH220330P510000  腾讯 220330 510.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         510.0       False  80233766               N/A\nHK.TCH220330C490000\n['HK.TCH220330C490000', 'HK.TCH220330P490000', 'HK.TCH220330C500000', 'HK.TCH220330P500000', 'HK.TCH220330C510000', 'HK.TCH220330P510000']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n\n\nInterface Limitations\n\nA maximum of 10 requests per 30 seconds\nThe upper limit of the incoming time span is 30 days\n\nTips\n\nThis interface does not support the query of expired option chains, please enter today or future date to the End date parameter.\nOpen interest (OI) is updated daily and the specific timing depends on the exchange.\nFor U.S. stock options, the data is updated during the PRE_MARKET session.\nFor Hong Kong stock options, the data is updated after the Regular Trading Hours.\n\n← Get Option Expiration Date\nGet Filtered Warrant →\n\nGet Option Chain"}, {"title": "Get Related Data of a Specific Security | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-referencestock-list.html", "html": " Futu API Doc v8.8\nProgramming Language \n简体中文 \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nGet Option Expiration Date\nGet Option Chain\nGet Filtered Warrant\nGet Related Data of a Specific Security\nGet Futures Contract Information\n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Related Data of a Specific Security\n\nget_referencestock_list(code, reference_type)\n\nDescription\n\nGet related data of securities, such as: obtaining warrants related to underlying stocks, obtaining contracts related to futures\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code.\nreference_type\tSecurityReferenceType\tRelated data type to be obtained.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, related data of security is returned.\nstr\tIf ret != RET_OK, error description is returned.\nRelated data of security fotmat as follows:\nField\tType\tDescription\ncode\tstr\tSecurity code.\nlot_size\tint\tThe number of shares per lot, contract multiplier for futures.\nstock_type\tSecurityType\tSecurity type.\nstock_name\tstr\tSecurity name.\nlist_time\tstr\tTime of listing. \n\nwrt_valid\tbool\tWhether it is a warrant. \n\nwrt_type\tWrtType\tWarrant type.\nwrt_code\tstr\tThe underlying stock.\nfuture_valid\tbool\tWhether it is a future. \n\nfuture_main_contract\tbool\tWhether the future main contract. \n\nfuture_last_trade_time\tstr\tLast trading time. \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\n# Get warrants related to the underlying stock\nret, data = quote_ctx.get_referencestock_list('HK.00700', SecurityReferenceType.WARRANT)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0]) # Take the first stock code\n    print(data['code'].values.tolist()) # Convert to list\nelse:\n    print('error:', data)\nprint('******************************************')\n# Port related contracts\nret, data = quote_ctx.get_referencestock_list('HK.A50main', SecurityReferenceType.FUTURE)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0]) # Take the first stock code\n    print(data['code'].values.tolist()) # Convert to list\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\nOutput\n        code  lot_size stock_type stock_name   list_time  wrt_valid wrt_type  wrt_code  future_valid  future_main_contract  future_last_trade_time\n0     HK.24719      1000    WARRANT     TENGXUNDONGYAJIUSIGUA  2018-07-20       True      PUT  HK.00700         False                   NaN                     NaN\n...        ...       ...        ...        ...         ...        ...      ...       ...           ...                   ...                     ...\n1617  HK.63402     10000    WARRANT     GS#TENCTRC2108Y  2020-11-26       True     BULL  HK.00700         False                   NaN                     NaN\n\n[1618 rows x 11 columns]\nHK.24719\n['HK.24719', 'HK.27886', 'HK.28621', 'HK.14339', 'HK.27952', 'HK.18693', 'HK.20306', 'HK.53635', 'HK.47269', 'HK.27227', \n...        ...       ...        ...        ...         ...        ...      ...       ... \n'HK.63402']\n******************************************\n        code  lot_size stock_type         stock_name list_time  wrt_valid  wrt_type  wrt_code  future_valid  future_main_contract future_last_trade_time\n0  HK.A50main      5000     FUTURE      A50 Future Main(DEC0)                False       NaN       NaN          True                  True                        \n..         ...       ...        ...                ...       ...        ...       ...       ...           ...                   ...                    ...\n5  HK.A502106      5000     FUTURE      A50 JUN1                False       NaN       NaN          True                 False             2021-06-29\n\n[6 rows x 11 columns]\nHK.A50main\n['HK.A50main', 'HK.A502011', 'HK.A502012', 'HK.A502101', 'HK.A502103', 'HK.A502106']\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n\n\nInterface Limitations\n\nA maximum of 10 requests per 30 seconds\nWhen obtaining warrants related to the underlying stock, it is not subject to the above frequency restriction\n\n← Get Filtered Warrant\nGet Futures Contract Information →\n\nGet Related Data of a Specific Security"}, {"title": "获取期货合约资料 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-future-info.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n相关衍生品 \n\n获取期权链到期日\n获取期权链\n筛选窝轮\n获取窝轮和期货列表\n获取期货合约资料\n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取期货合约资料\n\nget_future_info(code_list)\n\n介绍\n\n获取期货合约资料\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t股票代码列表 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回期货合约资料数据\nstr\t当 ret != RET_OK，返回错误描述\n期货合约资料数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nowner\tstr\t标的\nexchange\tstr\t交易所\ntype\tstr\t合约类型\nsize\tfloat\t合约规模\nsize_unit\tstr\t合约规模单位\nprice_currency\tstr\t报价货币\nprice_unit\tstr\t报价单位\nmin_change\tfloat\t最小变动\nmin_change_unit\tstr\t最小变动的单位\ntrade_time\tstr\t交易时间\ntime_zone\tstr\t时区\nlast_trade_time\tstr\t最后交易时间 \n\nexchange_format_url\tstr\t交易所规格 url\norigin_code\tstr\t实际合约代码\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_future_info([\"HK.MPImain\", \"HK.HAImain\"])\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['code'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    code      name       owner exchange  type     size size_unit price_currency price_unit  min_change min_change_unit                        trade_time time_zone last_trade_time                                exchange_format_url           origin_code\n0  HK.MPImain   內房期货主连  恒生中国内地地产指数      港交所  股指期货     50.0    指数点×港元             港元        指数点        0.50             指数点  (09:15 - 12:00), (13:00 - 16:30)       CCT                  https://sc.hkex.com.hk/TuniS/www.hkex.com.hk/P...           HK.MPI2112\n1  HK.HAImain   海通证券期货主连    HK.06837      港交所  股票期货  10000.0         股             港元      每股/港元        0.01              港元  (09:30 - 12:00), (13:00 - 16:00)       CCT                  https://sc.hkex.com.hk/TuniS/www.hkex.com.hk/P...           HK.HAI2112\nHK.MPImain\n['HK.MPImain', 'HK.HAImain']\n\n1\n2\n3\n4\n5\n\n\n接口限制\n\n每 30 秒内最多请求 30 次获取期货合约资料接口\n每次请求的代码列表中，期货个数上限为 200 个\n\n← 获取窝轮和期货列表\n条件选股 →\n\n获取期货合约资料"}, {"title": "Quote related | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/qa/quote.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\nOpenD Related\nQuote related\nTransaction related\nOthers\n#\nQuote related\n#\nQ1: Subscription failed\n\nA: When the subscription interface returns an error, there are two common situations.\n\nInsufficient subscription quota\n\nPlease refer to Subscription Quota & Historical Candlestick Quota for the subscription quota rules.\n\nInsufficient quota right\n\nThe quota right that supports subscription are shown in the following table:\nMarket\tContracts\tQuota Right for Subscription\nHK Market\tSecrities\tLV2, SF\nOptions\tLV1, LV2\nFutures\tLV1, LV2\nUS Market\tSecrities\tLV1, LV2\nOptions\tLV1\nFutures\tLV2\nA-Share Market\tSecrities\tLV1\n\n\n\nPlease refer to Quote Right for the access method.\n\nNote: If your account has the above-mantioned quota rights, but the subscription still fails. The possible reason is that the quota right has been kicked out by other terminals.\n\n#\nQ2: Unsubscribe failed\n\nA: You can unsubscribe after you subscribe for at least one minute.\n\n#\nQ3: The unsubscribe was successful but the quota was not released\n\nA: The quota is released after all connections are unsubscribed to the market.\n\nFor example: Connection A and Connection B are both subscribing to HK.00700's listing data. After Connection A is unsubscribed, because Connection B is still calling HK.00700's listing data, the OpenD quota will not be released until all connections have been unsubscribed the listing data of HK.00700.\n\n#\nQ4: Will the quota be released if the script connection is closed if the subscription is less than one minute?\n\nA: No. After the connection is closed, the target type whose subscription duration is less than one minute will be automatically unsubscribed after reaching one minute, and the corresponding subscription quota will be released.\n\n#\nQ5: What is the specific restriction logic for requesting frequency restriction?\n\nA: At most n times within 30 seconds, it means that the interval between the first request and the n+1th request must be greater than 30 seconds.\n\n#\nQ6: What is the reason why self-selected stocks cannot be added?\n\nA: Please check whether the upper limit is exceeded first, or delete part of the self-selected stocks.\n\n#\nQ7: Why is the US stock quotation on the OpenAPI side different from the national comprehensive quotation on the client side?\n\nA: Since US stock trade are scattered on many exchanges, Futu provides two basic quotations for US stocks, one is Nasdaq Basic (quotes on the Nasdaq exchange), and the other is a comprehensive quotation for the United States (all 13 exchanges in the United States). However, OpenAPI's US stock quote currently only support Nasdaq Basic purchases through quotation card, and do not support comprehensive US quote.\nTherefore, if you purchase both the US comprehensive quotation card on the APP side and the Nasdaq Basic quotation card that is only used for OpenAPI, there may indeed be a difference in the quotation between the APP side and the OpenAPI side.\n\n#\nQ8: Where can I buy OpenAPI quotation cards?\n\nA:\n\nHK market\nHK stocks LV2 advanced market (only non-Chinese mainland IP)\nHK stock options futures LV2 advanced market (only non-Mainland China IP)\nHK stocks LV2 + option futures LV2 market (non-Mainland China IP only)\nHK Stocks Advanced Full Market Quotes (SF Quotes)\nUS market\nUS stocks Nasdaq Basic\nUS stocks Nasdaq Totalview\nUS options OPRA real-time data\n#\nQ9: Why sometimes, the response of the get interface to obtain real-time data is slow?\n\nA: Because the get interface for real-time data needs to be subscribed first, and it depends on the push to OpenD from the background. If the user uses the get interface to request immediately after subscribing, OpenD may not receive the background push yet. In order to prevent this from happening, the get interface has built-in waiting logic, and the push will be returned to the script immediately after receiving the push within 3 seconds, and empty data will be returned to the script if the background push is not received for more than 3 seconds. The get interfaces include: get_rt_ticker, get_rt_data, get_cur_kline, get_order_book, get_broker_queue, get_stock_quote. Therefore, when you find that the response of the get interface for obtaining real-time data is relatively slow, you can first check whether it is the cause of no trade data.\n\n#\nQ10: What kind of data can be obtained after purchasing the OpenAPI Nasdaq Basic quotation card?\n\nA: After the Nasdaq Basic quotation card purchase is activated, the categories that can be obtained include Nasdaq, NYSE, NYSE MKT stocks listed on the exchange (including US stocks and ETF, excluding US stock futures and US stock options). Supported data interfaces include: snapshots, historical candlestick, real-time ticker subscriptions, real-time one-stage subscriptions, real-time candlestick subscriptions, real-time quotation subscriptions, real-time Time Frame subscriptions, and price reminders.\n\n#\nQ11: How many levels does each market category support?\n\nA:\n\nQuotes category\tBMP\tLV1\tLV2\tSF\nHK stocks (including Stock, Warrants, bulls and bears, and inbound securities)\t0\t/\t10\tfull stock + thousand details\nHK stock options futures\t0\t1\t10\t/\nUS stocks (including ETF)\t/\t1\tUp to 40 order details\t/\nUS stock options\t/\t1\t/\t/\nUS futures\t/\t/\tUp to 40 order details\t/\nA-shares\t/\t5\t/\t/\n#\nQ12：Why does OpenD still have no quote right after I purchase and activate the quotation card?\n\nA:\n\nThe quote right of OpenAPI is not exactly the same as that of APP. Some quotation cards are only applicable to the APP side. Please confirm that the card you purchased is applicable to OpenD first. We have listed all the quotation cards applicable to OpenAPI in the section Authorities and Limitations. Please click here.\n\nAfter activating the quotation card, your quote right will be effective immediately. Please check after restarting OpenD.\n\n#\nQ13：How to Get Real-time Quotes Through Subscription Interface?\n\nThe First Stop：Subscription\n\nPass the code of underlying security and data type to Subscription Interface to finish subscribing.\n\nSubscription interface supports requesting real-time quote, real-time order book, real-time tick-by-tick, real-time Time Frame, real-time candlesticks and real-time broker queue. After a successful subscription, OpenD will continuously receive real-time data from Futo Server.\n\nAttention: The subscription quota is allocated by your total capital, trading amount and trading volume. Please refer to Subscription Quota & Historical Candlestick Quota for details. If your subscription quota is not enough, please check if there is any useless subscriptions in the quota. Unsubscribe to release the subscription quota in time.\n\nThe Second Step：Obtain Data\n\nWe provide two methods to obtain subscribed data from OpenD:\n\nMethos 1: Real-time data Callback\nSet corresponding callback functions to process the pushed data asyncronously.\n\nAfter the callback function is set, OpenD will immediately push the received real-time data to the callback function of the script for processing.\n\nIf the underlying security is very active, you may get a large amount of pushed data with high frequency. If you want to slower the push frequency of OpenD, we recommand you to config push frequency(qot_push_frequency) in OpenD Startup Parameter\n\nThe interfaces involved in mode 1 include: Real-time Quote Callback, Real-time Order Book Callback, Real-time Candlestick Callback, Real-time Time Frame Callback, Real-time Tick-by-Tick Callback, Real-time Broker Queue Callback.\n\nMethod 2: Get Real-time Data\nThrough the access to real-time data interface, you can use scripts to get the latest data received by OpenD. This approach is more flexible, and scripts do not need to deal with massive pushes. As long as OpenD continues to receive push from servers, the script can obtain the data on demand.\n\nAs the data is taken from the pushed data received by OpenD, there is no frequency limit for this type of interface.\n\nThe interfaces involved in mode 1 include: Get Real-time Quote of Securities, Get Real-time Order Book, Get Real-time Candlestick, Get Real-time Time Frame Data, Get Real-time Tick-by-Tick, Get Real-time Broker Queue.\n\n#\nQ14：What time period corresponds to each market state？\n\nA:\nMarket\tSecurity Type\tMarket State\tTime Period (Local time)\nHK Market\tSecurities (including stocks, ETFs, warrants, CBBCs, Inline Warrants)\t* NONE: No trading\tCST 08:55 - 09:00\n* ACTION: Pre-market trading\tCST 09:00 - 09:20\n* WAITING_OPEN: Waiting for opening\tCST 09:20 - 09:30\n* MORNING: Morning session\tCST 09:30 - 12:00\n* REST: Lunch break\tCST 12:00 - 13:00\n* AFTERNOON: Afternoon session\tCST 13:00 - 16:00\n* HK_CAS: After-hours bidding for HK stocks (The market state corresponding to the addition of CAS mechanism to the Hong Kong stock market)\tCST 16:00 - 16:08\n* CLOSED: Market closed\tCST 16:08 - 08:55（T+1）\nOptions, Futures (Day Market only)\t* NONE: Waiting for options opening\tCST 08:55 - 09:30\n* MORNING: Morning session\tCST 09:30 - 12:00\n* REST: Lunch break\tCST 12:00 - 13:00\n* AFTERNOON: Afternoon session\tCST 13:00 - 16:00\n* CLOSED: Market closed\tCST 16:00 - 08:55（T+1）\nFutures (Day and Night Market)\t* FUTURE_DAY_WAIT_FOR_OPEN: Futures market wait for opening\tDifferent trading time for different species\n* NIGHT_OPEN: Night market trading hours\n* NIGHT_END: Night market closed\n* FUTURE_DAY_WAIT_FOR_OPEN: Futures market wait for opening\n* FUTURE_DAY_OPEN: Day market trading hours\n* FUTURE_DAY_CLOSE: Day market closed\nUS Market\tSecurities (including stocks, ETFs)\t* PRE_MARKET_BEGIN: Pre-market trading\tEST 04:00 - 09:30\n* AFTERNOON: Regular trading hours\tEST 09:30 - 16:00\n* AFTER_HOURS_BEGIN: After-hours trading\tEST 16:00 - 20:00\n* AFTER_HOURS_END: Market closed of U.S. stock market\tEST 20:00 - 04:00（T+1）\nOptions\t* NONE: Waiting for options opening\tDifferent trading time for different species\n* REST：Lunch break\n* AFTERNOON: Regular trading hours\n* TRADE_AT_LAST: Late trading hours\n* NIGHT: Night market trading hours\n* CLOSED: Market closed\nFutures\t* NONE: Waiting for U.S. futures opening\tDifferent trading time for different species\n* FUTURE_OPEN: Trading hours of U.S. futures\n* FUTURE_BREAK: Break of U.S. futures\n* FUTRUE_BREAK_OVER: Trading hours of U.S. futures after break\n* FUTURE_CLOSE: Market closed of U.S. futures\nA-share Market\tSecurities (including stocks, ETFs)\t* NONE: No trading\tCST 08:55 - 09:15\n* Action: Pre-market trading\tCST 09:15 - 09:25\n* WAITING_OPEN: Waiting for opening\tCST 09:25 - 09:30\n* MORNING: Morning session\tCST 09:30 - 11:30\n* REST: Lunch break\tCST 11:30 - 13:00\n* AFTERNOON: Afternoon session\tCST 13:00 - 15:00\n* CLOSED: Market closed\tCST 15:00 - 08:55（T+1）\nSingapore Market\tFutures\t* FUTURE_DAY_WAIT_FOR_OPEN: Futures market wait for opening\tDifferent trading time for different species\n* NIGHT_OPEN: Night market trading hours\n* NIGHT_END: Night market closed\n* FUTURE_DAY_OPEN: Day market trading hours\n* FUTURE_DAY_CLOSE: Day market closed\nJapanese Market\tFutures\t* FUTURE_DAY_WAIT_FOR_OPEN: Futures market wait for opening\tJST 16:25（T-1）- 16:30（T-1）\n* NIGHT_OPEN: Night market trading hours\tJST 16:30（T-1） - 05:30\n* NIGHT_END: Night market closed\tJST 05:30 - 08:45\n* FUTURE_DAY_OPEN: Day market trading hours\tJST 08:45 - 15:15\n* FUTURE_DAY_CLOSE: Day market closed\tJST 15:15 - 16:25\n* CST, EST, JST represent China time, US Eastern time, and Japan time respectively.\n\n#\nQ15：Parameter format of stock code.\n\nA：\n\nFor users with different programming languages, parameter format of stock code is different.\n\nPython users\nFormat of stock code: market.code.\nFor example: Tencent. Parameter code should be passed in 'HK.00700'.\n\nNon-Python users\nFor stock structure, refer to Security.\nFor example: Tencent. Parameter market should be passed in QotMarket_HK_Security, parameter code should be passed in '00700'.\n\nQuick inquiries. View the code and market through APP: Quotes > Watchlists > All.\nFor Quote Market, refer to here.\n\n\n#\nQ16：Stock Price Adjustment\n\nA：\n\n#\nOverview\n\nPrice adjustment refers to adjusting stock price and trading volume after corporate actions, so that the price chart can better represent actual price moves and trading volume.\nCorporate actions such as stock split, reverse stock split, bonus issue, rights issue, allotment, secondary offering, and dividend payment can affect the stock price. Price adjustment eliminates the impact of corporate actions on stock price and trading volume, and maintains the continuity of the stock price moves.\n\nTips\n\nThe information on this page is mainly intended for the China A-share market.\n#\nGlossary\nCorporate action: Actions on equity and stock conducted by a listed company that affect the company's stock price and number of shares.\nDefault adjustment: Keep the current stock price unchanged, and use it as the benchmark to re-calculate all previous stock prices.\nCumulative adjustment: Keep the stock price before the earliest corporate action unchanged, and use it as the benchmark to re-calculate all future stock prices.\nPrice adjustment factor: The ratio used to re-calculate the adjusted and cumulative stock prices and number of shares after a corporate action. There are two types of price adjustment factors: the default adjustment factor for calculating the adjusted price and the cumulative adjustment factor for calculating the cumulative price.\nEx-div and pay date: The next trading day of the registration date. The stock exchange must calculate the adjusted stock price before market open on the ex-and-pay date. It is also the date on which dividends are distributed to shareholders and changes in the number of shares take place.\n#\nPrice Adjustment Methods\n\nThere are two price adjustment methods: two-step method and continuous multiplication. OpenAPI uses different adjustment methods for different markets.\n\nTwo-step method: The stock price is adjusted based on corporate actions; there are 2 factors in this method: factor A for cash dividends and factor B for all other corporate actions.\nContinuous multiplication: The stock price is adjusted the continuously multiplying the adjustment factors. This method can be seen as a special case of two-step method with factor B as 0.\n\nTips:\n\nOpenAPI uses continuous multiplication for calculating the adjusted price of US stocks, with the price adjustment factor B set to 0.\nOpenAPI uses two-step method for stocks other than US stocks (China A-shares, Hong Kong stocks, Singapore stocks, etc.) and for calculating the cumulative price of US stocks.\n#\nCalculation Formulae\n#\nSingle Adjustment\nDefault adjustment:\nAdjusted price = Actual price × Default adjustment factor A + Default adjustment factor B\nCumulative adjustment:\nCumulative price = Actual price × Cumulative adjustment factor A + Cumulative adjustment factor B\n#\nMultiple Price Adjustments\nDefault adjustment: In chronological order, select the adjustment factors later than the adjustment date, and first use earlier adjustment factors for calculation. Take a double adjustment as an example:\n\nCumulative adjustment: In reverse chronological order, select the adjustment factors earlier than or on the calculation date, and first use later adjustment factors for calculation. Take a double adjustment as an example:\n\n#\nExamples\n#\nExample of a single adjustment\n\nTake the stock of Muyuan Foods as an example:\n\nScreening weighting factors are as follows:\nEx-Div and Pay Date\tStock Symbol\tCorporate Action Details\tDefault Adjustment Factor A\tDefault Adjustment Factor B\n06/03/2021\tSZ.002714\t10-share dividends: 4 shares and ￥14.61 (tax included)\t0.71429\t-1.04357\nData on actual price:\nDate\tStock Symbol\tActual Closing Price\n06/02/2021\tSZ.002714\t93.11\n06/03/2021\tSZ.002714\t66.25\nData on adjusted prices:\nDate\tStock Symbol\tAdjusted Closing Price\n06/02/2021\tSZ.002714\t65.4639719\n06/03/2021\tSZ.002714\t66.25\nMethod for calculating adjusted prices:\nMuyuan Foods conducted a stock split and paid cash dividends on 2021/06/03 (4 shares and ￥14.61 for every 10 shares owned), and here is how to calculate the adjusted closing price on 06/02/2021: Adjusted price (65.4639719 ) = Actual price (93.11) × Default adjustment factor A (0.71429) + Default adjustment factor B (-1.04357)\n\n#\nExample of multiple cumulative adjustment\n\nFollowing on the previous example, here is how to calculate the cumulative price of Muyuan Foods on 06/02/2021:\n\nAdjustment factors are as follows:\nEx-Date\tStock Symbol\tCorporate Action Details\tCumulative Factor A\tCumulative Factor B\n07/04/2014\tSZ.002714\t10-share dividends: ￥2.34 (tax included)\t1\t0.234\n06/10/2015\tSZ.002714\t10-share dividends: 10 shares and ￥0.61 tax included)\t2\t0.061\n07/08/2016\tSZ.002714\t10-share dividends: 10 shares and ￥3.53 tax included) (tax included)\t2\t0.353\n07/11/2017\tSZ.002714\t10-share dividends: 8 shares and ￥6.9 (tax included)\t1.8\t0.69\n07/03/2018\tSZ.002714\t10-share dividends: ￥6.91 (tax included)\t1\t0.691\n07/04/2019\tSZ.002714\t10-share dividends: ￥0.5 (tax included)\t1\t0.05\n06/04/2020\tSZ.002714\t10-share dividends: 7 shares and ￥5.5 (tax included)\t1.7\t0.55\nData on actual prices:\nDate\tStock Symbol\tActual Price\n06/02/2021\tSZ.002714\t93.11\nData on cumulative prices:\nDate\tStock Symbol\tCumulative Price\n06/02/2021\tSZ.002714\t1150.5114\nMethod for calculating cumulative prices:\nTo calculate the cumulative price of Muyuan Foods on June 2, 2021, all the corporate actions by June 2, 2021 need to be taken into account. The detailed calculations are as follows:\n\n← OpenD Related\nTransaction related →\n\nQuote related\nQ1: Subscription failed\nQ2: Unsubscribe failed\nQ3: The unsubscribe was successful but the quota was not released\nQ4: Will the quota be released if the script connection is closed if the subscription is less than one minute?\nQ5: What is the specific restriction logic for requesting frequency restriction?\nQ6: What is the reason why self-selected stocks cannot be added?\nQ7: Why is the US stock quotation on the OpenAPI side different from the national comprehensive quotation on the client side?\nQ8: Where can I buy OpenAPI quotation cards?\nQ9: Why sometimes, the response of the get interface to obtain real-time data is slow?\nQ10: What kind of data can be obtained after purchasing the OpenAPI Nasdaq Basic quotation card?\nQ11: How many levels does each market category support?\nQ12：Why does OpenD still have no quote right after I purchase and activate the quotation card?\nQ13：How to Get Real-time Quotes Through Subscription Interface?\nQ14：What time period corresponds to each market state？\nQ15：Parameter format of stock code.\nQ16：Stock Price Adjustment"}, {"title": "OpenD Related | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/qa/opend.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\nOpenD Related\nQuote related\nTransaction related\nOthers\n#\nOpenD Related\n#\nQ1: OpenD automatically exited due to failure to complete \"Questionnaire Evaluation and Agreement Confirmation\"\n\nA: You need to carryout relevant questionnaire evaluation and agreement confirmation before you can use OpenD. Please go to complete.\n\n#\nQ2: OpenD exited due to \"the program's own data does not exist\"\n\nA: Generally, the copy of the own data fails due to permission problems. You can try to copy the file extracted from Appdata.dat in the program directory to the program data directory.\n\nWindows program data directory:%appdata%/com.futunn.FutuOpenD/F3CNN\nNon-windows program data directory: ~/.com.futunn.FutuOpenD/F3CNN\n#\nQ3: OpenD service failed to start\n\nA: Please check:\n\nWhether there are other programs occupying the configured port;\nIs there a OpenD configured with the same port already running?\n#\nQ4: How to verify the mobile phone verification code?\n\nA: On the OpenD interface or remotely to the Telnet port, enter the command ʻinput_phone_verify_code -code=123456`.\n\nTips\n\n123456 is the mobile phone verification code received\nthere is a space before '-code=123456'\n#\nQ5: Are other programming languages supported?\n\nA: OpenD provides a socket-based protocol. Currently we provide and maintain Python, C++, Java, C# and JavaScript interfaces, download entry.\n\nIf the above languages still cannot meet your needs, you can connect to the Protobuf protocol by yourself.\n\n#\nQ6: Verify the device lock multiple times on the same device\n\nA: The device ID is randomly generated and stored in the \\com.futunn.OpenD\\F3CNN\\Device.dat file.\n\nTips\n\nIf the file is deleted or damaged, OpenD will regenerate a new device ID and then verify the device lock.\nIn addition, users of mirror copy deployment need to be aware that if the Device.dat content of multiple machines is the same, it will also cause these machines to verify the device lock multiple times. Delete the Device.dat file to solve it.\n#\nQ7: Does OpenD provide a Docker image?\n\nA: Not currently available.\n\n#\nQ8: Can one account log in to multiple OpenD?\n\nA: One account can log in to OpenD or other client terminals on multiple machines, and up to 10 OpenD terminals are allowed to log in at the same time. At the same time, there is a restriction of \"market kicking\", and only one OpenD can obtain the highest authority market. For example, if two terminals log into the same account, there can only be one HK stock LV2 quotation and the other HK stock BMP quotation.\n\n#\nQ9: How to control the market permissions of OpenD and other clients (desktop and mobile)?\n\nA: In accordance with the regulations of the exchange, there will be a restriction on “market kicking” when multiple terminals are online at the same time, and only one terminal can obtain the highest authority market. The auto_hold_quote_right parameter is built-in in the startup parameters of the command line version of OpenD, which is used to flexibly configure market permissions. When this parameter option is enabled, OpenD will automatically retrieve it after the market permission is robbed. If it is robbed again within 10 seconds, other terminals will obtain the highest market quotation authority (OpenD will not rob again).\n\n#\nQ10: How to give priority to the OpenD market authority?\n\nA:\n\nConfigure the OpenD startup parameter auto_hold_quote_right to 1;\nMake sure not to grab the highest authority twice in a row within 10 seconds on the mobile or desktop Futubull (login counts once, and click \"Restart Quotes\" to count the second time).\n\n#\nQ11: How to give priority to the market authority of the mobile terminal (or desktop terminal)?\n\nA: Set OpenD startup parameter auto_hold_quote_right to 0, and login with mobile or PC Futubull after OpenD.\n\n#\nQ12: Use the Visualization OpenD to remember the password to log in. After a long time hang up, it prompts that the connection is disconnected. Do I need to log in again?\n\nA: Using the Visualization OpenD, if you choose to remember the password to log in, you will use the token recorded locally. Due to the time limit of the token, when the token expires, if there is network fluctuation or moomoo background release, it may cause the situation that it cannot be automatically connected after disconnecting from the background. Therefore, if you want visiulization OpenD for a long time to hang up, it is recommended to manually enter the password to log in, and OpenD will automatically handle this situation.\n\n#\nQ13: How to ask R&D staff to assist in troubleshooting when problems that cannot be solved are encountered?\n\nA:\n\nContact OpenAPI developers via QQ/WeChat to facilitate instant communication and file transferation.\n\nDetailed description: the time when the error occurred, OpenD version number, Futu API version number, script language name, interface name or protocol number, short code or screenshot with detailed input and return.\n\nIf necessary, OpenD log must be provided to facilitate location and confirmation of problems. Transaction issues require info log level, and market issues require debug log level. The log level log_level can be configured in OpenD.xml Configure. After configuration, OpenD needs to be restarted to take effect. After the problem recurs, package the log and send it to Futu R&D personnel.\n\nTips\n\nThe log path is as follows:\n\nwindows: %appdata%/com.futunn.FutuOpenD/Log\n\nNon-windows: ~/.com.futunn.FutuOpenD/Log\n\n#\nQ14: Script cannot connect to OpenD\n\nA: Please try to check first:\n\nWhether the port connected by the script is consistent with the port configured by OpenD.\nSince the upper limit of OpenD connection is 128, is there any useless connection that is not closed?\nCheck whether the listening address is correct. If the script and OpenD are not on the same machine, the OpenD listening address needs to be set to 0.0.0.0.\n#\nQ15: Disconnected after being connected for a while\n\nA: If it is a self-docking protocol, check whether there is a regular heartbeat to maintain the connection.\n\n#\nQ16: I can't connect to OpenD when I run Python scripts in multiprocessing mode through the multiprocessing module under Linux?\n\nA: After the process is created by default in the Linux/Mac environment, the thread created inside py-futu-api in the parent process will disappear in the child process, resulting in an internal program error. You can use spawn to start the process:\n\nimport multiprocessing as mp\nmp.set_start_method('spawn')\np = mp.Process(target=func)\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\nQ17: How to log in to two OpenD at the same time on one computer?\n\nA: Visualization OpenD does not support, but Command Line OpenD supports.\n\nUnzip the file downloaded from the official website, and copy the entire Command Line OpenD folder (e.g. OpenD_5.2.1408_Windows). Take Windows as an example, other systems can do the same operation.\n\nConfigure two OpenD.xml files that are placed in two Command Line OpenD folders. Configure items as follow:\n\nConfiguration file 1: api_port = 11111, login_account = Login Account 1, login_pwd = Login Password 1\n\nConfiguration file 2: api_port = 11112, login_account = Login Account 2, login_pwd = Login Password 2\n\nRun the two OpenD.exe.\n\nWhen calling the interface, note that the parameter port (OpenD listening address) should corresponds to the parameter api_port in the OpenD.xml file.\nFor example:\nfrom futu import *\n\n# Send requests to OpenD logged in account 1\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111, is_encrypt=False)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n# Send requests to OpenD logged in account 2\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11112, is_encrypt=False)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n#\nQ18: How do I execute the operation and maintenance commands for grabbing permissions through scripts when the market permission is kicked off by other clients?\n\nA:\n\nConfigure Telnet address and Telnet port.  \nStart OpenD (it will also start Telnet).\nAfter finding that the market quotation authority has been robbed, you can refer to the following code example and send the request_highest_quote_right command to OpenD via Telnet.\nfrom telnetlib import Telnet\nwith Telnet('127.0.0.1', 22222) as tn: # Telnet address is: 127.0.0.1, Telnet port is: 22222\n     tn.write(b'request_highest_quote_right\\r\\n')\n     reply = b''\n     while True:\n         msg = tn.read_until(b'\\r\\n', timeout=0.5)\n         reply += msg\n         if msg == b'':\n             break\n     print(reply.decode('gb2312'))\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\n#\nQ19: OpenD automatic upgrade failed\n\nA: The automatic update of OpenD failed to be executed by the update command. Possible reasons:\n\nThe file is occupied by other processes: you can try to close other OpenD processes, or restart the system and execute update again If the above still cannot be solved, you can download the update by yourself through Official Website.\n#\nQ20: Fail to launch the visualization OpenD on ubuntu22？\n\nA: When running the visualization OpenD on certain Linux distributions (such as Ubuntu 22.04), you may encounter the error: dlopen(): error loading libfuse.so.2. This occurs because libfuse is not installed by default on these systems. Typically you can resolve this issue by installing libfuse manually. For example, you can install it via the commane line on Ubuntu22.04 with:\n\nsudo apt update\nsudo apt install -y libfuse2\n\n \n\n        Copied!\n    \n1\n2\n\n\nOnce successfully installed, you will be able to run the visualization OpenD normally. Please refer to https://docs.appimage.org/user-guide/troubleshooting/fuse.html for more details.\n\n← Protocol Introduction\nQuote related →\n\nOpenD Related\nQ1: OpenD automatically exited due to failure to complete \"Questionnaire Evaluation and Agreement Confirmation\"\nQ2: OpenD exited due to \"the program's own data does not exist\"\nQ3: OpenD service failed to start\nQ4: How to verify the mobile phone verification code?\nQ5: Are other programming languages supported?\nQ6: Verify the device lock multiple times on the same device\nQ7: Does OpenD provide a Docker image?\nQ8: Can one account log in to multiple OpenD?\nQ9: How to control the market permissions of OpenD and other clients (desktop and mobile)?\nQ10: How to give priority to the OpenD market authority?\nQ11: How to give priority to the market authority of the mobile terminal (or desktop terminal)?\nQ12: Use the Visualization OpenD to remember the password to log in. After a long time hang up, it prompts that the connection is disconnected. Do I need to log in again?\nQ13: How to ask R&D staff to assist in troubleshooting when problems that cannot be solved are encountered?\nQ14: Script cannot connect to OpenD\nQ15: Disconnected after being connected for a while\nQ16: I can't connect to OpenD when I run Python scripts in multiprocessing mode through the multiprocessing module under Linux?\nQ17: How to log in to two OpenD at the same time on one computer?\nQ18: How do I execute the operation and maintenance commands for grabbing permissions through scripts when the market permission is kicked off by other clients?\nQ19: OpenD automatic upgrade failed\nQ20: Fail to launch the visualization OpenD on ubuntu22？"}, {"title": "获取期权链到期日 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-option-expiration-date.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n相关衍生品 \n\n获取期权链到期日\n获取期权链\n筛选窝轮\n获取窝轮和期货列表\n获取期货合约资料\n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取期权链到期日\n\nget_option_expiration_date(code, index_option_type=IndexOptionType.NORMAL)\n\n介绍\n\n通过标的股票，查询期权链的所有到期日。如需获取完整期权链，请配合 获取期权链 接口使用。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t标的股票代码\nindex_option_type\tIndexOptionType\t指数期权类型 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回期权链到期日相关数据\nstr\t当 ret != RET_OK，返回错误描述\n期权链到期日数据格式如下：\n字段\t类型\t说明\nstrike_time\tstr\t期权链行权日 \n\noption_expiry_date_distance\tint\t距离到期日天数 \n\nexpiration_cycle\tExpirationCycle\t交割周期 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret, data = quote_ctx.get_option_expiration_date(code='HK.00700')\nif ret == RET_OK:\n    print(data)\n    print(data['strike_time'].values.tolist())  # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\nOutput\n  strike_time  option_expiry_date_distance expiration_cycle\n0  2021-04-29                            4              N/A\n1  2021-05-28                           33              N/A\n2  2021-06-29                           65              N/A\n3  2021-07-29                           95              N/A\n4  2021-09-29                          157              N/A\n5  2021-12-30                          249              N/A\n6  2022-03-30                          339              N/A\n['2021-04-29', '2021-05-28', '2021-06-29', '2021-07-29', '2021-09-29', '2021-12-30', '2022-03-30']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\n\n接口限制\n\n每 30 秒内最多请求 60 次获取期权链到期日接口\n\n← 获取复权因子\n获取期权链 →\n\n获取期权链到期日"}, {"title": "Operation Command | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/opend/opend-operate.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nOverview\nCommand Line OpenD\nOperation Command\n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nOperation Command\n\nYou can do operate OpenD by sending Operation Command from the command line or Telent.\n\nCommand format: cmd -param_key1=param_value1 -param_key2=param_value2\nUsing the following example to describe how to use Telnet: help -cmd=exit\n\nConfigure Telnet address and Telnet port in the OpenD set up parameter.  \nStart OpenD (it will also start Telnet).\nVia Telnet，send the command help -cmd=exit to OpenD。\nfrom telnetlib import Telnet\nwith Telnet('127.0.0.1', 22222) as tn:  # Telnet address is: 127.0.0.1, Telnet port is: 22222\n    tn.write(b'help -cmd=exit\\r\\n')\n    reply = b''\n    while True:\n        msg = tn.read_until(b'\\r\\n', timeout=0.5)\n        reply += msg\n        if msg == b'':\n            break\n    print(reply.decode('gb2312'))\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n#\nCommand Help\n\nhelp -cmd=exit\n\nView the detailed information of the specified command, output the command list if no parameter is specified\n\nParameters:\ncmd: command\n#\nExit the Program\n\nExit\n\nExit OpenD\n\n#\nRequest Mobile Phone Verification Code\n\nreq_phone_verify_code\n\nRequested mobile phone verification code. Security verification is required when the device lock is enabled and the device is logged in at the first time.\n\nFrequency limitations:\nMaximal 1 request every 60 seconds\n#\nEnter the Phone Verification Code\n\nInput_phone_verify_code -code=123456\n\nEnter the phone verification code and continue the login process.\n\nParameters:\n\ncode: mobile phone verification code\n\nFrequency limitations:\n\nMaximal 10 requests every 60 seconds\n#\nRequest Graphic Verification Code\n\nreq_pic_verify_code\n\nRequest a graphic verification code. When you enter the wrong login password multiple times, you need to enter the graphic verification code.\n\nFrequency limitations:\nMaximal 10 requests every 60 seconds\n#\nEnter Graphic Verification Code\n\nInput_pic_verify_code -code=1234\n\nEnter the graphic verification code and continue the login process.\n\nParameters:\n\ncode: Graphic verification code\n\nFrequency limitations:\n\nMaximal 10 requests every 60 seconds\n#\nRelogin\n\nrelogin -login_pwd=123456\n\nThis command can be used when the user is required to log in again when the login password is changed or the device lock is opened midway. You can only relogin to the current account, and changing accounts is not supported. The password parameter is mainly used to the situation that the login password had been modified. If login_pwd is not set, the login password at startup will be used.\n\nParameters:\n\nlogin_pwd: login password in plaintext\n\nlogin_pwd_md5: login password in ciphertext (32-bit MD5 encrypted hexadecimal)\n\nFrequency limitations:\n\nMaximal 10 requests every hour\n#\nTime Delay Between Detection and Connection Point\n\nping\n\nDelay before detection and connection point\n\nFrequency limitations:\nMaximal 10 requests every 60 seconds\n#\nDisplay Delay Statistics Report\n\nshow_delay_report -detail_report_path=D:/detail.txt -push_count_type=sr2cs\n\nDisplay delay statistics report, including push delay, request delay and order delay. Data is cleaned up at 6:00 Beijing time every day.\n\nParameters:\n\ndetail_report_path: file output path (MAC system only supports absolute path, not relative path), optional parameter, if not specified, output to the console\n\npush_count_type: the type of push delay (sr2ss, ss2cr, cr2cs, ss2cs, sr2cs), sr2cs by default.\n\nsr refers to the server receiving time (currently only HK stocks support this time)\nss refers to the server sending time\ncr refers to OpenD receiving time\ncs refers to OpenD sending time\n#\nClose API Connection\n\nclose_api_conn -conn_id=123456\n\nClose an API connection, if not specified, close all connections\n\nParameters:\nconn_id: API connection ID\n#\nShow Subscription Status\n\nshow_sub_info -conn_id=123456 -sub_info_path=D:/detail.txt\n\nDisplay the subscription status of a connection, if not specified, display all connections\n\nParameters:\n\nconn_id: API connection ID\n\nsub_info_path: file output path (MAC system only supports absolute path, not relative path), optional parameter, if not specified, output to the console\n\n#\nRequest the Highest Quotation Permission\n\nrequest_highest_quote_right\n\nWhen the advanced quotation authority is occupied by other devices (such as desktop/mobile terminal), you can use this command to request the highest quotation authority again (And then, other devices that are logged in will not be able to use advanced quote).\n\nFrequency limitations:\nMaximal 10 requests every 60 seconds\n#\nUpdate\n\nupdate\n\nUpdate\n\n← Command Line OpenD\nOverview →\n\nOperation Command"}, {"title": "Quotation Definitions | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/quote.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nQuotation Definitions\n\nStockField\n\nNONE\n\nunknown\n\nCHANGE_RATE\n\nYield \n\nAMPLITUDE\n\nAmplitude \n\nVOLUME\n\nAverage daily trading volume \n\nTURNOVER\n\nAverage daily turnover \n\nTURNOVER_RATE\n\nTurnover rate \n\nAssetClass\n\nUNKNOW\n\nUnknown\n\nSTOCK\n\nStocks\n\nBOND\n\nBonds\n\nCOMMODITY\n\nCommodities\n\nCURRENCY_MARKET\n\nCurrency markets\n\nFUTURE\n\nFutures\n\nSWAP\n\nSwaps\n\nDarkStatus\n\nNONE\n\nNo grey market trading\n\nTRADING\n\nOngoing grey market trading\n\nEND\n\nGrey market trading finished\n\nStockField\n\nNONE\n\nunknown\n\nNET_PROFIT\n\nNet profit \n\nNET_PROFIX_GROWTH\n\nNet profit growth rate \n\nSUM_OF_BUSINESS\n\nOperating income \n\nSUM_OF_BUSINESS_GROWTH\n\nYear-on-year growth rate of operating income \n\nNET_PROFIT_RATE\n\nNet profit rate \n\nGROSS_PROFIT_RATE\n\nGross profit margin \n\nDEBT_ASSET_RATE\n\nAsset-liability ratio \n\nRETURN_ON_EQUITY_RATE\n\nReturn on equity \n\nROIC\n\nReturn on invested capital \n\nROA_TTM\n\nReturn on assets TTM \n\nEBIT_TTM\n\nEarnings before interest and tax TTM \n\nEBITDA\n\nEarnings before interest, tax, depreciation and amortization \n\nOPERATING_MARGIN_TTM\n\nOperating profit margin TTM \n\nEBIT_MARGIN\n\nEBIT margin \n\nEBITDA_MARGIN\n\nEBITDA margin \n\nFINANCIAL_COST_RATE\n\nFinancial cost rate \n\nOPERATING_PROFIT_TTM\n\nOperating profit TTM \n\nSHAREHOLDER_NET_PROFIT_TTM\n\nNet profit attributable to the parent company \n\nNET_PROFIT_CASH_COVER_TTM\n\nThe proportion of cash income in profit \n\nCURRENT_RATIO\n\nCurrent ratio \n\nQUICK_RATIO\n\nQuick ratio \n\nCURRENT_ASSET_RATIO\n\nLiquidity rate \n\nCURRENT_DEBT_RATIO\n\nCurrent debt ratio \n\nEQUITY_MULTIPLIER\n\nEquity multiplier \n\nPROPERTY_RATIO\n\nEquity ratio \n\nCASH_AND_CASH_EQUIVALENTS\n\nCash and cash equivalent \n\nTOTAL_ASSET_TURNOVER\n\nTotal asset turnover rate \n\nFIXED_ASSET_TURNOVER\n\nFixed asset turnover rate \n\nINVENTORY_TURNOVER\n\nInventory turnover rate \n\nOPERATING_CASH_FLOW_TTM\n\nOperating cash flow TTM \n\nACCOUNTS_RECEIVABLE\n\nNet accounts receivable \n\nEBIT_GROWTH_RATE\n\nYear-on-year growth rate of EBIT \n\nOPERATING_PROFIT_GROWTH_RATE\n\nYear-on-year growth rate of operating profit \n\nTOTAL_ASSETS_GROWTH_RATE\n\nYear-on-year growth rate of total assets \n\nPROFIT_TO_SHAREHOLDERS_GROWTH_RATE\n\nYear-on-year growth rate of net profit attributed to parent company owner \n\nPROFIT_BEFORE_TAX_GROWTH_RATE\n\nYear-on-year growth rate of total profit \n\nEPS_GROWTH_RATE\n\nYear-on-year growth rate of EPS \n\nROE_GROWTH_RATE\n\nYear-on-year growth rate of ROE \n\nROIC_GROWTH_RATE\n\nYear-on-year growth rate of ROIC \n\nNOCF_GROWTH_RATE\n\nYear-on-year growth rate of operating cash flow \n\nNOCF_PER_SHARE_GROWTH_RATE\n\nYear-on-year growth rate of operating cash flow per share \n\nOPERATING_REVENUE_CASH_COVER\n\nOperating cash cover ratio \n\nOPERATING_PROFIT_TO_TOTAL_PROFIT\n\nOperating profit ratio \n\nBASIC_EPS\n\nBasic earnings per share \n\nDILUTED_EPS\n\nDiluted earnings per share \n\nNOCF_PER_SHARE\n\nNet operating cash flow per share \n\nFinancialQuarter\n\nNONE\n\nunknown\n\nANNUAL\n\nannual report\n\nFIRST_QUARTER\n\nFirst quarter report\n\nINTERIM\n\nInterim report\n\nTHIRD_QUARTER\n\nThird quarter report\n\nMOST_RECENT_QUARTER\n\nLatest quarter report\n\nStockField\n\nNONE\n\nUnknown\n\nPRICE\n\nlatest price\n\nMA5\n\nSimple moving average\n\nMA5\n\n5-day simple moving average (Not recommended)\n\nMA10\n\n10-day simple moving average (Not recommended)\n\nMA20\n\n20-day simple moving average (Not recommended)\n\nMA30\n\n30-day simple moving average (Not recommended)\n\nMA60\n\n60-day simple moving average (Not recommended)\n\nMA120\n\n120-day simple moving average (Not recommended)\n\nMA250\n\n250-day simple moving average (Not recommended)\n\nRSI\n\nRSI \n\nEMA\n\nExponential moving average\n\nEMA5\n\n5-day exponential moving average (Not recommended)\n\nEMA10\n\n10-day exponential moving average (Not recommended)\n\nEMA20\n\n20-day exponential moving average (Not recommended)\n\nEMA30\n\n30-day exponential moving average (Not recommended)\n\nEMA60\n\n60-day exponential moving average (Not recommended)\n\nEMA120\n\n120-day exponential moving average (Not recommended)\n\nEMA250\n\n250-day exponential moving average (Not recommended)\n\nKDJ_K\n\nK value of KDJ indicator \n\nKDJ_D\n\nD value of KDJ indicator \n\nKDJ_J\n\nJ value of KDJ indicator \n\nMACD_DIFF\n\nDIFF value of MACD indicator \n\nMACD_DEA\n\nDEA value of MACD indicator \n\nMACD\n\nMACD value of MACD indicator \n\nBOLL_UPPER\n\nUPPER value of BOLL indicator \n\nBOLL_MIDDLER\n\nMIDDLER value of BOLL indicator \n\nBOLL_LOWER\n\nLOWER value of BOLL indicator \n\nVALUE\n\nCustom value (stock_field1 does not support this field)\n\nRelativePosition\n\nNONE\n\nUnknown\n\nMORE\n\nStock_field1 is greater than stock_field2\n\nLESS\n\nStock_field1 is less than stock_field2\n\nCROSS_UP\n\nStock_field1 cross over stock_field2\n\nCROSS_DOWN\n\nStock_field1 cross below stock_field2\n\nPatternField\n\nNONE\n\n未知\n\nMA_ALIGNMENT_LONG\n\nMA bullish alignment (MA5 > MA10 > MA20 > MA30 > MA60 for two consecutive days, and the closing price of the day is greater than the closing price of the previous day)\n\nMA_ALIGNMENT_SHORT\n\nMA bearish alignment (MA5 < MA10 < MA20 < MA30 < MA60 for two consecutive days, and the closing price of the day is less than the closing price of the previous day)\n\nEMA_ALIGNMENT_LONG\n\nEMA bullish alignment (EMA5 > EMA10 > EMA20 > EMA30 > EMA60 for two consecutive days, and the closing price of the day is greater than the closing price of the previous day)\n\nEMA_ALIGNMENT_SHORT\n\nEMA bearish alignment (EMA5 < EMA10 < EMA20 < EMA30 < MA60 for two consecutive days, and the closing price of the day is less than the closing price of the previous day)\n\nRSI_GOLD_CROSS_LOW\n\nRSI low golden cross (short-term RSI crosses over long-term RSI below 50 (short-term RSI of the previous day is less than long-term RSI, short-term RSI of the current day is greater than long-term RSI))\n\nRSI_DEATH_CROSS_HIGH\n\nRSI high dead cross (short-term RSI crosses below long-term RSI above 50 (short-term RSI of the previous day is greater than long-term RSI, short-term RSI of the current day is less than long-term RSI))\n\nRSI_TOP_DIVERGENCE\n\nRSI top divergence (two adjacent candlestick peaks, the CLOSE of the later peak > the CLOSE of the earlier peak, the RSI12 value of the later peak < the RSI12 value of the earlier peak)\n\nRSI_BOTTOM_DIVERGENCE\n\nRSI bottom divergence (two adjacent candlestick troughs, the CLOSE of the later trough < the CLOSE of the earlier trough, the RSI12 value of the later trough > the RSI12 value of the earlier trough)\n\nKDJ_GOLD_CROSS_LOW\n\nKDJ low golden cross (D value is less than or equal to 30, and the K value of the previous day is less than the D value, and the K value of the day is greater than the D value)\n\nKDJ_DEATH_CROSS_HIGH\n\nKDJ high death cross (D value is greater than or equal to 70, and the K value of the previous day is greater than the D value, and the K value of the day is less than the D value)\n\nKDJ_TOP_DIVERGENCE\n\nKDJ top divergence (two adjacent candlestick peaks, the CLOSE of the later peak > the CLOSE of the earlier peak, the J value of the later peak < the J value of the earlier peak)\n\nKDJ_BOTTOM_DIVERGENCE\n\nKDJ bottom divergence (two adjacent candlestick troughs, the CLOSE of the later trough < the CLOSE of the earlier trough, the J value of the later trough > the J value of the earlier trough)\n\nMACD_GOLD_CROSS_LOW\n\nMACD golden cross (DIFF crosses over DEA ​​(DIFF is less than DEA of the previous day, and DIFF is greater than DEA of the current day))\n\nMACD_DEATH_CROSS_HIGH\n\nMACD dead cross (DIFF crosses below DEA (DIFF is greater than DEA of the previous day, and DIFF is less than DEA of the current day))\n\nMACD_TOP_DIVERGENCE\n\nMACD top divergence (two adjacent candlestick peaks, the CLOSE of the later peak > the CLOSE of the earlier peak, the MACD value of the later peak < the MACD value of the earlier peak)\n\nMACD_BOTTOM_DIVERGENCE\n\nMACD bottom deviation (two adjacent candlestick troughs, the CLOSE of the later trough < the CLOSE of the earlier trough, the MACD value of the later trough > the MACD value of the earlier trough)\n\nBOLL_BREAK_UPPER\n\nBreak up bollinger upper bound (the stock price of the previous day was lower than the upper bound, and the stock price of the current day is greater than the upper bound)\n\nBOLL_BREAK_LOWER\n\nBreak up bollinger lower bound (the stock price of the previous day was greater than the lower bound, and the stock price of the current day is less than the lower bound)\n\nBOLL_CROSS_MIDDLE_UP\n\nCross over bollinger mid line (the stock price of the previous day was lower than the mid line, and the stock price of the current day is greater than the mid line)\n\nBOLL_CROSS_MIDDLE_DOWN\n\nCross below bollinger mid line (the stock price of the previous day was greater than the mid line, and the stock price of the current day is less than the mid line)\n\nUserSecurityGroupType\n\nNONE\n\nunknown\n\nCUSTOM\n\nCustom groups\n\nSYSTEM\n\nSystem groups\n\nALL\n\nAll groups\n\nIndexOptionType\n\nNONE\n\nunknown\n\nNORMAL\n\nOrdinary index option\n\nSMALL\n\nSmall Index Options\n\nIpoPeriod\n\nNONE\n\nunknown\n\nTODAY\n\nListed today\n\nTOMORROW\n\nTo be listed tomorrow\n\nNEXTWEEK\n\nTo be listed next week\n\nLASTWEEK\n\nHas been listed last week\n\nLASTMONTH\n\nHas been listed last month\n\nIssuer\n\nUNKNOW\n\nunknown\n\nSG\n\nSociete Generale\n\nBP\n\nBNP Paribas\n\nCS\n\nCredit Suisse\n\nCT\n\nCiti Bank\n\nEA\n\nThe Bank of East Aisa\n\nGS\n\nGoldman Sachs\n\nHS\n\nHSBC\n\nJP\n\nJPMorgan Chase\n\nMB\n\nMacquarie Bank\n\nSC\n\nStandard Chartered Bank\n\nUB\n\nUnion Bank of Switzerland\n\nBI\n\nBank of China\n\nDB\n\nDeutsche Bank\n\nDC\n\nDaiwa Bank\n\nML\n\nMerrill Lynch\n\nNM\n\nNomura Bank\n\nRB\n\nRabobank\n\nRS\n\nThe Royal Bank of Scotland\n\nBC\n\nBarclays\n\nHT\n\nHaitong Bank\n\nVT\n\nBank Vontobel\n\nKC\n\nKBC Bank\n\nMS\n\nMorgan Stanley\n\nGJ\n\nGuotai Junan\n\nXZ\n\nDBS Bank\n\nHU\n\nHuatai\n\nKS\n\nKorea Investment\n\nCI\n\nCITIC Securities\n\nKL_FIELD\n\nALL\n\nAll\n\nDATE_TIME\n\nTime\n\nHIGH\n\nHigh\n\nOPEN\n\nOpen\n\nLOW\n\nLow\n\nCLOSE\n\nClose\n\nLAST_CLOSE\n\nClose yesterday\n\nTRADE_VOL\n\nVolume\n\nTRADE_VAL\n\nTurnover\n\nTURNOVER_RATE\n\nTurnover rate\n\nPE_RATIO\n\nP/E ratio\n\nCHANGE_RATE\n\nYield\n\nKLType\n\nNONE\n\nunknown\n\nK_1M\n\n1 minute candlestick\n\nK_DAY\n\n1 day candlestick\n\nK_WEEK\n\n1 week candlestick \n\nK_MON\n\n1 month candlestick \n\nK_YEAR\n\n1 year candlestick \n\nK_5M\n\n5 minutes candlestick\n\nK_15M\n\n15 minutes candlestick\n\nK_30M\n\n30 minutes candlestick \n\nK_60M\n\n60 minutes candlestick\n\nK_3M\n\n3 minutes candlestick \n\nK_QUARTER\n\n1 quarter candlestick \n\nPeriodType\n\nINTRADAY\n\nIntraday\n\nDAY\n\nDay\n\nWEEK\n\nWeek\n\nMONTH\n\nMonth\n\nPriceReminderMarketStatus\n\nUNKNOW\n\nunknown\n\nOPEN\n\nMarket opens\n\nUSPRE\n\nPre-market of US stocks\n\nUSAFTER\n\nAfter-hours of US stocks\n\nModifyUserSecurityOp\n\nNONE\n\nUnknown\n\nADD\n\nAdd\n\nDEL\n\nDelete\n\nMOVE_OUT\n\nRemove from group\n\nOptionAreaType\n\nNONE\n\nunknown\n\nAMERICAN\n\nAmerican Option\n\nEUROPEAN\n\nEuropean Option\n\nBERMUDA\n\nBermuda Option\n\nOptionCondType\n\nALL\n\nAll\n\nWITHIN\n\nIn the money\n\nOUTSIDE\n\nOut of the money\n\nOptionType\n\nALL\n\nall\n\nCALL\n\nCall option\n\nPUT\n\nPut option\n\nPlate\n\nALL\n\nAll plates\n\nINDUSTRY\n\nIndustry plate\n\nREGION\n\nRegional plate \n\nCONCEPT\n\nConcept plate\n\nOTHER\n\nOther plates \n\nPriceReminderFreq\n\nNONE\n\nUnknown\n\nALWAYS\n\nKeep reminding\n\nONCE_A_DAY\n\nOnce a day\n\nONCE\n\nOnly remind once\n\nPriceReminderType\n\nNONE\n\nUnknown\n\nPRICE_UP\n\nPrice rise to\n\nPRICE_DOWN\n\nPrice fall to\n\nCHANGE_RATE_UP\n\nDaily increase rate exceeds \n\nCHANGE_RATE_DOWN\n\nDaily decline rate exceeds \n\nFIVE_MIN_CHANGE_RATE_UP\n\nIncreate rate in 5 minutes exceeds \n\nFIVE_MIN_CHANGE_RATE_DOWN\n\nDecline rate in 5 minutes exceeds \n\nVOLUME_UP\n\nVolume exceeds\n\nTURNOVER_UP\n\nTurnover exceeds\n\nTURNOVER_RATE_UP\n\nTurnover rate exceeds \n\nBID_PRICE_UP\n\nBid price higher than\n\nASK_PRICE_DOWN\n\nAsk price lower than\n\nBID_VOL_UP\n\nBid volume higher than\n\nASK_VOL_UP\n\nAsk volume higher than\n\nTHREE_MIN_CHANGE_RATE_UP\n\nIncreate rate in 3 minutes exceeds \n\nTHREE_MIN_CHANGE_RATE_DOWN\n\nDecline rate in 3 minutes exceeds \n\nPriceType\n\nUNKNOW\n\nUnknown\n\nOUTSIDE\n\nOut of the money\n\nWITH_IN\n\nIn the money\n\nPushDataType\n\nUNKNOW\n\nUnknown\n\nREALTIME\n\nReal-time data\n\nBYDISCONN\n\nPull supplementary data (up to 50) during disconnection from Futu server\n\nCACHE\n\nNon-real-time non-supplementary data\n\nMarket\n\nNONE\n\nUnknown market\n\nHK\n\nHK market\n\nUS\n\nUS market\n\nSH\n\nShanghai market\n\nSZ\n\nShenzhen market\n\nSG\n\nSingapore market\n\nJP\n\nJapanese market\n\nMarketState\n\nCorresponding time period of each market state, click here to learn more\n\nNONE\n\nNo trading\n\nAUCTION\n\nPre-market trading\n\nWAITING_OPEN\n\nWaiting for opening\n\nMORNING\n\nMorning session\n\nREST\n\nLunch break\n\nAFTERNOON\n\nAfternoon session / Regular trading hours for U.S stock market\n\nCLOSED\n\nMarket closed\n\nPRE_MARKET_BEGIN\n\nPre-market trading of U.S stock market\n\nPRE_MARKET_END\n\nPre-market ending of U.S stock market\n\nAFTER_HOURS_BEGIN\n\nAfter-hours trading of U.S stock market\n\nAFTER_HOURS_END\n\nMarket closed of U.S. stock market\n\nNIGHT_OPEN\n\nNight market trading hours\n\nNIGHT_END\n\nNight market closed\n\nNIGHT\n\nNight market trading hours for U.S. index options\n\nTRADE_AT_LAST\n\nLate trading hours for U.S. index options\n\nFUTURE_DAY_OPEN\n\nDay market trading hours\n\nFUTURE_DAY_BREAK\n\nDay market break\n\nFUTURE_DAY_CLOSE\n\nDay market closed\n\nFUTURE_DAY_WAIT_OPEN\n\nFutures market wait for opening\n\nHK_CAS\n\nAfter-hours bidding for HK stocks\n\nFUTURE_NIGHT_WAIT\n\nFutures night market wait for opening (Obsolete)\n\nFUTURE_AFTERNOON\n\nFutures afternoon (Obsolete)\n\nFUTURE_SWITCH_DATE\n\nWaiting for U.S. futures opening\n\nFUTURE_OPEN\n\nTrading hours of U.S. futures\n\nFUTURE_BREAK\n\nBreak of U.S. futures\n\nFUTURE_BREAK_OVER\n\nTrading hours of U.S. futures after break\n\nFUTURE_CLOSE\n\nMarket closed of U.S. futures\n\nSTIB_AFTER_HOURS_WAIT\n\nAfter-hours matching period on the Sci-tech innovation plate (Obsolete)\n\nSTIB_AFTER_HOURS_BEGIN\n\nAfter-hours trading on the Sci-tech innovation plate begins (Obsolete)\n\nSTIB_AFTER_HOURS_END\n\nAfter-hours trading on the Sci-tech innovation plate ends (Obsolete)\n\nQotRight\n\nUNKNOW\n\nUnknown\n\nBMP\n\nBMP (subscription is not supported for this permission)\n\nLEVEL1\n\nLevel1\n\nLEVEL2\n\nLevel2\n\nSF\n\nHK Securities FullTick Quotes\n\nNO\n\nNo permission\n\nSecurityReferenceType\n\nUNKNOW\n\nUnknown\n\nWARRANT\n\nWarrants for stocks\n\nFUTURE\n\nContracts related to futures main\n\nAuType\n\nNONE\n\nActual\n\nQFQ\n\nAdjust forward\n\nHFQ\n\nAdjust backward\n\nSecurityStatus\n\nNONE\n\nUnknown\n\nNORMAL\n\nNormal status\n\nLISTING\n\nTo be listed\n\nPURCHASING\n\nPurchasing\n\nSUBSCRIBING\n\nSubscribing\n\nBEFORE_DRAK_TRADE_OPENING\n\nBefore the grey market trading opens\n\nDRAK_TRADING\n\nOngoing grey market trading\n\nDRAK_TRADE_END\n\nGrey market trading closed\n\nTO_BE_OPEN\n\nTo be open\n\nSUSPENDED\n\nSuspended\n\nCALLED\n\nCalled\n\nEXPIRED_LAST_TRADING_DATE\n\nExpired latest trading date\n\nEXPIRED\n\nExpired\n\nDELISTED\n\nDelisted\n\nCHANGE_TO_TEMPORARY_CODE\n\nDuring the company action, the trading was closed and transferred to the temporary code trading\n\nTEMPORARY_CODE_TRADE_END\n\nTemporary trading ends\n\nCHANGED_PLATE_TRADE_END\n\nPlate changed, the old code is not available for trading\n\nCHANGED_CODE_TRADE_END\n\nThe code has been changed, the old code is not available for trading\n\nRECOVERABLE_CIRCUIT_BREAKER\n\nRecoverable circuit breaker\n\nUN_RECOVERABLE_CIRCUIT_BREAKER\n\nUnrecoverable circuit breaker\n\nAFTER_COMBINATION\n\nAfter-hours matchmaking\n\nAFTER_TRANSATION\n\nAfter-hours trading\n\nSecurityType\n\nNONE\n\nUnknown\n\nBOND\n\nBonds\n\nBWRT\n\nBlanket warrants\n\nSTOCK\n\nStocks\n\nETF\n\nETFs\n\nWARRANT\n\nWarrants\n\nIDX\n\nIndexs\n\nPLATE\n\nPlates\n\nDRVT\n\nOptions\n\nPLATESET\n\nPlate sets\n\nFUTURE\n\nFutures\n\nSetPriceReminderOp\n\nNONE\n\nUnknown\n\nADD\n\nAdd\n\nDEL\n\nDelete\n\nENABLE\n\nEnable\n\nDISABLE\n\nDisable\n\nMODIFY\n\nModify\n\nDEL_ALL\n\nDelete all (delete all price alerts under the specified stock)\n\nSortDir\n\nNONE\n\nNot sorted\n\nASCEND\n\nAscending\n\nDESCEND\n\nDescending\n\nSortField\n\nNONE\n\nUnknown\n\nCODE\n\nCode\n\nCUR_PRICE\n\nLatest price\n\nPRICE_CHANGE_VAL\n\nPrice changed\n\nCHANGE_RATE\n\nYield\n\nSTATUS\n\nStatus\n\nBID_PRICE\n\nBid price\n\nASK_PRICE\n\nAsk price\n\nBID_VOL\n\nBid volume\n\nASK_VOL\n\nAsk volume\n\nVOLUME\n\nVolume\n\nTURNOVER\n\nTurnover\n\nAMPLITUDE\n\nAmplitude\n\nSCORE\n\nComprehensive score\n\nPREMIUM\n\nPremium\n\nEFFECTIVE_LEVERAGE\n\nEffective leverage\n\nDELTA\n\nHedging value \n\nIMPLIED_VOLATILITY\n\nImplied volatility \n\nTYPE\n\nType\n\nSTRIKE_PRICE\n\nStrike price\n\nBREAK_EVEN_POINT\n\nBreak even point\n\nMATURITY_TIME\n\nMaturity date\n\nLIST_TIME\n\nListing date\n\nLAST_TRADE_TIME\n\nLastest trading day\n\nLEVERAGE\n\nLeverage ratio\n\nIN_OUT_MONEY\n\nIn/out of the money %\n\nRECOVERY_PRICE\n\nRecovery price \n\nCHANGE_PRICE\n\nChange price\n\nCHANGE\n\nChange ratio\n\nSTREET_RATE\n\nOutstanding percentage (the propotioin of retail investors)\n\nSTREET_VOL\n\nOutstanding quantity (the volume held by retail investors)\n\nWARRANT_NAME\n\nWarrant name\n\nISSUER\n\nIssuer\n\nLOT_SIZE\n\nLot size\n\nISSUE_SIZE\n\nIssue size\n\nUPPER_STRIKE_PRICE\n\nUpper bound \n\nLOWER_STRIKE_PRICE\n\nLower bound \n\nINLINE_PRICE_STATUS\n\nIn/out of bounds \n\nPRE_CUR_PRICE\n\nLatest price of pre-market\n\nAFTER_CUR_PRICE\n\nLatest price of after-hours\n\nPRE_PRICE_CHANGE_VAL\n\nPre-market changes\n\nAFTER_PRICE_CHANGE_VAL\n\nAfter-hours changes\n\nPRE_CHANGE_RATE\n\nPre-market change rate %\n\nAFTER_CHANGE_RATE\n\nAfter-hours change rate %\n\nPRE_AMPLITUDE\n\nPre-market amplitude %\n\nAFTER_AMPLITUDE\n\nAfter-hours amplitude %\n\nPRE_TURNOVER\n\nPre-market turnover\n\nAFTER_TURNOVER\n\nAfter-hours turnover\n\nLAST_SETTLE_PRICE\n\nLast settle price\n\nPOSITION\n\nPosition\n\nPOSITION_CHANGE\n\nDaily increase of position\n\nStockField\n\nNONE\n\nunknown\n\nSTOCK_CODE\n\nStock code, does not accept list inputs as an interval\n\nSTOCK_NAME\n\nStock name, does not accept list inputs as an interval\n\nCUR_PRICE\n\nThe latest price \n\nCUR_PRICE_TO_HIGHEST52_WEEKS_RATIO\n\n(CP - WH52) / WH52\nCP: Current price\nWH52: 52-week high\nCorresponding to the “percentage from 52-week high” on the PC terminal \n\nCUR_PRICE_TO_LOWEST52_WEEKS_RATIO\n\n(CP - WL52) / WL52\nCP: Current price\nWL52: 52-week low\nCorresponding to the “percentage from 52-week low” on the PC terminal \n\nHIGH_PRICE_TO_HIGHEST52_WEEKS_RATIO\n\n(TH - WH52) / WH52\nTH: Today's high\nWH52: 52-week high\n\n\nLOW_PRICE_TO_LOWEST52_WEEKS_RATIO\n\n(TL - WL52) / WL52\nTL: Today's low\nWL52: 52-week low\n\n\nVOLUME_RATIO\n\nVolume ratio \n\nBID_ASK_RATIO\n\nBid-ask ratio \n\nLOT_PRICE\n\nPrice per lot \n\nMARKET_VAL\n\nMarket value \n\nPE_ANNUAL\n\nTrailing P/E \n\nPE_TTM\n\nP/E TTM \n\nPB_RATE\n\nP/B ratio \n\nCHANGE_RATE_5MIN\n\nChange rate in 5 minutes \n\nCHANGE_RATE_BEGIN_YEAR\n\nPrice change rate from this year \n\nPS_TTM\n\nP/S TTM \n\nPCF_TTM\n\nPCF TTM \n\nTOTAL_SHARE\n\nTotal number of shares \n\nFLOAT_SHARE\n\nShares outstanding \n\nFLOAT_MARKET_VAL\n\nMarket value outstanding \n\nSubType\n\nNONE\n\nUnknown\n\nQUOTE\n\nBasic quote\n\nORDER_BOOK\n\nOrder book\n\nTICKER\n\nTick-by-tick\n\nRT_DATA\n\nTime Frame\n\nK_DAY\n\nDaily candlesticks\n\nK_5M\n\n5 minutes candlesticks\n\nK_15M\n\n15 minutes candlesticks\n\nK_30M\n\n30 minutes candlesticks\n\nK_60M\n\n60 minutes candlesticks\n\nK_1M\n\n1 minute candlesticks\n\nK_WEEK\n\nWeekly candlesticks\n\nK_MON\n\nMonthly candlesticks\n\nBROKER\n\nBroker's queue\n\nK_QURATER\n\nSeasonal candlesticks\n\nK_YEAR\n\nAnnual candlesticks\n\nK_3M\n\n3 minutes candlesticks\n\nTickerDirect\n\nNONE\n\nunknown\n\nBUY\n\nActive buy \n\nSELL\n\nActive sell \n\nNEUTRAL\n\nNeutral transaction \n\nTickerType\n\nUNKNOWN\n\nUnknown\n\nAUTO_MATCH\n\nRegular sale\n\nLATE\n\nPre-market trade\n\nNON_AUTO_MATCH\n\nNon-regular sale\n\nINTER_AUTO_MATCH\n\nRegular sale for same broker\n\nINTER_NON_AUTO_MATCH\n\nNon-regular sale for same broker\n\nODD_LOT\n\nOdd lot trade\n\nAUCTION\n\nAuction trade\n\nBULK\n\nBunched trade\n\nCRASH\n\nCash trade\n\nCROSS_MARKET\n\nIntermarket sweep\n\nBULK_SOLD\n\nBunched sold trade\n\nFREE_ON_BOARD\n\nPrice variation trade\n\nRULE127_OR155\n\nRule 127 (NYSE only) or Rule 155 (NYSE MKT only)\n\nDELAY\n\nDelay the transaction\n\nMARKET_CENTER_CLOSE_PRICE\n\nMarket center close price\n\nNEXT_DAY\n\nNext day\n\nMARKET_CENTER_OPENING\n\nMarket center opening trade\n\nPRIOR_REFERENCE_PRICE\n\nPrior reference price\n\nMARKET_CENTER_OPEN_PRICE\n\nMarket center open price\n\nSELLER\n\nSeller\n\nT\n\nForm T(pre-open and post-close market trade)\n\nEXTENDED_TRADING_HOURS\n\nExtended trading hours/sold out of sequence\n\nCONTINGENT\n\nContingent trade\n\nAVERAGE_PRICE\n\nAverage price trade\n\nOTC_SOLD\n\nSold(out of sequence)\n\nODD_LOT_CROSS_MARKET\n\nOdd lot cross trade\n\nDERIVATIVELY_PRICED\n\nDerivatively priced\n\nREOPENINGP_RICED\n\nRe-Opening price\n\nCLOSING_PRICED\n\nClosing price\n\nCOMPREHENSIVE_DELAY_PRICE\n\nConsolidated late price per listing packet\n\nOVERSEAS\n\nOne party to the transaction is not a member of the Hong Kong Stock Exchange and is an over-the-counter transaction\n\nTradeDateMarket\n\nNONE\n\nUnknown\n\nHK\n\nHK market \n\nUS\n\nUS market \n\nCN\n\nA-share market\n\nNT\n\nNorthbound Trading\n\nST\n\nSouthbound Trading\n\nJP_FUTURE\n\nJapanese future market\n\nSG_FUTURE\n\nSingapore future market\n\nTradeDateType\n\nWHOLE\n\nWhole day trading\n\nMORNING\n\nTrading in the morning, closed in the afternoon\n\nAFTERNOON\n\nTrading in the afternoon, closed in the morning\n\nWarrantStatus\n\nNONE\n\nUnknown\n\nNORMAL\n\nNormal status\n\nSUSPEND\n\nSuspended\n\nSTOP_TRADE\n\nStop trading\n\nPENDING_LISTING\n\nWaiting to be listed\n\nWrtType\n\nNONE\n\nUnknown\n\nCALL\n\nLong warrants\n\nPUT\n\nShort warrants\n\nBULL\n\nCall warrants\n\nBEAR\n\nPut warrants\n\nINLINE\n\nInline Warrants\n\nExchType\n\nNONE\n\nUnknown\n\nHK_MAINBOARD\n\nHKEx·Main Board\n\nHK_GEMBOARD\n\nHKEx·GEM\n\nHK_HKEX\n\nHKEx\n\nUS_NYSE\n\nNYSE\n\nUS_NASDAQ\n\nNASDAQ\n\nUS_PINK\n\nOTC Mkt\n\nUS_AMEX\n\nAMEX\n\nUS_OPTION\n\nUS \n\nUS_NYMEX\n\nNYMEX\n\nUS_COMEX\n\nCOMEX\n\nUS_CBOT\n\nCBOT\n\nUS_CME\n\nCME\n\nUS_CBOE\n\nCBOE\n\nCN_SH\n\nSH Stock Ex\n\nCN_SZ\n\nSZ Stock Ex\n\nCN_STIB\n\nSTAR\n\nSG_SGX\n\nSGX\n\nJP_OSE\n\nOSE\n\nSecurity\n\nmessage Security\n{\n    required int32 market = 1; //QotMarket, quote market\n    required string code = 2; //Code\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\nKLine\n\nmessage KLine\n{\n    required string time = 1; //String of timestamp (Format: yyyy-MM-dd HH:mm:ss)\n    required bool isBlank = 2; //Whether it is a point with empty content, if it is true, only time information\n    optional double highPrice = 3; //High\n    optional double openPrice = 4; //Open\n    optional double lowPrice = 5; //Low\n    optional double closePrice = 6; //Close\n    optional double lastClosePrice = 7; //Close yesterday\n    optional int64 volume = 8; //Volume\n    optional double turnover = 9; //Turnover\n    optional double turnoverRate = 10; // Turnover rate (this field is in decimal form, so 0.2 is equivalent to 20%)\n    optional double pe = 11; //P/E ratio\n    optional double changeRate = 12; //Yield (This field is in percentage form, so 20 is equivalent to 20%.)\n    optional double timestamp = 13; //Timestamp\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\n\nOptionBasicQotExData\n\nmessage OptionBasicQotExData\n{\n    required double strikePrice = 1; //Strike price\n    required int32 contractSize = 2; //Contract size (integer)\n    optional double contractSizeFloat = 17; //Contract size (float)\n    required int32 openInterest = 3; //Number of open positions\n    required double impliedVolatility = 4; //Implied volatility (This field is in percentage form, so 20 is equivalent to 20%.)\n    required double premium = 5; //Premium (This field is in percentage form, so 20 is equivalent to 20%.)\n    required double delta = 6; //Greek value Delta\n    required double gamma = 7; //Greek value Gamma\n    required double vega = 8; //Greek value Vega\n    required double theta = 9; //Greek value Theta\n    required double rho = 10; //Greek value Rho\n    optional int32 netOpenInterest = 11; //Net open contract number , only HK options support this field\n    optional int32 expiryDateDistance = 12; //The number of days from the expiry date, a negative number means it has expired.\n    optional double contractNominalValue = 13; //Contract nominal amount , only HK options support this field\n    optional double ownerLotMultiplier = 14; //Equal number of underlying stocks, index options do not have this field , only HK options support this field  \n    optional int32 optionAreaType = 15; //OptionAreaType, option type (by exercise time).\n    optional double contractMultiplier = 16; //Contract multiplier\n    optional int32 indexOptionType = 18; //Qot_Common.IndexOptionType, index option type\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\n\nFutureBasicQotExData\n\nmessage FutureBasicQotExData\n{\n    required double lastSettlePrice = 1; //Close yesterday\n    required int32 position = 2; //Hold position\n    required int32 positionChange = 3; //Daily change in position\n    optional int32 expiryDateDistance = 4; //The number of days from the expiration date\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n\nBasicQot\n\nmessage BasicQot\n{\n    required Security security = 1; //Stock\n    optional string name = 24; // stock name\n    required bool isSuspended = 2; //whether trading is suspended\n    required string listTime = 3; //listed date string (This field is deprecated. Format: yyyy-MM-dd)\n    required double priceSpread = 4; //Spread\n    required string updateTime = 5; //Update time string of the latest price (Format: yyyy-MM-dd HH:mm:ss), not applicable to other fields\n    required double highPrice = 6; //High\n    required double openPrice = 7; //Open\n    required double lowPrice = 8; //low\n    required double curPrice = 9; //The latest price\n    required double lastClosePrice = 10; //Close yesterday\n    required int64 volume = 11; //Volume\n    required double turnover = 12; //Turnover\n    required double turnoverRate = 13; //Turnover rate (This field is in percentage form, so 20 is equivalent to 20%.)\n    required double amplitude = 14; //Amplitude (This field is in percentage form, so 20 is equivalent to 20%.)\n    optional int32 darkStatus = 15; //Grey market trading status\n    optional OptionBasicQotExData optionExData = 16; //Option specific field\n    optional double listTimestamp = 17; //Time stamp of listing date (This field is deprecated.)\n    optional double updateTimestamp = 18; //Update timestamp of the latest price, not applicable to other fields\n    optional PreAfterMarketData preMarket = 19; //Pre-market data\n    optional PreAfterMarketData afterMarket = 20; //After-hours data\n    optional int32 secStatus = 21; //Security status\n    optional FutureBasicQotExData futureExData = 22; //Futures specific field\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n\n\nPreAfterMarketData\n\n//US stocks support pre-market and after-hours data\n//The Sci-tech Innovation Plate only supports after-hours data: trading volume, turnover\nmessage PreAfterMarketData\n{\n    optional double price = 1; //Pre-market or after-hours## Price\n    optional double highPrice = 2; //Pre-market or after-hours## High\n    optional double lowPrice = 3; //Pre-market or after-hours## Low\n    optional int64 volume = 4; //Pre-market or after-hours## Volume\n    optional double turnover = 5; //Pre-market or after-hours## Turnover\n    optional double changeVal = 6; //Pre-market or after-hours## Change in price\n    optional double changeRate = 7; //Pre-market or after-hours## Yield (This field is in percentage form, so 20 is equivalent to 20%.)\n    optional double amplitude = 8; //Pre-market or after-hours## Amplitude (This field is in percentage form, so 20 is equivalent to 20%.)\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\n\nTimeShare\n\nmessage TimeShare\n{\n    required string time = 1; //Time string (Format: yyyy-MM-dd HH:mm:ss)\n    required int32 minute = 2; //Minutes after 0 o'clock\n    required bool isBlank = 3; //Whether the content is empty, if true, it contents only time\n    optional double price = 4; //Current price\n    optional double lastClosePrice = 5; //Close yesterday\n    optional double avgPrice = 6; //Average price\n    optional int64 volume = 7; //Volume\n    optional double turnover = 8; //Turnover\n    optional double timestamp = 9; //Timestamp\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n\nSecurityStaticBasic\n\n\nmessage SecurityStaticBasic\n{\n    required Qot_Common.Security security = 1; //Stock\n    required int64 id = 2; //Stock ID\n    required int32 lotSize = 3; //Lot size, the option type represents the number of shares in a contract\n    required int32 secType = 4; //Qot_Common.SecurityType, stock type\n    required string name = 5; //Stock name\n    required string listTime = 6; //Listing time string (This field is deprecated. Format: yyyy-MM-dd)\n    optional bool delisting = 7; //Delisted or not\n    optional double listTimestamp = 8; //Listing timestamp (This field is deprecated.)\n    optional int32 exchType = 9; //Qot_Common.ExchType, Exchange Type\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\n\nWarrantStaticExData\n\nmessage WarrantStaticExData\n{\n    required int32 type = 1; //Qot_Common.WarrantType, Warrant Type\n    required Qot_Common.Security owner = 2; //The underlying stock\n}   \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\nOptionStaticExData\n\nmessage OptionStaticExData\n{\n    required int32 type = 1; //Qot_Common.OptionType, option\n    required Qot_Common.Security owner = 2; //Underlying stock\n    required string strikeTime = 3; //Exercise date (Format: yyyy-MM-dd)\n    required double strikePrice = 4; //Strike price\n    required bool suspend = 5; //Suspended or not\n    required string market = 6; //Issuance market name\n    optional double strikeTimestamp = 7; //Exercise date timestamp\n    optional int32 indexOptionType = 8; //Qot_Common.IndexOptionType, type of index option, only valid for index option\n}   \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n\nFutureStaticExData\n\nmessage FutureStaticExData\n{\n    required string lastTradeTime = 1; //The lastest trading day, only future non-main contracts have this field\n    optional double lastTradeTimestamp = 2; //The lastest trading day timestamp, only future non-main contracts have this field\n    required bool isMainContract = 3; //Futures main contract or not\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\nSecurityStaticInfo\n\nmessage SecurityStaticInfo\n{\n    required SecurityStaticBasic basic = 1; //Basic security information\n    optional WarrantStaticExData warrantExData = 2; //Additional information for warrants\n    optional OptionStaticExData optionExData = 3; //Additional information for options\n    optional FutureStaticExData futureExData = 4; //Additional information for futures\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n\nBroker\n\nmessage Broker\n{\n    required int64 id = 1; //Broker ID\n    required string name = 2; //Broker name\n    required int32 pos = 3; //Broker position\n  \n    //The following fields are specific to SF quote\n    optional int64 orderID = 4; //Exchange order ID, which is different from the order ID returned by the trading interface\n    optional int64 volume = 5; //Number of shares in order\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\nTicker\n\nmessage Ticker\n{\n    required string time = 1; //Time string (Format: yyyy-MM-dd HH:mm:ss)\n    required int64 sequence = 2; //Unique identification\n    required int32 dir = 3; //TickerDirection, buy or sell direction\n    required double price = 4; //Price\n    required int64 volume = 5; //Volume\n    required double turnover = 6; // turnover\n    optional double recvTime = 7; //Local timestamp of received push data, used to locate delay\n    optional int32 type = 8; //TickerType, type by pen\n    optional int32 typeSign = 9; //Pattern-by-stroke type sign\n    optional int32 pushDataType = 10; //Used to distinguish push situations, this field is only available when pushing\n    optional double timestamp = 11; //time stamp}\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n\n\nOrderBookDetail\n\nmessage OrderBookDetail\n{\n    required int64 orderID = 1; //Exchange order ID, which is different from the order ID returned by the trading interface\n    required int64 volume = 2; //Number of shares in order\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\nOrderBook\n\nmessage OrderBook\n{\n    required double price = 1; //Order price\n    required int64 volume = 2; //Order quantity\n    required int32 orederCount = 3; //Number of commissioned orders\n    repeated OrderBookDetail detailList = 4; //Order information, unique to HK SF, US LV2 market\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n\nShareHoldingChange\n\nmessage ShareHoldingChange\n{\n    required string holderName = 1; //Holder name (institution name or fund name or executive name)\n    required double holdingQty = 2; //Current number of holdings\n    required double holdingRatio = 3; //Current shareholding percentage (This field is in percentage form, so 20 is equivalent to 20%.)\n    required double changeQty = 4; //The number of changes from the previous time\n    required double changeRatio = 5; //The percentage of change from the last time (This field is in percentage form, so 20 is equivalent to 20%.. It is the ratio relative to itself, not to total. For example, if the total share capital is 10,000 shares, holding 100 shares, the shareholding percentage is 1%, if 50 shares are sold, the change ratio is 50% instead of 0.5%)\n    required string time = 6; //Release time (Format: yyyy-MM-dd HH:mm:ss)\n    optional double timestamp = 7; //Timestamp\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\nSubInfo\n\nmessage SubInfo\n{\n    required int32 subType = 1; //Qot_Common.SubType, subscription type\n    repeated Qot_Common.Security securityList = 2; //Subscribe to securities of this type of market\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\nConnSubInfo\n\nmessage ConnSubInfo\n{\n    repeated SubInfo subInfoList = 1; //The connection subscription information\n    required int32 usedQuota = 2; //The subscription quota that the connection has used\n    required bool isOwnConnData = 3; //Used to distinguish whether it is self-connected data\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\nPlateInfo\n\nmessage PlateInfo\n{\n    required Qot_Common.Security plate = 1; //Plate\n    required string name = 2; //Plate name\n    optional int32 plateType = 3; //Plate type, only 3207 (to get the plate to which the stock belongs) agreement returns this field\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\nRehab\n\nmessage Rehab\n{\n    required string time = 1; //Time string (Format: yyyy-MM-dd)\n    required int64 companyActFlag = 2; //CompanyAct combination flag bit, which specifies whether certain field values ​​are valid\n    required double fwdFactorA = 3; //Adjustments factor A\n    required double fwdFactorB = 4; //Adjustments factor B\n    required double bwdFactorA = 5; //Adjustments factor A\n    required double bwdFactorB = 6; //Adjustments factor B\n    optional int32 splitBase = 7; //Stock split (for example, 1 split 5, Base is 1, Ert is 5)\n    optional int32 splitErt = 8;\n    optional int32 joinBase = 9; //Reverse stock split (for example, 50 in 1, Base is 50, Ert is 1)\n    optional int32 joinErt = 10;\n    optional int32 bonusBase = 11; //Bonus shares (for example, 10 free 3, Base is 10, Ert is 3)\n    optional int32 bonusErt = 12;\n    optional int32 transferBase = 13; //Transfer bonus shares (for example, 10 to 3, Base is 10, Ert is 3)\n    optional int32 transferErt = 14;\n    optional int32 allotBase = 15; //Allotment (for example, 10 get 2 free, allotment price is 6.3 yuan, Base is 10, Ert is 2, and Price is 6.3)\n    optional int32 allotErt = 16;\n    optional double allotPrice = 17;\n    optional int32 addBase = 18; //Additional shares (for example, 10 get 2 free, additional issuance price is 6.3 yuan, Base is 10, Ert is 2, and Price is 6.3)\n    optional int32 addErt = 19;\n    optional double addPrice = 20;\n    optional double dividend = 21; //Cash dividend (for example, if every 10 shares are paid out 0.5 yuan, the field value is 0.05)\n    optional double spDividend = 22; //Special dividend (for example, if a special dividend is 0.5 yuan for every 10 shares, the value of this field is 0.05)\n    optional double timestamp = 23; //Timestamp\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n\nFor CompanyAct combination flag bit, refer to CompanyAct.\n\nExpirationCycle\n\nNONE\n\nUnknown\n\nWEEK\n\nWeekly options\n\nMONTH\n\nMonthly options\n\nStockHolder\n\nNONE\n\nUnknown\n\nINSTITUTE\n\nInstitute\n\nFUND\n\nFund\n\nEXECUTIVE\n\nExecutives\n\n← Price Reminder Callback\nOverview →\n\nQuotation Definitions"}, {"title": "Basic Functions | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/ftapi/init.html#8418", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nBasic Functions\nGeneral Definitions\nProtocol Introduction\n\nQ&A \n\n#\nBasic Functions\n#\nSet Interface Information(deprecated)\n\nset_client_info(client_id, client_ver)\n\nIntroduction\n\nSet calling interface information (unnecessary).\n\nParameters\n\nclient_id: the identification of the client\nclient_ver: the version number of the client\nExample\nfrom futu import *\nSysConfig.set_client_info(\"MyFutuAPI\", 0)\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n#\nSet Protocol Format\n\nset_proto_fmt(proto_fmt)\n\nIntroduction\n\nSet the communication protocol body format, Protobuf and Json formats are currently supported , default ProtoBuf, unnecessary interface\n\nParameters\n\nproto_fmt: protocol format, refer to ProtoFMT\nExample\nfrom futu import *\nSysConfig.set_proto_fmt(ProtoFMT.Protobuf)\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n#\nSet Protocol Encryption Globally\n\nEnable_proto_encrypt(is_encrypt)\n\nIntroduction Setting protocol encryption can help users protect their requests and returned contents globally. For more information about Protocol Encryption Process, please check here.\n\nParameters\n\nParameter\tType\tDescription\nis_encrypt\tbool\tEnable encryption or not.\nExample\nfrom futu import *\nSysConfig.enable_proto_encrypt(True)\nSysConfig.set_init_rsa_file(\"conn_key.txt\")   # rsa private key file path\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\nSet the Path of Private Key\n\nset_init_rsa_file(file)\n\nIntroduction\n\nSet the Path of Private Key in Futu API. For more information about Protocol Encryption Process, please check here.\n\nParameters\n\nParameter\tType\tDescription\nfile\tstr\tPrivate key file path.\n\nExample\n\nfrom futu import *\nSysConfig.enable_proto_encrypt(True)\nSysConfig.set_init_rsa_file(\"conn_key.txt\")   # rsa private key file path\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.close()\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\nSet Thread Mode\n\nset_all_thread_daemon(all_daemon)\n\nIntroduction Whether to set all internally threads to be daemon threads.\n\nIf it is set to be daemon threads, then after the main thread exits, the process also exits.\nFor example, when using the real-time callback API, you need to make sure the main thread survives by yourself. Otherwise, when the main thread exits, the process also exits and you will no longer receive the push data.\nIf it is set to a non-daemon thread, the process will not exit after the main thread exits. For example, if you do not call close() to close the connection after creating a quote or trade object, the process will not exit even if the main thread exits.\n\nParameters\n\nParameter\tType\tDescription\nall_daemon\tbool\tWhether to set threads to be daemon threads. \nExample\nfrom futu import *\nSysConfig.set_all_thread_daemon(True)\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n# the process will exit without calling quote_ctx.close(), \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n#\nSet Callback\n\nset_handler(handler)\n\nIntroduction\n\nSet asynchronous callback processing object\n\nParameters\n\nhandler: callback processing object\nClass\tDescription\nSysNotifyHandlerBase\tOpenD notification processing base class\nStockQuoteHandlerBase\tQuote processing base class\nOrderBookHandlerBase\tOrder book processing base class\nCurKlineHandlerBase\tReal-time candlestick processing base class\nTickerHandlerBase\tTick-By-Tick processing base class\nRTDataHandlerBase\tTime Frame data processing base class\nBrokerHandlerBase\tBroker queue processing base class\nPriceReminderHandlerBase\tPrice reminder processing base class\nTradeOrderHandlerBase\tOrder processing base class\nTradeDealHandlerBase\tOrder fill processing base class\n\nExample\n\nimport time\nfrom futu import *\nclass OrderBookTest(OrderBookHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, data = super(OrderBookTest,self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"OrderBookTest: error, msg: %s\" % data)\n            return RET_ERROR, data\n        print(\"OrderBookTest \", data) # OrderBookTest's own processing logic\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = OrderBookTest()\nquote_ctx.set_handler(handler) # Setting real-time order book callback\nquote_ctx.subscribe(['HK.00700'], [SubType.ORDER_BOOK]) # Subscribe to the order book type, OpenD starts to receive pushed data from the server continuously\ntime.sleep(15) # Set the script to receive OpenD push duration to 15 seconds\nquote_ctx.close() # Close the current connection, OpenD will automatically cancel the subscription of the corresponding stock in 1 minute\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\n#\nGet Connection ID\n\nget_sync_conn_id()\n\nIntroduction\n\nGet the connection ID, the value will be available after the connection is successfully initialized\n\nReturn\n\nconn_id: connection ID\nExample\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.get_sync_conn_id()\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n#\nEvent Notification Callback\n\nSysNotifyHandlerBase\n\nIntroduction\n\nNotify OpenD of some important news, such as disconnection, etc.\n\nProtocol ID\n\n1003\n\nReturn\n\nField\tType\tDescription\nret\tint\tReturned value. On success, ret == RET_OK. On error, ret != RET_OK.\ndata\ttuple\tIf ret == RET_OK, event notification data is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nThe format of event notification data is as follows:\nField\tType\tDescription\nnotify_type\tSysNotifyType\tNotification data type\nsub_type\tProgramStatusType\tSubtype. If notify_type == SysNotifyType.PROGRAM_STATUS, program status type is returned.\nGtwEventType\tSubtype. If notify_type == SysNotifyType.GTW_EVENT, OpenD event type is returned.\n0\tIf notify_type !=SysNotifyType.PROGRAM_STATUS and notify_type !=SysNotifyType.GTW_EVENT, no useful information is returned.\nmsg\tdict\tEvent information. If notify_type == SysNotifyType.CONN_STATUS, connection status event information is returned.\nEvent information. If notify_type == SysNotifyType.QOT_RIGHT, quote right event information is returned.\n\n\n\nThe format of connection status event information is as follows(The value of connection status is a bool type, with True for normal, and False for disconnected):\n{\n    'qot_logined': bool1, \n    'trd_logined': bool2,\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\nThe format of quote right event information is as follows(the type of quote right refers to Quote Right):\n{\n    'hk_qot_right': value1,\n    'hk_option_qot_right': value2,\n    'hk_future_qot_right': value3,\n    'us_qot_right': value4,\n    'us_option_qot_right': value5,\n    'us_future_qot_right': value6,  // deprecated\n    'cn_qot_right': value7,\n\t'us_index_qot_right': value8,\n\t'us_otc_qot_right': value9,\n\t'sg_future_qot_right': value10,\n\t'jp_future_qot_right': value11,\n\t'us_future_qot_right_cme': value12,\n\t'us_future_qot_right_cbot': value13,\n\t'us_future_qot_right_nymex': value14,\n\t'us_future_qot_right_comex': value15,\n\t'us_future_qot_right_cboe': value16,\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nExample\nimport time\nfrom futu import *\n\nclass SysNotifyTest(SysNotifyHandlerBase):\n    def on_recv_rsp(self, rsp_str):\n        ret_code, data = super(SysNotifyTest, self).on_recv_rsp(rsp_str)\n        notify_type, sub_type, msg = data\n        if ret_code != RET_OK:\n            logger.debug(\"SysNotifyTest: error, msg: {}\".format(msg))\n            return RET_ERROR, data\n        if (notify_type == SysNotifyType.GTW_EVENT):  #  OpenD event notification\n            print(\"GTW_EVENT, type: {} msg: {}\".format(sub_type, msg))\n        elif (notify_type == SysNotifyType.PROGRAM_STATUS):  # Notification of change in program status\n            print(\"PROGRAM_STATUS, type: {} msg: {}\".format(sub_type, msg))\n        elif (notify_type == SysNotifyType.CONN_STATUS):  ## Notification of change in connection status\n            print(\"CONN_STATUS, qot: {}\".format(msg['qot_logined']))\n            print(\"CONN_STATUS, trd: {}\".format(msg['trd_logined']))\n        elif (notify_type == SysNotifyType.QOT_RIGHT):  # Notification of change in quote right\n            print(\"QOT_RIGHT, hk: {}\".format(msg['hk_qot_right']))\n            print(\"QOT_RIGHT, hk_option: {}\".format(msg['hk_option_qot_right']))\n            print(\"QOT_RIGHT, hk_future: {}\".format(msg['hk_future_qot_right']))\n            print(\"QOT_RIGHT, us: {}\".format(msg['us_qot_right']))\n            print(\"QOT_RIGHT, us_option: {}\".format(msg['us_option_qot_right']))\n            print(\"QOT_RIGHT, us_future: {}\".format(msg['us_future_qot_right']))\n            print(\"QOT_RIGHT, cn: {}\".format(msg['cn_qot_right']))\n            print(\"QOT_RIGHT, us_index: {}\".format(msg['us_index_qot_right']))\n\t\t\tprint(\"QOT_RIGHT, us_otc: {}\".format(msg['us_otc_qot_right']))\n\t\t\tprint(\"QOT_RIGHT, sg_future: {}\".format(msg['sg_future_qot_right']))\n\t\t\tprint(\"QOT_RIGHT, jp_future: {}\".format(msg['jp_future_qot_right']))\n            print(\"QOT_RIGHT, us_future_cme: {}\".format(msg['us_future_qot_right_cme']))\n            print(\"QOT_RIGHT, us_future_cbot: {}\".format(msg['us_future_qot_right_cbot']))\n            print(\"QOT_RIGHT, us_future_nymex: {}\".format(msg['us_future_qot_right_nymex']))\n            print(\"QOT_RIGHT, us_future_comex: {}\".format(msg['us_future_qot_right_comex']))\n            print(\"QOT_RIGHT, us_future_cboe: {}\".format(msg['us_future_qot_right_cboe']))\n        return RET_OK, data\n\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = SysNotifyTest()\nquote_ctx.set_handler(handler)   # Set real-time swing callback\ntime.sleep(15)  # Set the script to receive OpenD push duration to 15 seconds\nquote_ctx.close()  # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n\n\n← Trading Definitions\nGeneral Definitions →\n\nBasic Functions\nSet Interface Information(deprecated)\nSet Protocol Format\nSet Protocol Encryption Globally\nSet the Path of Private Key\nSet Thread Mode\nSet Callback\nGet Connection ID\nEvent Notification Callback"}, {"title": "General Definitions | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/ftapi/common.html#8800", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nBasic Functions\nGeneral Definitions\nProtocol Introduction\n\nQ&A \n\n#\nGeneral Definitions\n#\nInterface Result\n\nRET_CODE\n\nRET_OK\n\nSuccess\n\nRET_ERROR\n\nFailed\n\n#\nProtocol Format\n\nProtoFMT\n\nProtobuf\n\nGoogle Protobuf\n\nJson\n\nJson\n\n#\nPacket Encryption Algorithm\n#\nProgram Status Type\n\nProgramStatusType\n\nNONE\n\nUnknown\n\nLOADED\n\nThe necessary modules have been loaded\n\nLOGING\n\nLogging in\n\nNEED_PIC_VERIFY_CODE\n\nNeed graphic verification code\n\nNEED_PHONE_VERIFY_CODE\n\nNeed mobile phone verification code\n\nLOGIN_FAILED\n\nLogin failed\n\nFORCE_UPDATE\n\nThe client version is too low\n\nNESSARY_DATA_PREPARING\n\nPulling necessary information\n\nNESSARY_DATA_MISSING\n\nMissing necessary information\n\nUN_AGREE_DISCLAIMER\n\nDisclaimer is not agreed\n\nREADY\n\nReady to use\n\nFORCE_LOGOUT\n\nOpenD was forced to log out\n\n#\nOpenD Event Notification Type\n\nGtwEventType\n\nLocalCfgLoadFailed\n\nFailed to load the local configuration file\n\nAPISvrRunFailed\n\nFailed to run the OpenD monitoring service\n\nForceUpdate\n\nForce upgrade of the OpenD\n\nLoginFailed\n\nFailed to log in to Futu servers\n\nUnAgreeDisclaimer\n\nDid not agree to the disclaimer, unable to run\n\nLOGIN_FAILED\n\nLogin failed\n\nNetCfgMissing\n\nMissing network connection configuration\n\nKickedOut\n\nLogin kicked offline\n\nLoginPwdChanged\n\nLogin password has been changed\n\nBanLogin\n\nThis account is not allowed to log in by Futu servers\n\nNeedPicVerifyCode\n\nNeed graphic verification code\n\nNeedPhoneVerifyCode\n\nNeed mobile verification code\n\nAppDataNotExist\n\nProgram package data loss\n\nNessaryDataMissing\n\nThe necessary data is not synchronized successfully\n\nTradePwdChanged\n\nTransaction password change notice\n\nEnableDeviceLock\n\nNeed to enable device lock\n\n#\nSystem Notification Type\n\nSysNotifyType\n\nGTW_EVENT\n\nGateway event\n\nPROGRAM_STATUS\n\nProgram status changes\n\nCONN_STATUS\n\nStatus of Connection to Futu servers has been changed\n\nQOT_RIGHT\n\nQuotes authority changed\n\n#\nPackage Unique Identifier\n\nPacketID\n\nmessage PacketID\n{\n  required uint64 connID = 1; //The current TCP connection ID, the unique identifier of a connection, returned by the InitConnect protocol\n  required uint32 serialNo = 2; //Increment serial number\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\nProgram Status\n\nProgramStatus\n\nmessage ProgramStatus\n{\n  required ProgramStatusType type = 1; //Current status\n  optional string strExtDesc = 2; //Additional description\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n← Basic Functions\nProtocol Introduction →\n\nGeneral Definitions\nInterface Result\nProtocol Format\nPacket Encryption Algorithm\nProgram Status Type\nOpenD Event Notification Type\nSystem Notification Type\nPackage Unique Identifier\nProgram Status"}, {"title": "Overview | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/opend/opend-intro.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nOverview\nCommand Line OpenD\nOperation Command\n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nOverview\n\nOpenD, which can be runned on your local computer or cloud server, is the gateway program of Futu API. It is responsible for transferring protocol requests to Futu servers and returning the processed data. It is a necessary prerequisite for running Futu API programs.\n\nOpenD can be runned under 4 operating systems: Windows, MacOS, CentOS and Ubuntu.\n\nYou need to log in to OpenD with your Futu ID (Futubull ID) and login password.\n\nAfter a successful login into OpenD, the socket service is started for Futu API to connect and communicate.\n\n#\nRunning Mode\n\nThere are 2 modes to run OpenD, you can choose 1 of them below:\n\nVisualisation OpenD: Provide interface applications, easy to operate, especially suitable for beginners. Please refer to Visualization OpenD for installation and operation.\nCommand Line OpenD: Provide command line execution program, which needs to be configured by yourself, which is suitable for users who are familiar with the command line or running on the server for a long time. Please refer to Command Line OpenD for installation and operation.\n#\nOperation While Running\n\nWhile OpenD is running, you can view user quota, quote right, connection status, delay statistics, and operate closing API connection, re-login, logging out etc. with Operation Command.\nFor more information, please see the following table:\n\nMethod\tVisualisation OpenD\tCommand Line OpenD\nDirect Method\tthrough the UI interface\tSend Operation Command through command line\nIndirect Medhod\tSend Operation Command through Telnet\tSend Operation Command through Telnet\n\n← Strategy Setup\nCommand Line OpenD →\n\nOverview"}, {"title": "Get Adjustment Factor | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-rehab.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Adjustment Factor\n\nget_rehab(code)\n\nDescription\n\nGet the stock adjustment factor\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, data for adjustment is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nData for adjustment format as follows:\n\nField\tType\tDescription\nex_div_date\tstr\tEx-dividend date.\nsplit_base\tfloat\tSplit numerator.\n\nsplit_ert\tfloat\tSplit dominator.\njoin_base\tfloat\tJoint numerator.\n\njoin_ert\tfloat\tJoint dominator.\nsplit_ratio\tfloat\tSplit ratio. \n\nper_cash_div\tfloat\tDividend per share.\nbounce_base\tfloat\tBounce numerator.\n\nbounce_ert\tfloat\tBounce dominator.\nper_share_div_ratio\tfloat\tBounce ratio. \n\ntransfer_base\tfloat\tConversion numerator.\n\ntransfer_ert\tfloat\tConversion dominator.\nper_share_trans_ratio\tfloat\tConversion ratio. \n\nallot_base\tfloat\tAllotment numerator.\n\nallot_ert\tfloat\tAllotment dominator.\nallotment_ratio\tfloat\tAllotment ratio. \n\nallotment_price\tfloat\tIssuance price.\nadd_base\tfloat\tAdditional issuance numerator.\n\nadd_ert\tfloat\tAdditional issuance dominator.\nstk_spo_ratio\tfloat\tAdditional issuance ratio. \n\nstk_spo_price\tfloat\tAdditional issuance price.\nforward_adj_factorA\tfloat\tForward adjustment factor A.\nforward_adj_factorB\tfloat\tForward adjustment factor B.\nbackward_adj_factorA\tfloat\tBackward adjustment factor A.\nbackward_adj_factorB\tfloat\tBackward adjustment factor B.\n\nPrice after forward adjustment = price before forward adjustment * Forward adjustment factor A + Forward adjustment factor B\nPrice after backward adjustment = price before backward adjustment * Backward adjustment factor A + Backward adjustment factor B\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_rehab(\"HK.00700\")\nif ret == RET_OK:\n    print(data)\n    print(data['ex_div_date'][0]) # Take the first ex-dividend date\n    print(data['ex_div_date'].values.tolist()) # Convert to list\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    ex_div_date  split_ratio  per_cash_div  per_share_div_ratio  per_share_trans_ratio  allotment_ratio  allotment_price  stk_spo_ratio  stk_spo_price  forward_adj_factorA  forward_adj_factorB  backward_adj_factorA  backward_adj_factorB\n0   2005-04-19          NaN          0.07                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -0.07                   1.0                  0.07\n..         ...          ...           ...                  ...                    ...              ...              ...            ...            ...                  ...                  ...                   ...                   ...\n15  2019-05-17          NaN          1.00                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -1.00                   1.0                  1.00\n\n[16 rows x 13 columns]\n2005-04-19\n['2005-04-19', '2006-05-15', '2007-05-09', '2008-05-06', '2009-05-06', '2010-05-05', '2011-05-03', '2012-05-18', '2013-05-20', '2014-05-15', '2014-05-16', '2015-05-15', '2016-05-20', '2017-05-19', '2018-05-18', '2019-05-17']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\nInterface Limitations\n\nA maximum of 60 requests per 30 seconds\n\n← Get Historical Candlesticks\nGet Option Expiration Date →\n\nGet Adjustment Factor"}, {"title": "Get Plates of Stocks | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-owner-plate.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Plates of Stocks\n\nget_owner_plate(code_list)\n\nDescription\n\nGet the information of plates to which the stocks belong\n\nParameters\n\nParameter\tType\tDescription\ncode_list\tlist\tStock code list. \n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, data of the sector is returned.\nstr\tIf ret != RET_OK, error description is returned.\nData of the sector format as follows:\nField\tType\tDescription\ncode\tstr\tSecurities code.\nname\tstr\tStock name.\nplate_code\tstr\tPlate code.\nplate_name\tstr\tPlate name.\nplate_type\tPlate\tPlate type. \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\ncode_list = ['HK.00001']\nret, data = quote_ctx.get_owner_plate(code_list)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0]) # Take the first stock code\n    print(data['plate_code'].values.tolist()) # Convert plate code to list\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\nOutput\n    code          name          plate_code                            plate_name plate_type\n0   HK.00001  CKH HOLDINGS  HK.HSI Constituent  ConstituentStocks in Hang Seng Index      OTHER\n..       ...           ...                 ...                                   ...        ...\n8   HK.00001  CKH HOLDINGS           HK.BK1983                                HK ADR      OTHER\n\n[9 rows x 5 columns]\nHK.00001\n['HK.HSI Constituent', 'HK.GangGuTong', 'HK.BK1000', 'HK.BK1061', 'HK.BK1107', 'HK.BK1331', 'HK.BK1600', 'HK.BK1922', 'HK.BK1983']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\nInterface Limitations\n\nA maximum of 10 requests per 30 seconds\nThe maximum number of stocks of each request list is 200\nOnly supports stocks and indices\n\n← Get Capital Distribution\nGet Historical Candlesticks →\n\nGet Plates of Stocks"}, {"title": "Get Capital Distribution | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-capital-distribution.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Capital Distribution\n\nget_capital_distribution(stock_code)\n\nDescription\n\nAccess to capital distribution\n\nParameters\n\nParameter\tType\tDescription\nstock_code\tstr\tStock code.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, stock fund distribution data is returned.\nstr\tIf ret != RET_OK, error description is returned.\nStock fund distribution data format as follows:\nField\tType\tDescription\ncapital_in_super\tfloat\tInflow capital quota, extra-large order.\ncapital_in_big\tfloat\tInflow capital quota, large order.\ncapital_in_mid\tfloat\tInflow capital quota, midium order.\ncapital_in_small\tfloat\tInflow capital quota, small order.\ncapital_out_super\tfloat\tOutflow capital quota, extra-large order.\ncapital_out_big\tfloat\tOutflow capital quota, large order.\ncapital_out_mid\tfloat\tOutflow capital quota, midium order.\ncapital_out_small\tfloat\tOutflow capital quota, small order.\nupdate_time\tstr\tUpdated time string. \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_capital_distribution(\"HK.00700\")\nif ret == RET_OK:\n    print(data)\n    print(data['capital_in_big'][0]) # Take the amount of inflow capital of the first article, big order\n    print(data['capital_in_big'].values.tolist()) # Convert to list\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n   capital_in_super  capital_in_big  ...  capital_out_small          update_time\n0      2.261085e+09    2.141964e+09  ...       2.887413e+09  2022-06-08 15:59:59\n\n[1 rows x 9 columns]\n2141963720.0\n[2141963720.0]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\nInterface Limitations\n\nA maximum of 30 requests per 30 seconds\nOnly support stocks, warrants and funds.\nFor more capital flow introduction, please refer to here.\nOutput data only includes tha data during Regular Trading Hours, not the data during Pre and Post-Market Hours.\n\n← Get Capital Flow\nGet Plates of Stocks →\n\nGet Capital Distribution"}, {"title": "Get Capital Flow | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-capital-flow.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Capital Flow\n\nget_capital_flow(stock_code, period_type = PeriodType.INTRADAY, start=None, end=None)\n\nDescription\n\nGet the flow of a specific stock\n\nParameters\n\nParameter\tType\tDescription\nstock_code\tstr\tStock code.\nperiod_type\tPeriodType\tPeriod Type.\nstart\tstr\tStart time. \n\nend\tstr\tEnd time. \nThe combination of start and end is as follows\nstart type\tend type\tDescription\nstr\tstr\tstart and end are the specified dates respectively.\nNone\tstr\tstart is 365 days before end.\nstr\tNone\tend is 365 days after start.\nNone\tNone\tend is the current date, start is 365 days before.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, capital flow data is returned.\nstr\tIf ret != RET_OK, error description is returned.\nCapital flow data format as follows:\nField\tType\tDescription\nin_flow\tfloat\tNet inflow of capital.\nmain_in_flow\tfloat\tBlock Orders Net Inflow. \n\nsuper_in_flow\tfloat\tExtra-large Orders Net Inflow.\nbig_in_flow\tfloat\tLarge Orders Net Inflow.\nmid_in_flow\tfloat\tMedium Orders Net Inflow.\nsml_in_flow\tfloat\tSmall Orders Net Inflow.\ncapital_flow_item_time\tstr\tStart time string. \n\nlast_valid_time\tstr\tLast valid time string of data. \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_capital_flow(\"HK.00700\", period_type = PeriodType.INTRADAY)\nif ret == RET_OK:\n    print(data)\n    print(data['in_flow'][0]) # Take the first net inflow of capital\n    print(data['in_flow'].values.tolist()) # Convert to list\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    last_valid_time       in_flow  ...  main_in_flow  capital_flow_item_time\n0               N/A -1.857915e+08  ... -1.066828e+08     2021-06-08 00:00:00\n..              ...           ...  ...           ...                     ...\n245             N/A  2.179240e+09  ...  2.143345e+09     2022-06-08 00:00:00\n\n[246 rows x 8 columns]\n-185791500.0\n[-185791500.0, -18315000.0, -672100100.0, -714394350.0, -698391950.0, -818886750.0, 304827400.0, 73026200.0, -2078217500.0, \n..                   ...           ...                    ...\n2031460.0, 638067040.0, 622466600.0, -351788160.0, -328529240.0, 715415020.0, 76749700.0, 2179240320.0]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\nInterface Limitations\n\nA maximum of 30 requests per 30 seconds\nSupported for stocks, warrants and funds only\nHistorical period (day, month, year) Only provides data for the latest 1 year; Intraday period only provides data for the latest day.\nData with historical period (day, month, year), is only supported for the last 2 years. While Data with intraday period is only supported for the latest day.\nOutput data only includes tha data during Regular Trading Hours, not the data during Pre and Post-Market Hours.\n\n← Get Market Status of Securities\nGet Capital Distribution →\n\nGet Capital Flow"}, {"title": "Get Real-time Broker Queue | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-broker.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nSubscription \n\nPush and Callback \n\nGet \n\nGet Market Snapshot\nGet Real-time Quote\nGet Real-time Order Book\nGet Real-time Candlestick\nGet Real-time Time Frame Data\nGet Real-time Tick-by-Tick\nGet Real-time Broker Queue\n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Real-time Broker Queue\n#\nGet Real-time Broker Queue\n\nget_broker_queue(code)\n\nDescription\n\nObtain real-time data of market participants on the order book. (Require real-time data subscription.)\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\nbid_frame_table\tpd.DataFrame\tIf ret == RET_OK, queue of bid brokers is returned.\nstr\tIf ret != RET_OK, error description is returned.\nask_frame_table\tpd.DataFrame\tIf ret == RET_OK, queue of ask brokers is returned.\nstr\tIf ret != RET_OK, error description is returned.\nQueue of bid brokers format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\nbid_broker_id\tint\tBid broker ID.\nbid_broker_name\tstr\tBid broker name.\nbid_broker_pos\tint\tBroker level.\norder_id\tint\tExchange order ID. \n\norder_volume\tint\tOrder volume. \nQueue of ask brokers format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\nask_broker_id\tint\tAsk Broker ID.\nask_broker_name\tstr\tAsk Broker name.\nask_broker_pos\tint\tBroker level.\norder_id\tint\tExchange order ID. \n\norder_volume\tint\tOrder volume. \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.BROKER], subscribe_push=False)\n# First subscribe to the broker queue type. After the subscription is successful, OpenD will continue to receive pushes from the server, False means that there is no need to push the data to the script temporarily\nif ret_sub == RET_OK: # Subscription successful\n     ret, bid_frame_table, ask_frame_table = quote_ctx.get_broker_queue('HK.00700') # Get a broker queue data\n     if ret == RET_OK:\n         print(bid_frame_table)\n     else:\n         print('error:', bid_frame_table)\nelse:\n     print('subscription failed')\nquote_ctx.close() # Close the current connection, OpenD will automatically cancel the corresponding type of subscription for the corresponding stock after 1 minute\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\nOutput\n    code     name  bid_broker_id                                    bid_broker_name  bid_broker_pos order_id order_volume\n0   HK.00700  TENCENT           5338            J.P. Morgan Broking (Hong Kong) Limited               1      N/A          N/A\n..       ...      ...            ...                                                ...             ...      ...          ...\n36  HK.00700  TENCENT           8305  Futu Securities International (Hong Kong) Limited               4      N/A          N/A\n\n[37 rows x 7 columns]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\nTips\n\nThis API provides the function of obtaining real-time data at one time. If you need to obtain pushed data continuously, please refer to the Real-time Broker Queue Callback API.\nFor the difference between get real-time data and real-time data callback, please refer to How to Get Real-time Quotes Through Subscription Interface.\n\n← Get Real-time Tick-by-Tick\nGet Market Status of Securities →\n\nGet Real-time Broker Queue"}, {"title": "Quote Object | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/base.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nQuote Object\n#\nCreate and Initialize the Connection\n\nOpenQuoteContext(host='127.0.0.1', port=11111, is_encrypt=None)\n\nIntroduction\n\nCreate and initialize market connection\n\nParameters\n\nParameter|Type|Description 😐:-|:- host|str|OpenD listening address. port|int|OpenD listening port. is_encrypt|bool|Whether to enable encryption. \n\nExample\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111, is_encrypt=False)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\nClose Connection\n\nclose()\n\nIntroduction\n\nClose the interface quotation object. By default, the threads created inside the Futu API will prevent the process from exiting, and the process can exit normally only after all Contexts are closed. But through set_all_thread_daemon, all internal threads can be set as daemon threads. At this time, even if the close of Context is not called, the process can exit normally.\n\nExample\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\nStart-up\n\nstart()\n\nIntroduction\n\nStart to receive push data asynchronously\n\n#\nStop\n\nstop()\n\nIntroduction\n\nStop receiving push data asynchronously\n\n← Overview\nSubscribe and Unsubscribe →\n\nQuote Object"}, {"title": "获取融资融券数据 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/trade/get-margin-ratio.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n交易接口总览\n交易对象\n\n账户 \n\n资产持仓 \n\n查询账户资金\n查询最大可买可卖\n查询持仓\n获取融资融券数据\n\n订单 \n\n成交 \n\n交易定义\n\n基础接口 \n\nQ&A \n\n#\n获取融资融券数据\n\nget_margin_ratio(code_list)\n\n介绍\n\n查询股票的融资融券数据。\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t股票代码列表 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK 时，返回融资融券数据\nstr\t当 ret != RET_OK 时，返回错误描述\n融资融券数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nis_long_permit\tbool\t是否允许融资\nis_short_permit\tbool\t是否允许融券\nshort_pool_remain\tfloat\t卖空池剩余 \n\nshort_fee_rate\tfloat\t融券参考利率 \n\nalert_long_ratio\tfloat\t融资预警比率 \n\nalert_short_ratio\tfloat\t融券预警比率 \n\nim_long_ratio\tfloat\t融资初始保证金率 \n\nim_short_ratio\tfloat\t融券初始保证金率 \n\nmcm_long_ratio\tfloat\t融资 margin call 保证金率 \n\nmcm_short_ratio\tfloat\t融券 margin call 保证金率 \n\nmm_long_ratio\tfloat\t融资维持保证金率 \n\nmm_short_ratio\tfloat\t融券维持保证金率 \n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.get_margin_ratio(code_list=['HK.00700','HK.09988'])  \nif ret == RET_OK:\n    print(data)\n    print(data['is_long_permit'][0])  # 取第一条的是否允许融资\n    print(data['im_short_ratio'].values.tolist())  # 转为 list\nelse:\n    print('error:', data)\ntrd_ctx.close()  # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\nOutput\n       code  is_long_permit  is_short_permit  short_pool_remain  short_fee_rate  alert_long_ratio  alert_short_ratio  im_long_ratio  im_short_ratio  mcm_long_ratio  mcm_short_ratio  mm_long_ratio  mm_short_ratio\n0  HK.00700            True             True          1826900.0            0.89              33.0               56.0           35.0            60.0            32.0             53.0           25.0            40.0\n1  HK.09988            True             True          1150600.0            0.95              48.0               46.0           50.0            50.0            47.0             43.0           40.0            30.0\nTrue\n[60.0, 50.0]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n接口限制\n\n每 30 秒内最多请求 10 次获取融资融券数据接口。\n每次请求，接口参数股票代码列表，支持传入的标的数量上限是 100 个。\n仅支持港股正股和美股正股的查询。\n\n← 查询持仓\n下单 →\n\n获取融资融券数据"}, {"title": "Real-time Broker <PERSON><PERSON> Callback | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/update-broker.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nSubscription \n\nPush and Callback \n\nReal-time Quote Callback\nReal-time Order Book Callback\nReal-time Candlestick Callback\nReal-time Time Frame Callback\nReal-time Tick-by-Tick Callback\nReal-time Broker Queue Callback\n\nGet \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nReal-time Broker Queue Callback\n\non_recv_rsp(self, rsp_pb)\n\nDescription\n\nReal-time broker queue callback, asynchronous processing of real-time broker queue push of subscribed stocks. After receiving the real-time broker queue data push, it will call back to this function. You need to override on_recv_rsp in the derived class.\n\nParameters\n\nParameter\tType\tDescription\nrsp_pb\tQot_UpdateBroker_pb2.Response\tThis parameter does not need to be processed directly in the derived class.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\ttuple\tIf ret == RET_OK, broker queue data is returned.\nstr\tIf ret != RET_OK, error description is returned.\n\nBroker queue data format as follows:\n\nField\tType\tDescription\nstock_code\tstr\tStock code.\nbid_frame_table\tpd.DataFrame\tData from bid.\nask_frame_table\tpd.DataFrame\tData from ask.\nBid_frame_table format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\nbid_broker_id\tint\tBid broker ID.\nbid_broker_name\tstr\tBid broker name.\nbid_broker_pos\tint\tBroker level.\norder_id\tint\tExchange order ID. \n\norder_volume\tint\tOrder volume. \nAsk_frame_table format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\nask_broker_id\tint\tAsk Broker ID.\nask_broker_name\tstr\tAsk Broker name.\nask_broker_pos\tint\tBroker level.\norder_id\tint\tExchange order ID. \n\norder_volume\tint\tOrder volume. \n\nExample\n\nimport time\nfrom futu import *\n    \nclass BrokerTest(BrokerHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, err_or_stock_code, data = super(BrokerTest, self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"BrokerTest: error, msg: {}\".format(err_or_stock_code))\n            return RET_ERROR, data\n        print(\"BrokerTest: stock: {} data: {} \".format(err_or_stock_code, data)) # BrokerTest's own processing logic\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = BrokerTest()\nquote_ctx.set_handler(handler) # Set real-time broker push callback\nret, data = quote_ctx.subscribe(['HK.00700'], [SubType.BROKER]) # Subscribe to the broker type, OpenD starts to receive continuous push from the server\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\ntime.sleep(15) # Set the script to receive OpenD push duration to 15 seconds\nquote_ctx.close() # Close the current link, OpenD will automatically cancel the corresponding type of subscription for the corresponding stock after 1 minute\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\nOutput\nBrokerTest: stock: HK.00700 data: [        code     name  bid_broker_id                                    bid_broker_name  bid_broker_pos order_id order_volume\n0   HK.00700  TENCENT           5338            J.P. Morgan Broking (Hong Kong) Limited               1      N/A          N/A\n..       ...      ...            ...                                                ...             ...      ...          ...\n36  HK.00700  TENCENT           8305  Futu Securities International (Hong Kong) Limited               4      N/A          N/A\n\n[37 rows x 7 columns],         code     name  ask_broker_id                                ask_broker_name  ask_broker_pos order_id order_volume\n0   HK.00700  TENCENT           1179  Huatai Financial Holdings (Hong Kong) Limited               1      N/A          N/A\n..       ...      ...            ...                                            ...             ...      ...          ...\n39  HK.00700  TENCENT           6996  China Investment Information Services Limited               1      N/A          N/A\n\n[40 rows x 7 columns]] \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n\nTips\n\nThis interface provides the function of continuously obtaining pushed data. If you need to obtain real-time data at one time, please refer to the Get Real-time Orderbook API.\nFor the difference between get real-time data and real-time data callback, please refer to How to Get Real-time Quotes Through Subscription Interface.\n\n← Real-time Tick-by-Tick Callback\nGet Market Snapshot →\n\nReal-time Broker Queue Callback"}, {"title": "Overview | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/overview.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nOverview\nModule\tInterface Name\tFunction Description\nReal-time Data\tSubscription\tsubscribe\tSubscribe real-time market data\nunsubscribe\tUnsubscribe subscriptions\nunsubscribe_all\tUnsubscribe all subscriptions\nquery_subscription\tGet subscription information\nPush and Callback\tStockQuoteHandlerBase\tReal-time quote callback\nOrderBookHandlerBase\tReal-time order book callback\nCurKlineHandlerBase\tReal-time candlestick callback\nTickerHandlerBase\tReal-time tick-by-tick callback\nRTDataHandlerBase\tReal-time time frame callback\nBrokerHandlerBase\tReal-time broker queue callback\nGet\tget_market_snapshot\tGet market snapshot\nget_stock_quote\tGet real-time quote\nget_order_book\tGet real-time order book\nget_cur_kline\tGet real-time candlestick\nget_rt_data\tGet real-time time frame data\nget_rt_ticker\tGet real-time tick-by-tick\nget_broker_queue\tGet real-time broker queue\nBasic Data\tget_market_state\tGet market status of securities\nget_capital_flow\tGet capital flow\nget_capital_distribution\tGet capital distribution\nget_owner_plate\tGet the stock ownership plate\nrequest_history_kline\tGet historical candlesticks\nget_rehab\tGet the stock adjustment factor\nRelated Derivatives\tget_option_expiration_date\tQuery all expiration dates of option chains through the underlying stock.\nget_option_chain\tGet the option chain from an underlying stock\nget_warrant\tGet filtered warrant (for HK market only)\nget_referencestock_list\tGet related data of securities\nget_future_info\tGet futures contract information\nMarket Filter\tget_stock_filter\tFilter stocks by condition\nget_plate_stock\tGet the list of stocks in the plate\nget_plate_list\tGet plate list\nget_stock_basicinfo\tGet stock basic information\nget_ipo_list\tGet IPO information of a specific market\nget_global_state\tGet global status\nrequest_trading_days\tGet trading calendar\nCustomization\tget_history_kl_quota\tGet usage details of historical candlestick quota\nset_price_reminder\tAdd, delete, modify, enable, and disable price reminders for specified stocks\nget_price_reminder\tGet a list of price reminders set for the specified stock or market\nget_user_security_group\tGet a list of groups from the user watchlist\nget_user_security\tGet a list of a specified group from watchlist\nmodify_user_security\tModify the specific group from the watchlist\nPriceReminderHandlerBase\tThe price reminder notification callback\n\n← Operation Command\nQuote Object →\n\nOverview"}, {"title": "行情定义 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/quote.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n行情定义\n#\n累积过滤属性\n\nStockField\n\nNONE\n\n未知\n\nCHANGE_RATE\n\n涨跌幅 \n\nAMPLITUDE\n\n振幅 \n\nVOLUME\n\n日均成交量 \n\nTURNOVER\n\n日均成交额 \n\nTURNOVER_RATE\n\n换手率 \n\n#\n资产类别\n\nAssetClass\n\nUNKNOW\n\n未知\n\nSTOCK\n\n股票\n\nBOND\n\n债券\n\nCOMMODITY\n\n商品\n\nCURRENCY_MARKET\n\n货币市场\n\nFUTURE\n\n期货\n\nSWAP\n\n掉期（互换）\n\n#\n公司行动\n#\n暗盘状态\n\nDarkStatus\n\nNONE\n\n无暗盘交易\n\nTRADING\n\n暗盘交易中\n\nEND\n\n暗盘交易结束\n\n#\n财务过滤属性\n\nStockField\n\nNONE\n\n未知\n\nNET_PROFIT\n\n净利润 \n\nNET_PROFIX_GROWTH\n\n净利润增长率 \n\nSUM_OF_BUSINESS\n\n营业收入 \n\nSUM_OF_BUSINESS_GROWTH\n\n营收同比增长率 \n\nNET_PROFIT_RATE\n\n净利率 \n\nGROSS_PROFIT_RATE\n\n毛利率 \n\nDEBT_ASSET_RATE\n\n资产负债率 \n\nRETURN_ON_EQUITY_RATE\n\n净资产收益率 \n\nROIC\n\n投入资本回报率 \n\nROA_TTM\n\n资产回报率 TTM \n\nEBIT_TTM\n\n息税前利润 TTM \n\nEBITDA\n\n税息折旧及摊销前利润 \n\nOPERATING_MARGIN_TTM\n\n营业利润率 TTM \n\nEBIT_MARGIN\n\nEBIT 利润率 \n\nEBITDA_MARGIN\n\nEBITDA 利润率 \n\nFINANCIAL_COST_RATE\n\n财务成本率 \n\nOPERATING_PROFIT_TTM\n\n营业利润 TTM \n\nSHAREHOLDER_NET_PROFIT_TTM\n\n归属于母公司的净利润 \n\nNET_PROFIT_CASH_COVER_TTM\n\n盈利中的现金收入比例 \n\nCURRENT_RATIO\n\n流动比率 \n\nQUICK_RATIO\n\n速动比率 \n\nCURRENT_ASSET_RATIO\n\n流动资产率 \n\nCURRENT_DEBT_RATIO\n\n流动负债率 \n\nEQUITY_MULTIPLIER\n\n权益乘数 \n\nPROPERTY_RATIO\n\n产权比率 \n\nCASH_AND_CASH_EQUIVALENTS\n\n现金和现金等价物 \n\nTOTAL_ASSET_TURNOVER\n\n总资产周转率 \n\nFIXED_ASSET_TURNOVER\n\n固定资产周转率 \n\nINVENTORY_TURNOVER\n\n存货周转率 \n\nOPERATING_CASH_FLOW_TTM\n\n经营活动现金流 TTM \n\nACCOUNTS_RECEIVABLE\n\n应收账款净额 \n\nEBIT_GROWTH_RATE\n\nEBIT 同比增长率 \n\nOPERATING_PROFIT_GROWTH_RATE\n\n营业利润同比增长率 \n\nTOTAL_ASSETS_GROWTH_RATE\n\n总资产同比增长率 \n\nPROFIT_TO_SHAREHOLDERS_GROWTH_RATE\n\n归母净利润同比增长率 \n\nPROFIT_BEFORE_TAX_GROWTH_RATE\n\n总利润同比增长率 \n\nEPS_GROWTH_RATE\n\nEPS 同比增长率 \n\nROE_GROWTH_RATE\n\nROE 同比增长率 \n\nROIC_GROWTH_RATE\n\nROIC 同比增长率 \n\nNOCF_GROWTH_RATE\n\n经营现金流同比增长率 \n\nNOCF_PER_SHARE_GROWTH_RATE\n\n每股经营现金流同比增长率 \n\nOPERATING_REVENUE_CASH_COVER\n\n经营现金收入比 \n\nOPERATING_PROFIT_TO_TOTAL_PROFIT\n\n营业利润占比 \n\nBASIC_EPS\n\n基本每股收益 \n\nDILUTED_EPS\n\n稀释每股收益 \n\nNOCF_PER_SHARE\n\n每股经营现金净流量 \n\n#\n财务过滤属性周期\n\nFinancialQuarter\n\nNONE\n\n未知\n\nANNUAL\n\n年报\n\nFIRST_QUARTER\n\n一季报\n\nINTERIM\n\n中报\n\nTHIRD_QUARTER\n\n三季报\n\nMOST_RECENT_QUARTER\n\n最近季报\n\n#\n自定义技术指标属性\n\nStockField\n\nNONE\n\n未知\n\nPRICE\n\n最新价格\n\nMA\n\n简单均线\n\nMA5\n\n5日简单均线（不建议使用）\n\nMA10\n\n10日简单均线（不建议使用）\n\nMA20\n\n20日简单均线（不建议使用）\n\nMA30\n\n30日简单均线（不建议使用）\n\nMA60\n\n60日简单均线（不建议使用）\n\nMA120\n\n120日简单均线（不建议使用）\n\nMA250\n\n250日简单均线（不建议使用）\n\nRSI\n\nRSI \n\nEMA\n\n指数移动均线\n\nEMA5\n\n5日指数移动均线（不建议使用）\n\nEMA10\n\n10日指数移动均线（不建议使用）\n\nEMA20\n\n20日指数移动均线（不建议使用）\n\nEMA30\n\n30日指数移动均线（不建议使用）\n\nEMA60\n\n60日指数移动均线（不建议使用）\n\nEMA120\n\n120日指数移动均线（不建议使用）\n\nEMA250\n\n250日指数移动均线（不建议使用）\n\nKDJ_K\n\nKDJ 指标的 K 值 \n\nKDJ_D\n\nKDJ 指标的 D 值 \n\nKDJ_J\n\nKDJ 指标的 J 值 \n\nMACD_DIFF\n\nMACD 指标的 DIFF 值 \n\nMACD_DEA\n\nMACD 指标的 DEA 值 \n\nMACD\n\nMACD \n\nBOLL_UPPER\n\nBOLL 指标的 UPPER 值 \n\nBOLL_MIDDLER\n\nBOLL 指标的 MIDDLER 值 \n\nBOLL_LOWER\n\nBOLL 指标的 LOWER 值 \n\nVALUE\n\n自定义数值（stock_field1 不支持此字段）\n\n#\n相对位置\n\nRelativePosition\n\nNONE\n\n未知\n\nMORE\n\n大于，stock_field1 位于stock_field2 的上方\n\nLESS\n\n小于，stock_field1 位于stock_field2 的下方\n\nCROSS_UP\n\n升穿，stock_field1 从下往上穿stock_field2\n\nCROSS_DOWN\n\n跌穿，stock_field1 从上往下穿stock_field2\n\n#\n形态技术指标属性\n\nPatternField\n\nNONE\n\n未知\n\nMA_ALIGNMENT_LONG\n\nMA多头排列（连续两天MA5>MA10>MA20>MA30>MA60，且当日收盘价大于前一天收盘价）\n\nMA_ALIGNMENT_SHORT\n\nMA空头排列（连续两天MA5<MA10<MA20<MA30<MA60，且当日收盘价小于前一天收盘价）\n\nEMA_ALIGNMENT_LONG\n\nEMA多头排列（连续两天EMA5>EMA10>EMA20>EMA30>EMA60，且当日收盘价大于前一天收盘价）\n\nEMA_ALIGNMENT_SHORT\n\nEMA空头排列（连续两天EMA5<EMA10<EMA20<EMA30<EMA60，且当日收盘价小于前一天收盘价）\n\nRSI_GOLD_CROSS_LOW\n\nRSI低位金叉（50以下，短线RSI上穿长线RSI（前一日短线RSI小于长线RSI，当日短线RSI大于长线RSI））\n\nRSI_DEATH_CROSS_HIGH\n\nRSI高位死叉（50以上，短线RSI下穿长线RSI（前一日短线RSI大于长线RSI，当日短线RSI小于长线RSI））\n\nRSI_TOP_DIVERGENCE\n\nRSI顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的RSI12值<前面波峰的RSI12值）\n\nRSI_BOTTOM_DIVERGENCE\n\nRSI底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE<前面的波谷对应的CLOSE，后面波谷的RSI12值>前面波谷的RSI12值）\n\nKDJ_GOLD_CROSS_LOW\n\nKDJ低位金叉（D值小于或等于30，且前一日K值小于D值，当日K值大于D值）\n\nKDJ_DEATH_CROSS_HIGH\n\nKDJ高位死叉（D值大于或等于70，且前一日K值大于D值，当日K值小于D值）\n\nKDJ_TOP_DIVERGENCE\n\nKDJ顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的J值<前面波峰的J值）\n\nKDJ_BOTTOM_DIVERGENCE\n\nKDJ底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE<前面的波谷对应的CLOSE，后面波谷的J值>前面波谷的J值）\n\nMACD_GOLD_CROSS_LOW\n\nMACD低位金叉（DIFF上穿DEA（前一日DIFF小于DEA，当日DIFF大于DEA））\n\nMACD_DEATH_CROSS_HIGH\n\nMACD高位死叉（DIFF下穿DEA（前一日DIFF大于DEA，当日DIFF小于DEA））\n\nMACD_TOP_DIVERGENCE\n\nMACD顶背离（相邻的两个K线波峰，后面的波峰对应的CLOSE>前面的波峰对应的CLOSE，后面波峰的macd值<前面波峰的macd值）\n\nMACD_BOTTOM_DIVERGENCE\n\nMACD底背离（相邻的两个K线波谷，后面的波谷对应的CLOSE<前面的波谷对应的CLOSE，后面波谷的macd值>前面波谷的macd值）\n\nBOLL_BREAK_UPPER\n\nBOLL突破上轨（前一日股价低于上轨值，当日股价大于上轨值）\n\nBOLL_BREAK_LOWER\n\nBOLL突破下轨（前一日股价高于下轨值，当日股价小于下轨值）\n\nBOLL_CROSS_MIDDLE_UP\n\nBOLL向上破中轨（前一日股价低于中轨值，当日股价大于中轨值）\n\nBOLL_CROSS_MIDDLE_DOWN\n\nBOLL向下破中轨（前一日股价大于中轨值，当日股价小于中轨值）\n\n#\n自选股分组类型\n\nUserSecurityGroupType\n\nNONE\n\n未知\n\nCUSTOM\n\n自定义分组\n\nSYSTEM\n\n系统分组\n\nALL\n\n全部分组\n\n#\n指数期权类别\n\nIndexOptionType\n\nNONE\n\n未知\n\nNORMAL\n\n普通的指数期权\n\nSMALL\n\n小型指数期权\n\n#\n上市时段\n\nIpoPeriod\n\nNONE\n\n未知\n\nTODAY\n\n今日上市\n\nTOMORROW\n\n明日上市\n\nNEXTWEEK\n\n未来一周上市\n\nLASTWEEK\n\n过去一周上市\n\nLASTMONTH\n\n过去一月上市\n\n#\n窝轮发行商\n\nIssuer\n\nUNKNOW\n\n未知\n\nSG\n\n法兴\n\nBP\n\n法巴\n\nCS\n\n瑞信\n\nCT\n\n花旗\n\nEA\n\n东亚\n\nGS\n\n高盛\n\nHS\n\n汇丰\n\nJP\n\n摩通\n\nMB\n\n麦银\n\nSC\n\n渣打\n\nUB\n\n瑞银\n\nBI\n\n中银\n\nDB\n\n德银\n\nDC\n\n大和\n\nML\n\n美林\n\nNM\n\n野村\n\nRB\n\n荷合\n\nRS\n\n苏皇\n\nBC\n\n巴克莱\n\nHT\n\n海通\n\nVT\n\n瑞通\n\nKC\n\n比联\n\nMS\n\n摩利\n\nGJ\n\n国君\n\nXZ\n\n星展\n\nHU\n\n华泰\n\nKS\n\n韩投\n\nCI\n\n信证\n\n#\nK 线字段\n\nKL_FIELD\n\nALL\n\n所有\n\nDATE_TIME\n\n时间\n\nHIGH\n\n最高价\n\nOPEN\n\n开盘价\n\nLOW\n\n最低价\n\nCLOSE\n\n收盘价\n\nLAST_CLOSE\n\n昨收价\n\nTRADE_VOL\n\n成交量\n\nTRADE_VAL\n\n成交额\n\nTURNOVER_RATE\n\n换手率\n\nPE_RATIO\n\n市盈率\n\nCHANGE_RATE\n\n涨跌幅\n\n#\nK 线类型\n\nKLType\n\nNONE\n\n未知\n\nK_1M\n\n1分 K\n\nK_DAY\n\n日 K\n\nK_WEEK\n\n周 K \n\nK_MON\n\n月 K \n\nK_YEAR\n\n年 K \n\nK_5M\n\n5分 K\n\nK_15M\n\n15分 K\n\nK_30M\n\n30分 K \n\nK_60M\n\n60分 K\n\nK_3M\n\n3分 K \n\nK_QUARTER\n\n季 K \n\n#\n周期类型\n\nPeriodType\n\nINTRADAY\n\n实时\n\nDAY\n\n日\n\nWEEK\n\n周\n\nMONTH\n\n月\n\n#\n到价提醒市场状态\n\nPriceReminderMarketStatus\n\nUNKNOW\n\n未知\n\nOPEN\n\n盘中\n\nUSPRE\n\n美股盘前\n\nUSAFTER\n\n美股盘后\n\n#\n自选股操作\n\nModifyUserSecurityOp\n\nNONE\n\n未知\n\nADD\n\n新增\n\nDEL\n\n删除自选\n\nMOVE_OUT\n\n移出分组\n\n#\n期权类型（按行权时间）\n\nOptionAreaType\n\nNONE\n\n未知\n\nAMERICAN\n\n美式\n\nEUROPEAN\n\n欧式\n\nBERMUDA\n\n百慕大\n\n#\n期权价内/外\n\nOptionCondType\n\nALL\n\n所有\n\nWITHIN\n\n价内\n\nOUTSIDE\n\n价外\n\n#\n期权类型（按方向）\n\nOptionType\n\nALL\n\n所有\n\nCALL\n\n看涨期权\n\nPUT\n\n看跌期权\n\n#\n板块集合类型\n\nPlate\n\nALL\n\n所有板块\n\nINDUSTRY\n\n行业板块\n\nREGION\n\n地域板块 \n\nCONCEPT\n\n概念板块\n\nOTHER\n\n其他板块 \n\n#\n到价提醒频率\n\nPriceReminderFreq\n\nNONE\n\n未知\n\nALWAYS\n\n持续提醒\n\nONCE_A_DAY\n\n每日一次\n\nONCE\n\n仅提醒一次\n\n#\n到价提醒类型\n\nPriceReminderType\n\nNONE\n\n未知\n\nPRICE_UP\n\n价格涨到\n\nPRICE_DOWN\n\n价格跌到\n\nCHANGE_RATE_UP\n\n日涨幅超 \n\nCHANGE_RATE_DOWN\n\n日跌幅超 \n\nFIVE_MIN_CHANGE_RATE_UP\n\n5 分钟涨幅超 \n\nFIVE_MIN_CHANGE_RATE_DOWN\n\n5 分钟跌幅超 \n\nVOLUME_UP\n\n成交量超过\n\nTURNOVER_UP\n\n成交额超过\n\nTURNOVER_RATE_UP\n\n换手率超过 \n\nBID_PRICE_UP\n\n买一价高于\n\nASK_PRICE_DOWN\n\n卖一价低于\n\nBID_VOL_UP\n\n买一量高于\n\nASK_VOL_UP\n\n卖一量高于\n\nTHREE_MIN_CHANGE_RATE_UP\n\n3 分钟涨幅超 \n\nTHREE_MIN_CHANGE_RATE_DOWN\n\n3 分钟跌幅超 \n\n#\n窝轮价内/外\n\nPriceType\n\nUNKNOW\n\n未知\n\nOUTSIDE\n\n价外，界内证表示界外\n\nWITH_IN\n\n价内，界内证表示界内\n\n#\n逐笔推送类型\n\nPushDataType\n\nUNKNOW\n\n未知\n\nREALTIME\n\n实时推送的数据\n\nBYDISCONN\n\n与富途服务器连接断开期间，拉取补充的数据 \n\nCACHE\n\n非实时非连接断开补充数据\n\n#\n行情市场\n\nMarket\n\nNONE\n\n未知市场\n\nHK\n\n香港市场\n\nUS\n\n美国市场\n\nSH\n\n沪股市场\n\nSZ\n\n深股市场\n\nSG\n\n新加坡市场\n\nJP\n\n日本市场\n\n#\n市场状态\n\nMarketState\n\n各市场状态的对应时段：点击这里了解更多\n\nNONE\n\n无交易\n\nAUCTION\n\n盘前竞价\n\nWAITING_OPEN\n\n等待开盘\n\nMORNING\n\n早盘\n\nREST\n\n午间休市\n\nAFTERNOON\n\n午盘 / 美股持续交易时段\n\nCLOSED\n\n收盘\n\nPRE_MARKET_BEGIN\n\n美股盘前交易时段\n\nPRE_MARKET_END\n\n美股盘前交易结束\n\nAFTER_HOURS_BEGIN\n\n美股盘后交易时段\n\nAFTER_HOURS_END\n\n美股盘后结束\n\nNIGHT_OPEN\n\n夜市交易时段\n\nNIGHT_END\n\n夜市收盘\n\nNIGHT\n\n美指期权夜市交易时段\n\nTRADE_AT_LAST\n\n美指期权盘尾交易时段\n\nFUTURE_DAY_OPEN\n\n日市交易时段\n\nFUTURE_DAY_BREAK\n\n日市休市\n\nFUTURE_DAY_CLOSE\n\n日市收盘\n\nFUTURE_DAY_WAIT_OPEN\n\n期货待开盘\n\nHK_CAS\n\n港股盘后竞价\n\nFUTURE_NIGHT_WAIT\n\n夜市等待开盘（已废弃）\n\nFUTURE_AFTERNOON\n\n期货下午开盘（已废弃）\n\nFUTURE_SWITCH_DATE\n\n美期待开盘\n\nFUTURE_OPEN\n\n美期交易时段\n\nFUTURE_BREAK\n\n美期中盘休息\n\nFUTURE_BREAK_OVER\n\n美期休息后交易时段\n\nFUTURE_CLOSE\n\n美期收盘\n\nSTIB_AFTER_HOURS_WAIT\n\n科创板的盘后撮合时段（已废弃）\n\nSTIB_AFTER_HOURS_BEGIN\n\n科创板的盘后交易开始（已废弃）\n\nSTIB_AFTER_HOURS_END\n\n科创板的盘后交易结束（已废弃）\n\n#\n行情权限\n\nQotRight\n\nUNKNOW\n\n未知\n\nBMP\n\nBMP（此权限不支持订阅）\n\nLEVEL1\n\nLevel1\n\nLEVEL2\n\nLevel2\n\nSF\n\n港股 SF 高级全盘行情\n\nNO\n\n无权限\n\n#\n关联数据类型\n\nSecurityReferenceType\n\nUNKNOW\n\n未知\n\nWARRANT\n\n正股相关的窝轮\n\nFUTURE\n\n期货主连的相关合约\n\n#\nK 线复权类型\n\nAuType\n\nNONE\n\n不复权\n\nQFQ\n\n前复权\n\nHFQ\n\n后复权\n\n#\n股票状态\n\nSecurityStatus\n\nNONE\n\n未知\n\nNORMAL\n\n正常状态\n\nLISTING\n\n待上市\n\nPURCHASING\n\n申购中\n\nSUBSCRIBING\n\n认购中\n\nBEFORE_DRAK_TRADE_OPENING\n\n暗盘开盘前\n\nDRAK_TRADING\n\n暗盘交易中\n\nDRAK_TRADE_END\n\n暗盘已收盘\n\nTO_BE_OPEN\n\n待开盘\n\nSUSPENDED\n\n停牌\n\nCALLED\n\n已收回\n\nEXPIRED_LAST_TRADING_DATE\n\n已过最后交易日\n\nEXPIRED\n\n已过期\n\nDELISTED\n\n已退市\n\nCHANGE_TO_TEMPORARY_CODE\n\n公司行动中，交易关闭，转至临时代码交易\n\nTEMPORARY_CODE_TRADE_END\n\n临时买卖结束，交易关闭\n\nCHANGED_PLATE_TRADE_END\n\n已转板，旧代码交易关闭\n\nCHANGED_CODE_TRADE_END\n\n已换代码，旧代码交易关闭\n\nRECOVERABLE_CIRCUIT_BREAKER\n\n可恢复性熔断\n\nUN_RECOVERABLE_CIRCUIT_BREAKER\n\n不可恢复性熔断\n\nAFTER_COMBINATION\n\n盘后撮合\n\nAFTER_TRANSATION\n\n盘后交易\n\n#\n股票类型\n\nSecurityType\n\nNONE\n\n未知\n\nBOND\n\n债券\n\nBWRT\n\n一揽子权证\n\nSTOCK\n\n正股\n\nETF\n\n信托,基金\n\nWARRANT\n\n窝轮\n\nIDX\n\n指数\n\nPLATE\n\n板块\n\nDRVT\n\n期权\n\nPLATESET\n\n板块集\n\nFUTURE\n\n期货\n\n#\n设置到价提醒操作类型\n\nSetPriceReminderOp\n\nNONE\n\n未知\n\nADD\n\n新增\n\nDEL\n\n删除\n\nENABLE\n\n启用\n\nDISABLE\n\n禁用\n\nMODIFY\n\n修改\n\nDEL_ALL\n\n删除全部（删除指定股票下的所有到价提醒）\n\n#\n排序方向\n\nSortDir\n\nNONE\n\n不排序\n\nASCEND\n\n升序\n\nDESCEND\n\n降序\n\n#\n排序字段\n\nSortField\n\nNONE\n\n未知\n\nCODE\n\n代码\n\nCUR_PRICE\n\n最新价\n\nPRICE_CHANGE_VAL\n\n涨跌额\n\nCHANGE_RATE\n\n涨跌幅 %\n\nSTATUS\n\n状态\n\nBID_PRICE\n\n买入价\n\nASK_PRICE\n\n卖出价\n\nBID_VOL\n\n买量\n\nASK_VOL\n\n卖量\n\nVOLUME\n\n成交量\n\nTURNOVER\n\n成交额\n\nAMPLITUDE\n\n振幅 %\n\nSCORE\n\n综合评分\n\nPREMIUM\n\n溢价 %\n\nEFFECTIVE_LEVERAGE\n\n有效杠杆\n\nDELTA\n\n对冲值 \n\nIMPLIED_VOLATILITY\n\n引伸波幅 \n\nTYPE\n\n类型\n\nSTRIKE_PRICE\n\n行权价\n\nBREAK_EVEN_POINT\n\n打和点\n\nMATURITY_TIME\n\n到期日\n\nLIST_TIME\n\n上市日期\n\nLAST_TRADE_TIME\n\n最后交易日\n\nLEVERAGE\n\n杠杆比率\n\nIN_OUT_MONEY\n\n价内/价外 %\n\nRECOVERY_PRICE\n\n收回价 \n\nCHANGE_PRICE\n\n换股价\n\nCHANGE\n\n换股比率\n\nSTREET_RATE\n\n街货比 %\n\nSTREET_VOL\n\n街货量\n\nWARRANT_NAME\n\n窝轮名称\n\nISSUER\n\n发行人\n\nLOT_SIZE\n\n每手\n\nISSUE_SIZE\n\n发行量\n\nUPPER_STRIKE_PRICE\n\n上限价 \n\nLOWER_STRIKE_PRICE\n\n下限价 \n\nINLINE_PRICE_STATUS\n\n界内界外 \n\nPRE_CUR_PRICE\n\n盘前最新价\n\nAFTER_CUR_PRICE\n\n盘后最新价\n\nPRE_PRICE_CHANGE_VAL\n\n盘前涨跌额\n\nAFTER_PRICE_CHANGE_VAL\n\n盘后涨跌额\n\nPRE_CHANGE_RATE\n\n盘前涨跌幅 %\n\nAFTER_CHANGE_RATE\n\n盘后涨跌幅 %\n\nPRE_AMPLITUDE\n\n盘前振幅 %\n\nAFTER_AMPLITUDE\n\n盘后振幅 %\n\nPRE_TURNOVER\n\n盘前成交额\n\nAFTER_TURNOVER\n\n盘后成交额\n\nLAST_SETTLE_PRICE\n\n昨结\n\nPOSITION\n\n持仓量\n\nPOSITION_CHANGE\n\n日增仓\n\n#\n简单过滤属性\n\nStockField\n\nNONE\n\n未知\n\nSTOCK_CODE\n\n股票代码，不能填区间上下限值。\n\nSTOCK_NAME\n\n股票名称，不能填区间上下限值。\n\nCUR_PRICE\n\n最新价 \n\nCUR_PRICE_TO_HIGHEST52_WEEKS_RATIO\n\n(CP - WH52) / WH52\nCP：现价\nWH52：52 周最高\n对应 PC 端“离 52 周高点百分比” \n\nCUR_PRICE_TO_LOWEST52_WEEKS_RATIO\n\n(CP - WL52) / WL52\nCP：现价\nWL52：52 周最低\n对应 PC 端“离 52 周低点百分比” \n\nHIGH_PRICE_TO_HIGHEST52_WEEKS_RATIO\n\n(TH - WH52) / WH52\nTH：今日最高\nWH52：52 周最高\n\n\nLOW_PRICE_TO_LOWEST52_WEEKS_RATIO\n\n(TL - WL52) / WL52\nTL：今日最低\nWL52：52 周最低\n\n\nVOLUME_RATIO\n\n量比 \n\nBID_ASK_RATIO\n\n委比 \n\nLOT_PRICE\n\n每手价格 \n\nMARKET_VAL\n\n市值 \n\nPE_ANNUAL\n\n市盈率(静态) \n\nPE_TTM\n\n市盈率 TTM \n\nPB_RATE\n\n市净率 \n\nCHANGE_RATE_5MIN\n\n五分钟价格涨跌幅 \n\nCHANGE_RATE_BEGIN_YEAR\n\n年初至今价格涨跌幅 \n\nPS_TTM\n\n市销率 TTM \n\nPCF_TTM\n\n市现率 TTM \n\nTOTAL_SHARE\n\n总股数 \n\nFLOAT_SHARE\n\n流通股数 \n\nFLOAT_MARKET_VAL\n\n流通市值 \n\n#\n订阅类型\n\nSubType\n\nNONE\n\n未知\n\nQUOTE\n\n基础报价\n\nORDER_BOOK\n\n摆盘\n\nTICKER\n\n逐笔\n\nRT_DATA\n\n分时\n\nK_DAY\n\n日 K\n\nK_5M\n\n5 分 K\n\nK_15M\n\n15 分 K\n\nK_30M\n\n30 分 K\n\nK_60M\n\n60 分 K\n\nK_1M\n\n1 分 K\n\nK_WEEK\n\n周 K\n\nK_MON\n\n月 K\n\nBROKER\n\n经纪队列\n\nK_QURATER\n\n季 K\n\nK_YEAR\n\n年 K\n\nK_3M\n\n3 分 K\n\n#\n逐笔成交方向\n\nTickerDirect\n\nNONE\n\n未知\n\nBUY\n\n外盘 \n\nSELL\n\n内盘 \n\nNEUTRAL\n\n中性盘 \n\n#\n逐笔成交类型\n\nTickerType\n\nUNKNOWN\n\n未知\n\nAUTO_MATCH\n\n自动对盘\n\nLATE\n\n开市前成交盘\n\nNON_AUTO_MATCH\n\n非自动对盘\n\nINTER_AUTO_MATCH\n\n同一证券商自动对盘\n\nINTER_NON_AUTO_MATCH\n\n同一证券商非自动对盘\n\nODD_LOT\n\n碎股交易\n\nAUCTION\n\n竞价交易\n\nBULK\n\n批量交易\n\nCRASH\n\n现金交易\n\nCROSS_MARKET\n\n跨市场交易\n\nBULK_SOLD\n\n批量卖出\n\nFREE_ON_BOARD\n\n离价交易\n\nRULE127_OR155\n\n第 127 条交易（纽交所规则）或第 155 条交易\n\nDELAY\n\n延迟交易\n\nMARKET_CENTER_CLOSE_PRICE\n\n中央收市价\n\nNEXT_DAY\n\n隔日交易\n\nMARKET_CENTER_OPENING\n\n中央开盘价交易\n\nPRIOR_REFERENCE_PRICE\n\n前参考价\n\nMARKET_CENTER_OPEN_PRICE\n\n中央开盘价\n\nSELLER\n\n卖方\n\nT\n\nT 类交易（盘前和盘后交易）\n\nEXTENDED_TRADING_HOURS\n\n延长交易时段\n\nCONTINGENT\n\n合单交易\n\nAVERAGE_PRICE\n\n平均价成交\n\nOTC_SOLD\n\n场外售出\n\nODD_LOT_CROSS_MARKET\n\n碎股跨市场交易\n\nDERIVATIVELY_PRICED\n\n衍生工具定价\n\nREOPENINGP_RICED\n\n再开盘定价\n\nCLOSING_PRICED\n\n收盘定价\n\nCOMPREHENSIVE_DELAY_PRICE\n\n综合延迟价格\n\nOVERSEAS\n\n交易的一方不是香港交易所的成员，属于场外交易\n\n#\n交易日查询市场\n\nTradeDateMarket\n\nNONE\n\n未知\n\nHK\n\n香港市场 \n\nUS\n\n美国市场 \n\nCN\n\nA 股市场\n\nNT\n\n深（沪）股通\n\nST\n\n港股通（深、沪）\n\nJP_FUTURE\n\n日本期货\n\nSG_FUTURE\n\n新加坡期货\n\n#\n交易日类型\n\nTradeDateType\n\nWHOLE\n\n全天交易\n\nMORNING\n\n上午交易，下午休市\n\nAFTERNOON\n\n下午交易，上午休市\n\n#\n窝轮状态\n\nWarrantStatus\n\nNONE\n\n未知\n\nNORMAL\n\n正常状态\n\nSUSPEND\n\n停牌\n\nSTOP_TRADE\n\n终止交易\n\nPENDING_LISTING\n\n等待上市\n\n#\n窝轮类型\n\nWrtType\n\nNONE\n\n未知\n\nCALL\n\n认购窝轮\n\nPUT\n\n认沽窝轮\n\nBULL\n\n牛证\n\nBEAR\n\n熊证\n\nINLINE\n\n界内证\n\n#\n所属交易所\n\nExchType\n\nNONE\n\n未知\n\nHK_MAINBOARD\n\n港交所·主板\n\nHK_GEMBOARD\n\n港交所·创业板\n\nHK_HKEX\n\n港交所\n\nUS_NYSE\n\n纽交所\n\nUS_NASDAQ\n\n纳斯达克\n\nUS_PINK\n\nOTC市场\n\nUS_AMEX\n\n美交所\n\nUS_OPTION\n\n美国 \n\nUS_NYMEX\n\nNYMEX\n\nUS_COMEX\n\nCOMEX\n\nUS_CBOT\n\nCBOT\n\nUS_CME\n\nCME\n\nUS_CBOE\n\nCBOE\n\nCN_SH\n\n上交所\n\nCN_SZ\n\n深交所\n\nCN_STIB\n\n科创板\n\nSG_SGX\n\n新交所\n\nJP_OSE\n\n大阪交易所\n\n#\n证券标识\n\nSecurity\n\nmessage Security\n{\n    required int32 market = 1; //QotMarket，行情市场\n    required string code = 2; //代码\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\nK 线数据\n\nKLine\n\nmessage KLine\n{\n    required string time = 1; //时间戳字符串（格式：yyyy-MM-dd HH:mm:ss）\n    required bool isBlank = 2; //是否是空内容的点,若为 true 则只有时间信息\n    optional double highPrice = 3; //最高价\n    optional double openPrice = 4; //开盘价\n    optional double lowPrice = 5; //最低价\n    optional double closePrice = 6; //收盘价\n    optional double lastClosePrice = 7; //昨收价\n    optional int64 volume = 8; //成交量\n    optional double turnover = 9; //成交额\n    optional double turnoverRate = 10; //换手率（该字段为百分比字段，展示为小数表示）\n    optional double pe = 11; //市盈率\n    optional double changeRate = 12; //涨跌幅（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    optional double timestamp = 13; //时间戳\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\n#\n基础报价的期权特有字段\n\nOptionBasicQotExData\n\nmessage OptionBasicQotExData\n{\n    required double strikePrice = 1; //行权价\n    required int32 contractSize = 2; //每份合约数(整型数据)\n    optional double contractSizeFloat = 17; //每份合约数（浮点型数据）\n    required int32 openInterest = 3; //未平仓合约数\n    required double impliedVolatility = 4; //隐含波动率（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    required double premium = 5; //溢价（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    required double delta = 6; //希腊值 Delta\n    required double gamma = 7; //希腊值 Gamma\n    required double vega = 8; //希腊值 Vega\n    required double theta = 9; //希腊值 Theta\n    required double rho = 10; //希腊值 Rho\n    optional int32 netOpenInterest = 11; //净未平仓合约数，仅港股期权适用\n    optional int32 expiryDateDistance = 12; //距离到期日天数，负数表示已过期\n    optional double contractNominalValue = 13; //合约名义金额，仅港股期权适用\n    optional double ownerLotMultiplier = 14; //相等正股手数，指数期权无该字段，仅港股期权适用\n    optional int32 optionAreaType = 15; //OptionAreaType，期权类型（按行权时间）\n    optional double contractMultiplier = 16; //合约乘数\n    optional int32 indexOptionType = 18; //IndexOptionType，指数期权类型\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\n#\n基础报价的期货特有字段\n\nFutureBasicQotExData\n\nmessage FutureBasicQotExData\n{\n    required double lastSettlePrice = 1; //昨结\n    required int32 position = 2; //持仓量\n    required int32 positionChange = 3; //日增仓\n    optional int32 expiryDateDistance = 4; //距离到期日天数\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n#\n基础报价\n\nBasicQot\n\nmessage BasicQot\n{\n    required Security security = 1; //股票\n    optional string name = 24; // 股票名称\n    required bool isSuspended = 2; //是否停牌\n    required string listTime = 3; //上市日期字符串（此字段停止维护，不建议使用，格式：yyyy-MM-dd）\n    required double priceSpread = 4; //价差\n    required string updateTime = 5; //最新价的更新时间字符串（格式：yyyy-MM-dd HH:mm:ss），对其他字段不适用\n    required double highPrice = 6; //最高价\n    required double openPrice = 7; //开盘价\n    required double lowPrice = 8; //最低价\n    required double curPrice = 9; //最新价\n    required double lastClosePrice = 10; //昨收价\n    required int64 volume = 11; //成交量\n    required double turnover = 12; //成交额\n    required double turnoverRate = 13; //换手率（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    required double amplitude = 14; //振幅（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    optional int32 darkStatus = 15; //DarkStatus, 暗盘交易状态\t\n    optional OptionBasicQotExData optionExData = 16; //期权特有字段\n    optional double listTimestamp = 17; //上市日期时间戳（此字段停止维护，不建议使用）\n    optional double updateTimestamp = 18; //最新价的更新时间戳，对其他字段不适用\n    optional PreAfterMarketData preMarket = 19; //盘前数据\n    optional PreAfterMarketData afterMarket = 20; //盘后数据\n    optional int32 secStatus = 21; //SecurityStatus, 股票状态\n    optional FutureBasicQotExData futureExData = 22; //期货特有字段\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n\n#\n盘前盘后数据\n\nPreAfterMarketData\n\n//美股支持盘前盘后数据\n//科创板仅支持盘后数据：成交量，成交额\nmessage PreAfterMarketData\n{\n    optional double price = 1;  // 盘前或盘后## 价格\n    optional double highPrice = 2;  // 盘前或盘后## 最高价\n    optional double lowPrice = 3;  // 盘前或盘后## 最低价\n    optional int64 volume = 4;  // 盘前或盘后## 成交量\n    optional double turnover = 5;  // 盘前或盘后## 成交额\n    optional double changeVal = 6;  // 盘前或盘后## 涨跌额\n    optional double changeRate = 7;  // 盘前或盘后## 涨跌幅（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    optional double amplitude = 8;  // 盘前或盘后## 振幅（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\n#\n分时数据\n\nTimeShare\n\nmessage TimeShare\n{\n    required string time = 1; //时间字符串（格式：yyyy-MM-dd HH:mm:ss）\n    required int32 minute = 2; //距离0点过了多少分钟\n    required bool isBlank = 3; //是否是空内容的点,若为 true 则只有时间信息\n    optional double price = 4; //当前价\n    optional double lastClosePrice = 5; //昨收价\n    optional double avgPrice = 6; //均价\n    optional int64 volume = 7; //成交量\n    optional double turnover = 8; //成交额\n    optional double timestamp = 9; //时间戳\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n#\n证券基本静态信息\n\nSecurityStaticBasic\n\n\nmessage SecurityStaticBasic\n{\n    required Qot_Common.Security security = 1; //股票\n    required int64 id = 2; //股票 ID\n    required int32 lotSize = 3; //每手数量,期权类型表示一份合约的股数\n    required int32 secType = 4; //Qot_Common.SecurityType,股票类型\n    required string name = 5; //股票名字\n    required string listTime = 6; //上市时间字符串（此字段停止维护，不建议使用，格式：yyyy-MM-dd）\n    optional bool delisting = 7; //是否退市\n    optional double listTimestamp = 8; //上市时间戳（此字段停止维护，不建议使用）\n    optional int32 exchType = 9; //Qot_Common.ExchType,所属交易所\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\n#\n窝轮额外静态信息\n\nWarrantStaticExData\n\nmessage WarrantStaticExData\n{\n    required int32 type = 1; //Qot_Common.WarrantType,窝轮类型\n    required Qot_Common.Security owner = 2; //所属正股\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\n期权额外静态信息\n\nOptionStaticExData\n\nmessage OptionStaticExData\n{\n    required int32 type = 1; //Qot_Common.OptionType,期权\n    required Qot_Common.Security owner = 2; //标的股\n    required string strikeTime = 3; //行权日（格式：yyyy-MM-dd）\n    required double strikePrice = 4; //行权价\n    required bool suspend = 5; //是否停牌\n    required string market = 6; //发行市场名字\n    optional double strikeTimestamp = 7; //行权日时间戳\n    optional int32 indexOptionType = 8; //Qot_Common.IndexOptionType, 指数期权的类型，仅在指数期权有效\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n#\n期货额外静态信息\n\nFutureStaticExData\n\nmessage FutureStaticExData\n{\n    required string lastTradeTime = 1; //最后交易日，只有非主连期货合约才有该字段\n    optional double lastTradeTimestamp = 2; //最后交易日时间戳，只有非主连期货合约才有该字段\n    required bool isMainContract = 3; //是否主连合约\n}    \n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\n证券静态信息\n\nSecurityStaticInfo\n\nmessage SecurityStaticInfo\n{\n    required SecurityStaticBasic basic = 1; //证券基本静态信息\n    optional WarrantStaticExData warrantExData = 2; //窝轮额外静态信息\n    optional OptionStaticExData optionExData = 3; //期权额外静态信息\n    optional FutureStaticExData futureExData = 4; //期货额外静态信息\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n#\n买卖经纪\n\nBroker\n\nmessage Broker\n{\n    required int64 id = 1; //经纪 ID\n    required string name = 2; //经纪名称\n    required int32 pos = 3; //经纪档位\n    \n    //以下为港股 SF 行情特有字段\n    optional int64 orderID = 4; //交易所订单 ID，与交易接口返回的订单 ID 并不一样\n    optional int64 volume = 5; //订单股数\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n#\n逐笔成交\n\nTicker\n\nmessage Ticker\n{\n    required string time = 1; //时间字符串（格式：yyyy-MM-dd HH:mm:ss）\n    required int64 sequence = 2; // 唯一标识\n    required int32 dir = 3; //TickerDirection, 买卖方向\n    required double price = 4; //价格\n    required int64 volume = 5; //成交量\n    required double turnover = 6; //成交额\n    optional double recvTime = 7; //收到推送数据的本地时间戳，用于定位延迟\n    optional int32 type = 8; //TickerType, 逐笔类型\n    optional int32 typeSign = 9; //逐笔类型符号\n    optional int32 pushDataType = 10; //用于区分推送情况，仅推送时有该字段\n    optional double timestamp = 11; //时间戳\n}\t\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n\n#\n买卖档明细\n\nOrderBookDetail\n\nmessage OrderBookDetail\n{\n    required int64 orderID = 1; //交易所订单 ID，与交易接口返回的订单 ID 并不一样\n    required int64 volume = 2; //订单股数\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\n买卖档\n\nOrderBook\n\nmessage OrderBook\n{\n    required double price = 1; //委托价格\n    required int64 volume = 2; //委托数量\n    required int32 orederCount = 3; //委托订单个数\n    repeated OrderBookDetail detailList = 4; //订单信息，港股 SF，美股深度摆盘特有\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n#\n持股变动\n\nShareHoldingChange\n\nmessage ShareHoldingChange\n{\n    required string holderName = 1; //持有者名称（机构名称 或 基金名称 或 高管姓名）\n    required double holdingQty = 2; //当前持股数量\n    required double holdingRatio = 3; //当前持股百分比（该字段为百分比字段，默认不展示 %，如 20 实际对应 20%）\n    required double changeQty = 4; //较上一次变动数量\n    required double changeRatio = 5; //较上一次变动百分比（该字段为百分比字段，默认不展示 %，如20实际对应20%。是相对于自身的比例，而不是总的。如总股本1万股，持有100股，持股百分比是1%，卖掉50股，变动比例是50%，而不是0.5%）\n    required string time = 6; //发布时间（格式：yyyy-MM-dd HH:mm:ss）\n    optional double timestamp = 7; //时间戳\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n#\n单个订阅类型信息\n\nSubInfo\n\nmessage SubInfo\n{\n    required int32 subType = 1;  //Qot_Common.SubType,订阅类型\n    repeated Qot_Common.Security securityList = 2; \t//订阅该类型行情的证券\n}\t\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\n单条连接订阅信息\n\nConnSubInfo\n\nmessage ConnSubInfo\n{\n    repeated SubInfo subInfoList = 1; //该连接订阅信息\n    required int32 usedQuota = 2; //该连接已经使用的订阅额度\n    required bool isOwnConnData = 3; //用于区分是否是自己连接的数据\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\n板块信息\n\nPlateInfo\n\nmessage PlateInfo\n{\n    required Qot_Common.Security plate = 1; //板块\n    required string name = 2; //板块名字\n    optional int32 plateType = 3; //PlateSetType 板块类型, 仅3207（获取股票所属板块）协议返回该字段\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\n复权信息\n\nRehab\n\nmessage Rehab\n{\n    required string time = 1; //时间字符串（格式：yyyy-MM-dd）\n    required int64 companyActFlag = 2; //公司行动(CompanyAct)组合标志位,指定某些字段值是否有效\n    required double fwdFactorA = 3; //前复权因子 A\n    required double fwdFactorB = 4; //前复权因子 B\n    required double bwdFactorA = 5; //后复权因子 A\n    required double bwdFactorB = 6; //后复权因子 B\n    optional int32 splitBase = 7; //拆股(例如，1拆5，Base 为1，Ert 为5)\n    optional int32 splitErt = 8;\t\n    optional int32 joinBase = 9; //合股(例如，50合1，Base 为50，Ert 为1)\n    optional int32 joinErt = 10;\t\n    optional int32 bonusBase = 11; //送股(例如，10送3, Base 为10,Ert 为3)\n    optional int32 bonusErt = 12;\t\n    optional int32 transferBase = 13; //转赠股(例如，10转3, Base 为10,Ert 为3)\n    optional int32 transferErt = 14;\t\n    optional int32 allotBase = 15; //配股(例如，10送2, 配股价为6.3元, Base 为10, Ert 为2, Price 为6.3)\n    optional int32 allotErt = 16;\t\n    optional double allotPrice = 17;\t\n    optional int32 addBase = 18; //增发股(例如，10送2, 增发股价为6.3元, Base 为10, Ert 为2, Price 为6.3)\n    optional int32 addErt = 19;\t\n    optional double addPrice = 20;\t\n    optional double dividend = 21; //现金分红(例如，每10股派现0.5元,则该字段值为0.05)\n    optional double spDividend = 22; //特别股息(例如，每10股派特别股息0.5元,则该字段值为0.05)\n    optional double timestamp = 23; //时间戳\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n\n公司行动组合标志位参见 CompanyAct\n#\n交割周期\n\nExpirationCycle\n\nNONE\n\n未知\n\nWEEK\n\n周期权\n\nMONTH\n\n月期权\n\n#\n股票持有者（已废弃）\n\nStockHolder\n\nNONE\n\n未知\n\nINSTITUTE\n\n机构\n\nFUND\n\n基金\n\nEXECUTIVE\n\n高管\n\n← 到价提醒回调\n交易接口总览 →\n\n行情定义"}, {"title": "通用定义 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/ftapi/common.html#7467", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\n基础功能\n通用定义\n底层协议介绍\n\nQ&A \n\n#\n通用定义\n#\n接口调用结果\n\nRET_CODE\n\nRET_OK\n\n成功\n\nRET_ERROR\n\n失败\n\n#\n协议格式\n\nProtoFMT\n\nProtobuf\n\nGoogle Protobuf 格式\n\nJson\n\nJson 格式\n\n#\n包加密算法\n#\n程序状态类型\n\nProgramStatusType\n\nNONE\n\n未知\n\nLOADED\n\n已完成必要模块加载\n\nLOGING\n\n登录中\n\nNEED_PIC_VERIFY_CODE\n\n需要图形验证码\n\nNEED_PHONE_VERIFY_CODE\n\n需要手机验证码\n\nLOGIN_FAILED\n\n登录失败\n\nFORCE_UPDATE\n\n客户端版本过低\n\nNESSARY_DATA_PREPARING\n\n正在拉取必要信息\n\nNESSARY_DATA_MISSING\n\n缺少必要信息\n\nUN_AGREE_DISCLAIMER\n\n未同意免责声明\n\nREADY\n\n正常可用状态\n\nFORCE_LOGOUT\n\nOpenD 登录后被强制退出登录\n\n#\n网关事件通知类型\n\nGtwEventType\n\nLocalCfgLoadFailed\n\n本地配置文件加载失败\n\nAPISvrRunFailed\n\n网关监听服务运行失败\n\nForceUpdate\n\n强制升级网关\n\nLoginFailed\n\n登录富途服务器失败\n\nUnAgreeDisclaimer\n\n未同意免责声明，无法运行\n\nLOGIN_FAILED\n\n登录失败\n\nNetCfgMissing\n\n缺少网络连接配置\n\nKickedOut\n\n登录被踢下线\n\nLoginPwdChanged\n\n登录密码变更\n\nBanLogin\n\n牛牛后台不允许该账号登录\n\nNeedPicVerifyCode\n\n登录需要输入图形验证码\n\nNeedPhoneVerifyCode\n\n登录需要输入手机验证码\n\nAppDataNotExist\n\n程序打包数据丢失\n\nNessaryDataMissing\n\n必要的数据没同步成功\n\nTradePwdChanged\n\n交易密码变更通知\n\nEnableDeviceLock\n\n需启用设备锁\n\n#\n系统通知类型\n\nSysNotifyType\n\nGTW_EVENT\n\n网关事件\n\nPROGRAM_STATUS\n\n程序状态变化\n\nCONN_STATUS\n\n与后台服务的连接状态变化\n\nQOT_RIGHT\n\n行情权限变化\n\n#\n包唯一标识\n\nPacketID\n\nmessage PacketID\n{\n\t  required uint64 connID = 1; //当前 TCP 连接的连接 ID，一条连接的唯一标识，InitConnect 协议会返回\n\t  required uint32 serialNo = 2; //自增序列号\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\n程序状态\n\nProgramStatus\n\nmessage ProgramStatus\n{\n\t  required ProgramStatusType type = 1; //当前状态\n\t  optional string strExtDesc = 2; // 额外描述\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n← 基础功能\n底层协议介绍 →\n\n通用定义\n接口调用结果\n协议格式\n包加密算法\n程序状态类型\n网关事件通知类型\n系统通知类型\n包唯一标识\n程序状态"}, {"title": "获取实时经纪队列 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-broker.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取实时经纪队列\n\nget_broker_queue(code)\n\n介绍\n\n获取已订阅股票的实时经纪队列数据，必须要先订阅。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\nbid_frame_table\tpd.DataFrame\t当 ret == RET_OK，bid_frame_table 返回买盘经纪队列数据\nstr\t当 ret != RET_OK，bid_frame_table 返回错误描述\nask_frame_table\tpd.DataFrame\t当 ret == RET_OK，ask_frame_table 返回卖盘经纪队列数据\nstr\t当 ret != RET_OK，ask_frame_table 返回错误描述\n买盘经纪队列格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nbid_broker_id\tint\t经纪买盘 ID\nbid_broker_name\tstr\t经纪买盘名称\nbid_broker_pos\tint\t经纪档位\norder_id\tint\t交易所订单 ID \n\norder_volume\tint\t单笔委托数量 \n卖盘经纪队列格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nask_broker_id\tint\t经纪卖盘 ID\nask_broker_name\tstr\t经纪卖盘名称\nask_broker_pos\tint\t经纪档位\norder_id\tint\t交易所订单 ID \n\norder_volume\tint\t单笔委托数量 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.BROKER], subscribe_push=False)\n# 先订阅经纪队列类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:   # 订阅成功\n    ret, bid_frame_table, ask_frame_table = quote_ctx.get_broker_queue('HK.00700')   # 获取一次经纪队列数据\n    if ret == RET_OK:\n        print(bid_frame_table)\n    else:\n        print('error:', bid_frame_table)\nelse:\n    print('subscription failed')\nquote_ctx.close()   # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\nOutput\n        code  name  bid_broker_id bid_broker_name  bid_broker_pos order_id order_volume\n0   HK.00700  腾讯控股           5338          J.P.摩根               1      N/A          N/A\n..       ...   ...            ...             ...             ...      ...          ...\n36  HK.00700  腾讯控股           8305  富途证券国际(香港)有限公司               4      N/A          N/A\n\n[37 rows x 7 columns]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\n提示\n\n此接口提供了一次性获取实时数据的功能，如需持续获取推送数据，请参考 实时经纪队列回调 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 获取实时逐笔\n获取标的市场状态 →\n\n获取实时经纪队列"}, {"title": "获取实时逐笔 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-ticker.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取实时逐笔\n\nget_rt_ticker(code, num=500)\n\n介绍\n\n获取已订阅股票的实时逐笔数据，必须要先订阅。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\nnum\tint\t最近逐笔个数\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回逐笔数据\nstr\t当 ret != RET_OK，返回错误描述\n逐笔数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nsequence\tint\t逐笔序号\ntime\tstr\t成交时间 \n\nprice\tfloat\t成交价格\nvolume\tint\t成交数量 \n\nturnover\tfloat\t成交金额\nticker_direction\tTickerDirect\t逐笔方向\ntype\tTickerType\t逐笔类型\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.TICKER], subscribe_push=False)\n# 先订阅逐笔类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:  # 订阅成功\n    ret, data = quote_ctx.get_rt_ticker('HK.00700', 2)  # 获取港股00700最近2个逐笔\n    if ret == RET_OK:\n        print(data)\n        print(data['turnover'][0])   # 取第一条的成交金额\n        print(data['turnover'].values.tolist())   # 转为 list\n    else:\n        print('error:', data)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\n       code  name                 time  price   volume     turnover ticker_direction             sequence        type\n0  HK.00700  腾讯控股  2023-07-19 15:59:58  332.4      100      33240.0             SELL  7257436441708356760  AUTO_MATCH\n1  HK.00700  腾讯控股  2023-07-19 16:08:12  333.0  1667000  555111000.0          NEUTRAL  7257438563422200985     AUCTION\n33240.0\n[33240.0, 555111000.0]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n接口限制\n\n最多能获取最近 1000 个逐笔数据，更多历史逐笔数据暂未提供\n港股期权期货在 LV1 权限下，不支持获取逐笔\n\n提示\n\n此接口提供了一次性获取实时数据的功能，如需持续获取推送数据，请参考 实时逐笔回调 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 获取实时分时\n获取实时经纪队列 →\n\n获取实时逐笔"}, {"title": "获取实时分时 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-rt.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取实时分时\n\nget_rt_data(code)\n\n介绍\n\n获取已订阅股票的实时分时数据，必须要先订阅。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回分时数据\nstr\t当 ret != RET_OK，返回错误描述\n分时数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\ntime\tstr\t时间 \n\nis_blank\tbool\t数据状态 \n\nopened_mins\tint\t零点到当前多少分钟\ncur_price\tfloat\t当前价格\nlast_close\tfloat\t昨天收盘的价格\navg_price\tfloat\t平均价格 \n\nvolume\tfloat\t成交量\nturnover\tfloat\t成交金额\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.RT_DATA], subscribe_push=False)\n# 先订阅分时数据类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:   # 订阅成功\n    ret, data = quote_ctx.get_rt_data('HK.00700')   # 获取一次分时数据\n    if ret == RET_OK:\n        print(data)\n    else:\n        print('error:', data)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close()   # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\nOutput\n         code  name                 time  is_blank  opened_mins  cur_price  last_close   avg_price   volume     turnover\n0    HK.00700  腾讯控股  2023-07-19 09:30:00     False          570      330.6       336.4  330.639590   380900  125940620.0\n..        ...   ...                  ...       ...          ...        ...         ...         ...      ...          ...\n330  HK.00700  腾讯控股  2023-07-19 16:00:00     False          960      333.0       336.4  330.400642  1766300  *********.0\n\n[331 rows x 10 columns]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n\n提示\n\n此接口提供了一次性获取实时数据的功能，如需持续获取推送数据，请参考 实时分时回调 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 获取实时 K 线\n获取实时逐笔 →\n\n获取实时分时"}, {"title": "获取实时摆盘 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-order-book.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取实时摆盘\n\nget_order_book(code, num=10)\n\n介绍\n\n获取已订阅股票的实时摆盘，必须要先订阅。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nnum\tint\t请求摆盘档数 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tdict\t当 ret == RET_OK，返回摆盘数据\nstr\t当 ret != RET_OK，返回错误描述\n\n摆盘数据格式如下：\n\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nsvr_recv_time_bid\tstr\t富途服务器从交易所收到买盘数据的时间 \n\nsvr_recv_time_ask\tstr\t富途服务器从交易所收到卖盘数据的时间 \n\nBid\tlist\t每个元祖包含如下信息：委托价格，委托数量，委托订单数，委托订单明细 \n\nAsk\tlist\t每个元祖包含如下信息：委托价格，委托数量，委托订单数，委托订单明细 \n\n其中，Bid 和 Ask 字段的结构如下：\n\n 'Bid': [ (bid_price1, bid_volume1, order_num, {'orderid1': order_volume1, 'orderid2': order_volume2, …… }), (bid_price2, bid_volume2, order_num,  {'orderid1': order_volume1, 'orderid2': order_volume2, …… }),…]\n 'Ask': [ (ask_price1, ask_volume1，order_num, {'orderid1': order_volume1, 'orderid2': order_volume2, …… }), (ask_price2, ask_volume2, order_num, {'orderid1': order_volume1, 'orderid2': order_volume2, …… }),…] \n\n \n\n        Copied!\n    \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret_sub = quote_ctx.subscribe(['HK.00700'], [SubType.ORDER_BOOK], subscribe_push=False)[0]\n# 先订阅买卖摆盘类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:  # 订阅成功\n    ret, data = quote_ctx.get_order_book('HK.00700', num=3)  # 获取一次 3 档实时摆盘数据\n    if ret == RET_OK:\n        print(data)\n    else:\n        print('error:', data)\nelse:\n    print('subscription failed')\nquote_ctx.close()  # 关闭当条连接，OpenD 会在 1 分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\nOutput\n{'code': 'HK.00700', 'name': '腾讯控股', 'svr_recv_time_bid': '', 'svr_recv_time_ask': '', 'Bid': [(332.8, 4700, 4, {}), (332.6, 158300, 11, {}), (332.4, 172700, 19, {})], 'Ask': [(333.0, 2393100, 98, {}), (333.2, 90300, 27, {}), (333.4, 107100, 16, {})]}\n\n \n\n        Copied!\n    \n1\n\n\n接口限制\n\n富途服务器从交易所收到数据的时间字段，仅支持A股正股、港股正股、ETFs、窝轮、牛熊，且仅开盘时间才有此数据。\n富途服务器从交易所收到数据的时间字段，部分情况下接收时间可能为零，例如：服务器重启或第一次推送的缓存数据。\n\n提示\n\n此接口提供了一次性获取实时数据的功能，如需持续获取推送数据，请参考 实时摆盘回调 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 获取实时报价\n获取实时 K 线 →\n\n获取实时摆盘"}, {"title": "获取实时 K 线 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-kl.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取实时 K 线\n\nget_cur_kline(code, num, ktype=KLType.K_DAY, autype=AuType.QFQ)\n\n介绍\n\n获取已订阅股票的实时 K 线数据，必须要先订阅。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nnum\tint\tK 线数据个数 \n\nktype\tKLType\tK 线类型\nautype\tAuType\t复权类型\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回 K 线数据数据\nstr\t当 ret != RET_OK，返回错误描述\nK 线数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\ntime_key\tstr\t时间 \n\nopen\tfloat\t开盘价\nclose\tfloat\t收盘价\nhigh\tfloat\t最高价\nlow\tfloat\t最低价\nvolume\tint\t成交量\nturnover\tfloat\t成交额\npe_ratio\tfloat\t市盈率\nturnover_rate\tfloat\t换手率 \n\nlast_close\tfloat\t昨收价 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.K_DAY], subscribe_push=False)\n# 先订阅 K 线类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:  # 订阅成功\n    ret, data = quote_ctx.get_cur_kline('HK.00700', 2, KLType.K_DAY, AuType.QFQ)  # 获取港股00700最近2个 K 线数据\n    if ret == RET_OK:\n        print(data)\n        print(data['turnover_rate'][0])   # 取第一条的换手率\n        print(data['turnover_rate'].values.tolist())   # 转为 list\n    else:\n        print('error:', data)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\n       code  name             time_key   open  close   high    low    volume      turnover  pe_ratio  turnover_rate  last_close\n0  HK.00700  腾讯控股  2023-07-18 00:00:00  351.8  336.4  351.8  335.0  29147987  9.911757e+09    15.283        0.00304       352.6\n1  HK.00700  腾讯控股  2023-07-19 00:00:00  330.6  333.0  333.8  327.0  21913296  7.240461e+09    15.128        0.00229       336.4\n0.00304\n[0.00304, 0.00229]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n\n接口限制\n\n此接口为获取实时 K 线接口，最多能获取最近的 1000 根。如需获取历史 K 线，请参考 获取历史 K 线\n市盈率和换手率字段，只有日 K 及以上周期的正股才有数据\n期权，仅提供日K, 1分K，5分K，15分K，60分K。\n\n提示\n\n此接口提供了一次性获取实时数据的功能，如需持续获取推送数据，请参考 实时 K 线回调 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 获取实时摆盘\n获取实时分时 →\n\n获取实时 K 线"}, {"title": "获取窝轮和期货列表 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-referencestock-list.html", "html": " Futu API 文档 v8.8\n编程语言\n简体中文\n编程语言\n简体中文\n\n介绍\n\n快速上手\n\nOpenD\n\n行情接口\n\n行情接口总览\n行情对象\n\n实时行情\n\n基本数据\n\n相关衍生品\n\n获取期权链到期日\n获取期权链\n筛选窝轮\n获取窝轮和期货列表\n获取期货合约资料\n\n全市场筛选\n\n个性化\n\n行情定义\n\n交易接口\n\n基础接口\n\nQ&A\n\n# 获取窝轮和期货列表\n\nget_referencestock_list(code, reference_type)\n\n介绍\n\n获取证券的关联数据，如：获取正股相关窝轮、获取期货相关合约\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t证券代码\nreference_type\tSecurityReferenceType\t要获得的相关数据\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回证券的关联数据\nstr\t当 ret != RET_OK，返回错误描述\n证券的关联数据格式如下：\n字段\t类型\t说明\ncode\tstr\t证券代码\nlot_size\tint\t每手股数，期货表示合约乘数\nstock_type\tSecurityType\t证券类型\nstock_name\tstr\t证券名字\nlist_time\tstr\t上市时间\n\nwrt_valid\tbool\t是否是窝轮\n\nwrt_type\tWrtType\t窝轮类型\nwrt_code\tstr\t所属正股\nfuture_valid\tbool\t是否是期货\n\nfuture_main_contract\tbool\t是否主连合约\n\nfuture_last_trade_time\tstr\t最后交易时间\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\n# 获取正股相关的窝轮\nret, data = quote_ctx.get_referencestock_list('HK.00700', SecurityReferenceType.WARRANT)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['code'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nprint('******************************************')\n# 港期相关合约\nret, data = quote_ctx.get_referencestock_list('HK.A50main', SecurityReferenceType.FUTURE)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['code'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\nOutput\n        code  lot_size stock_type stock_name   list_time  wrt_valid wrt_type  wrt_code  future_valid  future_main_contract  future_last_trade_time\n0     HK.24719      1000    WARRANT    腾讯东亚九四沽A  2018-07-20       True      PUT  HK.00700         False                   NaN                     NaN\n..         ...       ...        ...                ...       ...        ...       ...       ...           ...                   ...                    ...\n1617  HK.63402     10000    WARRANT    腾讯高盛一八牛Y  2020-11-26       True     BULL  HK.00700         False                   NaN                     NaN\n\n[1618 rows x 11 columns]\nHK.24719\n['HK.24719', 'HK.27886', 'HK.28621', 'HK.14339', 'HK.27952', 'HK.18693', 'HK.20306', 'HK.53635', 'HK.47269', 'HK.27227', \n...        ...       ...        ...        ...         ...        ...      ...       ... \n'HK.63402']\n******************************************\n        code  lot_size stock_type         stock_name list_time  wrt_valid  wrt_type  wrt_code  future_valid  future_main_contract future_last_trade_time\n0  HK.A50main      5000     FUTURE      安硕富时 A50 ETF主连(2012)                False       NaN       NaN          True                  True                       \n..         ...       ...        ...                ...       ...        ...       ...       ...           ...                   ...                    ...\n5  HK.A502106      5000     FUTURE      安硕富时 A50 ETF2106                False       NaN       NaN          True                 False             2021-06-29\n\n[6 rows x 11 columns]\nHK.A50main\n['HK.A50main', 'HK.A502011', 'HK.A502012', 'HK.A502101', 'HK.A502103', 'HK.A502106']\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n\n\n接口限制\n\n每 30 秒内最多请求 10 次获取证券关联数据接口\n当获取正股相关窝轮时，不受上述限频限制\n\n← 筛选窝轮 获取期货合约资料 →\n\n获取窝轮和期货列表"}, {"title": "获取期权链 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-option-chain.html", "html": " Futu API 文档 v8.8\n编程语言\n简体中文\n编程语言\n简体中文\n\n介绍\n\n快速上手\n\nOpenD\n\n行情接口\n\n行情接口总览\n行情对象\n\n实时行情\n\n基本数据\n\n相关衍生品\n\n获取期权链到期日\n获取期权链\n筛选窝轮\n获取窝轮和期货列表\n获取期货合约资料\n\n全市场筛选\n\n个性化\n\n行情定义\n\n交易接口\n\n基础接口\n\nQ&A\n\n# 获取期权链\n\nget_option_chain(code, index_option_type=IndexOptionType.NORMAL, start=None, end=None, option_type=OptionType.ALL, option_cond_type=OptionCondType.ALL, data_filter=None)\n\n介绍\n\n通过标的股票查询期权链。此接口仅返回期权链的静态信息，如需获取报价或摆盘等动态信息，请用此接口返回的股票代码，自行 订阅 所需要的类型。\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t标的股票代码\nindex_option_type\tIndexOptionType\t指数期权类型\n\nstart\tstr\t开始日期，该日期指到期日\n\nend\tstr\t结束日期（包括这一天），该日期指到期日\n\noption_type\tOptionType\t期权看涨看跌类型\n\noption_cond_type\tOptionCondType\t期权价内外类型\n\ndata_filter\tOptionDataFilter\t数据筛选条件\n\nstart 和 end 的组合如下：\n\nStart 类型\tEnd 类型\t说明\nstr\tstr\tstart 和 end 分别为指定的日期\nNone\tstr\tstart 为 end 往前 30 天\nstr\tNone\tend 为 start 往后30天\nNone\tNone\tstart 为当前日期，end 往后 30 天\n\nOptionDataFilter 字段如下\n\n字段\t类型\t说明\nimplied_volatility_min\tfloat\t隐含波动率过滤起点\n\nimplied_volatility_max\tfloat\t隐含波动率过滤终点\n\ndelta_min\tfloat\t希腊值 Delta 过滤起点\n\ndelta_max\tfloat\t希腊值 Delta 过滤终点\n\ngamma_min\tfloat\t希腊值 Gamma 过滤起点\n\ngamma_max\tfloat\t希腊值 Gamma 过滤终点\n\nvega_min\tfloat\t希腊值 Vega 过滤起点\n\nvega_max\tfloat\t希腊值 Vega 过滤终点\n\ntheta_min\tfloat\t希腊值 Theta 过滤起点\n\ntheta_max\tfloat\t希腊值 Theta 过滤终点\n\nrho_min\tfloat\t希腊值 Rho 过滤起点\n\nrho_max\tfloat\t希腊值 Rho 过滤终点\n\nnet_open_interest_min\tfloat\t净未平仓合约数过滤起点\n\nnet_open_interest_max\tfloat\t净未平仓合约数过滤终点\n\nopen_interest_min\tfloat\t未平仓合约数过滤起点\n\nopen_interest_max\tfloat\t未平仓合约数过滤终点\n\nvol_min\tfloat\t成交量过滤起点\n\nvol_max\tfloat\t成交量过滤终点\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回期权链数据\nstr\t当 ret != RET_OK，返回错误描述\n期权链数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t名字\nlot_size\tint\t每手股数，期权表示每份合约股数\n\nstock_type\tSecurityType\t股票类型\noption_type\tOptionType\t期权类型\nstock_owner\tstr\t标的股\nstrike_time\tstr\t行权日\n\nstrike_price\tfloat\t行权价\nsuspension\tbool\t是否停牌\n\nstock_id\tint\t股票 ID\nindex_option_type\tIndexOptionType\t指数期权类型\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret1, data1 = quote_ctx.get_option_expiration_date(code='HK.00700')\n\nfilter1 = OptionDataFilter()\nfilter1.delta_min = 0\nfilter1.delta_max = 0.1\n\nif ret1 == RET_OK:\n    expiration_date_list = data1['strike_time'].values.tolist()\n    for date in expiration_date_list:\n        ret2, data2 = quote_ctx.get_option_chain(code='HK.00700', start=date, end=date, data_filter=filter1)\n        if ret2 == RET_OK:\n            print(data2)\n            print(data2['code'][0])  # 取第一条的股票代码\n            print(data2['code'].values.tolist())  # 转为 list\n        else:\n            print('error:', data2)\n        time.sleep(3)\nelse:\n    print('error:', data1)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n\nOutput\n                     code                 name  lot_size stock_type option_type stock_owner strike_time  strike_price  suspension  stock_id index_option_type\n0     HK.TCH210429C350000   腾讯 210429 350.00 购       100       DRVT        CALL    HK.00700  2021-04-29         350.0       False  80235167               N/A\n1     HK.TCH210429P350000   腾讯 210429 350.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         350.0       False  80235247               N/A\n2     HK.TCH210429C360000   腾讯 210429 360.00 购       100       DRVT        CALL    HK.00700  2021-04-29         360.0       False  80235163               N/A\n3     HK.TCH210429P360000   腾讯 210429 360.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         360.0       False  80235246               N/A\n4     HK.TCH210429C370000   腾讯 210429 370.00 购       100       DRVT        CALL    HK.00700  2021-04-29         370.0       False  80235165               N/A\n5     HK.TCH210429P370000   腾讯 210429 370.00 沽       100       DRVT         PUT    HK.00700  2021-04-29         370.0       False  80235248               N/A\nHK.TCH210429C350000\n['HK.TCH210429C350000', 'HK.TCH210429P350000', 'HK.TCH210429C360000', 'HK.TCH210429P360000', 'HK.TCH210429C370000', 'HK.TCH210429P370000']\n...\n                   code                name  lot_size stock_type option_type stock_owner strike_time  strike_price  suspension  stock_id index_option_type\n0   HK.TCH220330C490000  腾讯 220330 490.00 购       100       DRVT        CALL    HK.00700  2022-03-30         490.0       False  80235143               N/A\n1   HK.TCH220330P490000  腾讯 220330 490.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         490.0       False  80235193               N/A\n2   HK.TCH220330C500000  腾讯 220330 500.00 购       100       DRVT        CALL    HK.00700  2022-03-30         500.0       False  80233887               N/A\n3   HK.TCH220330P500000  腾讯 220330 500.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         500.0       False  80233912               N/A\n4   HK.TCH220330C510000  腾讯 220330 510.00 购       100       DRVT        CALL    HK.00700  2022-03-30         510.0       False  80233747               N/A\n5   HK.TCH220330P510000  腾讯 220330 510.00 沽       100       DRVT         PUT    HK.00700  2022-03-30         510.0       False  80233766               N/A\nHK.TCH220330C490000\n['HK.TCH220330C490000', 'HK.TCH220330P490000', 'HK.TCH220330C500000', 'HK.TCH220330P500000', 'HK.TCH220330C510000', 'HK.TCH220330P510000']\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n\n\n接口限制\n\n每 30 秒内最多请求 10 次获取期权链接口\n传入的时间跨度上限为 30 天\n\n提示\n\n此接口不支持查询已过期的期权链，结束日期 参数请输入今天或未来的日期\nOpen interest (OI) 数据每日更新，更新时点取决于具体交易所。美股期权在盘前时段更新，港股期权在盘后更新。\n\n← 获取期权链到期日 筛选窝轮 →\n\n获取期权链"}, {"title": "实时分时回调 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/update-rt.html", "html": " Futu API 文档 v8.8\n编程语言\n简体中文\n编程语言\n简体中文\n\n介绍\n\n快速上手\n\nOpenD\n\n行情接口\n\n行情接口总览\n行情对象\n\n实时行情\n\n订阅\n\n推送回调\n\n实时报价回调\n实时摆盘回调\n实时 K 线回调\n实时分时回调\n实时逐笔回调\n实时经纪队列回调\n\n拉取\n\n基本数据\n\n相关衍生品\n\n全市场筛选\n\n个性化\n\n行情定义\n\n交易接口\n\n基础接口\n\nQ&A\n\n# 实时分时回调\n\non_recv_rsp(self, rsp_pb)\n\n介绍\n\n实时分时回调，异步处理已订阅股票的实时分时推送。\n在收到实时分时数据推送后会回调到该函数，您需要在派生类中覆盖 on_recv_rsp。\n\n参数\n\n参数\t类型\t说明\nrsp_pb\tQot_UpdateRT_pb2.Response\t派生类中不需要直接处理该参数\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回分时数据\nstr\t当 ret != RET_OK，返回错误描述\n分时数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\ntime\tstr\t时间\n\nis_blank\tbool\t数据状态\n\nopened_mins\tint\t零点到当前多少分钟\ncur_price\tfloat\t当前价格\nlast_close\tfloat\t昨天收盘的价格\navg_price\tfloat\t平均价格\n\nvolume\tfloat\t成交量\nturnover\tfloat\t成交金额\n\nExample\n\nimport time\nfrom futu import *\n\nclass RTDataTest(RTDataHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, data = super(RTDataTest, self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"RTDataTest: error, msg: %s\" % data)\n            return RET_ERROR, data\n        print(\"RTDataTest \", data) # RTDataTest 自己的处理逻辑\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = RTDataTest()\nquote_ctx.set_handler(handler)  # 设置实时分时推送回调\nret, data = quote_ctx.subscribe(['HK.00700'], [SubType.RT_DATA]) # 订阅分时类型，OpenD 开始持续收到服务器的推送\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\ntime.sleep(15)  # 设置脚本接收 OpenD 的推送持续时间为15秒\nquote_ctx.close()   # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅    \n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\nOutput\nRTDataTest         code  name                 time  is_blank  opened_mins  cur_price  last_close   avg_price     turnover   volume\n0  HK.00700  腾讯控股  2023-07-19 16:00:00     False          960      333.0       336.4  330.400642  *********.0  1766300\n\n1\n2\n\n\n提示\n\n此接口提供了持续获取推送数据的功能，如需一次性获取实时数据，请参考 获取实时分时 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 实时 K 线回调 实时逐笔回调 →\n\n实时分时回调"}, {"title": "Get the List of Trading Accounts | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/trade/get-acc-list.html#9665", "html": " Futu API Doc v8.8\nProgramming Language\n简体中文\nProgramming Language\n简体中文\n\nIntroduction\n\nQuick Start\n\nOpenD\n\nQuote API\n\nTrade API\n\nOverview\nTransaction Objects\n\nAccounts\n\nGet the List of Trading Accounts\nUnlock Trade\n\nAssets and Positions\n\nOrders\n\nDeals\n\nTrading Definitions\n\nBasic API\n\nQ&A\n\n# Get the List of Trading Accounts\n\nget_acc_list()\n\nDescription\n\nGet a list of trading accounts. Before calling other trading interfaces, please obtain this list first and confirm that the trading account to be operated is correct.\n\nParameters\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, trading account list is returned.\nstr\tIf ret != RET_OK, error description is returned.\nTrading account list format as follows:\nField\tType\tDescription\nacc_id\tint\tTrading account.\ntrd_env\tTrdEnv\tTrading environment.\nacc_type\tTrdAccType\tAccount type.\nuni_card_num\tstr\tUniversal account number, same as the display in the mobile terminal.\ncard_num\tstr\tTrading account number\n\nsim_acc_type\tSimAccType\tSimulate account type.\n\nsecurity_firm\tSecurityFirm\tSecurities firm to which the account belongs.\ntrdmarket_auth\tlist\tTransaction market authority.\n\nacc_status\tAccStatus\tAccount status.\n\nDescription\n\nAfter the paper trading of HK/US stock options is opened, this function will return 2 paper trading accounts when obtaining the list of HK/US trading accounts. The first one is the original account, and the second one is the option paper trading account.\n\nExample\n\nfrom futu import *\ntrd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111, security_firm=SecurityFirm.FUTUSECURITIES)\nret, data = trd_ctx.get_acc_list()\nif ret == RET_OK:\n    print(data)\n    print(data['acc_id'][0])  # Get the first account ID\n    print(data['acc_id'].values.tolist())  # convert to list\nelse:\n    print('get_acc_list error: ', data)\ntrd_ctx.close()\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\nOutput\n               acc_id   trd_env acc_type      uni_card_num          card_num   security_firm sim_acc_type            trdmarket_auth acc_status\n0  281756479345015383      REAL   MARGIN  ****************  ****************  FUTUSECURITIES          N/A  [HK, US, HKFUND, USFUND]     ACTIVE\n1             8377516  SIMULATE     CASH               N/A               N/A             N/A        STOCK                      [HK]     ACTIVE\n2            ********  SIMULATE   MARGIN               N/A               N/A             N/A       OPTION                      [HK]     ACTIVE\n3  281756455983234027      REAL   MARGIN               N/A  ****************  FUTUSECURITIES          N/A                      [HK]   DISABLED\n281756479345015383\n[281756479345015383, 8377516, ********, 281756455983234027]\n\n1\n2\n3\n4\n5\n6\n7\n\n\n← Transaction Objects Unlock Trade →\n\nGet the List of Trading Accounts"}, {"title": "Trading Definitions | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/trade/trade.html#8311", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nOverview\nTransaction Objects\n\nAccounts \n\nAssets and Positions \n\nOrders \n\nDeals \n\nTrading Definitions\n\nBasic API \n\nQ&A \n\n#\nTrading Definitions\n#\nAccount Risk Control Level\n\nCltRiskLevel\n\nNONE\n\nUnknown\n\nSAFE\n\nSafe\n\nWARNING\n\nWarning\n\nDANGER\n\nDanger\n\nABSOLUTE_SAFE\n\nAbsolutely safe\n\nOPT_DANGER\n\nDanger \n\nTips\n\nIt is recommanded to use risk_status field to get risk status of futures account, see CltRiskStatus\n#\nCurrency Type\n\nCurrency\n\nNONE\n\nUnknown currency\n\nHKD\n\nHK dollar\n\nUSD\n\nUS dollar\n\nCNH\n\nOffshore RMB\n\nJPY\n\nJapanese Yen\n\nSGD\n\nSG dollar\n\nAUD\n\nAustralian dollar\n\n#\nTrailType\n\nTrailType\n\nNONE\n\nUnknown\n\nRATIO\n\nTrailing ratio\n\nAMOUNT\n\nTrailing amount\n\n#\nModify Order Operation\n\nModifyOrderOp\n\nNONE\n\nUnknown operation\n\nNORMAL\n\nModify order\n\nCANCEL\n\nCancel \n\nDISABLE\n\nDisable \n\nENABLE\n\nEnable \n\nDELETE\n\nDelete \n\n#\nTransaction Status\n\nDealStatus\n\nOK\n\nTransaction success\n\nCANCELLED\n\nTransaction cancelled\n\nCHANGED\n\nTransaction changed\n\n#\nOrder Status\n\nOrderStatus\n\nNONE\n\nUnknown status\n\nWAITING_SUBMIT\n\nQueued \n\nSUBMITTING\n\nSubmitting \n\nSUBMITTED\n\nWorking \n\nFILLED_PART\n\nPartially filled \n\nFILLED_ALL\n\nFully filled\n\nCANCELLED_PART\n\nPartially cancelled\n\nCANCELLED_ALL\n\nFully cancelled\n\nFAILED\n\nFailed. Rejected by server.\n\nDIS<PERSON>LE<PERSON>\n\nDeactivated \n\nDELETED\n\nDeleted, only unfilled orders can be deleted \n\n#\nOrder Type\n\nTips\n\nOrder types supported in live trading.\nPaper trade only supports limit orders (NORMAL) and market orders (MARKET).\n\nOrderType\n\nNONE\n\nUnknown type.\n\nNORMAL\n\nLimit orders.\n\nMARKET\n\nMarket orders.\n\nABSOLUTE_LIMIT\n\nAbsolute limit orders. \n\nAUCTION\n\nAt-auction market orders. \n\nAUCTION_LIMIT\n\nAt-auction limit orders. \n\nSPECIAL_LIMIT\n\nSpecial limit orders. \n\nSPECIAL_LIMIT_ALL\n\nAON special limit orders. \n\nSTOP\n\nStop orders.\n\nSTOP_LIMIT\n\nStop Limit orders.\n\nMARKET_IF_TOUCHED\n\nMarket if Touched orders.\n\nLIMIT_IF_TOUCHED\n\nLimit if Touched orders.\n\nTRAILING_STOP\n\nTrailing Stop orders.\n\nTRAILING_STOP_LIMIT\n\nTrailing Stop Limit orders.\n\nTWAP_LIMIT\n\nTime Weighted Average Price Limit orders (HK and US securities only). \n\nTWAP\n\nTime Weighted Average Price Market orders (US securities only). \n\nVWAP_LIMIT\n\nVolume Weighted Average Price Limit orders (HK and US securities only). \n\nVWAP\n\nVolume Weighted Average Price Market orders (US securities only). \n\n#\nPosition Direction\n\nPositionSide\n\nNONE\n\nUnknown position\n\nLONG\n\nLong position, by default\n\nSHORT\n\nShort position\n\n#\nAccount Type\n\nTrdAccType\n\nNONE\n\nUnknown type\n\nCASH\n\nCash account\n\nMARGIN\n\nMargin Account\n\n#\nTrading Environment\n\nTrdEnv\n\nSIMULATE\n\nSimulated environment\n\nREAL\n\nReal environment\n\n#\nTransaction Market\n\nTrdMarket\n\nNONE\n\nUnknown market\n\nHK\n\nHong Kong market\n\nUS\n\nUS market\n\nCN\n\nA-share market \n\nHKCC\n\nHKCC market \n\nFUTURES\n\nFutures market\n\nFUTURES_SIMULATE_US\n\nUS futures simulated market \n\nFUTURES_SIMULATE_HK\n\nHong Kong futures simulated market \n\nFUTURES_SIMULATE_SG\n\nSingapore futures simulated market \n\nFUTURES_SIMULATE_JP\n\nJapan futures simulated market \n\nHKFUND\n\nHK fund market \n\nUSFUND\n\nUS fund market \n\n#\nAccount Status\n\nAccStatus\n\nACTIVE\n\nActive account\n\nDISABLED\n\nDisabled account\n\n#\nTransaction Securities Market\n#\nTransaction Direction\n\nTrdSide\n\nNONE\n\nUnknown direction\n\nBUY\n\nBuy\n\nSELL\n\nSell\n\nSELL_SHORT\n\nSell short\n\nBUY_BACK\n\nBuy back\n\nTips\n\nIt is recommanded that only use Buy or Sell as the input parameter of direction of place_order interface.\nBuyBack and SellShort is only used as the display field for Get Order List , Get History Order List, Orders Push Callback, Get Today's Deals, Get Historical Deals and Deals Push Callback interface.\n\n#\nOrder Validity Period\n\nTimeInForce\n\nDAY\n\nGood for the day\n\nGTC\n\nGood until cancel\n\n#\nSecurities Firm to Which the Account Belongs\n\nSecurityFirm\n\nNONE\n\nUnknown\n\nFUTUSECURITIES\n\nFUTU HK\n\nFUTUINC\n\nMoomoo US\n\nFUTUSG\n\nMoomoo SG\n\nFUTUAU\n\nMoomoo AU\n\n#\nSimulate Account Type\n\nSimAccType\n\nNONE\n\nUnknown\n\nSTOCK\n\nStock simulation account\n\nOPTION\n\nOption simulation account\n\nFUTURES\n\nFutures simulation account\n\n#\nAccount Risk Control Status\n\nCltRiskStatus\n\nNONE\n\nUnknown\n\nLEVEL1\n\nVery Safe\n\nLEVEL2\n\nSafe\n\nLEVEL3\n\nSafe\n\nLEVEL4\n\nLow Risk\n\nLEVEL5\n\nMedium Risk\n\nLEVEL6\n\nHigh Risk\n\nLEVEL7\n\nWarning\n\nLEVEL8\n\nMargin Call\n\nLEVEL9\n\nMargin Call\n\n#\nDay-trading Status\n\nDtStatus\n\nNONE\n\nUnknown\n\nUnlimited\n\nUnlimited \n\nEM_Call\n\nEM-Call \n\nDT_Call\n\nDT-Call \n\n#\nTransaction Category\n\nTrdCategory\n\nenum TrdCategory\n{\n    TrdCategory_Unknown = 0; //Unknown\n    TrdCategory_Security = 1; //Securities\n    TrdCategory_Future = 2; //Futures\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\nAccount Cash Information\n\nAccCashInfo\n\nmessage AccCashInfo\n{\n    optional int32 currency = 1; //Currency type, refer to Currency\n    optional double cash = 2; //Cash balance\n    optional double availableBalance = 3; //Available cash withdrawal amount\n    optional double netCashPower = 4;\t\t// Net cash power\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n#\nTransaction Protocol Public Header\n\nTrdHeader\n\nmessage TrdHeader\n{\n  required int32 trdEnv = 1; //Trading environment, refer to the enumeration definition of TrdEnv\n  required uint64 accID = 2; //Trading account, trading account should match to trading environment and market permissions, otherwise an error will be returned\n  required int32 trdMarket = 3; //Trading market, refer to the enumeration definition of TrdMarket\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\nTrading Account\n\nTrdAcc\n\nmessage TrdAcc\n{\n  required int32 trdEnv = 1; //Trading environment, refer to the enumeration definition of TrdEnv\n  required uint64 accID = 2; //Trading account\n  repeated int32 trdMarketAuthList = 3; //The trading market permissions supported by the trading account, can have multiple trading market permissions, currently only a single, refer to the enumeration definition of TrdMarket\n  optional int32 accType = 4; //Account type, refer to TrdAccType\n  optional string cardNum = 5; //card number\n  optional int32 securityFirm = 6; //security firm，refer to SecurityFirm\n  optional int32 simAccType = 7; //simulate account type, see SimAccType\n  optional string uniCardNum = 8; //Universal account number\n  optional int32 accStatus = 9; //Account status，refer to TrdAccStatus\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n#\nAccount Funds\n\nFunds\n\nmessage Funds\n{\n  required double power = 1; //Maximum Buying Power (Minimum OpenD version requirements: 5.0.1310. This field is the approximate value calculated according to the marginable initial margin of 50%. But in fact, this ratio of each financial contract is not the same. We recommend using Buy on Margin, returned by Query the Maximum Quantity that Can be Bought or Sold, to get the maximum quantity can buy.) \n  required double totalAssets = 2; //Net Assets\n  required double cash = 3; //Cash (Only Single market accounts use this field. If your account is an universial account, please use cashInfoList to get cash for each currency.)\n  required double marketVal = 4; //Securities Market Value (only applicable to securities accounts)\n  required double frozenCash = 5; //Funds on Hold \n  required double debtCash = 6; //Interest Charged Amount (Minimum OpenD version requirements: 5.0.1310) \n  required double avlWithdrawalCash = 7; //Withdrawable Cash (Only Single market accounts use this field. If your account is an universial account, please use cashInfoList to get withdrawalbe cash for each currency.)\n\n  optional int32 currency = 8;            //The currency used for this query (only applicable to universal securities accounts and futures accounts). See Currency\n  optional double availableFunds = 9;     //Available funds (only applicable to futures accounts)\n  optional double unrealizedPL = 10;      //Unrealized gain or loss (only applicable to futures accounts)\n  optional double realizedPL = 11;        //Realized gain or loss (only applicable to futures accounts)\n  optional int32 riskLevel = 12;           //Risk control level (only applicable to futures accounts), See CltRiskLevel. It is recommanded to use riskStatus field to get the risk status of securities accounts or futures accounts.\n  optional double initialMargin = 13;      //Initial Margin (only applicable to futures accounts, minimum OpenD version requirements: 5.0.1310)\n  optional double maintenanceMargin = 14;  //Maintenance Margin (Minimum OpenD version requirements: 5.0.1310) \n  repeated AccCashInfo cashInfoList = 15;  //Cash information by currency (only applicable to futures accounts)\n  optional double maxPowerShort = 16; //Short Buying Power (Minimum OpenD version requirements: 5.0.1310. This field is the approximate value calculated according to the shortable initial margin of 60%. But in fact, this ratio of each financial contract is not the same. We recommend using the Short sell field, returned by the API of Query the Maximum Quantity that Can be Bought or Sold, to get the maximum quantity can be shorted.) \n  optional double netCashPower = 17;  //Cash Buying Power （Only Single market accounts use this field. If your account is an universial account, please use cashInfoList to get cash buying power for each currency.）\n  optional double longMv = 18;        //Long Market Value (Minimum OpenD version requirements: 5.0.1310) \n  optional double shortMv = 19;       //Short Market Value (Minimum OpenD version requirements: 5.0.1310) \n  optional double pendingAsset = 20;  //Asset in Transit (Minimum OpenD version requirements: 5.0.1310) \n  optional double maxWithdrawal = 21;          //Maximum Withdrawal (only applicable to securities accounts, minimum OpenD version requirements: 5.0.1310) \n  optional int32 riskStatus = 22;              //Risk status (only applicable to securities accounts, minimum OpenD version requirements: 5.0.1310), divided into 9 grades, LEVEL1 is the safest and LEVEL9 is the most dangerous. See CltRiskStatus\n  optional double marginCallMargin = 23;       //Margin-call Margin (Minimum OpenD version requirements: 5.0.1310) \n  \n  optional bool isPdt = 24;\t\t\t\t//Is it marked as a PDT. True: It is a PDT.  False: Not a PDT. Only applicable to securities accounts of moomoo US. Minimum OpenD version requirements: 5.8.2008.\n  optional string pdtSeq = 25;\t\t\t//Day Trades Left. Only applicable to securities accounts of moomoo US. Minimum OpenD version requirements: 5.8.2008. \n  optional double beginningDTBP = 26;\t\t//Beginning DTBP. Only applicable to securities accounts of moomoo US marked as a PDT. Minimum OpenD version requirements: 5.8.2008.\n  optional double remainingDTBP = 27;\t\t//Remaining DTBP. Only applicable to securities accounts of moomoo US marked as a PDT. Minimum OpenD version requirements: 5.8.2008.\n  optional double dtCallAmount = 28;\t\t//Day-trading Call Amount. Only applicable to securities accounts of moomoo US marked as a PDT. Minimum OpenD version requirements: 5.8.2008.\n  optional int32 dtStatus = 29;\t\t\t\t//Day-trading Status. Only applicable to securities accounts of moomoo US marked as a PDT. Minimum OpenD version requirements: 5.8.2008.\n  \n  optional double securitiesAssets = 30; // Net asset value of securities\n  optional double fundAssets = 31; // Net asset value of fund\n  optional double bondAssets = 32; // Net asset value of bond\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n\n#\nAccount Holding\n\nPosition\n\nmessage Position\n{\n    required uint64 positionID = 1; //Position ID, a unique identifier of a position\n    required int32 positionSide = 2; //Position direction, refer to the enumeration definition of PositionSide\n    required string code = 3; //Code\n    required string name = 4; //Name\n    required double qty = 5; //Holding quantity, 2 decimal places, the same below\n    required double canSellQty = 6; //Available quantity. Available quantity = Holding quantity - Frozen quantity. The unit of options and futures is \"contract\".\n    required double price = 7; //Market price, 3 decimal places, 2 decimal places for futures\n    optional double costPrice = 8; //Diluted Cost (for securities account). Average opening price (for futures account). No precision limit for securities. 2 decimal places for futures. If not passed, it means this value is invalid at this time.\n    required double val = 9; //Market value, 3 decimal places, value of this field for futures is 0\n    required double plVal = 10; //Amount of profit or loss, 3 decimal places,  2 decimal places for futures\n    optional double plRatio = 11; //Percentage of profit or loss (such as plRatio equal to 0.088 represents an increase of 8.8%), no precision limit, if not passed, it means this value is invalid at this time\n    optional int32 secMarket = 12; //The market to which the securities belong, refer to enumeration definition of TrdSecMarket\n    \n    //The following is the statistics of this position today\n    optional double td_plVal = 21; //Today's profit or loss, 3 decimal places, the same below,  2 decimal places for futures\n    optional double td_trdVal = 22; //Today's trading volume, not applicable for futures\n    optional double td_buyVal = 23; //Total value bought today, not applicable for futures\n    optional double td_buyQty = 24; //Total amount bought today, not applicable for futures\n    optional double td_sellVal = 25; //Total value sold today, not applicable for futures\n    optional double td_sellQty = 26; //Total amount sold today, not applicable for futures\n\n    optional double unrealizedPL = 28; //Unrealized profit or loss (only applicable to futures accounts)\n    optional double realizedPL = 29; //Realized profit or loss (only applicable to futures accounts)\n    optional int32 currency = 30;        // Currency type, refer to Currency\n    optional int32 trdMarket = 31;  //Trading market, refer to the enumeration definition of TrdMarket\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n\n#\nOrder\n\nOrder\n\nmessage Order\n{\n    required int32 trdSide = 1; //Trading direction, refer to TrdSide enumeration definition\n    required int32 orderType = 2; //Order type, refer to enumeration definition of OrderType\n    required int32 orderStatus = 3; //Order status, refer to enumeration definition of OrderStatus\n    required uint64 orderID = 4; //Order number\n    required string orderIDEx = 5; //Extended order number (only for checking the problem)\n    required string code = 6; //code\n    required string name = 7; //Name\n    required double qty = 8; //Order quantity,  3 decimal places, option unit is \"Zhang\"\n    optional double price = 9; //Order price, 3 decimal places\n    required string createTime = 10; //Create time, strictly in accordance with YYYY-MM-DD HH:MM:SS or YYYY-MM-DD HH:MM:SS.MS format\n    required string updateTime = 11; //The last update time, strictly according to YYYY-MM-DD HH:MM:SS or YYYY-MM-DD HH:MM:SS.MS format\n    optional double fillQty = 12; //Filled quantity, 2 decimal place accuracy, the option unit is \"contract\"\n    optional double fillAvgPrice = 13; //Average price of the fill, no precision limit\n    optional string lastErrMsg = 14; //The last error description, if there is an error, there will be this description of the reason for the last error, no error is empty\n    optional int32 secMarket = 15; //The market to which the securities belong, refer to enumeration definition of TrdSecMarket\n    optional double createTimestamp = 16; //Timestamp for creation\n    optional double updateTimestamp = 17; //Timestamp for last update\n    optional string remark = 18; //User remark string, the maximum length is 64 bytes\n    optional double auxPrice = 21; //Trigger price\n    optional int32 trailType = 22; //Trailing type, see Trd_Common.TrailType enumeration definition\n    optional double trailValue = 23; //Trailing amount / ratio\n    optional double trailSpread = 24; //Specify spread\n    optional int32 currency = 25;        // Currency type, refer to Currency\n    optional int32 trdMarket = 26;  //Trading market, refer to the enumeration definition of TrdMarket\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n\n#\nOrder Fee Item\n\nOrderFeeItem\n\nmessage OrderFeeItem\n{\n    optional string title = 1; //Fee title\n    optional double value = 2; //Fee Value\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n\n#\nOrder Fee\n\nOrderFee\n\nmessage OrderFee\n{\n    required string orderIDEx = 1; //Server order id\n    optional double feeAmount = 2; //Fee amount\n    repeated OrderFeeItem feeList = 3; //Fee details\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n\n#\nOrder Fill\n\nOrderFill\n\nmessage OrderFill\n{\n    required int32 trdSide = 1; //Trading direction, refer to enumeration definition of TrdSide\n    required uint64 fillID = 2; //OrderFill ID\n    required string fillIDEx = 3; //Extended OrderFill ID (only for checking the problem)\n    optional uint64 orderID = 4; //Order ID\n    optional string orderIDEx = 5; //Extended order ID (only when checking the problem)\n    required string code = 6; //code\n    required string name = 7; //Name\n    required double qty = 8; //Filled quantity, 2 decimal place accuracy, the option unit is \"contract\"\n    required double price = 9; //Price of the fill, 3 decimal places\n    required string createTime = 10; //Create time (transaction time), in strict accordance with YYYY-MM-DD HH:MM:SS or YYYY-MM-DD HH:MM:SS.MS format\n    optional int32 counterBrokerID = 11; //Counter Broker ID, valid for Hong Kong stocks\n    optional string counterBrokerName = 12; //Counter Broker Name, valid for Hong Kong stocks\n    optional int32 secMarket = 13; //Securities belong to the market, refer to enumeration definition of TrdSecMarket\n    optional double createTimestamp = 14; //Create a timestamp\n    optional double updateTimestamp = 15; //last update timestamp\n    optional int32 status = 16; //Deal status, refer to enumeration definition of OrderFillStatus\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n\n#\nMaximum Trading Quantity\n\nMaxTrdQtys\n\nmessage MaxTrdQtys\n{\n    //Due to the current server's implementation, it is required to sell the holding positions before a short selling, and to buy back short positions before a long buying (two steps). Nevertheless a bulish buying can be bought in one step with cash and financing. Please note this difference\n    required double maxCashBuy = 1; //Buy on cash. (Maximum quantity that can be bought in cash. The unit of options is \"contract\".Futures accounts are not applicable.)\n    optional double maxCashAndMarginBuy = 2; //Buy on margin. (Maximum quantity that can be bought on margin. The unit of options is \"contract\". Futures accounts are not applicable.)\n    required double maxPositionSell = 3; //Sell on position. (Maximum quantity can be sold. The unit of options is \"contract\".)\n    optional double maxSellShort = 4; //Short sell. (Maximum quantity can be shorted. The unit of options is \"contract\". Futures accounts are not applicable.)\n    optional double maxBuyBack = 5; //Short positions. (Buyback required quantity to close a position. When holding short positions, you must first buy back the short positions before you can continue to buy long. The unit of options and futures is \"contract\".)\n    optional double longRequiredIM = 6;         //Initial margin change when buying one contract of an asset. Only futures and options apply. No position: Returns the initial margin needed to buy one contract (a positive value).   Long position: Returns the initial margin required to buy one contract (a positive value). Short position: Returns the initial margin released for buying back one contract (a negative value). \n    optional double shortRequiredIM = 7;        //Initial margin change when selling one contract of an asset. Currently only futures and options apply. No position: Returns the initial margin needed to short one contract (a positive value). Long position: Returns the initial margin released for selling one contract (a negative value).  Short position: Returns the initial margin needed to short one contract (a positive value).\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n#\nFilter Conditions\n\nTrdFilterConditions\n\nmessage TrdFilterConditions\n{\n  repeated string codeList = 1; //Code filtering, only returns the products for these codes, and this condition is ignored if it is not set\n  repeated uint64 idList = 2; //ID primary key filter, only returns the products with these IDs, no filtering is not passed, orderID for order, fillID for deal, positionID for position\n  optional string beginTime = 3; //Start time, strictly in accordance with YYYY-MM-DD HH:MM:SS or YYYY-MM-DD HH:MM:SS.MS format. It is invalid for holding positions, and historical data must be filled in\n  optional string endTime = 4; //The end time, strictly in accordance with YYYY-MM-DD HH:MM:SS or YYYY-MM-DD HH:MM:SS.MS format. It is invalid for holding positions, and historical data must be filled in\n  repeated string orderIDExList = 5; // The server order id list, which can be used instead of orderID list, or choose one from orderID list\n}\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\n← Deals Push Callback\nBasic Functions →\n\nTrading Definitions"}, {"title": "实时经纪队列回调 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/update-broker.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n实时报价回调\n实时摆盘回调\n实时 K 线回调\n实时分时回调\n实时逐笔回调\n实时经纪队列回调\n\n拉取 \n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n实时经纪队列回调\n\non_recv_rsp(self, rsp_pb)\n\n介绍\n\n实时经纪队列回调，异步处理已订阅股票的实时经纪队列推送。\n在收到实时经纪队列数据推送后会回调到该函数，您需要在派生类中覆盖 on_recv_rsp。\n\n参数\n\n参数\t类型\t说明\nrsp_pb\tQot_UpdateBroker_pb2.Response\t派生类中不需要直接处理该参数\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\ttuple\t当 ret == RET_OK，返回经纪队列数据\nstr\t当 ret != RET_OK，返回错误描述\n\n经纪队列元组内容如下：\n\n字段\t类型\t说明\nstock_code\tstr\t股票\nbid_frame_table\tpd.DataFrame\t买盘数据\nask_frame_table\tpd.DataFrame\t卖盘数据\nbid_frame_table 格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nbid_broker_id\tint\t经纪买盘 ID\nbid_broker_name\tstr\t经纪买盘名称\nbid_broker_pos\tint\t经纪档位\norder_id\tint\t交易所订单 ID \n\norder_volume\tint\t单笔委托数量 \nask_frame_table 格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nask_broker_id\tint\t经纪卖盘 ID\nask_broker_name\tstr\t经纪卖盘名称\nask_broker_pos\tint\t经纪档位\norder_id\tint\t交易所订单 ID \n\norder_volume\tint\t单笔委托数量 \n\nExample\n\nimport time\nfrom futu import *\n    \nclass BrokerTest(BrokerHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, err_or_stock_code, data = super(BrokerTest, self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"BrokerTest: error, msg: {}\".format(err_or_stock_code))\n            return RET_ERROR, data\n        print(\"BrokerTest: stock: {} data: {} \".format(err_or_stock_code, data))  # BrokerTest 自己的处理逻辑\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = BrokerTest()\nquote_ctx.set_handler(handler)  # 设置实时经纪推送回调\nret, data = quote_ctx.subscribe(['HK.00700'], [SubType.BROKER]) # 订阅经纪类型，OpenD 开始持续收到服务器的推送\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\ntime.sleep(15)  # 设置脚本接收 OpenD 的推送持续时间为15秒\nquote_ctx.close()   # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n\nOutput\nBrokerTest: stock: HK.00700 data: [        code  name  bid_broker_id bid_broker_name  bid_broker_pos order_id order_volume\n0   HK.00700  腾讯控股           5338          J.P.摩根               1      N/A          N/A\n..       ...   ...            ...             ...             ...      ...          ...\n36  HK.00700  腾讯控股           8305  富途证券国际(香港)有限公司               4      N/A          N/A\n\n[37 rows x 7 columns],         code  name  ask_broker_id ask_broker_name  ask_broker_pos order_id order_volume\n0   HK.00700  腾讯控股           1179  华泰金融控股(香港)有限公司               1      N/A          N/A\n..       ...   ...            ...             ...             ...      ...          ...\n39  HK.00700  腾讯控股           6996      中国投资信息有限公司               1      N/A          N/A\n\n[40 rows x 7 columns]] \n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n\n提示\n\n此接口提供了持续获取推送数据的功能，如需一次性获取实时数据，请参考 获取实时经纪队列 接口\n获取实时数据 和 实时数据回调 的差别，请参考 如何通过订阅接口获取实时行情？\n\n← 实时逐笔回调\n获取快照 →\n\n实时经纪队列回调"}, {"title": "简易程序运行 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quick/demo.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\n可视化 OpenD\n编程环境搭建\n简易程序运行\n交易策略搭建示例\n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n简易程序运行\n#\nPython 示例\n#\n第一步：下载安装登录 OpenD\n\n请参考 这里，完成 OpenD 的下载、安装和登录。\n\n#\n第二步：下载 Python API\n\n方式一：在 cmd 中直接使用 pip 安装。\n\n初次安装：Windows 系统 $ pip install futu-api，Linux/Mac系统 $ pip3 install futu-api。\n二次升级：Windows 系统 $ pip install futu-api --upgrade，Linux/Mac系统 $ pip3 install futu-api --upgrade。\n\n方式二：点击下载最新版本的 Python API 安装包。\n\n#\n第三步：创建新项目\n\n打开 PyCharm，在 Welcome to PyCharm 窗口中，点击 New Project。如果你已经创建了一个项目，可以选择打开该项目。\n\n#\n第四步：创建新文件\n\n在该项目下，创建新 Python 文件，并把下面的示例代码拷贝到文件里。\n示例代码功能包括查看行情快照、模拟交易下单。\n\nfrom futu import *\n\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)  # 创建行情对象\nprint(quote_ctx.get_market_snapshot('HK.00700'))  # 获取港股 HK.00700 的快照数据\nquote_ctx.close() # 关闭对象，防止连接条数用尽\n\n\ntrd_ctx = OpenSecTradeContext(host='127.0.0.1', port=11111)  # 创建交易对象\nprint(trd_ctx.place_order(price=500.0, qty=100, code=\"HK.00700\", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE))  # 模拟交易，下单（如果是真实环境交易，在此之前需要先解锁交易密码）\n\ntrd_ctx.close()  # 关闭对象，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\n#\n第五步：运行文件\n\n右键点击运行，可以看到运行成功的返回信息如下：\n\n2020-11-05 17:09:29,705 [open_context_base.py] _socket_reconnect_and_wait_ready:255: Start connecting: host=127.0.0.1; port=11111;\n2020-11-05 17:09:29,705 [open_context_base.py] on_connected:344: Connected : conn_id=1; \n2020-11-05 17:09:29,706 [open_context_base.py] _handle_init_connect:445: InitConnect ok: conn_id=1; info={'server_version': 218, 'login_user_id': 7157878, 'conn_id': 6730043337026687703, 'conn_key': '3F17CF3EEF912C92', 'conn_iv': 'C119DDDD6314F18A', 'keep_alive_interval': 10, 'is_encrypt': False};\n(0,        code          update_time  last_price  open_price  high_price  ...  after_high_price  after_low_price  after_change_val  after_change_rate  after_amplitude\n0  HK.00700  2020-11-05 16:08:06       625.0       610.0       625.0  ...               N/A              N/A               N/A                N/A              N/A\n\n[1 rows x 132 columns])\n2020-11-05 17:09:29,739 [open_context_base.py] _socket_reconnect_and_wait_ready:255: Start connecting: host=127.0.0.1; port=11111;\n2020-11-05 17:09:29,739 [network_manager.py] work:366: Close: conn_id=1\n2020-11-05 17:09:29,739 [open_context_base.py] on_connected:344: Connected : conn_id=2; \n2020-11-05 17:09:29,740 [open_context_base.py] _handle_init_connect:445: InitConnect ok: conn_id=2; info={'server_version': 218, 'login_user_id': 7157878, 'conn_id': 6730043337169705045, 'conn_key': 'A624CF3EEF91703C', 'conn_iv': 'BF1FF3806414617B', 'keep_alive_interval': 10, 'is_encrypt': False};\n(0,        code stock_name trd_side order_type order_status  ... dealt_avg_price  last_err_msg  remark time_in_force fill_outside_rth\n0  HK.00700       腾讯控股      BUY     NORMAL   SUBMITTING  ...             0.0                                 DAY              N/A\n\n[1 rows x 16 columns])\n2020-11-05 17:09:32,843 [network_manager.py] work:366: Close: conn_id=2\n(0,        code stock_name trd_side      order_type order_status  ... dealt_avg_price  last_err_msg  remark time_in_force fill_outside_rth\n0  HK.00700       腾讯控股      BUY  ABSOLUTE_LIMIT    SUBMITTED  ...             0.0                                 DAY              N/A\n\n[1 rows x 16 columns])\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n\n\n← 编程环境搭建\n交易策略搭建示例 →\n\n简易程序运行"}, {"title": "订阅反订阅 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/sub.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n订阅反订阅\n获取订阅状态\n\n推送回调 \n\n拉取 \n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n订阅反订阅\n#\n订阅\n\nsubscribe(code_list, subtype_list, is_first_push=True, subscribe_push=True, is_detailed_orderbook=False, extended_time=False)\n\n介绍\n\n订阅注册需要的实时信息，指定股票和订阅的数据类型即可。\n香港市场（含正股、窝轮、牛熊、期权、期货）订阅，需要 LV1 及以上的权限，BMP 权限下不支持订阅。\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t需要订阅的股票代码列表 \n\nsubtype_list\tlist\t需要订阅的数据类型列表 \n\nis_first_push\tbool\t订阅成功之后是否立即推送一次缓存数据 \n\nsubscribe_push\tbool\t订阅后是否推送 \n\nis_detailed_orderbook\tbool\t是否订阅详细的摆盘订单明细 \n\nextended_time\tbool\t是否允许美股盘前盘后数据 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\nerr_message\tNoneType\t当 ret == RET_OK 时，返回 None\nstr\t当 ret != RET_OK 时，返回错误描述\n\nExample\n\nimport time\nfrom futu import *\nclass OrderBookTest(OrderBookHandlerBase):\n    def on_recv_rsp(self, rsp_pb):\n        ret_code, data = super(OrderBookTest,self).on_recv_rsp(rsp_pb)\n        if ret_code != RET_OK:\n            print(\"OrderBookTest: error, msg: %s\" % data)\n            return RET_ERROR, data\n        print(\"OrderBookTest \", data) # OrderBookTest 自己的处理逻辑\n        return RET_OK, data\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nhandler = OrderBookTest()\nquote_ctx.set_handler(handler)  # 设置实时摆盘回调\nquote_ctx.subscribe(['HK.00700'], [SubType.ORDER_BOOK])  # 订阅买卖摆盘类型，OpenD 开始持续收到服务器的推送\ntime.sleep(15)  #  设置脚本接收 OpenD 的推送持续时间为15秒\nquote_ctx.close()  # 关闭当条连接，OpenD 会在1分钟后自动取消相应股票相应类型的订阅\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n\nOutput\nOrderBookTest  {'code': 'HK.00700', 'svr_recv_time_bid': '2020-04-29 10:40:03.147', 'svr_recv_time_ask': '2020-04-29 10:40:03.147', 'Bid': [(416.8, 2600, 11, {}), (416.6, 13100, 17, {}), (416.4, 24600, 17, {}), (416.2, 28000, 13, {}), (416.0, 46900, 30, {}), (415.8, 10900, 7, {}), (415.6, 7100, 9, {}), (415.4, 13300, 3, {}), (415.2, 300, 3, {}), (415.0, 11200, 36, {})], 'Ask': [(417.0, 17600, 31, {}), (417.2, 17800, 24, {}), (417.4, 15300, 10, {}), (417.6, 28800, 17, {}), (417.8, 20700, 11, {}), (418.0, 114200, 155, {}), (418.2, 20600, 19, {}), (418.4, 24100, 28, {}), (418.6, 42700, 45, {}), (418.8, 181900, 76, {})]}\n\n \n\n        Copied!\n    \n1\n\n#\n取消订阅\n\nunsubscribe(code_list, subtype_list, unsubscribe_all=False)\n\n介绍\n\n取消订阅\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t取消订阅的股票代码列表 \n\nsubtype_list\tlist\t需要订阅的数据类型列表 \n\nunsubscribe_all\tbool\t取消所有订阅 \n\nReturn\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\nerr_message\tNoneType\t当 ret == RET_OK, 返回 None\nstr\t当 ret != RET_OK, 返回错误描述\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nprint('current subscription status :', quote_ctx.query_subscription())  # 查询初始订阅状态\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE, SubType.TICKER], subscribe_push=False)\n# 先订阅了 QUOTE 和 TICKER 两个类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:   # 订阅成功\n    print('subscribe successfully！current subscription status :', quote_ctx.query_subscription())  # 订阅成功后查询订阅状态\n    time.sleep(60)  # 订阅之后至少1分钟才能取消订阅\n    ret_unsub, err_message_unsub = quote_ctx.unsubscribe(['HK.00700'], [SubType.QUOTE])\n    if ret_unsub == RET_OK:\n        print('unsubscribe successfully！current subscription status:', quote_ctx.query_subscription())  # 取消订阅后查询订阅状态\n    else:\n        print('unsubscription failed！', err_message_unsub)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\ncurrent subscription status : (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\nsubscribe successfully！current subscription status : (0, {'total_used': 2, 'remain': 998, 'own_used': 2, 'sub_list': {'QUOTE': ['HK.00700'], 'TICKER': ['HK.00700']}})\nunsubscribe successfully！current subscription status: (0, {'total_used': 1, 'remain': 999, 'own_used': 1, 'sub_list': {'TICKER': ['HK.00700']}})\n\n \n\n        Copied!\n    \n1\n2\n3\n\n#\n取消所有订阅\n\nunsubscribe_all()\n\n介绍\n\n取消所有订阅\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\nerr_message\tNoneType\t当 ret == RET_OK, 返回 None\nstr\t当 ret != RET_OK, 返回错误描述\n\nExample\n\nfrom futu import *\nimport time\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nprint('current subscription status :', quote_ctx.query_subscription())  # 查询初始订阅状态\nret_sub, err_message = quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE, SubType.TICKER], subscribe_push=False)\n# 先订阅了 QUOTE 和 TICKER 两个类型。订阅成功后 OpenD 将持续收到服务器的推送，False 代表暂时不需要推送给脚本\nif ret_sub == RET_OK:  # 订阅成功\n    print('subscribe successfully！current subscription status :', quote_ctx.query_subscription())  # 订阅成功后查询订阅状态\n    time.sleep(60)  # 订阅之后至少1分钟才能取消订阅\n    ret_unsub, err_message_unsub = quote_ctx.unsubscribe_all()  # 取消所有订阅\n    if ret_unsub == RET_OK:\n        print('unsubscribe all successfully！current subscription status:', quote_ctx.query_subscription())  # 取消订阅后查询订阅状态\n    else:\n        print('Failed to cancel all subscriptions！', err_message_unsub)\nelse:\n    print('subscription failed', err_message)\nquote_ctx.close()  # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\ncurrent subscription status : (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\nsubscribe successfully！current subscription status : (0, {'total_used': 2, 'remain': 998, 'own_used': 2, 'sub_list': {'QUOTE': ['HK.00700'], 'TICKER': ['HK.00700']}})\nunsubscribe all successfully！current subscription status: (0, {'total_used': 0, 'remain': 1000, 'own_used': 0, 'sub_list': {}})\n\n \n\n        Copied!\n    \n1\n2\n3\n\n\n接口限制\n\n支持多种实时数据类型的订阅，参见 SubType ，每支股票订阅一个类型占用一个额度。\n订阅额度规则请参见 订阅额度 & 历史 K 线额度。\n至少订阅一分钟才可以反订阅。\n由于港股 SF 行情摆盘数据量较大，为保证 SF 行情的速度和 OpenD 的处理性能，目前 SF 权限用户仅限同时订阅 50 只证券类产品（含 hkex 的正股、窝轮、牛熊）的摆盘，剩余订阅额度仍可用于订阅其他类型，如：逐笔，买卖经纪等。\n港股期权期货在 LV1 权限下，不支持订阅逐笔类型。\n\n← 行情对象\n获取订阅状态 →\n\n订阅反订阅"}, {"title": "行情接口总览 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/overview.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n行情接口总览\n模块\t接口名\t功能简介\n实时行情\t订阅\tsubscribe\t订阅实时数据，指定股票代码和订阅的数据类型即可\nunsubscribe\t取消订阅\nunsubscribe_all\t取消所有订阅\nquery_subscription\t查询订阅信息\n推送回调\tStockQuoteHandlerBase\t报价推送\nOrderBookHandlerBase\t摆盘推送\nCurKlineHandlerBase\tK 线推送\nTickerHandlerBase\t逐笔推送\nRTDataHandlerBase\t分时推送\nBrokerHandlerBase\t经纪队列推送\n拉取\tget_market_snapshot\t获取市场快照\nget_stock_quote\t获取订阅股票报价的实时数据，有订阅要求限制\nget_order_book\t获取实时摆盘数据\nget_cur_kline\t实时获取指定股票最近 num 个 K 线数据\nget_rt_data\t获取指定股票的分时数据\nget_rt_ticker\t获取指定股票的实时逐笔。取最近 num 个逐笔\nget_broker_queue\t获取股票的经纪队列\n基本数据\tget_market_state\t获取股票对应市场的市场状态\nget_capital_flow\t获取个股资金流向\nget_capital_distribution\t获取个股资金分布\nget_owner_plate\t获取单支或多支股票的所属板块信息列表\nrequest_history_kline\t获取 K 线，不需要事先下载 K 线数据\nget_rehab\t获取给定股票的复权因子\n相关衍生品\tget_option_expiration_date\t通过标的股票，查询期权链的所有到期日\nget_option_chain\t通过标的股查询期权\nget_warrant\t拉取窝轮和相关衍生品数据接口\nget_referencestock_list\t获取证券的关联数据\nget_future_info\t获取期货合约资料\n全市场筛选\tget_stock_filter\t获取条件选股\nget_plate_stock\t获取特定板块下的股票列表\nget_plate_list\t获取板块集合下的子板块列表\nget_stock_basicinfo\t获取指定市场中特定类型或特定股票的基本信息\nget_ipo_list\t获取指定市场的 ipo 列表\nget_global_state\t获取全局市场状态\nrequest_trading_days\t获取交易日历\n个性化\tget_history_kl_quota\t获取已使用过的额度，即当前周期内已经下载过多少只股票\nset_price_reminder\t设置到价提醒\nget_price_reminder\t获取对某只股票(某个市场)设置的到价提醒列表\nget_user_security_group\t获取自选股分组列表\nget_user_security\t获取指定分组的自选股列表\nmodify_user_security\t修改指定分组的自选股列表\nPriceReminderHandlerBase\t到价提醒推送\n\n← 运维命令\n行情对象 →\n\n行情接口总览"}, {"title": "Others | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/qa/other.html#3495", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\nOpenD Related\nQuote related\nTransaction related\nOthers\n#\nOthers\n#\nQ1：Experience exchange channels\nE-mail: <EMAIL>\nWhen feeding back questions through the mailbox channel, we recommend you provide your Futu ID, the time when the error occurred, a detailed description of the problem as well as relevant screenshhots. Transaction issues (e.g., place_order failed) require the order number to facilitate location and confirmation of problems.\n\nQQ Group: 108534288(OpenAPI I), 229850364(OpenAPI Ⅱ)，567665692（OpenAPI Ⅲ） \n\nNote: To be validated, you should answer the questions about OpenAPI correctly, which can be found in the API Doc.\n\nTeam support\nInvestment teams or institutional clients can contact <PERSON> (QQ: 522904020), providing you with a dedicated team to assist in solving technical challenges and achieving your specific product needs.\n\n#\nQ2: Is there more complete strategy examples for reference?\n\nA:\n\nPython strategy examples are in the /futu/examples/ folder. You can find the path of Python API by executing the following command:\nimport futu\nprint(futu.__file__)\n\n \n\n        Copied!\n    \n1\n2\n\nThe C# strategy examples are in the /FTAPI4NET/Sample/ folder\nThe Java strategy examples are in the /FTAPI4J/sample/ folder\nThe C++ strategy examples are under the /FTAPI4CPP/Sample/ folder\nThe JavaScript strategy examples are in the /FTAPI4JS/sample/ folder\n#\nQ3: Import error when using python API\nA:\n\nFirst Scene:\nI have already installed futu-api, but still get error: No module named 'futu'?\nIt is possible that the interpreter your IDE currently uses is not the interpreter of the futu-api module you installed. In other words, your may have more than two Python environments installed on your computer. You can do the following 2 steps:\n\nRun the codes below to get the path of the current interpreter:\nimport sys\nprint(sys.executable)\n\n \n\n        Copied!\n    \n1\n2\n\n\nExample diagram:\n\n\nRun $ D:\\software\\anaconda3\\python.exe -m pip install futu-api in command line (The first half of the command comes from the result of step 1). This will install a futu-api module in the current interpreter.\n#\nQ4: Import successful, but you still cannot call the relevant interface.\n\nA: Usually in this case, you need to check if the ‘futu’ that was successfully imported is a correct Futu API.\n\nFirst Scene: There may be a file with the same name as 'futu'.\n\nThe current file name is futu.py\nThere is another file named futu.py under the path of the current file.\nThere is a folder named /futu under the path of the current file.\n\nTherefore, we strongly recommend that you do not name files / folders / projects as futu.\n\nSecond Scene: A third-party library called 'futu' was installed by mistake.\n\nThe correct name of the Futu API library is futu-api, not 'futu'.\n\nIf you have installed a third-party library named 'futu', please uninstall it and install futu-api.\n\nTake PyCharm as an example: Check the installation of libraries.\n\n\n\n\n#\nQ5: Protocol Encryption-Related\n\nA:\n\n#\nOverview\n\nTo ensure privacy and confidentiality, you can use the asymmetric encryption algorithm RSA to encrypt the request and return between Strategy Scripts (Futu API) and OpenD.\nIf Strategy Scripts (Futu API) is on the same computer as OpenD, it is usually not necessary to encrypt.\n\n#\nProtocol Encryption Process\n\nYou can try to solve this problem with the following steps:\n\nGenerate the key file automatically through a third-party web platform.\nTo be specific: Search 'Online RSA Key Generator' on Baidu or Google. Set Key Format as PKCS#1. Set Key Length as 1024 bit. No password required. Then click the bottom 'Generate key pair' \nCopy and paste the private key into a text file. Save it to a specified path of the computer which OpenD is located in.\nSpecify the path of the RSA private key file on the computer which OpenD is located in. The path is the specified path mentioned in Step 2.\nMethod 1: Specify the path mentioned in Step 2 through 'Encryption Private Key' in Visualization OpenD. As shown below: \nMethod 2: Specify the path mentioned in Step 2 through the code rsa_private_key in Command Line OpenD. As shown below: \nSave the text file in step 2 to a specified path of the computer which Strategy Scripts (Futu API) are located in, and set the path of private key in Strategy Scripts.\nEnable protocol encryption. There are two ways to enable protocol encryption.\nMethod 1: Encrypt the context independently (general). You can set encryption through the parameter is_encrypt when creating and initializing the connection in Quote Object or Transaction Objects.\nMethod 2: Encrypt the context globally (only Python). You can set encryption through the interface enable_proto_encrypt.\n\nTips\n\nWhen specifying the path of RSA private key in OpenD or in Strategy Scripts (Futu API), the path needs to be complete and include the file name.\nIt is not necessary to save RSA public key which can be calculated by private key.\n#\nQ6: Why is the DataFrame data I got incomplete?\n\nA: When printing pandas.DataFrame data, if there are too many columns and rows, pandas will collapse the data by default, resulting in an incomplete display.\nTherefore, it is not OpenD's fault. You can add the following code in front of your Python script to solve the problem.\n\nimport pandas as pd\npd.options.display.max_rows=5000\npd.options.display.max_columns=5000\npd.options.display.width=1000\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n\n#\nQ7: How to solve the problem that \"Cannot open libFTAPIChannel.dylib\" through C++ API on Mac?\n\nA: Execute the following command in the directory where the file \"libFTAPIChannel.dylib\" is stored: $ xattr -r -d com.apple.quarantine libFTAPIChannel.dylib.\n\n#\nQ8: For Python users, why do large log files continue to be generated under the log folder, after the log level is set to no in the OpenD configuration file?\n\nA：The log_level parameter in OpenD parameter configuration is only used to control the logs generated by OpenD. Python API also generates logs by default.\nIf you do not like it, you can add the following codes to your Python script:\n\nlogger.file_level = logging.FATAL  # Used to stop Python API log files generating\nlogger.console_level = logging.FATAL  # Used to stop printing Python log in running console\n\n \n\n        Copied!\n    \n1\n2\n\n#\nQ9: For versions 5.4 and above, the library name and configuration method of Java API have been changed.\n\nA:\n\nIf you are a user of Java API 5.3 and below, please note the following changes when updating the version.\nChanges to the configuration process:\n\nDownload Futu API from Futubull official website.\nDecompress the downloaded file. /FTAPI4J is the directory of Java API. Add /lib/futu-api-.x.y.z.jar file to your project settings. To establish a futu-api project, please refer to here.\n\nChanges to the directory:\n\nFor the Java version of Futu API, the library name is changed from ftapi4j.jar to futu-api-x.y.z.jar, where \"x.y.z\" represents the version number.\nFor the third-party library, the dependencies of /lib/jna.jar and /lib/jna-platform.jar are removed, and the dependencies of /lib/bcprov-jdk15on-1.68.jar and /lib/bcpkix-jdk15on-1.68.jar are added.\n+---ftapi4j                      FTAPI4J source code. If the JDK version used is not compatible, you can use the project to recompile the ftapi.jar.\n+---lib                          The folder with common libraries\n|    futu-api-x.y.z.jar          Java version of Futu API\n|    bcprov-jdk15on-1.68.jar     Third-party library, for encryption and decryption\n|    bcpkix-jdk15on-1.68.jar     Third-party library, for encryption and decryption\n|    protobuf-java-3.5.1.jar     Third-party library, for parsing protobuf data\n+---sample                       Sample project\n+---resources                    The default generated directory of the maven project\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\nIf you are a new user to the Futu API, we provide a more convenient way to configure Java API via maven repository for you. About the configuration process, please refer to here.\n\n#\nQ10: For Python users, when using pyinstaller to package scripts that need to run api, an error is reported: Common_pb2 module cannot be found.\n\nA: You can try to solve this problem with the following steps.\nStep 1. Suppose you need to package main.py. Using a command-line statement and run the statement: pyinstaller path\\main.py, without the \"- F\" parameter.\n\npyinstaller path\\main.py\n\n \n\n        Copied!\n    \n1\n\n\nAfter main.py is packaged, the /main folder will be created in the /dist directory where it is located. main.exe is in this folder.\n\n\nStep 2. Run the following code to find the installation path of futu-api: /path/futu.\n\nimport futu\nprint(futu.__file__)\n\n \n\n        Copied!\n    \n1\n2\n\n\nResults:\n\nC:\\Users\\<USER>\\Anaconda3\\lib\\site-packages\\futu\\__init__.py\n\n \n\n        Copied!\n    \n1\n\n\nStep 3. Copy all the files in the /common/pb to /main.\n\nStep 4. Create a folder in the /main and name it futu. Copy the /path/futu/VERSION.txt file to /main/futu.\n\nStep 5. Try running the statement pyinstaller main.py again.\n\n#\nQ11: Why the interface result is success, but the return did not behave as expected？\n\nA:\n\nA successful interface result means that server has successfully received and responded to your request, but the return may not behave as your expected.\n\nExample: If you call the subscribe during non-trading hours, your request can be responded successfully, but the exchange will not update the ticker data during this period. So you will temporarily not receive real-time data until trading hours.\n\nThe interface result (definition: Interface Result) can be viewed from the field returned. A field of 0 means the interface result success, a non-zero means the interface result failed.\n\nFor python user, the following two code statements are equivalent:\n\nif ret_code == RET_OK:\n\n \n\n        Copied!\n    \n1\n\nif ret_code == 0:\n\n \n\n        Copied!\n    \n1\n\n#\nQ12: WebSocket Related\n#\nOverview\n\nIn OpenAPI, WebSocket is mainly used in the following two aspects:\n\nIn Visualization OpenD, WebSocket is used to communicate between the UI interface and the underlying Command Line OpenD.\nThe communication between JavaScript API and OpenD uses WebSocket.\n\nWhen WebSocket starts, Command Line OpenD establishes a Socket connection (TCP) with the FTWebSocket transit service. This connection uses the default listening address and API protocol listening port.\nAt the same time, JavaScript API will establish a WebSocket connection (HTTP) with the FTWebSocket transit service. This connection will use the WebSocket listening address and WebSocket port.\n#\nUsage\n\nTo ensure the security of your account, when WebSocket listens non-local requests, we strongly recommend that you enable SSL and configure the WebSocket authentication key\n\nSSL is enabled by configuring the WebSocket certificate and the WebSocket private key. Command Line OpenD can set the file path by configuring OpenD.xml or configuring command line parameters. Visualization OpenD clicks the \"more\" drop-down menu to see the confifuration item.\n\nTips\n\nIf the certificate is self-signed, you need to install the certificate on the machine where the JavaScript API is called, or set not to verify the certificate.\n\n#\nGenerate Self-signed Certificate\n\nIt is not convenient to expand the details of self-signed certificate generation in this document, please check it yourself. Simple and available build steps are provided here:\n\nInstall openssl.\nModify openssl.cnf and add the IP address or domain name under the alt_names node on the machine where OpenD locates.\nFor example: IP.2 = xxx.xxx.xxx.xxx, DNS.2 = www.xxx.com\nGenerate private key and certificate (PEM)。\n\nThe certificate generation parameters are as follows：\nopenssl req -x509 -newkey rsa:2048 -out futu.cer -outform PEM -keyout futu.key -days 10000 -verbose -config openssl.cnf -nodes -sha256 -subj \"/CN=Futu CA\" -reqexts v3_req -extensions v3_req\n\nTips\n\nopenssl.cnf needs to be placed under the system path, or an absolute path needs to be specified in the build parameters.\nNote that while generating a private key, you need to specify that the password is not set (-notes).\n\nAttach the local self-signed certificate and the configuration file that generates the certificate for testing:\n\nopenssl.cnf\nfutu.cer\nfutu.key\n#\nQ13: Where are the quote servers and the trade servers of OpenAPI?\n\nA：\n\nQuote:\nFutu ID\tQuote Server Location\nFutubull ID\tTencent Cloud Guangzhou and Hong Kong\nmoomoo ID\tTencent Cloud Virginia, USA and Singapore\nTrade:\nSecurities Firm\tTrade Server Location\nFUTU HK\tTencent Cloud Hong Kong\nMoomoo US\tTencent Cloud Virginia, USA\nMoomoo SG\tTencent Cloud Singapore\nMoomoo AU\tAWS Cloud Sydney\n#\nQ14: How to build C++ API?\n\nA: futu-api c++ SDK is supported on Windows/MacOS/Linux. Pre-built libs are provided for the common build environment on each platform:\n\nOS\tBuilding Environment\nWindows\tVisual Studio 2013\nCentos 7\tg++ 4.8.5\nUbuntu 16.04\tg++ 5.4.0\nMacOS\tXCode 11\n\nIf different compiler version is used, or different protobuf version is used, FTAPI and protobuf may be re-built. FTAPI source directory layout is:\n\nFTAPI directory structure：\n+---Bin                               Libs for common build environment\n+---Include                           Public headers, source files generated from proto files\n+---Sample                            Sample project\n\\---Src\n    +---FTAPI                         FTAPI source\n    +---protobuf-all-3.5.1.tar.gz     protobuf source\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n\n#\nBuild steps：\nBuild protobuf to generate libprotobuf static lib and protoc executable.\nGenerated C++ source files from proto files.\nBuild FTAPI to generate libFTAPI static lib\n#\nStep1: Build protobuf：\nWindows：\nInstall CMake\nOpen Visual Studio command prompt, change directory to protobuf/cmake\nRun：cmake -G \"Visual Studio 12 2013\" -DCMAKE_INSTALL_PREFIX=install -Dprotobuf_BUILD_TESTS=OFF  This will generate Visual Studio 2013 solution file. Change -G parameter for other Visual Studio versions.\nOpen Visual Studio solution file, set platform toolset to v120_xp, then build.\nLinux（Refer to protobuf/src/README)\nRun ./autogen.sh\nRun CXXFLAGS=\"-std=gnu++11\" ./configure --disable-shared\nRun make\nPut generated libprotobuf.a in Bin/Linux\nMacOS（Refer to protobuf/src/README)\nInstall dependencies with brew：autoconf automake libtool\nRun ./configure CC=clang CXX=\"clang++ -std=gnu++11 -stdlib=libc++\" --disable-shared\n#\nStep2: Generate C++ sources from proto files\nUse protoc to convert protofiles under Include/Proto to C++ source files. For example, the following command converts Common.proto to Common.pb.h and Common.pb.cc:\nprotoc -I=\"path-to-FTAPI/Include/Proto\" --cpp_out=\".\" path-to-FTAPI/Include/Proto/Common.proto\nPut the generated .h and .cc files in Include/Proto\n#\nStep3: Build FTAPI\nWindows：Create Visual Studio C++ static lib project，add source files under Src/FTAPI and Include，and set platform toolset to v120_xp.\nMac：Create XCode C++ static lib project，add source files under Src/FTAPI and Include\nLinux：Use cmake to build FTAPI static lib, run following command under path-to-FTAPI/Src:\ncmake -DTARGET_OS=Linux\n#\nQ15: The Guide for Universal Account Upgrade\n#\n1. Universal Account Upgrade\n\nThe universal account allows trading securities, futures, and forex across various markets using multiple currencies within one account. Upgrading one or multiple single-market accounts to a universal account involves migrating under your old account. This includes:\n\nCreating a universal account\nTransferring assets from your existing single-market account to the universal account\nClosing the single-market account\n#\n2. OpenD Version Upgrade\n\nWe are scheduled to upgrade your accounts to universal accounts on September 14th and 15th. Please check your OpenD and API versions in advance.\n\nVersion 7.01 and below\nDue to the outdated versions, OpenD will discontinue service on September 14th, during which you will be logged out of OpenD automatically. We recommend upgrading your OpenD and API to the latest version before September 14th, and stopping any live trading strategies over the weekend of September 14th to 15th.\n\nVersion 7.02 to 8.2\nDue to the older versions, OpenD no longer supports universal accounts. We recommend upgrading your OpenD and API to the latest version before September 14th, and stopping any live trading strategies over the weekend of September 14th to 15th.\n\nVersion 8.3 and above\nYou can use these versions normally. However, we also recommend not running any live trading strategies over the weekend of September 14th to 15th.\n\nAfter upgrading, your assets will be transferred to the new universal account, causing strategies targeting the old account to malfunction. We recommend conducting necessary checks and tests before live trading, to ensure everything is set up properly.\n\n#\n3. Changes in OpenAPI after the OpenD upgrade\n\nPython API will no longer support creating transaction objects with OpenHKTradeContext, OpenUSTradeContext, OpenHKCCTradeContext, and OpenCNTradeContext. Please refer to the Create the connection, and use OpenSecTradeContext instead.\n\nFor non-Python API users, when using the Trd_GetAccList, please set the needGeneralSecAccount to true in order to get Universal account information.\n\nAdd Account Status\nWhen using the Get the List of Trading Accounts, the results will now include an acc_status field.\n\nThe universal accounts are marked as ACTIVE.\nThe old single-market accounts are marked as DISABLED.\n\nChanges in Trading API: Place Orders, Modify or Cancel Orders, Query the Maximum Quantity that Can be Bought or Sold\n\nExecuting transactions and querying purchasing power can only be allowed via the acc_id or acc_index of ACTIVE accounts.\nUsing the acc_id or acc_index of DISABLED accounts will cause errors.\nPython API：please specify the acc_id as the upgraded universal account.\nNon-Python API：in the TrdHeader, please specify the accID as the upgraded universal account.\n#\n4. Need help?\n\nTeam Support\nIf you encounter any issues during the upgrade process or while using the universal account, you can contact our technical/product teams through official channels.\n\nStay Focused\nWe will continue to publish the latest notifications and assistance information through Futu API Doc, emails, APP messages, QQ, etc. Please pay attention to official updates.\n\n← Transaction related\n\nOthers\nQ1：Experience exchange channels\nQ2: Is there more complete strategy examples for reference?\nQ3: Import error when using python API\nQ4: Import successful, but you still cannot call the relevant interface.\nQ5: Protocol Encryption-Related\nQ6: Why is the DataFrame data I got incomplete?\nQ7: How to solve the problem that \"Cannot open libFTAPIChannel.dylib\" through C++ API on Mac?\nQ8: For Python users, why do large log files continue to be generated under the log folder, after the log level is set to no in the OpenD configuration file?\nQ9: For versions 5.4 and above, the library name and configuration method of Java API have been changed.\nQ10: For Python users, when using pyinstaller to package scripts that need to run api, an error is reported: Common_pb2 module cannot be found.\nQ11: Why the interface result is success, but the return did not behave as expected？\nQ12: WebSocket Related\nQ13: Where are the quote servers and the trade servers of OpenAPI?\nQ14: How to build C++ API?\nQ15: The Guide for Universal Account Upgrade"}, {"title": "获取复权因子 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-rehab.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取复权因子\n\nget_rehab(code)\n\n介绍\n\n获取股票的复权因子\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回复权数据\nstr\t当 ret != RET_OK，返回错误描述\n\n复权数据格式如下：\n\n字段\t类型\t说明\nex_div_date\tstr\t除权除息日\nsplit_base\tfloat\t拆股分子\n\nsplit_ert\tfloat\t拆股分母\njoin_base\tfloat\t合股分子\n\njoin_ert\tfloat\t合股分母\nsplit_ratio\tfloat\t拆合股比例 \n\nper_cash_div\tfloat\t每股派现\nbonus_base\tfloat\t送股分子\n\nbonus_ert\tfloat\t送股分母\nper_share_div_ratio\tfloat\t送股比例 \n\ntransfer_base\tfloat\t转增股分子\n\ntransfer_ert\tfloat\t转增股分母\nper_share_trans_ratio\tfloat\t转增股比例 \n\nallot_base\tfloat\t配股分子\n\nallot_ert\tfloat\t配股分母\nallotment_ratio\tfloat\t配股比例 \n\nallotment_price\tfloat\t配股价\nadd_base\tfloat\t增发股分子\n\nadd_ert\tfloat\t增发股分母\nstk_spo_ratio\tfloat\t增发比例 \n\nstk_spo_price\tfloat\t增发价格\nforward_adj_factorA\tfloat\t前复权因子 A\nforward_adj_factorB\tfloat\t前复权因子 B\nbackward_adj_factorA\tfloat\t后复权因子 A\nbackward_adj_factorB\tfloat\t后复权因子 B\n\n前复权价格 = 不复权价格 × 前复权因子 A + 前复权因子 B\n后复权价格 = 不复权价格 × 后复权因子 A + 后复权因子 B\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_rehab(\"HK.00700\")\nif ret == RET_OK:\n    print(data)\n    print(data['ex_div_date'][0])    # 取第一条的除权除息日\n    print(data['ex_div_date'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    ex_div_date  split_ratio  per_cash_div  per_share_div_ratio  per_share_trans_ratio  allotment_ratio  allotment_price  stk_spo_ratio  stk_spo_price  forward_adj_factorA  forward_adj_factorB  backward_adj_factorA  backward_adj_factorB\n0   2005-04-19          NaN          0.07                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -0.07                   1.0                  0.07\n..         ...          ...           ...                  ...                    ...              ...              ...            ...            ...                  ...                  ...                   ...                   ...\n15  2019-05-17          NaN          1.00                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -1.00                   1.0                  1.00\n\n[16 rows x 13 columns]\n2005-04-19\n['2005-04-19', '2006-05-15', '2007-05-09', '2008-05-06', '2009-05-06', '2010-05-05', '2011-05-03', '2012-05-18', '2013-05-20', '2014-05-15', '2014-05-16', '2015-05-15', '2016-05-20', '2017-05-19', '2018-05-18', '2019-05-17']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\n接口限制\n\n每 30 秒内最多请求 60 次获取复权因子接口。\n\n← 获取历史 K 线\n获取期权链到期日 →\n\n获取复权因子"}, {"title": "获取股票所属板块 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-owner-plate.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取股票所属板块\n\nget_owner_plate(code_list)\n\n介绍\n\n获取单支或多支股票的所属板块信息列表\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t股票代码列表 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回所属板块数据\nstr\t当 ret != RET_OK，返回错误描述\n所属板块数据格式如下：\n字段\t类型\t说明\ncode\tstr\t证券代码\nname\tstr\t股票名称\nplate_code\tstr\t板块代码\nplate_name\tstr\t板块名字\nplate_type\tPlate\t板块类型 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\ncode_list = ['HK.00001']\nret, data = quote_ctx.get_owner_plate(code_list)\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['plate_code'].values.tolist())   # 板块代码转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\nOutput\n        code name          plate_code plate_name plate_type\n0   HK.00001   长和  HK.HSI Constituent      恒指成份股      OTHER\n..       ...  ...                 ...        ...        ...\n8   HK.00001   长和           HK.BK1983    香港股票ADR      OTHER\n\n[9 rows x 5 columns]\nHK.00001\n['HK.HSI Constituent', 'HK.GangGuTong', 'HK.BK1000', 'HK.BK1061', 'HK.BK1107', 'HK.BK1331', 'HK.BK1600', 'HK.BK1922', 'HK.BK1983']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n\n\n接口限制\n\n每 30 秒内最多请求 10 次获取股票所属板块接口\n每次请求的股票列表中，股票个数上限为 200 个\n仅支持正股和指数\n\n← 获取资金分布\n获取历史 K 线 →\n\n获取股票所属板块"}, {"title": "获取资金流向 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-capital-flow.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取资金流向\n\nget_capital_flow(stock_code, period_type = PeriodType.INTRADAY, start=None, end=None)\n\n介绍\n\n获取个股资金流向\n\n参数\n\n参数\t类型\t说明\nstock_code\tstr\t股票代码\nperiod_type\tPeriodType\t周期类型\nstart\tstr\t开始时间 \n\nend\tstr\t结束时间 \nstart 和 end 的组合如下\nstart 类型\tend 类型\t说明\nstr\tstr\tstart 和 end 分别为指定的日期\nNone\tstr\tstart 为 end 往前 365 天\nstr\tNone\tend 为 start 往后 365 天\nNone\tNone\tend 为 当前日期，start 往前 365 天\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回资金流向数据\nstr\t当 ret != RET_OK，返回错误描述\n资金流向数据格式如下：\n字段\t类型\t说明\nin_flow\tfloat\t整体净流入\nmain_in_flow\tfloat\t主力大单净流入 \n\nsuper_in_flow\tfloat\t特大单净流入\nbig_in_flow\tfloat\t大单净流入\nmid_in_flow\tfloat\t中单净流入\nsml_in_flow\tfloat\t小单净流入\ncapital_flow_item_time\tstr\t开始时间 \n\nlast_valid_time\tstr\t数据最后有效时间 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_capital_flow(\"HK.00700\", period_type = PeriodType.INTRADAY)\nif ret == RET_OK:\n    print(data)\n    print(data['in_flow'][0])    # 取第一条的净流入的资金额度\n    print(data['in_flow'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n    last_valid_time       in_flow  ...  main_in_flow  capital_flow_item_time\n0               N/A -1.857915e+08  ... -1.066828e+08     2021-06-08 00:00:00\n..              ...           ...  ...           ...                     ...\n245             N/A  2.179240e+09  ...  2.143345e+09     2022-06-08 00:00:00\n\n[246 rows x 8 columns]\n-185791500.0\n[-185791500.0, -18315000.0, -672100100.0, -714394350.0, -698391950.0, -818886750.0, 304827400.0, 73026200.0, -2078217500.0, \n..                   ...           ...                    ...\n2031460.0, 638067040.0, 622466600.0, -351788160.0, -328529240.0, 715415020.0, 76749700.0, 2179240320.0]\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n\n\n接口限制\n\n每 30 秒内最多请求 30 次获取资金流向接口。\n仅支持正股、窝轮和基金。\n历史周期（日、月、年）仅提供最近 1 年数据；实时周期仅提供最新一天的数据。\n返回数据只包括盘中数据，不包含盘前盘后数据。\n\n← 获取标的市场状态\n获取资金分布 →\n\n获取资金流向"}, {"title": "Command Line OpenD | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/opend/opend-cmd.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nOverview\nCommand Line OpenD\nOperation Command\n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nCommand Line OpenD\n#\nStep 1: Download\n\nCommand line OpenD can be runned under 4 operating systems: Windows、MacOS、CentOS、Ubuntu (Click to download).\n\nOpenD - Windows、MacOS 、CentOS 、Ubuntu\n#\nStep 2: Decompression\nExtract the file downloaded in the previous step and find the OpenD configuration file FutuOpenD.xml and the program packaged data file Appdata.dat in the folder.\nFutuOpenD.xml is used to configure the startup parameters of the OpenD program. If it does not exist, the program cannot start correctly.\nAppdata.dat is a large amount of data information the program needs to use, packaging data to reduce the time of downloading data while starting OpenD. If it does not exist, the program can not start correctly.\nCommand line OpenD supports user-defined file paths, refer to Command line startup parameters。\n#\nStep 3: Parameter Configuration\nOpen and edit the configuration file FutuOpenD.xml as the picture below. For general use, you only need to change your account and login password, and other options can be modified according to the instructions in the following table.\n\nConfiguration item list：\n\nConfiguration Item\tDescription\nip\tlistening address. \n\napi_port\tAPI protocol receiving port. \n\nlogin_account\tLogin account. \n\nlogin_pwd\tLogin password in plaintext. \n\nlogin_pwd_md5\tLogin password ciphertext (32-bit MD5 encrypted hexadecimal). \n\nLang\tLanguage.\n\nlog_level\tLog level of OpenD. \n\npush_proto_type\tAPI protocol type. \n\nqot_push_frequency\tAPI subscription data push frequency \n\ntelnet_ip\tRemote operation command listening address. \n\ntelnet_port\tRemote operation command listening port. \n\nrsa_private_key\tAPI protocol RSA encrypted private key (PKCS#1) file absolute path.\n\nprice_reminder_push\tWhether to receive the price reminder. \n\nauto_hold_quote_right\tWhether to automatically grab quote right after being kicked. \n\nfuture_trade_api_time_zone\tSpecify the futures trading API time zone. \n\nwebsocket_ip\tWebSocket listening address. \n\nwebsocket_port\tWebSocket service listening port. \n\nwebsocket_key_md5\tKey ciphertext (32-bit MD5 encrypted hexadecimal). \n\nwebsocket_private_key\tWebSocket certificate private key file path. \n\nwebsocket_cert\tWebSocket certificate file path. \n\npdt_protection\tWhether to turn on the Pattern Day Trade Protection. \n\ndtcall_confirmation\tWhether to turn on the Day-Trading Call Warning. \n\nTips\n\nTo ensure safety of your trading accounts, if the listening address is not local, you must configure a private key to use the trading interface. The quote interface is not subject to this restriction.\n\nWhen the WebSocket listening address is not local, you need to configure SSL to start it, and a password should not be set during the certificate private key generation.\n\nCiphertext is represented in hexadecimal after plaintext is encrypted by 32-bit MD5, which can be calculated by searching online MD5 encryption (note that there may be a risk of records colliding with libraries calculated through third-party websites) or by downloading MD5 computing tools. The 32-bit MD5 ciphertext is shown in the red box area (********************************):\n\nOpenD reads FutuOpenD.xml in the same directory by default. On MacOS, due to the system protection mechanism, OpenD.app will be assigned a random path at run time, so that the original path can not be found. At this point, there are the following methods:\n\nExecute fixrun.sh under tar package\nSpecify the configuration file path with the command line parameter -cfg_file, as described below\n\nThe log level defaults to the info level. During the system development phase, it is not recommended to close the log or modify the log to the warning, error, fatal level to prevent failure to locate problems.\n\n#\nStep 4: Command Line Startup\nOn the command line, change the directory to the folder which FutuOpenD is located, and run the following command to start Command Line FutuOpenD with configuration from FutuOpenD.xml.\nWindows：FutuOpenD\nLinux：./FutuOpenD\nMacOS：./FutuOpenD.app/Contents/MacOS/FutuOpenD\nCommand Line Startup Parameters\n\nIf the same parameters exist on both the command line and the configuration file, the command line parameters take precedence. For details of the parameters, please see the following table:\n\nparameter list:\n\nConfiguration Item\tDescription\nlogin_account\tLogin account.\n\nlogin_pwd\tPlaintext of login password.\n\nlogin_pwd_md5\tLogin password ciphertext (32-bit MD5 encrypted hexadecimal).\n\ncfg_file\tThe absolute path of OpenD configuration file.\n\nconsole\tWhether to display the console. \n\nlang\tOpenD language\n\napi_ip\tAPI service listening address.\n\napi_port\tAPI listening port.\nhelp\tOutput startup command line parameters and exit the program.\nlog_level\tLog level of OpenD.\n\nno_monitor\tWhether to start the daemon. \n\nwebsocket_ip\tWebSocket listening address.\n\nwebsocket_port\tWebSocket service listening port.\nwebsocket_private_key\tWebSocket certificate private key file path. \n\nwebsocket_cert\tWebSocket certificate file path.\n\nwebsocket_key_md5\tKey ciphertext (32-bit MD5 encrypted hexadecimal). \n\nprice_reminder_push\tWhether to receive the price reminder.\n\nauto_hold_quote_right\tWhether to automatically grab quote right after being kicked.\n\nfuture_trade_api_time_zone\tSpecify the futures Trade API time zone. \n\n← Overview\nOperation Command →\n\nCommand Line OpenD"}, {"title": "Get Market Status of Securities | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/get-market-state.html", "html": " Futu API Doc v8.8\nProgramming Language\n简体中文\nProgramming Language\n简体中文\n\nIntroduction\n\nQuick Start\n\nOpenD\n\nQuote API\n\nOverview\nQuote Object\n\nReal-Time Data\n\nBasic Data\n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives\n\nMarket Filter\n\nCustomization\n\nQuotation Definitions\n\nTrade API\n\nBasic API\n\nQ&A\n\n# Get Market Status of Securities\n\nget_market_state(code_list)\n\nDescription\n\nGet market status of underlying security\n\nParameters\n\nParameter\tType\tDescription\ncode_list\tlist\tA list of security codes that need to query for market status.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, market status data is returned.\nstr\tIf ret != RET_OK, error description is returned.\nMarket status data format as follows:\nField\tType\tDescription\ncode\tstr\tSecurity code.\nstock_name\tstr\tSecurity name.\nmarket_state\tMarketState\tMarket state.\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_market_state(['SZ.000001', 'HK.00700'])\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n\nOutput\n    code         stock_name   market_state\n0  SZ.000001    Ping An Bank  AFTERNOON\n1  HK.00700     Tencent       AFTERNOON\n\n1\n2\n3\n\n\nInterface Limitations\n\nA maximum of 10 requests per 30 seconds\nThe maximum number of stock codes for each request is 400.\n\n← Get Real-time Broker Queue Get Capital Flow →\n\nGet Market Status of Securities"}, {"title": "获取标的市场状态 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-market-state.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取标的市场状态\n\nget_market_state(code_list)\n\n介绍\n\n获取指定标的的市场状态\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t需要查询市场状态的股票代码列表 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回市场状态数据\nstr\t当 ret != RET_OK，返回错误描述\n市场状态数据\n字段\t类型\t说明\ncode\tstr\t股票代码\nstock_name\tstr\t股票名称\nmarket_state\tMarketState\t市场状态\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_market_state(['SZ.000001', 'HK.00700'])\nif ret == RET_OK:\n    print(data)\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n\nOutput\n    code         stock_name   market_state\n0  SZ.000001    平安银行     AFTERNOON\n1  HK.00700     腾讯控股     AFTERNOON\n\n \n\n        Copied!\n    \n1\n2\n3\n\n\n接口限制\n\n每 30 秒内最多请求 10 次获取标的市场状态接口。\n每次请求的股票代码个数上限为 400 个。\n\n← 获取实时经纪队列\n获取资金流向 →\n\n获取标的市场状态"}, {"title": "获取资金分布 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-capital-distribution.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n基本数据 \n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取资金分布\n\nget_capital_distribution(stock_code)\n\n介绍\n\n获取资金分布\n\n参数\n\n参数\t类型\t说明\nstock_code\tstr\t股票代码\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回股票资金分布数据\nstr\t当 ret != RET_OK，返回错误描述\n资金分布数据格式如下：\n字段\t类型\t说明\ncapital_in_super\tfloat\t流入资金额度，特大单\ncapital_in_big\tfloat\t流入资金额度，大单\ncapital_in_mid\tfloat\t流入资金额度，中单\ncapital_in_small\tfloat\t流入资金额度，小单\ncapital_out_super\tfloat\t流出资金额度，特大单\ncapital_out_big\tfloat\t流出资金额度，大单\ncapital_out_mid\tfloat\t流出资金额度，中单\ncapital_out_small\tfloat\t流出资金额度，小单\nupdate_time\tstr\t更新时间字符串 \n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_capital_distribution(\"HK.00700\")\nif ret == RET_OK:\n    print(data)\n    print(data['capital_in_big'][0])    # 取第一条的流入资金额度，大单\n    print(data['capital_in_big'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n   capital_in_super  capital_in_big  ...  capital_out_small          update_time\n0      2.261085e+09    2.141964e+09  ...       2.887413e+09  2022-06-08 15:59:59\n\n[1 rows x 9 columns]\n2141963720.0\n[2141963720.0]\n\n1\n2\n3\n4\n5\n6\n\n\n接口限制\n\n每 30 秒内最多请求 30 次获取资金分布接口。\n仅支持正股、窝轮和基金。\n更多资金分布介绍，请参考 这里。\n返回数据只包括盘中数据，不包含盘前盘后数据。\n\n← 获取资金流向\n获取股票所属板块 →\n\n获取资金分布"}, {"title": "获取历史 K 线 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/request-history-kline.html", "html": " Futu API 文档 v8.8\n编程语言\n简体中文\n编程语言\n简体中文\n\n介绍\n\n快速上手\n\nOpenD\n\n行情接口\n\n行情接口总览\n行情对象\n\n实时行情\n\n基本数据\n\n获取标的市场状态\n获取资金流向\n获取资金分布\n获取股票所属板块\n获取历史 K 线\n获取复权因子\n\n相关衍生品\n\n全市场筛选\n\n个性化\n\n行情定义\n\n交易接口\n\n基础接口\n\nQ&A\n\n# 获取历史 K 线\n\nrequest_history_kline(code, start=None, end=None, ktype=KLType.K_DAY, autype=AuType.QFQ, fields=[KL_FIELD.ALL], max_count=1000, page_req_key=None, extended_time=False)\n\n介绍\n\n获取历史 K 线\n\n参数\n\n参数\t类型\t说明\ncode\tstr\t股票代码\nstart\tstr\t开始时间\n\nend\tstr\t结束时间\n\nktype\tKLType\tK 线类型\nautype\tAuType\t复权类型\nfields\tKLFields\t需返回的字段列表\nmax_count\tint\t本次请求最大返回的 K 线根数\n\npage_req_key\tbytes\t分页请求\n\nextended_time\tbool\t是否允许美股盘前盘后数据\nstart 和 end 的组合如下\nStart 类型\tEnd 类型\t说明\nstr\tstr\tstart 和 end 分别为指定的日期\nNone\tstr\tstart 为 end 往前 365 天\nstr\tNone\tend 为 start 往后 365 天\nNone\tNone\tend 为当前日期，start 往前 365 天\n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回历史 K 线数据\nstr\t当 ret != RET_OK，返回错误描述\npage_req_key\tbytes\t下一页请求的 key\n历史 K 线数据格式如下:\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\ntime_key\tstr\tK 线时间\n\nopen\tfloat\t开盘价\nclose\tfloat\t收盘价\nhigh\tfloat\t最高价\nlow\tfloat\t最低价\npe_ratio\tfloat\t市盈率\n\nturnover_rate\tfloat\t换手率\nvolume\tint\t成交量\nturnover\tfloat\t成交额\nchange_rate\tfloat\t涨跌幅\nlast_close\tfloat\t昨收价\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret, data, page_req_key = quote_ctx.request_history_kline('HK.00700', start='2019-09-11', end='2019-09-18', max_count=5)  # 每页5个，请求第一页\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['close'].values.tolist())   # 第一页收盘价转为 list\nelse:\n    print('error:', data)\nwhile page_req_key != None:  # 请求后面的所有结果\n    print('*************************************')\n    ret, data, page_req_key = quote_ctx.request_history_kline('HK.00700', start='2019-09-11', end='2019-09-18', max_count=5, page_req_key=page_req_key) # 请求翻页后的数据\n    if ret == RET_OK:\n        print(data)\n    else:\n        print('error:', data)\nprint('All pages are finished!')\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\n        code  name             time_key       open      close       high        low  pe_ratio  turnover_rate    volume      turnover  change_rate  last_close\n0   HK.00700  腾讯控股  2019-09-11 00:00:00  307.85001  312.45926  314.11859  306.37505    36.405        0.00165  17133836  5.437023e+09     1.740968   307.11253\n..       ...   ...                  ...        ...        ...        ...        ...       ...            ...       ...           ...          ...         ...\n4   HK.00700  腾讯控股  2019-09-17 00:00:00  309.87808  310.24682  311.90615  307.48127    36.153        0.00097  10007748  3.165508e+09    -0.883527   313.01237\n\n[5 rows x 13 columns]\nHK.00700\n[312.45926, 315.59355, 315.77792, 313.01237, 310.24682]\n*************************************\n       code  name             time_key       open      close       high        low  pe_ratio  turnover_rate   volume      turnover  change_rate  last_close\n0  HK.00700  腾讯控股  2019-09-18 00:00:00  310.61556  309.69371  312.09052  308.40312     36.09        0.00077  7957229  2.516701e+09    -0.178281   310.24682\nAll pages are finished!\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n\n接口限制\n\n分 K 提供最近 8 年数据，日 K 及以上提供最近 10 年的数据。\n我们会根据您账户的资产和交易的情况，下发历史 K 线额度。因此，30 天内您只能获取有限只股票的历史 K 线数据。具体规则参见 订阅额度 & 历史 K 线额度。您当日消耗的历史 K 线额度，会在 30 天后自动释放。\n每 30 秒内最多请求 60 次历史 K 线接口。注意：如果您是分页获取数据，此限频规则仅适用于每只股票的首页，后续页请求不受限频规则的限制。\n换手率，仅提供日 K 及以上级别。\n期权，仅提供日K, 1分K，5分K，15分K，60分K。\n美股 盘前和盘后 K 线，仅支持 60 分钟及以下级别。由于美股盘前和盘后时段为非常规交易时段，此时段的 K 线数据可能不足 2 年。\n美股的 成交额，仅提供 2015-10-12 之后的数据。\n\n← 获取股票所属板块 获取复权因子 →\n\n获取历史 K 线"}, {"title": "Get Historical Candlesticks | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quote/request-history-kline.html", "html": " Futu API Doc v8.8\nProgramming Language \n简体中文 \n\nIntroduction \n\nQuick Start \n\nOpenD \n\nQuote API \n\nOverview\nQuote Object\n\nReal-Time Data \n\nBasic Data \n\nGet Market Status of Securities\nGet Capital Flow\nGet Capital Distribution\nGet Plates of Stocks\nGet Historical Candlesticks\nGet Adjustment Factor\n\nRelated Derivatives \n\nMarket Filter \n\nCustomization \n\nQuotation Definitions\n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nGet Historical Candlesticks\n\nrequest_history_kline(code, start=None, end=None, ktype=KLType.K_DAY, autype=AuType.QFQ, fields=[KL_FIELD.ALL], max_count=1000, page_req_key=None, extended_time=False)\n\nDescription\n\nGet historical candlesticks\n\nParameters\n\nParameter\tType\tDescription\ncode\tstr\tStock code.\nstart\tstr\tStart time. \n\nend\tstr\tEnd time. \n\nktype\tKLType\tCandlestick type.\nautype\tAuType\tType of adjustment.\nfields\tKL_FIELD\tList of fields to be returned.\nmax_count\tint\tThe maximum number of candlesticks returned in this request. \n\npage_req_key\tbytes\tThe key of the page request. If the number of candlesticks between start and end is more than max_count, then None should be passed at the first time you call this interface, and the page_req_key returned by the last call must be passed in the subsequent pagerequests.\nextended_time\tbool\tNeed pre-market and after-hours data for US stocks or not. False: not need, True: need.\nThe combination of start and end is as follows\nStart type\tEnd type\tDescription\nstr\tstr\tstart and end are the specified dates respectively.\nNone\tstr\tstart is 365 days before end.\nstr\tNone\tend is 365 days after start.\nNone\tNone\tend is the current date, start is 365 days before.\n\nReturn\n\nField\tType\tDescription\nret\tRET_CODE\tInterface result.\ndata\tpd.DataFrame\tIf ret == RET_OK, historical candlestick data is returned.\nstr\tIf ret != RET_OK, error description is returned.\npage_req_key\tbytes\tThe key of the next page request.\nHistorical candlestick data format as follows:\nField\tType\tDescription\ncode\tstr\tStock code.\nname\tstr\tStock name.\ntime_key\tstr\tCandlestick time. \n\nopen\tfloat\tOpen.\nclose\tfloat\tClose.\nhigh\tfloat\tHigh.\nlow\tfloat\tLow.\npe_ratio\tfloat\tP/E ratio. \n\nturnover_rate\tfloat\tTurnover rate.\nvolume\tint\tVolume.\nturnover\tfloat\tTurnover.\nchange_rate\tfloat\tChange rate.\nlast_close\tfloat\tYesterday's close.\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\nret, data, page_req_key = quote_ctx.request_history_kline('HK.00700', start='2019-09-11', end='2019-09-18', max_count=5) # 5 per page, request the first page\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0]) # Take the first stock code\n    print(data['close'].values.tolist()) # The closing price of the first page is converted to a list\nelse:\n    print('error:', data)\nwhile page_req_key != None: # Request all results after\n    print('*************************************')\n    ret, data, page_req_key = quote_ctx.request_history_kline('HK.00700', start='2019-09-11', end='2019-09-18', max_count=5,page_req_key=page_req_key) # Request the page after turning data\n    if ret == RET_OK:\n        print(data)\n    else:\n        print('error:', data)\nprint('All pages are finished!')\nquote_ctx.close() # After using the connection, remember to close it to prevent the number of connections from running out\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n\nOutput\n    code     name             time_key       open      close       high        low  pe_ratio  turnover_rate    volume      turnover  change_rate  last_close\n0   HK.00700  TENCENT  2019-09-11 00:00:00  307.85001  312.45926  314.11859  306.37505    36.405        0.00165  17133836  5.437023e+09     1.740968   307.11253\n..       ...      ...                  ...        ...        ...        ...        ...       ...            ...       ...           ...          ...         ...\n4   HK.00700  TENCENT  2019-09-17 00:00:00  309.87808  310.24682  311.90615  307.48127    36.153        0.00097  10007748  3.165508e+09    -0.883527   313.01237\n\n[5 rows x 13 columns]\nHK.00700\n[312.45926, 315.59355, 315.77792, 313.01237, 310.24682]\n*************************************\n       code     name             time_key       open      close       high        low  pe_ratio  turnover_rate   volume      turnover  change_rate  last_close\n0  HK.00700  TENCENT  2019-09-18 00:00:00  310.61556  309.69371  312.09052  308.40312     36.09        0.00077  7957229  2.516701e+09    -0.178281   310.24682\nAll pages are finished!\n\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n\n\nInterface Restrictions\n\nCandlestick data with timeframes of 60 minutes and below, is only supported for the last 8 years. Data with timeframes of daily and above, is supported for the last 10 years.\nWe will issue historical candlestick quota based on your account assets and transaction conditions. Therefore, you can only obtain historical candlestick data for a limited number of stocks within 30 days. For specific rules, please refer to Subscription Quota & Historical Candlestick Quota. The historical candlestick quota you consume on that day will be automatically released after 30 days.\nA maximum of 60 requests per 30 seconds. Note: If you obtain data by page, this frequency limit rule is only applicable to the first time calling the interface, and subsequent pages request frequency is unlimited.\nChange rate, only supports timeframes of daily and above.\nOptions related candlestick data, only supports 1 day, 1 minute, 5 minutes, 15 minutes and 60 minutes.\nThe pre-market and after-hours candlestick of US stocks, only supports timeframes of 60 minutes and below. Since the pre-market and after-hours of the US stock market are irregular trading hours, the candlestick data for this period may be less than 2 years.\nTurnover of US stocks, only supports data after 2015-10-12.\n\n← Get Plates of Stocks\nGet Adjustment Factor →\n\nGet Historical Candlesticks"}, {"title": "获取快照 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quote/get-market-snapshot.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\nOpenD \n\n行情接口 \n\n行情接口总览\n行情对象\n\n实时行情 \n\n订阅 \n\n推送回调 \n\n拉取 \n\n获取快照\n获取实时报价\n获取实时摆盘\n获取实时 K 线\n获取实时分时\n获取实时逐笔\n获取实时经纪队列\n\n基本数据 \n\n相关衍生品 \n\n全市场筛选 \n\n个性化 \n\n行情定义\n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n获取快照\n\nget_market_snapshot(code_list)\n\n介绍\n\n获取快照数据\n\n参数\n\n参数\t类型\t说明\ncode_list\tlist\t股票代码列表 \n\n返回\n\n参数\t类型\t说明\nret\tRET_CODE\t接口调用结果\ndata\tpd.DataFrame\t当 ret == RET_OK，返回股票快照数据\nstr\t当 ret != RET_OK，返回错误描述\n股票快照数据格式如下：\n字段\t类型\t说明\ncode\tstr\t股票代码\nname\tstr\t股票名称\nupdate_time\tstr\t当前价更新时间 \n\nlast_price\tfloat\t最新价格\nopen_price\tfloat\t今日开盘价\nhigh_price\tfloat\t最高价格\nlow_price\tfloat\t最低价格\nprev_close_price\tfloat\t昨收盘价格\nvolume\tint\t成交数量\nturnover\tfloat\t成交金额\nturnover_rate\tfloat\t换手率 \n\nsuspension\tbool\t是否停牌 \n\nlisting_date\tstr\t上市日期 \n\nequity_valid\tbool\t是否正股 \n\nissued_shares\tint\t总股本\ntotal_market_val\tfloat\t总市值 \n\nnet_asset\tint\t资产净值\nnet_profit\tint\t净利润\nearning_per_share\tfloat\t每股盈利\noutstanding_shares\tint\t流通股本\nnet_asset_per_share\tfloat\t每股净资产\ncircular_market_val\tfloat\t流通市值 \n\ney_ratio\tfloat\t收益率 \n\npe_ratio\tfloat\t市盈率 \n\npb_ratio\tfloat\t市净率 \n\npe_ttm_ratio\tfloat\t市盈率 TTM \n\ndividend_ttm\tfloat\t股息 TTM，派息\ndividend_ratio_ttm\tfloat\t股息率 TTM \n\ndividend_lfy\tfloat\t股息 LFY，上一年度派息\ndividend_lfy_ratio\tfloat\t股息率 LFY \n\nstock_owner\tstr\t窝轮所属正股的代码或期权的标的股代码\nwrt_valid\tbool\t是否是窝轮 \n\nwrt_conversion_ratio\tfloat\t换股比率\nwrt_type\tWrtType\t窝轮类型\nwrt_strike_price\tfloat\t行使价格\nwrt_maturity_date\tstr\t格式化窝轮到期时间\nwrt_end_trade\tstr\t格式化窝轮最后交易时间\nwrt_leverage\tfloat\t杠杆比率 \n\nwrt_ipop\tfloat\t价内/价外 \n\nwrt_break_even_point\tfloat\t打和点\nwrt_conversion_price\tfloat\t换股价\nwrt_price_recovery_ratio\tfloat\t正股距收回价 \n\nwrt_score\tfloat\t窝轮综合评分\nwrt_code\tstr\t窝轮对应的正股（此字段已废除，修改为 stock_owner）\nwrt_recovery_price\tfloat\t窝轮收回价\nwrt_street_vol\tfloat\t窝轮街货量\nwrt_issue_vol\tfloat\t窝轮发行量\nwrt_street_ratio\tfloat\t窝轮街货占比 \n\nwrt_delta\tfloat\t窝轮对冲值\nwrt_implied_volatility\tfloat\t窝轮引伸波幅\nwrt_premium\tfloat\t窝轮溢价 \n\nwrt_upper_strike_price\tfloat\t上限价 \n\nwrt_lower_strike_price\tfloat\t下限价 \n\nwrt_inline_price_status\tPriceType\t界内界外 \n\nwrt_issuer_code\tstr\t发行人代码\noption_valid\tbool\t是否是期权 \n\noption_type\tOptionType\t期权类型\nstrike_time\tstr\t期权行权日 \n\noption_strike_price\tfloat\t行权价\noption_contract_size\tfloat\t每份合约数\noption_open_interest\tint\t总未平仓合约数\noption_implied_volatility\tfloat\t隐含波动率\noption_premium\tfloat\t溢价\noption_delta\tfloat\t希腊值 Delta\noption_gamma\tfloat\t希腊值 Gamma\noption_vega\tfloat\t希腊值 Vega\noption_theta\tfloat\t希腊值 Theta\noption_rho\tfloat\t希腊值 Rho\nindex_option_type\tIndexOptionType\t指数期权类型\noption_net_open_interest\tint\t净未平仓合约数 \n\noption_expiry_date_distance\tint\t距离到期日天数 \n\noption_contract_nominal_value\tfloat\t合约名义金额 \n\noption_owner_lot_multiplier\tfloat\t相等正股手数 \n\noption_area_type\tOptionAreaType\t期权类型（按行权时间）\noption_contract_multiplier\tfloat\t合约乘数\nplate_valid\tbool\t是否为板块类型 \n\nplate_raise_count\tint\t板块类型上涨支数\nplate_fall_count\tint\t板块类型下跌支数\nplate_equal_count\tint\t板块类型平盘支数\nindex_valid\tbool\t是否有指数类型 \n\nindex_raise_count\tint\t指数类型上涨支数\nindex_fall_count\tint\t指数类型下跌支数\nindex_equal_count\tint\t指数类型平盘支数\nlot_size\tint\t每手股数，股票期权表示每份合约的股数 \n，期货表示合约乘数\nprice_spread\tfloat\t当前向上的摆盘价差 \n\nask_price\tfloat\t卖价\nbid_price\tfloat\t买价\nask_vol\tfloat\t卖量\nbid_vol\tfloat\t买量\nenable_margin\tbool\t是否可融资（已废弃） \n\nmortgage_ratio\tfloat\t股票抵押率（已废弃）\nlong_margin_initial_ratio\tfloat\t融资初始保证金率（已废弃） \n\nenable_short_sell\tbool\t是否可卖空（已废弃） \n\nshort_sell_rate\tfloat\t卖空参考利率（已废弃） \n\nshort_available_volume\tint\t剩余可卖空数量（已废弃）\n\nshort_margin_initial_ratio\tfloat\t卖空（融券）初始保证金率（已废弃） \n\nsec_status\tSecurityStatus\t股票状态\namplitude\tfloat\t振幅 \n\navg_price\tfloat\t平均价\nbid_ask_ratio\tfloat\t委比 \n\nvolume_ratio\tfloat\t量比\nhighest52weeks_price\tfloat\t52 周最高价\nlowest52weeks_price\tfloat\t52 周最低价\nhighest_history_price\tfloat\t历史最高价\nlowest_history_price\tfloat\t历史最低价\npre_price\tfloat\t盘前价格\npre_high_price\tfloat\t盘前最高价\npre_low_price\tfloat\t盘前最低价\npre_volume\tint\t盘前成交量\npre_turnover\tfloat\t盘前成交额\npre_change_val\tfloat\t盘前涨跌额\npre_change_rate\tfloat\t盘前涨跌幅 \n\npre_amplitude\tfloat\t盘前振幅 \n\nafter_price\tfloat\t盘后价格\nafter_high_price\tfloat\t盘后最高价\nafter_low_price\tfloat\t盘后最低价\nafter_volume\tint\t盘后成交量 \n\nafter_turnover\tfloat\t盘后成交额 \n\nafter_change_val\tfloat\t盘后涨跌额\nafter_change_rate\tfloat\t盘后涨跌幅 \n\nafter_amplitude\tfloat\t盘后振幅 \n\nfuture_valid\tbool\t是否期货\nfuture_last_settle_price\tfloat\t昨结\nfuture_position\tfloat\t持仓量\nfuture_position_change\tfloat\t日增仓\nfuture_main_contract\tbool\t是否主连合约\nfuture_last_trade_time\tstr\t最后交易时间 \n\ntrust_valid\tbool\t是否基金\ntrust_dividend_yield\tfloat\t股息率 \n\ntrust_aum\tfloat\t资产规模 \n\ntrust_outstanding_units\tint\t总发行量\ntrust_netAssetValue\tfloat\t单位净值\ntrust_premium\tfloat\t溢价 \n\ntrust_assetClass\tAssetClass\t资产类别\n\nExample\n\nfrom futu import *\nquote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)\n\nret, data = quote_ctx.get_market_snapshot(['SH.600000', 'HK.00700'])\nif ret == RET_OK:\n    print(data)\n    print(data['code'][0])    # 取第一条的股票代码\n    print(data['code'].values.tolist())   # 转为 list\nelse:\n    print('error:', data)\nquote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n\nOutput\n        code  name          update_time  last_price  open_price  high_price  low_price  prev_close_price    volume      turnover  turnover_rate  suspension listing_date  lot_size  price_spread  stock_owner  ask_price  bid_price  ask_vol  bid_vol  enable_margin  mortgage_ratio  long_margin_initial_ratio  enable_short_sell  short_sell_rate  short_available_volume  short_margin_initial_ratio  amplitude  avg_price  bid_ask_ratio  volume_ratio  highest52weeks_price  lowest52weeks_price  highest_history_price  lowest_history_price  close_price_5min  after_volume  after_turnover sec_status  equity_valid  issued_shares  total_market_val     net_asset    net_profit  earning_per_share  outstanding_shares  circular_market_val  net_asset_per_share  ey_ratio  pe_ratio  pb_ratio  pe_ttm_ratio  dividend_ttm  dividend_ratio_ttm  dividend_lfy  dividend_lfy_ratio  wrt_valid  wrt_conversion_ratio wrt_type  wrt_strike_price  wrt_maturity_date  wrt_end_trade  wrt_recovery_price  wrt_street_vol   \n0  SH.600000  浦发银行  2023-07-19 15:00:00        7.41        7.36        7.43       7.36              7.39  18189577  1.346770e+08          0.062       False   1999-11-10       100          0.01          NaN       7.41        7.4    44513   215500           True             0.0                       40.0              False              NaN                     NaN                         NaN      0.947      7.404        -24.004         1.046                  8.22             6.630000              12.080000             -2.898137              7.41             0             0.0     NORMAL          True    29352175642      2.174996e+11  6.011913e+11  5.116084e+10              1.743         29352175642         2.174996e+11               20.482     0.079     4.251     0.361         4.568         0.320                4.32         0.000               0.000      False                   NaN      N/A               NaN                NaN            NaN                 NaN             NaN  \\\n1   HK.00700  腾讯控股  2023-07-19 16:08:14      333.00      330.60      333.80     327.00            336.40  21913296  7.240461e+09          0.229       False   2004-06-16       100          0.20          NaN     333.00      332.8  2393100     4700           True             0.0                       30.0               True             0.93               4989900.0                        30.0      2.021    330.414        -52.783         1.025                414.20           186.226308             709.500065             -9.802011            332.60             0             0.0     NORMAL          True     9574268633      3.188231e+12  8.892772e+11  2.107392e+11             22.011          9574268633         3.188231e+12               92.882     0.244    15.128     3.585        14.638        20.523                6.16        20.523               6.163      False                   NaN      N/A               NaN                NaN            NaN                 NaN             NaN   \n\n   wrt_issue_vol  wrt_street_ratio  wrt_delta  wrt_implied_volatility  wrt_premium  wrt_leverage  wrt_ipop  wrt_break_even_point  wrt_conversion_price  wrt_price_recovery_ratio  wrt_score  wrt_upper_strike_price  wrt_lower_strike_price wrt_inline_price_status  wrt_issuer_code  option_valid option_type  strike_time  option_strike_price  option_contract_size  option_open_interest  option_implied_volatility  option_premium  option_delta  option_gamma  option_vega  option_theta  option_rho  option_net_open_interest  option_expiry_date_distance  option_contract_nominal_value  option_owner_lot_multiplier option_area_type  option_contract_multiplier index_option_type  index_valid  index_raise_count  index_fall_count  index_equal_count  plate_valid  plate_raise_count  plate_fall_count  plate_equal_count  future_valid  future_last_settle_price  future_position  future_position_change  future_main_contract  future_last_trade_time  trust_valid  trust_dividend_yield  trust_aum   \n0            NaN               NaN        NaN                     NaN          NaN           NaN       NaN                   NaN                   NaN                       NaN        NaN                     NaN                     NaN                     N/A              NaN         False         N/A          NaN                  NaN                   NaN                   NaN                        NaN             NaN           NaN           NaN          NaN           NaN         NaN                       NaN                          NaN                            NaN                          NaN              N/A                         NaN               N/A        False                NaN               NaN                NaN        False                NaN               NaN                NaN         False                       NaN              NaN                     NaN                   NaN                     NaN        False                   NaN        NaN  \\\n1            NaN               NaN        NaN                     NaN          NaN           NaN       NaN                   NaN                   NaN                       NaN        NaN                     NaN                     NaN                     N/A              NaN         False         N/A          NaN                  NaN                   NaN                   NaN                        NaN             NaN           NaN           NaN          NaN           NaN         NaN                       NaN                          NaN                            NaN                          NaN              N/A                         NaN               N/A        False                NaN               NaN                NaN        False                NaN               NaN                NaN         False                       NaN              NaN                     NaN                   NaN                     NaN        False                   NaN        NaN   \n\n   trust_outstanding_units  trust_netAssetValue  trust_premium trust_assetClass pre_price pre_high_price pre_low_price pre_volume pre_turnover pre_change_val pre_change_rate pre_amplitude after_price after_high_price after_low_price after_change_val after_change_rate after_amplitude  \n0                      NaN                  NaN            NaN              N/A       N/A            N/A           N/A        N/A          N/A            N/A             N/A           N/A         N/A              N/A             N/A              N/A               N/A             N/A  \n1                      NaN                  NaN            NaN              N/A       N/A            N/A           N/A        N/A          N/A            N/A             N/A           N/A         N/A              N/A             N/A              N/A               N/A             N/A  \nSH.600000\n['SH.600000', 'HK.00700']\n\n \n\n        Copied!\n    \n1\n2\n3\n4\n5\n6\n7\n8\n9\n10\n11\n12\n13\n\n\n接口限制\n\n每 30 秒内最多请求 60 次快照。\n每次请求，接口参数 股票代码列表 支持传入的标的数量上限是 400 个。\n港股 BMP 权限下，单次请求的香港证券（含窝轮、牛熊、界内证）快照数量上限是 20 个。\n港股期权期货 BMP 权限下，单次请求的香港期货和期权的快照数量上限是 20 个。\n\n← 实时经纪队列回调\n获取实时报价 →\n\n获取快照"}, {"title": "Environment Setup | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quick/env.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nVisualization OpenD\nEnvironment Setup\nProgram Samples\nStrategy Setup\n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nEnvironment Setup\n\nNotice\n\nWays of building programming environment are different for different programming languages.\n\n#\nPython Environment\n#\nEnvironment Requirement\nOperating system requirements:\n32-bit or 64-bit operating system of Windows 7/10\n64-bit operating system of Mac 10.11 and above\n64-bit operating system of CentOS 7 and above\n64-bit operating system of Ubuntu 16.04 and above\nPython version requirements:\nPython 3.6 or above\n#\nEnvironment Building\n#\n1. Install Python\n\nTo avoid running failures due to environmental problems, we recommend Python version 3.8.\n\nDownload page: Download Python\n\nTips\n\nAfter the installation, execute the following command to see if the installation is successful:\npython -V (Windows) or python3 -V (Linux/Mac)\n\n#\n2. Install PyCharm (Optional)\n\nWe recommend that using PyCharm as your Python IDE.\n\n#\n3. Install TA-Lib (Optional)\n\nTA-Lib is a functional library widely used in program trading for technical analysis of market data. It provides a variety of technical analysis functions to facilitate our quantitative investment.\n\nInstallation method: directly use pip installation in cmd\n$ pip install TA-Lib\n\n提示\n\nInstallation of TA-Lib is not necessary, you can skip this step\n\n← Visualization OpenD\nProgram Samples →\n\nEnvironment Setup"}, {"title": "Visualization OpenD | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/quick/opend-base.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nQuick Start \n\nVisualization OpenD\nEnvironment Setup\nProgram Samples\nStrategy Setup\n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nVisualization OpenD\n\nOpenD provides two operation modes: visualization and command line. Here is a description of Visualization OpenD which is relatively simple to operate.\n\nPlease refer to Command Line FutuOpenD for more informations for your interest.\n\n#\nVisualization OpenD\n#\nStep 1: Download\n\nVisualization OpenD can be runned under 4 operating systems: Windows、MacOS、CentOS、Ubuntu (Click to download).\n\nOpenD - Windows、MacOS 、CenOS 、Ubuntu\n#\nStep 2: Installation\nExtract the file and find the corresponding installation file to install OpenD.\nOpenD is installed in the % appdata% directory by default under Windows System.\n#\nStep 3: Configuration\nThe Visualization OpenD launch configuration is on the right side of the graphical interface, as shown in the following figure:\n\nConfiguration item list：\n\nConfiguration Item\tDescription\nIP\tAPI listening IP address. \n\nPort\tAPI listening port.\nLog Level\tLog level of OpenD. \n\nLanguage\tLanguage.\n\nTime Zone of Future Trade API\tSpecify the futures trading API time zone. \n\nData Push Frequency\tAPI subscription data push frequency control. \n\nTelnet IP\tListening address of remote operation command.\nTelnet Port\tListening port of remote operation command.\nEncrypted Private Key\tAbsolute path of RSA Encrypted Private Key.\nWebSocket IP\tWebSocket listening address. \n\nWebSocket Port\tWebSocket listening port.\nWebSocket Certificate\tWebSocket certificate file path. \n\nWebSocket Private Key\tWebSocket certificate private key file path. \n\nWebSocket Authentication Key\tCipher text of key (32-bit MD5 encrypted hexadecimal). \n\nTips\n\nVisual OpenD provides services by launching command line OpenD, interacted through WebSocket, so the WebSocket function must be started.\n\nTo ensure safety of your trading accounts, if the listening address is not local, you must configure a private key to use the trading interface. The quote interface is not subject to this restriction.\n\nWhen the WebSocket listening address is not local, you need to configure SSL to start it, and a password should not be set during the certificate private key generation.\n\nCiphertext is represented in hexadecimal after plaintext is encrypted by 32-bit MD5, which can be calculated by searching online MD5 encryption (note that there may be a risk of records colliding with libraries calculated through third-party websites) or by downloading MD5 computing tools. The 32-bit MD5 ciphertext is shown in the red box area (********************************): \n\nOpenD reads OpenD.xml in the same directory by default. On MacOS, due to the system protection mechanism, FutuOpenD.app will be assigned a random path at run time, so that the original path can not be found. At this point, there are the following methods:\n\nExecute fixrun.sh under tar package\nSpecify the configuration file path with the command line parameter -cfg_file, as described below\n\nThe log level defaults to the info level. During the system development phase, it is not recommended to close the log or modify the log to the warning, error, fatal level to prevent failure to locate problems.\n\n#\nStep 4: Login\nEnter your account number and password to login.\nYou need to complete the questionnaire evaluation and agreement confirmation when you log in for the first time.\nYou can see your account information and quote right, After logging in successfully.\n\n← Fee\nEnvironment Setup →\n\nVisualization OpenD"}, {"title": "Authorities and Limitations | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/intro/authority.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nOpenAPI Introduction\nAuthorities and Limitations\nFee\n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nAuthorities and Limitations\n#\nLogin Limitations\n#\nOpening Accounts\n\nYou need to finish opening your trading accounts on Futubull APP, before logging in to OpenAPI.\n\n#\nCompliance Confirmation\n\nAfter the first login, you need to complete API Questionnaire and Agreements before you can continue to use OpenAPI. Click here for Futubull users.\n\n#\nQuotation Data\n\nThere are several limitations for market quotation data as follow:\n\nQuote Right -- The authority to obtain the relevant market data.\nInterface Frequency Limitations -- Frequency limits of calling interfaces.\nSubscription Quota -- Number of real-time quotes subscribed at the same time.\nHistorical Candlestick Quota -- The total number of subjects pulling the historical candlestick per 30 days.\n#\nQuote Right\n\nYou need the corresponding permission to obtain data of each market through OpenAPI. The permission of OpenAPI is not exactly the same as that of APP. Different levels correspond to different time delay, order book levels, and the permission to use the interface.\n\nYou need to buy a quotation card before you can obtain the quotation of some varieties, the specific way to obtain is shown in the table below.\n\nMarket\tSecurity Type\tQuote Right Acquisition Method\nHK Market\tSecurities (including stocks, ETFs, warrants, CBBCs, Inline Warrants)\t* Chinese mainland customers: LV2 market quotes for free. Purchase HK Stocks Advanced Full Market Quotes for SF market quotes\n* Non-Chinese mainland customers: BMP market quotes for free. Purchase HK stocks LV2 advanced market for LV2 market quotes. Purchase HK Stocks Advanced Full Market Quotes for SF market quotes\nIndices\nPlates\nOptions\t* Chinese mainland customers: LV2 market quotes for free during promotion period.\n* Non-Chinese mainland customers: BMP market quotes for free. Purchase HK stock options futures LV2 advanced market for LV2 market data\nFutures\nUS Market\tSecurities (Covers NYSE, NYSE-American and Nasdaq listed equities, ETFs)\t* Purchase Nasdaq Basic for LV1 market quotes (basic quotes).\n* Non-professional users purchase Nasdaq Basic+TotalView (Non-Pro) for LV2 market quotes; professional users purchase Nasdaq Basic+TotalView (Pro) for LV2 quotes.\nPlates\nOTC Securities\tUnsupported.\nOptions (Covers US stock options, US index options)\t* Customers who meet the threshold \n : get LV1 market data for free\n* Customers who do not meet the threshold \n : Purchase OPRA Options Real-time Quote for LV1 market data\nFutures\t* For clients who have a futures account.\n\nFor CME Group quotes \n, please access the CME Group Futures LV2\nFor CME quotes, please access the CME Futures LV2\nFor CBOT quotes, please access the CBOT Futures LV2\nFor NYMEX quotes, please access the NYMEX Futures LV2\nFor NYMEX quotes, please access the COMEX Futures LV2\n\n* For clients who do not have a futures account: Unsupported.\nIndices\tUnsupported.\nA-share Market\tSecurities (including stocks, ETFs)\t* Chinese mainland customers: LV1 market data for free.\n* Non-Chinese mainland customers/institutional customers: Unsupported.\nIndices\nPlates\nSingapore Market\tFutures\tUnsupported.\nJapanese Market\tFutures\tUnsupported.\n\nTips\n\nIn the above table, the Chinese mainland customers and the Non-Chinese mainland customers are distinguished by the login IP address of OpenD.\n\n#\nInterface Frequency Limitations\n\nIn order to protect the server from malicious attacks, there are frequency limitations for all interfaces that need to send requests to Futu servers. The frequency limitation rules for each API are different. For more information, please see Interface Limitations at the bottom of each API page.\n\nExample:\nThe limitation rule of Get Market Snapshot is: A maximum of 60 requests every 30 seconds. You can request a uniform request every 0.5 seconds. You can also quickly request 60 times, rest for 30 seconds, and then request the next round. If the frequency limitation is exceeded, an error will be returned by the interface.\n\n#\nSubscription Quota & Historical Candlestick Quota\n\nThe limitation rules of subscription quota and historical candlestick quota as follows:\n\nUser Type\tSubscription Quota\tHistorical Candlestick Quota\nFinished Opening trading accounts.\t100\t100\nTotal asset > 10,000 HKD.\t300\t300\nSatisfy 1 of the items following:\n1. Total asset > 500,000 HKD;\n2. The number of monthly filled orders > 200;\n3. Monthly trading volume > 2 million HKD.\t1000\t1000\nSatisfy 1 of the items following:\n1. Total asset > 5 million HKD;\n2. The number of monthly filled orders > 2000;\n3. Monthly trading volume > 20 million HKD.\t2000\t2000\n\n1. Total asset\nTotal asset，refers to all your assets in Futu, including HK securities accounts, US securities accounts, SG securities accounts, A-shares securities accounts, Futures accounts etc., converted into HKD according to the spot exchange rate.\n\n2. The monthly number of filled orders\nIt is calculated by taking the larger value of the number of filled orders the last natural month and that of the current natural month, that is:\nmax (the number of filled orders of the last natural month, the number of filled orders of the current natural month)\n\n3. Monthly Trading volume\nIt is calculated by taking the larger value of the total trading volume of your last natural month and that of the current natural month, which is converted into HKD according to the spot exchange rate, that is:\nmax (the total trading volume of the last natural month, the total trading volume of the current natural month)\nThe calculation of futures trading value needs to be multiplied by the adjustment factor (0.1 by default). The formula for calculating futures trading volume is as follows:\nFutures trading value = ∑ (volume of a single transaction * trading price * contract multiplier * exchange rate * adjustment factor)\n\n4. Subscription Quota\nIt is applicable to the real-time data interface that can only be obtained after a subscription. One type of subscription for each stock takes up 1 subscription quota, and canceling the subscription will release the occupied quota.\nExample:\nSuppose your Subscription Quota is 100. When you subscribe to real-time order book for HK.00700, real-time ticker for US.AAPL, and real-time quotation for SH.600519 at the same time, the Subscription Quota will occupy 3, and the remaining Subscription Quota will be 97. At this time, if you cancel the real-time order book subscription of HK.00700, your Subscription Quota will become 2, and the remaining Subscription Quota will become 98.\n\n5. Historical Candlestick Quota\nSuitable for Get Historical Candlesticks interface. In the last 30 days, requests for historical candlesticks of each stock will occupy one Historical Candlestick Quota. Repeated requests for historical candlestick of the same stock within the last 30 days will not be counted repeatedly.\nExample:\nSuppose your Historical Candlestick Quota is 100, and today is July 5, 2020. You have requested historical candlesticks for a total of 60 stocks between June 5, 2020 and July 5, 2020. The remaining Historical Candlestick Quota is 40.\n\nTips\n\nSubscription Quota and Historical Candlestick Quota are automatically assigned and do not need to be applied manually.\nFor newly deposited accounts, the quota will automatically take effect within 2 hours.\nAsset in Transit\n will not be calculated in quota assign.\n#\nTrading Functions\nWhen you trade in a specific market, you need to first confirm whether a trading account has been opened in that market.\nFor example: you can only trade US stocks under the US trading account, but not under the HK trading account.\n\n← OpenAPI Introduction\nFee →\n\nAuthorities and Limitations\nLogin Limitations\nQuotation Data\nTrading Functions"}, {"title": "OpenAPI Introduction | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/intro/intro.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nOpenAPI Introduction\nAuthorities and Limitations\nFee\n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nOpenAPI Introduction\n#\nOverview\n\nOpenAPI provides wide varieties of market data and trading services for your programmed trading to meet the needs of every developer's programmed trading and help your Quant dreams.\n\nFutubull users can click here to learn more.\n\nOpenAPI consists of OpenD and Futu API:\n\nOpenD is the gateway program of Futu API, running on your local computer or cloud server. It is responsible for transferring the protocol requests to Futu servers, and returning the processed data.\nFutu API is an API SDK encapsulated by Futu, including mainstream programming languages (Python, Java, C#, C++, JavaScript), to reduce the difficulty of your trading strategy development. If the language you want to use is not listed above, you can still interface with the protocol yourself to complete the trading strategy development.\n\nDiagrams below illustrate the architecture of OpenAPI.\n\nThe first time using OpenAPI, you need to finish the following two steps:\n\nThe first step is to install and start a gateway program OpenD locally or in the cloud.\n\nOpenD exposes the interfaces in the way of TCP, which is responsible for transferring the protocol requests to Futu Server and returning the processed data. The protocol interface has nothing to do with the type of programming language.\n\nThe second step is to download Futu API and complete Environment Setup.\n\nFor your convenience, <PERSON>tu encapsulates API SDK for mainstream programming languages (hereinafter referred to as Futu API).\n\n#\nAccount\n\nOpenAPI involves two types of accounts, Futu ID and universal account.\n\n#\nFutu ID\n\nFutu ID is your user account (Futubull ID ), which can be used in Futubull APP and OpenAPI.\nYou can use your Futu ID and login password to log in to OpenD and obtain market data.\n\n#\nUniversal Account\n\nUniversal account allows trading across multiple markets (including Hong Kong stocks, US stocks, A-shares, and funds) in various currencies. There's no need for multiple accounts.\nUniversal Accounts come in two forms:\n\nSecurities Universal Account: Trade stocks, ETFs, options, and other securities across different markets.\nFutures Universal Account: Trade futures, including Hong Kong, US CME Group, Singapore, and Japanese futures.\n#\nFunctionality\n\nThere are 2 functions of OpenAPI: quotation and trading.\n\n#\nQuotation Functions\n#\nQuotation Data Categories\n\nIncluding stocks, indices, options and futures from HK, US and A-share market. Find the specific types of support in the table below. You need authorities for each kinds of market data. For more details on how to obtain authorities, please click here.\n\nMarket\tContract\tFutu Users\nHK Market\tStocks, ETFs, Warrants, CBBCs, Inline Warrants\t✓\nOptions\t✓\nFutures\t✓\nIndices\t✓\nPlates\t✓\nUS Market\tStocks, ETFs \n\t✓\nOTC Securities\tX\nOptions \n\t✓\nFutures\t✓\nIndices\tX\nPlates\t✓\nA-share Market\tStocks, ETFs\t✓\nIndices\t✓\nPlates\t✓\nSingapore Market\tStocks, ETFs, Warrants, REITs, DLCs\tX\nFutures\tX\nJapanese Market\tStocks, ETFs, REITs\tX\nFutures\tX\nAustralian Market\tStocks, ETFs\tX\nGlobal Markets\tForex\tX\n#\nMethod to Obtain Market Data\nSubscribe and receive pushed real-time quote, candlestick, tick-by-tick and order book.\nRequest for the latest market snapshot, historical candlesticks etc.\n#\nTrading Functions\n#\nTrading Capacity\n\nIncluding stocks, options and futures from HK, US, A-share, Singapore and Japanese markets. Find the specific types of support in the table below:\n\nMarket\tContracts\tPaper Trading\tLive Trading\nFUTU HK\tMoomoo US\tMoomoo SG\tMoomoo AU\tMoomoo MY\tMoomoo CA\tMoomoo JP\nHK Market\tStocks, ETFs, Warrants, CBBCs, Inline Warrants\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nOptions\n\t✓\t✓\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\tX\tX\tX\tX\tX\nUS Market\tStocks, ETFs\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nOptions\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nFutures\t✓\t✓\tX\t✓\tX\tX\tX\tX\nA-share Market\tChina Connect Securities stocks\t✓\t✓\t✓\t✓\tX\tX\tX\tX\nNon-China Connect Securities stocks\t✓\tX\tX\tX\tX\tX\tX\tX\nSingapore Market\tStocks, ETFs, Warrants, REITs, DLCs\tX\tX\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\t✓\tX\tX\tX\tX\nJapanese Market\tStocks, ETFs, REITs\tX\tX\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\tX\tX\tX\tX\tX\nAustralian Market\tStocks, ETFs\tX\tX\tX\tX\tX\tX\tX\tX\n#\nMethod of Trading\n\nThe trading interfaces are used for both live trading and paper trading.\n\n#\nFeatures\nFull platform and multi-language\nOpenD supports Windows, MacOS, CentOS, Ubuntu\nFutu API supports Python, Java, C#, C++, JavaScript, etc.\nStable speed and free\nStable technical architecture, directly connected to the exchanges\nThe fastest order is 0.0014s\nThere is no additional charge for trading via OpenAPI\nAbundant investment varieties\nSupporting real-time market data, live trading, and simulated trading in multiple markets including United States, Hong Kong, etc.\nProfessional institutional services\nCustomized market data and trading solutions\n\nAuthorities and Limitations →\n\nOpenAPI Introduction\nOverview\nAccount\nFunctionality\nFeatures"}, {"title": "Fee | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/intro/fee.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nOpenAPI Introduction\nAuthorities and Limitations\nFee\n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nFee\n#\nQuote\n\nLV2 HK market quotes and A-share LV1 market quotes are free for Chinese mainland customers.\nFor some variaties, you need to buy quotation cards before obtaining market data. For more details of quotation cards prices, please click Quote Right and go to data store.\n\n#\nTrade\n\nThere is no extra fee for tradings through OpenAPI. The transaction fee is the same as that of APP. You can check the specific charging plans from the following table:\n\nSecurities Firm\tCharging Options\nFUTU HK\tCharging Options\nMoomoo US\tCharging Options\nMoomoo SG\tCharging Options\nMoomoo AU\tCharging Options\n\n← Authorities and Limitations\nVisualization OpenD →\n\nFee\nQuote\nTrade"}, {"title": "编程环境搭建 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quick/env.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\n可视化 OpenD\n编程环境搭建\n简易程序运行\n交易策略搭建示例\n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n编程环境搭建\n\n注意\n\n不同的编程语言，编程环境搭建的方法有所不同。\n\n#\nPython 环境\n#\n环境要求\n操作系统要求：\nWindows 7/10 的 32 或 64 位操作系统\nMac 10.11 及以上的 64 位操作系统\nCentOS 7 及以上的 64 位操作系统\nUbuntu 16.04 以上的 64 位操作系统\nPython 版本要求：\nPython 3.6 及以上\n#\n环境搭建\n#\n1. 安装 Python\n\n为避免因环境问题导致的运行失败，我们推荐 Python 3.8 版本。\n\n下载地址：Python 下载\n\n提示\n\n当安装成功后，执行如下命令来查看是否安装成功:\npython -V（Windows） 或 python3 -V（Linux 和 Mac）\n\n#\n2. 安装 PyCharm（可选）\n\n我们推荐您使用 PyCharm 作为 Python IDE（集成开发环境）。\n\n#\n3. 安装 TA-Lib（可选）\n\nTA-Lib 用中文可以称作技术分析库，是一种广泛用在程序化交易中，进行金融市场数据的技术分析的函数库。它提供了多种技术分析的函数，方便我们量化投资中编程工作。\n\n安装方法：在 cmd 中直接使用 pip 安装\n$ pip install TA-Lib\n\n提示\n\n安装 TA-Lib 非必须，可先跳过该步骤\n\n← 可视化 OpenD\n简易程序运行 →\n\n编程环境搭建"}, {"title": "可视化 OpenD | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/quick/opend-base.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\n快速上手 \n\n可视化 OpenD\n编程环境搭建\n简易程序运行\n交易策略搭建示例\n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n可视化 OpenD\n\nOpenD 提供可视化和命令行两种运行方式，这里介绍操作比较简单的可视化 OpenD。\n\n如果想要了解命令行的方式请参考 命令行 OpenD 。\n\n#\n可视化 OpenD\n#\n第一步 下载\n\n可视化 OpenD 支持 Windows、MacOS、CentOS、Ubuntu 四种系统（点击完成下载）。\n\nOpenD - Windows、MacOS 、CenOS 、Ubuntu\n#\n第二步 安装运行\n解压文件，找到对应的安装文件可一键安装运行。\nWindows 系统默认安装在 %appdata% 目录下。\n#\n第三步 配置\n可视化 OpenD 启动配置在图形界面的右侧，如下图所示：\n\n配置项列表：\n\n配置项\t说明\n监听地址\tAPI 协议监听地址\n\n监听端口\tAPI 协议监听端口\n日志级别\tOpenD 日志级别\n\n语言\t中英语言\n\n期货交易 API 时区\t期货交易 API 时区\n\nAPI 推送频率\tAPI 订阅数据推送频率控制\n\nTelnet 地址\t远程操作命令监听地址\nTelnet 端口\t远程操作命令监听端口\n加密私钥路径\tAPI 协议 RSA 加密私钥（PKCS#1）文件绝对路径\nWebSocket 监听地址\tWebSocket 服务监听地址\n\nWebSocket 端口\tWebSocket 服务监听端口\nWebSocket 证书\tWebSocket 证书文件路径\n\nWebSocket 私钥\tWebSocket 证书私钥文件路径\n\nWebSocket 鉴权密钥\t密钥密文（32 位 MD5 加密 16 进制）\n\n提示\n\n可视化 OpenD，是通过启动命令行 OpenD 来提供服务，且通过 WebSocket 与命令行 OpenD 交互，所以必定启动 WebSocket 功能。\n\n为保证您的证券业务账户安全，如果监听地址不是本地，您必须配置私钥才能使用交易接口。行情接口不受此限制。\n\n当 WebSocket 监听地址不是本地，需配置 SSL 才可以启动，且证书私钥生成不可设置密码。\n\n密文是明文经过 32 位 MD5 加密后用 16 进制表示的数据，搜索在线 MD5 加密（注意，通过第三方网站计算可能有记录撞库的风险）或下载 MD5 计算工具可计算得到。32 位 MD5 密文如下图红框区域（********************************）： \n\nOpenD 默认读取同目录下的 OpenD.xml。在 MacOS 上，由于系统保护机制，OpenD.app 在运行时会被分配一个随机路径，导致无法找到原本的路径。此时有以下方法：\n\n执行 tar 包下的 fixrun.sh\n用命令行参数-cfg_file指定配置文件路径，见下面说明\n\n日志级别默认 info 级别，在系统开发阶段，不建议关闭日志或者将日志修改到 warning，error，fatal 级别，防止出现问题时无法定位。\n\n#\n第四步 登录\n输入账号密码，点击登录。\n首次登录，您需要先完成问卷评估与协议确认，完成后重新登录即可。\n登录成功后，您可以看到自己的账号信息和 行情权限。\n\n← 费用\n编程环境搭建 →\n\n可视化 OpenD"}, {"title": "费用 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/intro/fee.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\nOpenAPI 介绍\n权限和限制\n费用\n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n费用\n#\n行情\n\n中国内地 IP 个人客户，免费获取港股市场 LV2 行情及 A 股市场 LV1 行情。\n部分品种行情，需要购买行情卡后方可获取。您可以在 行情权限 一节，进入具体的行情卡购买页面查看价格。\n\n#\n交易\n\n通过 OpenAPI 进行交易，无附加收费，交易费用与通过 APP 交易的费用一致。具体收费方案如下表：\n\n所属券商\t收费方案\n富途证券(香港)\t收费方案\nmoomoo证券(美国)\t收费方案\nmoomoo证券(新加坡)\t收费方案\nmoomoo证券(澳大利亚)\t收费方案\n\n← 权限和限制\n可视化 OpenD →\n\n费用\n行情\n交易"}, {"title": "权限和限制 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/intro/authority.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\nOpenAPI 介绍\n权限和限制\n费用\n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\n权限和限制\n#\n登录限制\n#\n开户限制\n\n首先，您需要先在富途牛牛 APP上，完成交易业务账户的开通，才能成功登录 OpenAPI。\n\n\n#\n合规确认\n\n首次登录成功后，您需要完成问卷评估与协议确认，才能继续使用 OpenAPI。牛牛用户请 点击这里。\n\n#\n行情数据\n\n行情数据的限制主要体现在以下几方面：\n\n行情权限 —— 获取相关行情数据的权限\n接口限频 —— 调用行情接口的频率限制\n订阅额度 —— 同时订阅的实时行情的数量\n历史 K 线额度 —— 每 30 天最多可拉取多少个标的的历史 K 线\n#\n行情权限\n\n通过 OpenAPI 获取行情数据，需要相应的行情权限，OpenAPI 的行情权限跟 APP 的行情权限不完全一样，不同的权限等级对应不同的时延、摆盘档数以及接口使用权限。\n\n部分品种行情，需要购买行情卡后方可获取，具体获取方式见下表。\n\n市场\t标的类别\t获取方式\n香港市场\t证券类产品（含股票、ETFs、窝轮、牛熊、界内证）\t* 中国内地IP客户：免费获取 LV2 行情。如需获得 SF 权限，请购买 港股高级全盘行情\n* 港澳台及海外IP客户：免费获取 BMP 行情。如需获得 LV2 权限，请购买 港股 LV2 高级行情 。如需获得 SF 权限，请购买 港股高级全盘行情\n指数\n板块\n期权\t* 中国内地IP客户：推广期免费获取 LV2 行情\n* 港澳台及海外IP客户：免费获取 BMP 行情，如需获得 LV2 权限，请购买 港股期权期货 LV2 高级行情\n期货\n美国市场\t证券类产品（含纽交所、美交所、纳斯达克上市的股票、ETFs）\t* 如需获得 LV1 权限（基本报价），请购买 Nasdaq Basic 。\n* 如需获得 LV2 权限（基本报价+深度摆盘），非专业用户请购买 Nasdaq Basic+TotalView (Non-Pro) ；专业用户请购买 Nasdaq Basic+TotalView (Pro) 。\n板块\nOTC 股票\t暂不支持获取\n期权（含普通股票期权、指数期权）\t* 达到门槛 \n 的客户：免费获得 LV1 权限。\n* 未达到门槛 \n 的客户：请购买 OPRA 期权 LV1 实时行情 获得 LV1 权限。\n期货\t* 已开通期货账户 \n 的客户：\n如需获取 CME Group 行情 \n ，请购买 CME Group 期货 LV2\n如需获取 CME 行情，请购买 CME 期货 LV2\n如需获取 CBOT 行情，请购买 CBOT 期货 LV2\n如需获取 NYMEX 行情，请购买 NYMEX 期货 LV2\n如需获取 COMEX 行情，请购买 COMEX 期货 LV2\n\n* 未开通期货账户的客户：不支持获取\n指数\t暂不支持获取\nA 股市场\t证券类产品（含股票、ETFs）\t* 中国内地 IP 个人客户：免费获取 LV1 行情\n* 港澳台及海外IP客户/机构客户：暂不支持\n指数\n板块\n新加坡市场\t期货\t暂不支持获取\n日本市场\t期货\t暂不支持获取\n\n提示\n\n上述表格，中国内地IP客户和港澳台及海外IP客户，以 OpenD 登录的 IP 地址作为区分依据。\n\n#\n接口限频\n\n为保护服务器，防止恶意攻击，所有需要向富途服务器发送请求的接口，都会有频率限制。\n每个接口的限频规则会有不同，具体请参见每个接口页面下面的 接口限制。\n\n举例：\n快照 接口的限频规则是：每 30 秒内最多请求 60 次快照。您可以每隔 0.5 秒请求一次匀速请求，也可以快速请求 60 次后，休息 30 秒，再请求下一轮。如果超出限频规则，接口会返回错误。\n\n#\n订阅额度 & 历史 K 线额度\n\n订阅额度和历史 K 线额度限制如下：\n\n用户类型\t订阅额度\t历史 K 线额度\n开户用户\t100\t100\n总资产达 1 万 HKD\t300\t300\n以下三条满足任意一条即可：\n1. 总资产达 50 万 HKD；\n2. 月交易笔数 > 200；\n3. 月交易额 > 200 万 HKD\t1000\t1000\n以下三条满足任意一条即可：\n1. 总资产达 500 万 HKD；\n2. 月交易笔数 > 2000；\n3. 月交易额 > 2000 万 HKD\t2000\t2000\n\n1、总资产\n总资产，是指您在富途证券的所有资产，包括：港、美、A 股证券账户和期货账户，按照即时汇率换算成以港元为单位。\n\n2、月交易笔数\n月交易笔数，会综合您在富途证券的证券账户和期货账户，在当前自然月与上一自然月的交易情况，取您上个自然月的成交笔数与当前自然月的成交笔数的较大值进行计算，即：\nmax (上个自然月的成交笔数，当前自然月的成交笔数)。\n\n3、月交易额\n月交易额，会综合您在富途证券的证券账户和期货账户，在当前自然月与上一自然月的交易情况，取您上个自然月的成交总金额与当前自然月的成交总金额的较大值进行计算，即：\nmax（上个自然月的成交总金额，当前自然月的成交总金额）\n按照即期汇率换算成以港币为单位。其中，期货交易额的计算，需要乘以相应的调整系数（默认取 0.1），期货交易额计算公式如下：\n期货交易额=∑（单笔成交数 * 成交价 * 合约乘数 * 汇率 * 调整系数）\n\n4、订阅额度\n订阅额度，适用于 订阅 接口。每只股票订阅一个类型即占用 1 个订阅额度，取消订阅会释放已占用的额度。 举例：\n假设您的订阅额度是 100。 当您同时订阅了 HK.00700 的实时摆盘、US.AAPL 的实时逐笔、SH.600519 的实时报价时，此时订阅额度会占用 3 个，剩余的订阅额度为 97。 这时，如果您取消了 HK.00700 的实时摆盘订阅，您的订阅额度占用将变成 2 个，剩余订阅额度会变成 98。\n\n5、历史 K 线额度\n历史 K 线额度，适用于 获取历史 K 线 接口。最近 30 天内，每请求 1 只股票的历史 K 线，将会占用 1 个历史 K 线额度。最近 30 天内重复请求同一只股票的历史 K 线，不会重复累计。 同时，订阅同一股票的不同周期的K线只占用1个额度，不会重复累计。 举例：\n假设您的历史 K 线额度是 100，今天是 2020 年 7 月 5 日。 您在 2020 年 6 月 5 日~2020 年 7 月 5 日之间，共计请求了 60 只股票的历史 K 线，则剩余的历史 K 线额度为 40。\n\n提示\n\n订阅额度和历史 K 线额度为系统自动分配，不需要手动申请。\n新入金的账户，额度等级会在 2 小时内自动生效。\n在途资产\n 不会用于额度计算。\n#\n交易功能\n进行指定市场的交易时，需要先确认是否已开通该市场的交易业务账户。\n举例：您只能在美股交易业务账户下进行美股交易，无法在港股交易业务账户下进行美股交易。\n\n← OpenAPI 介绍\n费用 →\n\n权限和限制\n登录限制\n行情数据\n交易功能"}, {"title": "OpenAPI Introduction | Futu API Doc v8.8", "url": "https://openapi.futunn.com/futu-api-doc/en/intro/intro.html", "html": " Futu API Doc v8.8\nProgramming Language \nEnglish \n\nIntroduction \n\nOpenAPI Introduction\nAuthorities and Limitations\nFee\n\nQuick Start \n\nOpenD \n\nQuote API \n\nTrade API \n\nBasic API \n\nQ&A \n\n#\nOpenAPI Introduction\n#\nOverview\n\nOpenAPI provides wide varieties of market data and trading services for your programmed trading to meet the needs of every developer's programmed trading and help your Quant dreams.\n\nFutubull users can click here to learn more.\n\nOpenAPI consists of OpenD and Futu API:\n\nOpenD is the gateway program of Futu API, running on your local computer or cloud server. It is responsible for transferring the protocol requests to Futu servers, and returning the processed data.\nFutu API is an API SDK encapsulated by Futu, including mainstream programming languages (Python, Java, C#, C++, JavaScript), to reduce the difficulty of your trading strategy development. If the language you want to use is not listed above, you can still interface with the protocol yourself to complete the trading strategy development.\n\nDiagrams below illustrate the architecture of OpenAPI.\n\nThe first time using OpenAPI, you need to finish the following two steps:\n\nThe first step is to install and start a gateway program OpenD locally or in the cloud.\n\nOpenD exposes the interfaces in the way of TCP, which is responsible for transferring the protocol requests to Futu Server and returning the processed data. The protocol interface has nothing to do with the type of programming language.\n\nThe second step is to download Futu API and complete Environment Setup.\n\nFor your convenience, <PERSON>tu encapsulates API SDK for mainstream programming languages (hereinafter referred to as Futu API).\n\n#\nAccount\n\nOpenAPI involves two types of accounts, Futu ID and universal account.\n\n#\nFutu ID\n\nFutu ID is your user account (Futubull ID ), which can be used in Futubull APP and OpenAPI.\nYou can use your Futu ID and login password to log in to OpenD and obtain market data.\n\n#\nUniversal Account\n\nUniversal account allows trading across multiple markets (including Hong Kong stocks, US stocks, A-shares, and funds) in various currencies. There's no need for multiple accounts.\nUniversal Accounts come in two forms:\n\nSecurities Universal Account: Trade stocks, ETFs, options, and other securities across different markets.\nFutures Universal Account: Trade futures, including Hong Kong, US CME Group, Singapore, and Japanese futures.\n#\nFunctionality\n\nThere are 2 functions of OpenAPI: quotation and trading.\n\n#\nQuotation Functions\n#\nQuotation Data Categories\n\nIncluding stocks, indices, options and futures from HK, US and A-share market. Find the specific types of support in the table below. You need authorities for each kinds of market data. For more details on how to obtain authorities, please click here.\n\nMarket\tContract\tFutu Users\nHK Market\tStocks, ETFs, Warrants, CBBCs, Inline Warrants\t✓\nOptions\t✓\nFutures\t✓\nIndices\t✓\nPlates\t✓\nUS Market\tStocks, ETFs \n\t✓\nOTC Securities\tX\nOptions \n\t✓\nFutures\t✓\nIndices\tX\nPlates\t✓\nA-share Market\tStocks, ETFs\t✓\nIndices\t✓\nPlates\t✓\nSingapore Market\tStocks, ETFs, Warrants, REITs, DLCs\tX\nFutures\tX\nJapanese Market\tStocks, ETFs, REITs\tX\nFutures\tX\nAustralian Market\tStocks, ETFs\tX\nGlobal Markets\tForex\tX\n#\nMethod to Obtain Market Data\nSubscribe and receive pushed real-time quote, candlestick, tick-by-tick and order book.\nRequest for the latest market snapshot, historical candlesticks etc.\n#\nTrading Functions\n#\nTrading Capacity\n\nIncluding stocks, options and futures from HK, US, A-share, Singapore and Japanese markets. Find the specific types of support in the table below:\n\nMarket\tContracts\tPaper Trading\tLive Trading\nFUTU HK\tMoomoo US\tMoomoo SG\tMoomoo AU\tMoomoo MY\tMoomoo CA\tMoomoo JP\nHK Market\tStocks, ETFs, Warrants, CBBCs, Inline Warrants\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nOptions\n\t✓\t✓\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\tX\tX\tX\tX\tX\nUS Market\tStocks, ETFs\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nOptions\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\nFutures\t✓\t✓\tX\t✓\tX\tX\tX\tX\nA-share Market\tChina Connect Securities stocks\t✓\t✓\t✓\t✓\tX\tX\tX\tX\nNon-China Connect Securities stocks\t✓\tX\tX\tX\tX\tX\tX\tX\nSingapore Market\tStocks, ETFs, Warrants, REITs, DLCs\tX\tX\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\t✓\tX\tX\tX\tX\nJapanese Market\tStocks, ETFs, REITs\tX\tX\tX\tX\tX\tX\tX\tX\nFutures\t✓\t✓\tX\tX\tX\tX\tX\tX\nAustralian Market\tStocks, ETFs\tX\tX\tX\tX\tX\tX\tX\tX\n#\nMethod of Trading\n\nThe trading interfaces are used for both live trading and paper trading.\n\n#\nFeatures\nFull platform and multi-language\nOpenD supports Windows, MacOS, CentOS, Ubuntu\nFutu API supports Python, Java, C#, C++, JavaScript, etc.\nStable speed and free\nStable technical architecture, directly connected to the exchanges\nThe fastest order is 0.0014s\nThere is no additional charge for trading via OpenAPI\nAbundant investment varieties\nSupporting real-time market data, live trading, and simulated trading in multiple markets including United States, Hong Kong, etc.\nProfessional institutional services\nCustomized market data and trading solutions\n\nAuthorities and Limitations →\n\nOpenAPI Introduction\nOverview\nAccount\nFunctionality\nFeatures"}, {"title": "OpenAPI 介绍 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/intro/intro.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\nOpenAPI 介绍\n权限和限制\n费用\n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\nOpenAPI 介绍\n#\n概述\n\nOpenAPI 量化接口，为您的程序化交易，提供丰富的行情和交易接口，满足每一位开发者的量化投资需求，助力您的宽客梦想。\n\n牛牛用户可以 点击这里了解更多。\n\nOpenAPI 由 OpenD 和 Futu API 组成：\n\nOpenD 是 Futu API 的网关程序，运行于您的本地电脑或云端服务器，负责中转协议请求到富途后台，并将处理后的数据返回。\nFutu API 是富途为主流的编程语言（Python、Java、C#、C++、JavaScript）封装的 API SDK，以方便您调用，降低策略开发难度。如果您希望使用的语言没有在上述之列，您仍可自行对接裸协议，完成策略开发。\n\n下面的框架图和时序图，帮助您更好地了解 OpenAPI。\n\n初次接触 OpenAPI，您需要进行如下两步操作：\n\n第一步，在本地或云端安装并启动一个网关程序 OpenD。\n\nOpenD 以自定义 TCP 协议的方式对外暴露接口，负责中转协议请求到富途服务器，并将处理后的数据返回，该协议接口与编程语言无关。\n\n第二步，下载 Futu API，完成 环境搭建，以便快速调用。\n\n为方便您的使用，富途对主流的编程语言，封装了相应的 API SDK（以下简称 Futu API）。\n\n#\n账号\n\nOpenAPI 涉及 2 类账号，分别是 平台账号 和 综合账户。\n\n#\n平台账号\n平台账号是您在富途的用户 ID（牛牛号），此账号体系适用于富途牛牛 APP、OpenAPI。 您可以使用平台账号（牛牛号）和登录密码，登录 OpenD 并获取行情。\n#\n综合账户\n\n综合账户支持以多种货币在同一个账户内交易不同市场品类（港股、美股、A股通、基金）。您可以通过一个账户进行全市场交易，不需要再管理多个账户。\n综合账户包括综合账户 - 证券，综合账户 - 期货等业务账户：\n\n综合账户 - 证券，用于交易全市场的股票、ETFs、期权等证券类产品。\n综合账户 - 期货，用于交易全市场的期货产品，目前支持香港市场期货、美国市场 CME Group 期货、新加坡市场期货、日本市场期货。\n#\n功能\n\nOpenAPI 的功能主要有两部分：行情和交易。\n\n#\n行情功能\n#\n行情数据品类\n\n支持香港、美国、A 股市场的行情数据，涉及的品类包括股票、指数、期权、期货等，具体支持的品种见下表。\n获取行情数据需要相关权限，如需了解行情权限的获取方式以及限制规则，请 点击这里。\n\n市场\t品种\t牛牛用户\n香港市场\t股票、ETFs、窝轮、牛熊、界内证\t✓\n期权\t✓\n期货\t✓\n指数\t✓\n板块\t✓\n美国市场\t股票、ETFs\n\t✓\nOTC 股票\tX\n期权 \n\t✓\n期货\t✓\n指数\tX\n板块\t✓\nA 股市场\t股票、ETFs\t✓\n指数\t✓\n板块\t✓\n新加坡市场\t股票、ETFs、窝轮、REITs、DLCs\tX\n期货\tX\n日本市场\t股票、ETFs、REITs\tX\n期货\tX\n澳大利亚市场\t股票、ETFs\tX\n环球市场\t外汇\tX\n#\n行情数据获取方式\n订阅并接收实时报价、实时 K 线、实时逐笔、实时摆盘等数据推送\n拉取最新市场快照，历史 K 线等\n#\n交易功能\n#\n交易能力\n\n支持香港、美国、A 股、新加坡、日本 5 个市场的交易能力，涉及的品类包括股票、期权、期货等，具体见下表：\n\n市场\t品种\t模拟交易\t真实交易\nFUTU HK\tMoomoo US\tMoomoo SG\tMoomoo AU\tMoomoo MY\tMoomoo CA\tMoomoo JP\n香港市场\t股票、ETFs、窝轮、牛熊、界内证\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期权\n\t✓\t✓\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\tX\tX\tX\tX\tX\n美国市场\t股票、ETFs\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期权\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期货\t✓\t✓\tX\t✓\tX\tX\tX\tX\nA 股市场\tA 股通股票\t✓\t✓\t✓\t✓\tX\tX\tX\tX\n非 A 股通股票\t✓\tX\tX\tX\tX\tX\tX\tX\n新加坡市场\t股票、ETFs、窝轮、REITs、DLCs\tX\tX\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\t✓\tX\tX\tX\tX\n日本市场\t股票、ETFs、REITs\tX\tX\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\tX\tX\tX\tX\tX\n澳大利亚市场\t股票、ETFs\tX\tX\tX\tX\tX\tX\tX\tX\n#\n交易方式\n\n真实交易和模拟交易使用同一套交易接口。\n\n#\n特点\n全平台多语言：\nOpenD 支持 Windows、MacOS、CentOS、Ubuntu\nFutu API 支持 Python、Java、C#、C++、JavaScript 等主流语言\n稳定极速免费：\n稳定的技术架构，直连交易所一触即达\n下单最快只需 0.0014 s\n通过 OpenAPI 交易无附加收费\n丰富的投资品类：\n支持美国、香港等多个市场的实时行情、实盘交易及模拟交易\n专业的机构服务：\n定制化的行情交易解决方案\n\n权限和限制 →\n\nOpenAPI 介绍\n概述\n账号\n功能\n特点"}, {"title": "OpenAPI 介绍 | Futu API 文档 v8.8", "url": "https://openapi.futunn.com/futu-api-doc/intro/intro.html", "html": " Futu API 文档 v8.8\n编程语言 \n简体中文 \n\n介绍 \n\nOpenAPI 介绍\n权限和限制\n费用\n\n快速上手 \n\nOpenD \n\n行情接口 \n\n交易接口 \n\n基础接口 \n\nQ&A \n\n#\nOpenAPI 介绍\n#\n概述\n\nOpenAPI 量化接口，为您的程序化交易，提供丰富的行情和交易接口，满足每一位开发者的量化投资需求，助力您的宽客梦想。\n\n牛牛用户可以 点击这里了解更多。\n\nOpenAPI 由 OpenD 和 Futu API 组成：\n\nOpenD 是 Futu API 的网关程序，运行于您的本地电脑或云端服务器，负责中转协议请求到富途后台，并将处理后的数据返回。\nFutu API 是富途为主流的编程语言（Python、Java、C#、C++、JavaScript）封装的 API SDK，以方便您调用，降低策略开发难度。如果您希望使用的语言没有在上述之列，您仍可自行对接裸协议，完成策略开发。\n\n下面的框架图和时序图，帮助您更好地了解 OpenAPI。\n\n初次接触 OpenAPI，您需要进行如下两步操作：\n\n第一步，在本地或云端安装并启动一个网关程序 OpenD。\n\nOpenD 以自定义 TCP 协议的方式对外暴露接口，负责中转协议请求到富途服务器，并将处理后的数据返回，该协议接口与编程语言无关。\n\n第二步，下载 Futu API，完成 环境搭建，以便快速调用。\n\n为方便您的使用，富途对主流的编程语言，封装了相应的 API SDK（以下简称 Futu API）。\n\n#\n账号\n\nOpenAPI 涉及 2 类账号，分别是 平台账号 和 综合账户。\n\n#\n平台账号\n平台账号是您在富途的用户 ID（牛牛号），此账号体系适用于富途牛牛 APP、OpenAPI。 您可以使用平台账号（牛牛号）和登录密码，登录 OpenD 并获取行情。\n#\n综合账户\n\n综合账户支持以多种货币在同一个账户内交易不同市场品类（港股、美股、A股通、基金）。您可以通过一个账户进行全市场交易，不需要再管理多个账户。\n综合账户包括综合账户 - 证券，综合账户 - 期货等业务账户：\n\n综合账户 - 证券，用于交易全市场的股票、ETFs、期权等证券类产品。\n综合账户 - 期货，用于交易全市场的期货产品，目前支持香港市场期货、美国市场 CME Group 期货、新加坡市场期货、日本市场期货。\n#\n功能\n\nOpenAPI 的功能主要有两部分：行情和交易。\n\n#\n行情功能\n#\n行情数据品类\n\n支持香港、美国、A 股市场的行情数据，涉及的品类包括股票、指数、期权、期货等，具体支持的品种见下表。\n获取行情数据需要相关权限，如需了解行情权限的获取方式以及限制规则，请 点击这里。\n\n市场\t品种\t牛牛用户\n香港市场\t股票、ETFs、窝轮、牛熊、界内证\t✓\n期权\t✓\n期货\t✓\n指数\t✓\n板块\t✓\n美国市场\t股票、ETFs\n\t✓\nOTC 股票\tX\n期权 \n\t✓\n期货\t✓\n指数\tX\n板块\t✓\nA 股市场\t股票、ETFs\t✓\n指数\t✓\n板块\t✓\n新加坡市场\t股票、ETFs、窝轮、REITs、DLCs\tX\n期货\tX\n日本市场\t股票、ETFs、REITs\tX\n期货\tX\n澳大利亚市场\t股票、ETFs\tX\n环球市场\t外汇\tX\n#\n行情数据获取方式\n订阅并接收实时报价、实时 K 线、实时逐笔、实时摆盘等数据推送\n拉取最新市场快照，历史 K 线等\n#\n交易功能\n#\n交易能力\n\n支持香港、美国、A 股、新加坡、日本 5 个市场的交易能力，涉及的品类包括股票、期权、期货等，具体见下表：\n\n市场\t品种\t模拟交易\t真实交易\nFUTU HK\tMoomoo US\tMoomoo SG\tMoomoo AU\tMoomoo MY\tMoomoo CA\tMoomoo JP\n香港市场\t股票、ETFs、窝轮、牛熊、界内证\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期权\n\t✓\t✓\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\tX\tX\tX\tX\tX\n美国市场\t股票、ETFs\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期权\t✓\t✓\t✓\t✓\t✓\tX\tX\tX\n期货\t✓\t✓\tX\t✓\tX\tX\tX\tX\nA 股市场\tA 股通股票\t✓\t✓\t✓\t✓\tX\tX\tX\tX\n非 A 股通股票\t✓\tX\tX\tX\tX\tX\tX\tX\n新加坡市场\t股票、ETFs、窝轮、REITs、DLCs\tX\tX\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\t✓\tX\tX\tX\tX\n日本市场\t股票、ETFs、REITs\tX\tX\tX\tX\tX\tX\tX\tX\n期货\t✓\t✓\tX\tX\tX\tX\tX\tX\n澳大利亚市场\t股票、ETFs\tX\tX\tX\tX\tX\tX\tX\tX\n#\n交易方式\n\n真实交易和模拟交易使用同一套交易接口。\n\n#\n特点\n全平台多语言：\nOpenD 支持 Windows、MacOS、CentOS、Ubuntu\nFutu API 支持 Python、Java、C#、C++、JavaScript 等主流语言\n稳定极速免费：\n稳定的技术架构，直连交易所一触即达\n下单最快只需 0.0014 s\n通过 OpenAPI 交易无附加收费\n丰富的投资品类：\n支持美国、香港等多个市场的实时行情、实盘交易及模拟交易\n专业的机构服务：\n定制化的行情交易解决方案\n\n权限和限制 →\n\nOpenAPI 介绍\n概述\n账号\n功能\n特点"}]