import multiprocessing
import sys
from time import sleep
from datetime import datetime, time
from logging import INFO
import pandas as pd
import numpy as np

from vnpy.event import EventEngine
from vnpy.trader.setting import SETTINGS
from vnpy.trader.engine import MainEngine

# from vnpy_ib import IbGateway
from IB相关.信号对比.实盘信号.data_recorder_fake_bar.utils.ib_gateway import IbGateway
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG

# 订阅行情
from vnpy.trader.object import SubscribeRequest, Exchange
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json

# 数据库入库
# import clickhouse_driver
import pymysql
# import calendar

SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
# print(SETTINGS)

IB_setting = {
    "TWS地址": "**************",
    "TWS端口": 4002,
    "客户号": 10,
    "交易账户": ""
}


# Chinese futures market trading period (day/night)
DAY_START = time(8, 45)
DAY_END = time(17, 0)

NIGHT_START = time(20, 45)
NIGHT_END = time(2, 45)

# ck connect
# client = clickhouse_driver.Client(host='localhost', port=9000, database='stock_bar',user='remote',password='zhP@55word')
conn = pymysql.connect(host='localhost', port=3306, database='common_info',user='remote',password='zhP@55word')
cursor = conn.cursor()

def insert_into_mysql_database(df, tablename, cols):
    _values = ','.join(['%s']*len(cols.split(',')))
    sql = f"insert into {tablename} ("+cols+") VALUES(%s)"%_values
    all_array = df.to_numpy()
    all_tuple = []
    count = 0
    for i in all_array:
        tmp = tuple(i)
        all_tuple.append(tmp)
        count += 1
        if count%5000 == 0:       # 每5000条存储一次
            cursor.executemany(sql, all_tuple)
            conn.commit()
            all_tuple = []
        
    # 将没有存完的数据存储好
    if count%5000 !=0:
        cursor.executemany(sql, all_tuple)
        conn.commit()
    return

def check_trading_period():
    """"""
    current_time = datetime.now().time()

    trading = False
    if (
        (current_time >= DAY_START and current_time <= DAY_END)
        or (current_time >= NIGHT_START)
        or (current_time <= NIGHT_END)
    ):
        trading = True

    return trading

def del_closed_day(hours_list):
    print(hours_list)
    for elem in hours_list[:]:
        if 'CLOSED' in elem or elem == '':
            hours_list.remove(elem)
    return hours_list

def process_intraday_period(td_hours, symbol, time_zone, time_type):
    period_list = []
    for i in range(len(td_hours)):
        time_daily = td_hours[i]
        start_date = int(time_daily.split(':')[0])
        end_date = int(time_daily.split('-')[-1].split(":")[0])
        comdty = symbol.split('-')[0]
        exchange = symbol.split('.')[1]
        start_time = pd.to_datetime(time_daily.split('-')[0], format="%Y%m%d:%H%M")
        end_time = pd.to_datetime(time_daily.split('-')[1], format="%Y%m%d:%H%M")
        period_list.append([symbol, comdty, exchange, start_date, end_date, time_zone, time_type, start_time, end_time])
    return period_list

def process_tradinghours_info(tradinghours_info):
    time_info = []
    for symbol in tradinghours_info.keys():
        print(tradinghours_info[symbol])
        td_hours = tradinghours_info[symbol]['tradingHours'].split(';')
        ld_hours = tradinghours_info[symbol]['liquidHours'].split(';')
        time_zone = tradinghours_info[symbol]['timezone']
        print(time_zone)
        
        td_hours = del_closed_day(td_hours)
        ld_hours = del_closed_day(ld_hours)
        print(td_hours)
        print(ld_hours)
        td_list = process_intraday_period(td_hours, symbol, time_zone, 'trading_hours')
        ld_list = process_intraday_period(td_hours, symbol, time_zone, 'liquid_hours')
        time_info  = time_info + td_list + ld_list
    cols = ['contract', 'symbol', 'exchange','start_date','end_date','time_zone', 'time_type','start_time','end_time']
    time_df = pd.DataFrame(time_info, columns=cols)
    print(time_df)
    insert_into_mysql_database(time_df, 'common_info.tradinghours', ','.join(cols))

def run_child():
    """
    Running in the child process.
    """
    SETTINGS["log.file"] = True

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    main_engine.add_gateway(IbGateway)

    # cta_engine = main_engine.add_app(CtaStrategyApp)
    main_engine.write_log("主引擎创建成功")

    log_engine = main_engine.get_engine("log")
    event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
    main_engine.write_log("注册日志事件监听")

    sleep(10)
    main_engine.connect(IB_setting, "IB")
    main_engine.write_log("连接IB接口")
    
    from vnpy.trader.utility import extract_vt_symbol
    cta_strategy_setting = load_json('cta_strategy_setting.json')
    for strategyname in cta_strategy_setting.keys():
        vt_symbol = cta_strategy_setting[strategyname]['vt_symbol']
        print("合约：", vt_symbol)
        symbol, exchange = extract_vt_symbol(vt_symbol)
        req = SubscribeRequest(symbol=symbol, exchange=exchange)
        main_engine.subscribe(req, "IB")
    
    sleep(10)
    print("#"*100)
    # 获取信息并保存到数据库
    tradinghours_info = main_engine.get_gateway('IB').api.tradinghours
    process_tradinghours_info(tradinghours_info)
    main_engine.close()
    sys.exit(0)

def run_parent():
    """
    Running in the parent process.
    """
    print("启动CTA策略守护父进程")

    child_process = None

    trading = check_trading_period()

    # Start child process in trading period
    if trading and child_process is None:
        print("启动子进程")
        child_process = multiprocessing.Process(target=run_child)
        child_process.start()
        print("子进程启动成功")

def create_table():
    sql = "create table if not exists common_info.tradinghours ( \
    contract char(50),\
    symbol char(50), \
    exchange char(50), \
    start_date Int,\
    end_date Int,\
    time_zone char(50),\
    time_type char(50), \
    start_time datetime NULL,\
    end_time datetime NULL,\
    PRIMARY KEY(symbol, contract, end_date)\
    )\
    ENGINE = InnoDB"
    cursor.execute(sql)
    return

if __name__ == "__main__":
    # create_table()
    run_parent()
