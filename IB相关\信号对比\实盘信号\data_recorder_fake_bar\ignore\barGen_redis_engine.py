""""""
import traceback
from threading import Thread
from queue import Queue, Empty
from typing import Callable, Dict, Optional
from datetime import datetime,time
from vnpy.event import Event, EventEngine
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.object import (
    SubscribeRequest,
    TickData,
    BarData,
    ContractData
)
import redis
from vnpy.trader.event import EVENT_TICK, EVENT_CONTRACT, EVENT_TIMER
from vnpy.trader.utility import load_json, save_json 
from vnpy.trader.object import BarData, TickData, Interval
from vnpy.trader.constant import (
    Exchange
)
from vnpy.trader.setting import SETTINGS
APP_NAME = "BarGenEngine"
EVENT_BAR = "eBarGen."
EVENT_BAR_RECORD = "eBarGenRec."

# 定时重载 setting.json
class BarGenEngine(BaseEngine):
    """"""
    setting_filename = "barGen_redis_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.queue = Queue()
        self.thread = Thread(target=self.run)
        self.active = False

        self.bar_recordings = []
        # self.bar_generators = {}
        
        self.bars: Dict[str, BarData] = {}
        self.last_ticks: Dict[str, TickData] = {}
        #self.last_dt: datetime = None
        self.last_dts: Dict[str, datetime] = {}

        self.tick_time: Dict[str, str] = {} # cache tick time for debug info
        self.timer_count = 0
        self.timer_interval = 60
        # create connection
        self.r = redis.Redis(host=SETTINGS["redis.host"], port=SETTINGS["redis.port"], password=SETTINGS["redis.password"])
        self.load_setting()
        self.register_event()
        # self.start()  # need test vigar 1216 !
        self.put_event()

    def load_setting(self):
        """"""
        setting = load_json(self.setting_filename)
        self.write_log(f"barGen load setting : {setting}")
        self.bar_recordings = setting.get("bar", [])
        self.write_log(f"barGen load setting bar_recordings : {self.bar_recordings}")
        for elem in self.bar_recordings:
            self.write_log(f"subscribe elem {elem}")
            self.subscribe_recording(elem)

    def save_setting(self):
        """"""
        setting = {
            "bar": self.bar_recordings
        }
        save_json(self.setting_filename, setting)

    def run(self):
        """"""
        while self.active:
            try:
                if datetime.now().minute%30==0 and datetime.now().second==0:
                    # self.load_setting()
                    pass
            except Exception:
                msg = f"barGen run 触发异常已停止\n{traceback.format_exc()}"
                self.write_log(f"barGen error: {msg}")

    def close(self):
        """"""
        self.active = False
        if self.thread.isAlive():
            self.thread.join()

    def start(self):
        """"""
        self.active = True
        self.thread.start()

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            contract = self.main_engine.get_contract(vt_symbol)
            if not contract:
                self.write_log(f"找不到合约：{vt_symbol}")
                return
            self.write_log(f"prepare to send subscribe req：{vt_symbol}")
            self.subscribe(contract)
        except Exception:
            msg = f"barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"添加K线记录成功：{vt_symbol}")

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)

    # def update_tick(self, tick: TickData):
    #     """"""
    #     if tick.vt_symbol in self.bar_recordings:
    #         bg = self.get_bar_generator(tick.vt_symbol)
    #         bg.update_tick(copy(tick))
    
    # set skip flag
    def future_tick_filter(self, tick):
        ret = False
        # if time(11, 31) > datetime.now().time() > time(11, 30) and tick.datetime.minute == 30:
        if tick.datetime.hour == 11 and tick.datetime.minute == 30:
            ret = True
        if tick.datetime.hour==10 and tick.datetime.minute == 15:
            if tick.exchange in [Exchange.DCE, Exchange.CZCE, Exchange.SHFE]:
                ret = True
        return ret
    
    def update_redis(self, tick: TickData):
        data = {
            "price": tick.last_price,
            "limit_up": tick.limit_up,
            "limit_down": tick.limit_down,
            "volume": tick.volume,
            "open_interest": tick.open_interest,
            "datetime": tick.datetime.strftime('%Y%m%d%H%M%S.%f')# %f is microsecond
        }
        vt_symbol = f"{tick.symbol}.{tick.exchange.value}"
        for key, value in data.items():
            self.r.hset(vt_symbol, key, value)

    # def get_redis_price(vt_symbol: str):
    def get_redis_price(self, vt_symbol: str):
        (realtime_price, limit_up, limit_down) = (0, 0, 0)
        # data = redis_client.hgetall(vt_symbol)
        data = self.r.hgetall(vt_symbol)
        # print_local_log(f"get_redis_price ret: {data}")
        if b'price' in data.keys():
            realtime_price = float(data[b'price'].decode())
        if b'limit_up' in data.keys():
            limit_up = float(data[b'limit_up'].decode())
        if b'limit_down' in data.keys():
            limit_down = float(data[b'limit_down'].decode())
        return (realtime_price, limit_up, limit_down)

    def update_tick(self, tick: TickData):
        if not tick.last_price:
            return
        
        self.update_redis(tick)
        
        # reach time, send all bars
        last_dt = self.last_dts.get(tick.vt_symbol, None)
        
        if last_dt and last_dt.minute != tick.datetime.minute:
            if self.future_tick_filter(tick):
                return
            else:
                bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
                if bar:
                    bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                    self.record_bar(bar)
                    self.bars[tick.vt_symbol] = None
                
        bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
        if not bar:
            bar = BarData(
                symbol=tick.symbol,
                exchange=tick.exchange,
                interval=Interval.MINUTE,
                datetime=tick.datetime,
                gateway_name=tick.gateway_name,
                open_price=tick.last_price,
                high_price=tick.last_price,
                low_price=tick.last_price,
                close_price=tick.last_price,
                open_interest=tick.open_interest
            )
            self.bars[bar.vt_symbol] = bar
        else:
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.open_interest = tick.open_interest
            bar.datetime = tick.datetime

        last_tick: Optional[TickData] = self.last_ticks.get(tick.vt_symbol, None)
        if last_tick:
            bar.volume += max(tick.volume - last_tick.volume, 0)
            bar.turnover += max(tick.turnover - last_tick.turnover, 0)

        self.last_ticks[tick.vt_symbol] = tick
        self.last_dts[tick.vt_symbol] = tick.datetime
            
    def process_timer_event(self, event: Event):
        """"""
        self.timer_count += 1
        if self.timer_count < self.timer_interval:
            return
        
        tick_time_str = self.tick_time_info()
        self.write_log(f"process_timer_event: {tick_time_str}")
        self.timer_count = 0
        
        # add logic to handle 22:59 bar logic
        if datetime.now().minute==31 and datetime.now().hour==2:
            # get all bar 
            self.write_log(f"force gen all bar unfinished: ")
            all_symbol = self.bars.keys()
            for symbol in all_symbol:
                bar: Optional[BarData] = self.bars.get(symbol,None)
                if bar:
                    if bar.datetime.minute==59:
                        bar.datetime = bar.datetime.replace(secodn=0,microsecond=0)
                        self.record_bar(bar)
                        self.bars[symbol] = None
                    
    def process_tick_event(self, event: Event):
        """"""
        # update cache
        tick = event.data
        time_str = tick.datetime.strftime('%Y%m%d%H%M%S')
        self.tick_time[tick.symbol] = time_str
        # self.write_log(f"@@@redis_engine: process_tick_event: {tick}")
        self.update_tick(tick)
        
    def tick_time_info(self):
        ret = "{"
        for k,v in self.tick_time.items():
            info = f"{k} {v}\n"
            ret = ret + info
        ret = ret+"}"
        return ret

    def process_contract_event(self, event: Event):
        """"""
        contract = event.data
        vt_symbol = contract.vt_symbol

        if vt_symbol in self.bar_recordings:
            self.subscribe(contract)

    def write_log(self, msg: str):
        """"""
        self.main_engine.write_log(msg)

    def put_event(self):
        """"""
        # bar_symbols = list(self.bar_recordings)
        # bar_symbols.sort()
        pass

    # 处理bar,供其它应用处理
    def record_bar(self, bar: BarData):
        """"""
        try:
            time_str = datetime.now().strftime("%Y-%m-%d-%H%M%S")
            bar_time = bar.datetime.time()
            if time(2,30)<bar_time<time(9,0) or time(11,30)<bar_time<time(13,30):
                pass
            else:
                self.write_log(f" ======1======record bar memory: {time_str}: {bar.vt_symbol} {bar.datetime} o:{bar.open_price} h:{bar.high_price} l:{bar.low_price} c:{bar.close_price}")
                self.write_log(f" ======2========put to event_engine: {bar}")
                event = Event(EVENT_BAR, bar)
                event2 = Event(EVENT_BAR_RECORD, bar)
                self.event_engine.put(event)
                self.event_engine.put(event2)
        except Exception:
            msg = f"record_bar 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)

    # def get_bar_generator(self, vt_symbol: str):
    #     """"""
    #     bg = self.bar_generators.get(vt_symbol, None)

    #     if not bg:
    #         bg = BarGenerator(self.record_bar)
    #         self.bar_generators[vt_symbol] = bg
    #     return bg

    def subscribe(self, contract: ContractData):
        """"""
        req = SubscribeRequest(
            symbol=contract.symbol,
            exchange=contract.exchange
        )
        self.write_log(f"send subscribe req {contract.symbol}")
        self.main_engine.subscribe(req, contract.gateway_name)
