import multiprocessing

from multiprocessing import Process

from dateutil.relativedelta import relativedelta
from pandas import DataFrame
from vnpy.trader.setting import SETTINGS

SETTINGS["database.database"] = "vnpy_stk_xt"
SETTINGS["datafeed.name"] = "xt"
SETTINGS["datafeed.username"] = "token"
SETTINGS["datafeed.password"] = "******"

from vnpy.trader.database import get_database, DB_TZ

from vnpy.trader.constant import Exchange, Interval

EXCHANGE_VT2XT: dict[Exchange, str] = {Exchange.SSE: "SH", Exchange.SZSE: "SZ", Exchange.BSE: "BJ", Exchange.SHFE: "SF",
                                       Exchange.CFFEX: "IF", Exchange.INE: "INE", Exchange.DCE: "DF",
                                       Exchange.CZCE: "ZF", Exchange.GFEX: "GF", }

EXCHANGE_XT2VT = {v: k for k, v in EXCHANGE_VT2XT.items()}

from vnpy.trader.datafeed import get_datafeed
from datetime import datetime

try:
    from symbol_info import all_symbol_pres
except:
    from vnpy.utils.symbol_info import all_symbol_pres

# 设置合约品种
symbols = {
    # "SHFE": ["AG", "AL", "AU", "BU", "CU", "FU", "HC", "LU", "NI", "NR", "PB", "RB", "RU", "SC", "SN", "SP", "SS", "WR",
    #          "ZN"],
    # "DCE": ["A","B","BB","C","CS","EG","EB","FB","I","J","JD","JM","L","LH","M","P","PG","PP","RR","V","Y"],
    # "CZCE": ["AP","CF","CJ","CY","FG","JR","LR","MA","OI","PF","PM","RI","RM","RS","SA","SF","SM","SR","TA","UR","WH","ZC"],
    # "CFFEX": ["IC", "IF", "IH", "T", "TF", "TS"],
    "SSE": ["000300", "600941", "601186", "600233", "601878", "601666", "603087", "603737", "600320", "600808",
            "605090",
            "600975", "603013", "603367", "603328", "600400", "600897", "603360", "603098", "603275", "603666",
            "603669", "603190", "600706", "600892", "605169", "605288", "603326", "603280", "600241", "600243"],
    "SZSE": ["002352", "000157", "000513", "002850", "000830", "002698", "001323", "002506", "000521", "001301",
             "002755", "000980", "002204", "002038", "000917", "000755", "003000", "002990", "002344", "002918",
             "002845", "002520", "000713", "000151", "001217", "002940", "002412", "002976", "002213", "003036",
             "002357", "002529", "000953"]}
# symbols = {}
# for exchange, symbols_list in all_symbol_pres.items():
#     if exchange == 'INE':
#         symbols[exchange] = [symbol for symbol in symbols_list if symbol != 'sctas']
#     else:
#         symbols[exchange] = symbols_list

# 设置下载时间段
start_date = datetime(2017, 1, 1, tzinfo=DB_TZ)
end_date = datetime(2099, 1, 1, tzinfo=DB_TZ)

# interval = Interval.HOUR
# interval = Interval.MINUTE
interval = Interval.DAILY

from vnpy.trader.constant import Exchange
from vnpy.trader.object import HistoryRequest

database = get_database()
bar_overview = database.get_bar_overview()


def update_history_data() -> None:
    """更新历史合约信息"""
    # 在子进程中加载xtquant
    from xtquant.xtdata import download_history_data

    # 初始化数据服务
    datafeed = get_datafeed()
    datafeed.init()

    # 下载历史合约信息
    download_history_data("", "historycontract")
    download_history_data("", "historymaincontract")

    print("xtquant历史合约信息下载完成")


# 批量下载
def query_save_data(datafeed, req):
    data = datafeed.query_bar_history(req)
    if data:
        database.save_bar_data(data)
        print(f"{req.symbol}历史数据下载完成")


def download_data(datafeed, symbol_type="00"):
    overview_symbols = [(d.symbol, d.exchange.value, d.interval) for d in bar_overview]

    symbols_to_download = []
    for exchange, symbols_list in symbols.items():
        for symbol in symbols_list:
            if (symbol + symbol_type, exchange, interval) not in overview_symbols:
                symbols_to_download.append((symbol + symbol_type, Exchange(exchange), interval))
    print(f'symbols_to_download:{symbols_to_download}')

    for symbol, exchange, _ in symbols_to_download:
        req = HistoryRequest(symbol=symbol, exchange=exchange, start=start_date, interval=interval, end=end_date, )
        query_save_data(datafeed, req)

def update_data(datafeed, process_dividend=True):
    end = datetime.now().astimezone(DB_TZ)
    from xtquant import xtdata

    for d in bar_overview:
        if not d.interval == interval:
            continue
        symbol = d.symbol
        exchange = d.exchange
        start = d.end

        if process_dividend:
            # 改为如果在数据库最新更新时间，到下一个月，有换月、分红等操作，就重新下载
            xt_symbol: str = symbol + "." + EXCHANGE_VT2XT[exchange]
            dividend_start: str = start.strftime("%Y%m%d")
            dividend_end: str = (end + relativedelta(months=1)).strftime("%Y%m%d")
            dividend_df = xtdata.get_divid_factors(xt_symbol, dividend_start, dividend_end)
            if isinstance(dividend_df, DataFrame) and not dividend_df.empty:
                print(f"{symbol}有分红或换月操作，重新下载历史数据")
                # 删除数据库中该品种的所有数据
                database.delete_bar_data(symbol=symbol, exchange=exchange, interval=interval)
                start = start_date

        req = HistoryRequest(symbol=symbol, exchange=exchange, start=start, interval=interval, end=end, )
        query_save_data(datafeed, req)


def main():
    # 使用子进程更新历史合约信息
    process: Process = Process(target=update_history_data)
    process.start()
    process.join()  # 等待子进程执行完成

    datafeed = get_datafeed()
    datafeed.init()

    download_data(datafeed, symbol_type="")
    update_data(datafeed)


if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()

        # # 每日定时自动更新
        # current_time = datetime.now().time()
        # start_time = time(17, 0, 0)  # 每天17:00开始更新
        #
        # while True:
        #     sleep(60)  # 每分钟检查一次
        #     if current_time == start_time:
        #         # download_data()
        #         update_data()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(e)
        import requests, traceback

        print(traceback.format_exc())
        from send_to_wechat import WeChat

        wx = WeChat()
        import requests

        ip = requests.get('https://ifconfig.me').text
        wx.send_data(f"{ip}:{__file__}: An error occurred! ", touser='liaoyuan')
