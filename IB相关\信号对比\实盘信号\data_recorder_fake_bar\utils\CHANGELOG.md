# 更新日志 2025年7月11日

## 版本升级说明

本次升级主要围绕架构优化、行情数据处理及数据记录可靠性等方面进行了显著改进：

### 1. 架构优化与网关职责分离

-   **统一行情订阅路由：** `run_stk.py` 中扩展了 `MainEngine`，所有行情订阅（`subscribe` 和 `unsubscribe`）都被强制路由到专门的“行情”网关（`quote`），确保行情数据的统一入口和管理。
-   **独立行情与交易网关：** 在 `run_stk.py` 的 `StockTradingApp` 中，明确区分并初始化了两个 `IbGateway` 实例：
    -   `quote_gateway` 配置为 `quote_only = 1`（仅推送行情数据）。
    -   `trade_gateway` 配置为 `quote_only = -1`（仅处理交易请求）。
    -   注：正常默认`quote_only = 0`均处理
-   **事件过滤与隔离：** `ib_gateway.py` 中的 `IbGateway.on_event` 方法根据 `quote_only` 模式对事件进行过滤，确保行情网关只推送行情相关事件（如 `TickData`, `BarData`, `ContractData`），交易网关只推送交易相关事件。提升数据流的清晰度和模块间的隔离性。
-   **合约对象跨网关共享：** `ib_gateway.py` 中的 `IbApi` 类通过描述器协议使用共享类级别变量（`contracts` 和 `ib_contracts`）以及 `default_name` 属性，实现了在不同网关实例间对合约对象的统一管理和访问。例如，`send_order` 依赖传入 `MainEngine` 的 `contract` 的 `gateway_name`，统一使用`default_name` "IB"，也包括shelve文件contract缓存的读写。
-   **防止网关意外重连：** `ib_gateway.py` 中的 `IbGateway.close()` 方法在关闭时显式取消注册了定时事件，解决了之前网关在主动关闭后仍可能因定时器触发 `check_connection()` 而自动重连的问题。

### 2. K线生成与数据处理增强

-   **可选的5秒K线订阅：** `ib_gateway.py` 中的 `IbGateway` 新增 `use_5s_bar` 配置，`IbApi.subscribe` 和 `IbApi.unsubscribe` 方法会根据此配置有条件地订阅或退订IB的5秒实时K线（`reqRealTimeBars`/`cancelRealTimeBars`），使得用户可以灵活选择是否使用5秒K线数据源。
-   **动态事件注册：** `barGen_redis_engine.py` 的 `BarGenEngine.start` 方法现在根据 `self.gateway.use_5s_bar` 的值，动态注册 `EVENT_BAR_MINI`（处理5秒K线）或 `EVENT_TICK`（从Tick数据合成分钟K线）事件，使得K线生成方式更加智能和可配置。

### 3. 数据记录与可靠性提升

-   **预收盘Tick记录：** `recorder_engine.py` 增加了对 `EVENT_PRE_CLOSE` 事件的监听和处理，能够记录股票的预收盘Tick数据。
-   **职责统一与分离：** `barGen_redis_engine.py` 中 `process_bar` 负责处理K线（包括补充缺失K线，统一补充基于tick或者基于5s bar合成的K线遗漏），`record_bar` 负责实际保存K线到Redis和推送事件，职责更明确。
-   **新的事件类型：** `event.py` 中新增 `EVENT_PRE_CLOSE` 事件类型。

### 4. 事件类型扩展

-   **新增事件类型：** 在 `event.py` 中新增了 `EVENT_BAR_AGG`（用于聚合K线记录）和 `EVENT_ORDER_ERROR_RECORD`（用于订单错误记录）事件类型，增强了数据记录的全面性和异常处理的追踪能力。

### 5. 算法模板扩展

-   **新增第三方算法支持：** 在 `base.py` 的 `AlgoTemplateEnum` 中添加了 `"ThirdPartyAlgo"`，表示系统现在能够集成和支持第三方算法，增强了算法交易的灵活性和扩展性。

### 6. IB网关交易算法升级

-   **订单算法优化：** 在 `ib_gateway.py` 的 `IbApi.send_order` 方法中，将原有的 `Adaptive` 算法替换为 `PctVol`（按成交量百分比）算法。
-   **动态时间计算：** 新增逻辑以根据合约的交易时段动态计算 `PctVol` 算法的 `startTime` 和 `endTime`，能够更好地适应不同合约的交易时间窗口，包括处理跨日情况，确保算法在正确的时间范围内执行。
-   **日志增强：** 优化了 `send_order` 方法的日志记录，增加了 `algoStrategy` 和 `algoParams` 等信息，提升了订单发出时的可追溯性。

### 7. 数据记录与数据库管理增强

-   **新增数据库表：**
    -   `AggregatedBarData`：用于存储聚合K线数据，支持更灵活的数据分析。
    -   `AggregatedBarOverview`：提供聚合K线数据的汇总概览，方便快速统计。
    -   `OrderError` 和 `OrderErrorData`：用于详细记录订单错误信息，提升错误追踪和分析能力。
-   **数据保存逻辑扩展：**
    -   `RecorderEngine.run` 方法新增对聚合K线 (`agg_bar`) 和订单错误 (`order_error`) 数据的处理和保存逻辑。
    -   聚合K线数据保存前会进行时区调整、类型转换和批量插入，并同步更新聚合K线汇总表，确保数据一致性和查询效率。
-   **数据库连接健康检查：**
    -   `RecorderEngine` 新增定时检查数据库连接机制，每20分钟自动检测并尝试重连，提高了数据持久化的可靠性。
-   **事件注册扩展：** 新增注册 `EVENT_BAR_AGG` 和 `EVENT_ORDER_ERROR_RECORD` 事件，以支持新的数据类型和错误信息的记录。

### 8. 订单分发器优化

-   **测试配置更新：** 在 `dispatch_algo.py` 中，更新了 `TEST_SYMBOLS` 为IB股票合约，并相应调整了 `TEST_PRICE_RANGE` 和 `TEST_VOLUME_RANGE`，同时对测试订单的价格进行了精确度处理，使其更贴近实盘场景。
-   **订单分发逻辑细化：** 优化了 `_check_completed_orders` 方法，当订单分发超时未被接收时，即使在测试模式下也能够正确处理并跳过，增强了分发逻辑的健壮性。

### 9. 风险引擎余额获取优化

-   **账户余额获取逻辑增强：** 在 `engine_risk.py` 的 `get_balance` 方法中，现在会排除“行情”网关（`quote`）来获取账户余额，并且在直接获取账户信息失败时，会更智能地遍历所有账户，寻找匹配的 `USD` 结尾账户并匹配网关名称，从而提高了余额检查的准确性和鲁棒性。

### 10. 算法引擎核心逻辑优化

-   **第三方算法集成：** 算法引擎现在默认使用 `ThirdPartyAlgo` 处理新创建的订单，并在算法设置中新增其默认配置，同时扩展了 `load_algo_template` 方法以加载 `ThirdPartyAlgo`，标志着对外部算法的全面支持。
-   **合约管理效率提升：**
    -   新增 `queried_contracts` 缓存以避免重复查询合约详情。
    -   优化了网关获取逻辑，优先使用“行情”网关，提高了兼容性。
    -   `query_contract` 方法现在可以精确查询指定合约，并且在算法启动因合约未就绪而失败时，会触发更精准的合约查询，增强了系统启动的鲁棒性。
-   **测试配置更新：** 调整了 `TEST_SYMBOLS`、`TEST_NOTIONAL_RANGE` 和 `TEST_PRICE_DIVIDER`，使测试环境更贴近IB股票的实际交易特性。
-   **事件源与行情退订优化：** `EVENT_BAR` 事件的来源已统一，同时改进了行情退订逻辑，确保在不同网关配置下都能正确退订行情。

### 11. 算法模板职责分离

-   **订单错误记录解耦：** 在 `template.py` 的 `send_alert` 方法中，移除了直接通过模型保存订单错误记录的逻辑，转而完全通过事件机制 (`EVENT_ORDER_ERROR_RECORD`) 推送告警。这使得算法模板更专注于算法逻辑本身，将数据持久化职责统一到 `recorder_engine` 中，增强了模块间的解耦和代码的可维护性。
-   **导入路径更新：** 调整了相关导入路径以适应新的文件结构。

### 12. 股票交易应用核心增强

-   **交易时段定义优化：** 调整了 `US_DAY_END` 时间，并引入 `CLOSE_TIME` 和 `check_trading_period` 的 `extended` 参数，以更精细地管理和定义交易及收盘后时段。
-   **MainEngine 取消订阅逻辑改进：** 显式地通过 `get_gateway` 获取“行情”网关并执行取消订阅，确保所有行情取消请求都能正确路由和处理，提升了市场数据管理的可靠性。
-   **行情网关 5 秒 K 线配置调整：** 将 `quote_gateway.use_5s_bar` 明确设置为 `False`，这可能意味着系统将不再直接通过行情网关订阅 5 秒 K 线，从而优化数据流或将 K 线生成逻辑集中化。

### 13. 精度处理优化

-   **`round_to`函数改进：** `utility.py` 中的 `round_to` 函数已更新，现在使用 `Decimal` 类型和 `ROUND_HALF_UP` 舍入方法进行更精确的四舍五入。这确保了在金融计算中，价格和数量的舍入行为更加一致和可预测，避免了浮点数精度问题可能导致的不准确性。

### 14. 新增第三方算法 (ThirdPartyAlgo)

-   **逻辑简化：** `ThirdPartyAlgo` 参考 `VolumeFollowSyncAlgo`，但移除了复杂的内部订单拆分、跟量计算和精细订单生命周期管理，转而依赖 `ib_gateway.py` 中集成的 IBKR 原生算法单（如 `PctVol`）。
-   **高层策略聚焦：** 该算法将更侧重于高层次的策略决策，订单的实际执行和微观管理将由 IBKR 算法负责，从而简化了算法逻辑并提高了其鲁棒性。

### 15. 新增股票算法交易主程序 (run_stk_algo.py) (推断)

-   **专注于算法引擎管理：** `run_stk_algo.py` (推断) 将参考 `run_stk.py`，但其核心职责可能从 CTA 策略和数据记录转移到启动和维护算法交易引擎 (`AlgoEngine`)。
-   **简化启动流程：** 可能会移除对特定 CTA 策略的加载和初始化逻辑，转而通过 `AlgoEngine` 来统一管理算法任务的生命周期。
