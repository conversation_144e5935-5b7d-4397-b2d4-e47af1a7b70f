import json
import logging
from datetime import datetime, timedelta,date
from dateutil.relativedelta import relativedelta
import pandas_market_calendars as mcal
import peewee

from peewee import (
    MySQLDatabase,
    Model,
    CharField,
    DoubleField,
    DateTimeField,
    IntegerField,
    fn,
    OperationalError,
    DoesNotExist
)

from vnpy.trader.constant import Exchange, Interval
from vnpy_mysql.mysql_database import MysqlDatabase, DbBarData, DbBarOverview

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('adjustment.log'),
        logging.StreamHandler()
    ]
)

# 配置peewee的logger
# logger = logging.getLogger('peewee')
# logger.setLevel(logging.DEBUG)

# ================== 数据库配置 ==================
# 公共信息数据库配置
common_info_db_config = {
    'host': '*************',
    'port': 3308,
    'user': 'USstk',
    'password': 'USstkP@55word',
    'database': 'common_info',
    'charset': 'utf8mb4',
    'autoconnect': False
}

# ================== 数据库连接 ==================
common_db = MySQLDatabase(**common_info_db_config)

# 使用mysql_database.py中的MysqlDatabase类
mysql_database = MysqlDatabase()

class CommonInfoModel(Model):
    class Meta:
        database = common_db

class StockAdjustment(CommonInfoModel):
    old_symbol = CharField()
    new_symbol = CharField()
    exchange = CharField()
    price_multi = DoubleField()
    ex_date = DateTimeField()

    class Meta:
        table_name = 'stock_adjustment_us'

# ================== 核心逻辑 ==================
def get_previous_trading_day(current_date):
    """获取给定日期的上一个美股交易日"""
    nyse = mcal.get_calendar('NYSE')
    trading_days = nyse.schedule(start_date=current_date - timedelta(days=10), end_date=current_date)
    if len(trading_days) < 2:  # 确保至少有两个交易日
        trading_days = nyse.schedule(start_date=current_date - timedelta(days=20), end_date=current_date)
    return trading_days.index[-2].date()  # 返回倒数第二个交易日

def process_adjustment(adj: StockAdjustment):
    """处理单个复权记录"""
    old_symbol = adj.old_symbol.strip()
    exchange = adj.exchange.strip()
    prev_trading_day = get_previous_trading_day(adj.ex_date)
    ex_date = prev_trading_day.strftime("%y%m%d")
    new_symbol = f"{old_symbol}_{ex_date}"
    price_multi = adj.price_multi
    # 使用date.today()计算4个月前的日期，并设置为当月1号，保证月线完整
    cutoff_date = (date.today() - relativedelta(months=4)).replace(day=1)

    try:
        # ===== 第一步：将原始数据的symbol修改为new_symbol =====
        logging.info(f"开始修改symbol：{old_symbol} -> {new_symbol}")

        # 更新原始数据的symbol
        with mysql_database.db.atomic():
            (DbBarData
             .update(symbol=new_symbol)
             .where(
                 (DbBarData.symbol == old_symbol) &
                 (DbBarData.exchange == exchange))
             .execute())

        logging.info(f"symbol修改完成：{old_symbol} -> {new_symbol}")

        with mysql_database.db.atomic():
            # 更新视图表
            (DbBarOverview
             .update(symbol=new_symbol)
             .where(
                 (DbBarOverview.symbol == old_symbol) &
                 (DbBarOverview.exchange == exchange))
             .execute())


        # ===== 第二步：复制近4个月的数据并进行复权处理 =====

        # 对复制的数据进行前复权计算
        logging.info(f"开始复制四个月数据并前复权处理：{old_symbol}")
        with mysql_database.db.atomic():
            # 加载需要更新的数据
            bars_to_update = mysql_database.load_bar_data(
                new_symbol,
                Exchange(exchange),
                Interval('1m'),
                cutoff_date,
                datetime.max
            )

            for bar in bars_to_update:
                bar.symbol = old_symbol
                # bar.open_price = round(bar.open_price * price_multi,4)
                # bar.high_price = round(bar.high_price * price_multi,4)
                # bar.low_price = round(bar.low_price * price_multi,4)
                # bar.close_price = round(bar.close_price * price_multi,4)
                bar.open_price = bar.open_price * price_multi
                bar.high_price = bar.high_price * price_multi
                bar.low_price = bar.low_price * price_multi
                bar.close_price = bar.close_price * price_multi

                bar.volume = bar.volume / price_multi

            # 保存更新后的数据
            if bars_to_update:
                mysql_database.save_bar_data(bars_to_update)

        logging.info(f"成功处理{old_symbol}四个月的数据并复权")

    except OperationalError as e:
        logging.error(f"数据库操作失败：{str(e)}")
        raise
    except Exception as e:
        logging.error(f"处理失败：{str(e)}")
        raise

def main():
    try:
        # 连接数据库
        common_db.connect()
        logging.info("数据库连接成功")

        # 获取待处理记录
        # 美股处理时，如果是中国上午运行，当时取到的日期比美国快一天，但也是需要美国次日的未来复权因子
        # 如果是中国下午运行，当时取到的日期是美国的新一个交易日，是需要当天的未来复权因子
        # 正好对上
        adjustments = StockAdjustment.select().where(
            StockAdjustment.ex_date == date.today()
        )

        logging.info(f"共获取到 {len(adjustments)} 条复权记录")

        # 处理每条记录
        for idx, adj in enumerate(adjustments, 1):
            try:
                logging.info(f"正在处理第 {idx} 条记录（{adj.old_symbol}）")
                process_adjustment(adj)
            except Exception as e:
                logging.error(f"处理失败：{str(e)}", exc_info=True)
                continue

    except OperationalError as e:
        logging.error(f"数据库连接失败：{str(e)}")
    except Exception as e:
        logging.error(f"全局错误：{str(e)}", exc_info=True)
    finally:
        # 关闭数据库连接
        if not common_db.is_closed():
            common_db.close()
        logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main()