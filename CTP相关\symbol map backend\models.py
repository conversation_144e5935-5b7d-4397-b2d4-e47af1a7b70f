from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

db = SQLAlchemy()

class MainContractManagement(db.Model):
    __tablename__ = 'main_contract_management'
    id = db.Column(db.Integer, primary_key=True)
    main_symbol = db.Column(db.String(20), nullable=False, unique=True)
    specific_symbol = db.Column(db.String(20), nullable=False)
    update_time = db.Column(db.DateTime, nullable=False, default=datetime.now)
    createUser = db.Column(db.String(64), nullable=False)  # 新增列

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.<PERSON><PERSON>, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
