from typing import Dict, List, Tuple, Set
import typer
from loguru import logger
import os
import shutil
from datetime import datetime

log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)

import sys, os
# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from utils.database_manager import IbProduct

# 复用原有的核心函数
from firstrate_.update_conid import get_conid_mapping

app = typer.Typer()

def read_latest_conids(file_path: str) -> Set[int]:
    """读取latest_ids.txt中的conid列表
    
    Args:
        file_path: latest_ids.txt文件路径
        
    Returns:
        Set[int]: conid集合
    """
    conids = set()
    
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return conids
        
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and line.isdigit():
                    conids.add(int(line))
                elif line:  # 非空但非数字的行
                    logger.warning(f"第{line_num}行包含无效数据: {line}")
                    
        logger.info(f"从{file_path}读取到 {len(conids)} 个有效conid")
        return conids
        
    except Exception as e:
        logger.error(f"读取文件时发生错误: {str(e)}")
        return conids

def update_conids_in_file(file_path: str, conid_mappings: Dict[int, dict]) -> int:
    """更新文件中的conid
    
    Args:
        file_path: 文件路径
        conid_mappings: conid映射字典
        
    Returns:
        int: 更新的conid数量
    """
    if not conid_mappings:
        logger.info("没有需要更新的conid")
        return 0
    
    # 备份原文件
    backup_dir = os.path.join(os.path.dirname(file_path), "bak")
    os.makedirs(backup_dir, exist_ok=True)
    backup_file = os.path.join(
        backup_dir,
        f"{os.path.basename(file_path)}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    shutil.copy2(file_path, backup_file)
    logger.info(f"已备份原文件到: {backup_file}")
    
    updated_count = 0
    updated_conids = []
    
    try:
        # 读取所有行
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 更新conid
        with open(file_path, 'w', encoding='utf-8') as f:
            for line in lines:
                line_content = line.strip()
                if line_content and line_content.isdigit():
                    old_conid = int(line_content)
                    if old_conid in conid_mappings:
                        new_conid = conid_mappings[old_conid]['new_conid']
                        f.write(f"{new_conid}\n")
                        updated_conids.append(old_conid)
                        updated_count += 1
                        logger.info(f"更新: {old_conid} -> {new_conid}")
                    else:
                        f.write(line)
                else:
                    f.write(line)
        
        logger.info(f"文件更新完成，共更新了 {updated_count} 个conid")
        return updated_count
        
    except Exception as e:
        logger.error(f"更新文件时发生错误: {str(e)}")
        # 如果更新失败，恢复备份文件
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, file_path)
            logger.info("已从备份文件恢复原始内容")
        raise

@app.command()
def update(
    latest_ids_file: str = typer.Option(
        "latest_ids.txt",
        "--file",
        "-f",
        help="latest_ids.txt文件路径"
    )
):
    """更新latest_ids.txt文件中的旧conid为新conid"""
    try:
        logger.info("开始处理latest_ids.txt中的conid更新...")
        
        # 1. 读取latest_ids.txt文件
        latest_ids_path = os.path.join(os.path.dirname(__file__), latest_ids_file)
        latest_conids = read_latest_conids(latest_ids_path)
        
        if not latest_conids:
            logger.error("未找到有效的conid数据")
            return
            
        logger.info(f"读取到 {len(latest_conids)} 个conid，开始检查更新需求...")
        
        # 2. 获取conid映射关系（复用原有逻辑）
        logger.info("正在获取conid映射关系...")
        conid_mappings, warning_mappings = get_conid_mapping(latest_conids)
        
        # 3. 输出警告信息
        if warning_mappings:
            logger.warning(f"发现 {len(warning_mappings)} 个链式判断错误的映射关系，这些映射将被跳过:")
            for old_conid, mapping_info in warning_mappings.items():
                logger.warning(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                             f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
            logger.warning("请人工核查这些映射关系")
        
        # 4. 输出需要更新的conid信息
        if conid_mappings:
            logger.info(f"找到 {len(conid_mappings)} 个需要更新的conid:")
            for old_conid, mapping_info in conid_mappings.items():
                logger.info(f"  {old_conid} ({mapping_info['old_symbol']}) -> "
                           f"{mapping_info['new_conid']} ({mapping_info['new_symbol']})")
        else:
            logger.info("没有需要更新的conid")
        
        # 5. 更新文件中的conid
        updated_count = update_conids_in_file(latest_ids_path, conid_mappings)
        
        # 6. 输出总结信息
        total_checked = len(latest_conids)
        logger.success(f"conid更新任务完成！")
        logger.info(f"总共检查: {total_checked} 个conid")
        logger.info(f"实际更新: {updated_count} 个conid")
        
        if warning_mappings:
            logger.info(f"需要人工核对: {len(warning_mappings)} 个conid")
        
    except Exception as e:
        logger.error(f"更新过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    app()