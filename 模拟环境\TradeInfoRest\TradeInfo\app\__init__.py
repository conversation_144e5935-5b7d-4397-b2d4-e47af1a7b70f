from flask import Flask
from flask_bootstrap import Bootstrap
from flask_mail import Mail
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from config import config
from flask_httpauth import HTTPBasicAuth

bootstrap = Bootstrap()
mail = Mail()
db = SQLAlchemy()
httpauth = HTTPBasicAuth()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'# 未登录用户访问需要登录的页面时，会被重定向到这里


def create_app(config_name):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    bootstrap.init_app(app)
    mail.init_app(app)
    db.init_app(app)
    login_manager.init_app(app)

    if app.config['SSL_REDIRECT']:
        from flask_sslify import SSLify
        sslify = SSLify(app)

    from .main import main as main_blueprint
    app.register_blueprint(main_blueprint)

    from .api import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api/v1')

    return app
