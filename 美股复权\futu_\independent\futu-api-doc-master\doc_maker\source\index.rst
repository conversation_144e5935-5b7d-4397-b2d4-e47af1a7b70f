.. futu-api documentation master file, created by
   sphinx-quickstart on Thu Aug 31 10:00:54 2017.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

=================================
futu-api |version| 指南
=================================

futuapi documentation

.. toctree::
	:caption: 基础
	:maxdepth: 4
	:hidden:

	intro/intro
	intro/FutuOpenDGuide

.. toctree::
	:caption: PROTOCOL
	:maxdepth: 4
	:hidden:
	
	protocol/intro
	protocol/base_define
	protocol/quote_protocol
	protocol/trade_protocol
		
.. toctree::
	:caption: Python API
	:maxdepth: 4
	:hidden:

	api/intro
	api/setup
	api/Base_API
	api/Quote_API
	api/Trade_API

.. toctree::
	:caption: .Net API
	:maxdepth: 4
	:hidden:

	net/intro
	net/Base_API
	net/Quote_API
	net/Trade_API
	net/start

.. toctree::
	:caption: .Java API
	:maxdepth: 4
	:hidden:

	java/intro
	java/Base_API
	java/Quote_API
	java/Trade_API
	java/start
	
.. toctree::
	:caption: CPP API
	:maxdepth: 4
	:hidden:

	cpp/intro
	cpp/base
	cpp/qot
	cpp/trd
	cpp/start

.. toctree::
	:caption: JavaScript API
	:maxdepth: 4
	:hidden:

	javascript/intro
	javascript/Quote_API
	javascript/Trade_API
	javascript/start
	
.. toctree::
	:caption: Q&A
	:maxdepth: 2
	:hidden:

	q&a/Q&A
