# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class DaItem(scrapy.Item):
    # define the fields for your item here like:
    # name = scrapy.Field()
    类型 = scrapy.Field()
    交易所 = scrapy.Field()
    合约名称 = scrapy.Field()
    交易所代码 = scrapy.Field()
    DA代码 = scrapy.Field()
    香港交易 = scrapy.Field()
    最低波幅 = scrapy.Field()
    合约大小 = scrapy.Field()
    合约月份 = scrapy.Field()
    涨跌停 = scrapy.Field()
    首次通知日 = scrapy.Field()
    最后交易日 = scrapy.Field()
    交割方式 = scrapy.Field()
