from flask import jsonify

from . import main
from ..logConf import logger


@main.app_errorhandler(403)
def forbidden(e):
    response = jsonify({'error': 'forbidden'})
    response.status_code = 403
    logger.info(e)
    return response


@main.app_errorhandler(404)
def page_not_found(e):
    response = jsonify({'error': 'not found'})
    response.status_code = 404
    logger.info(e)
    return response


@main.app_errorhandler(400)
def bad_request(e):
    response = jsonify({'error': 'bad request'})
    response.status_code = 400
    logger.info(e)
    return response


@main.app_errorhandler(409)
def conflict(e):
    response = jsonify({'error': 'conflict'})
    response.status_code = 409
    logger.info(e)
    return response


@main.app_errorhandler(401)
def unauthorized(e):
    response = jsonify({'error': 'unauthorized'})
    response.status_code = 401
    logger.info(e)
    return response


@main.app_errorhandler(500)
def internal_server_error(e):
    response = jsonify({'error': 'internal server error'})
    response.status_code = 500
    logger.info(e)
    return response
