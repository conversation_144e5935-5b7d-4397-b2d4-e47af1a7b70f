from vnpy_ctastrategy.backtesting import BacktestingEngine
from datetime import datetime
# from stock_configs import *
import pandas as pd
import datetime as dt

class BacktestInfo():
    trades = []
    vt_symbol = ''
    capital = 0
    size = 0
    slippage = 0
    rate = 0

def process_trades(trades):
    data = []
    for trades_num in trades.keys():
        data.append([trades[trades_num].symbol, trades[trades_num].datetime, trades[trades_num].direction, trades[trades_num].offset, trades[trades_num].price, trades[trades_num].volume])
    df = pd.DataFrame(data, columns = ['symbol', 'datetime','direction', 'offset','price','volume'])
    return df

def save_trades(trades, slippage, size):
    trades_df = process_trades(trades)
    trades_df['datetime'] = pd.to_datetime(trades_df['datetime'])
    trades_df['datetime'] = trades_df['datetime'].dt.tz_localize(None)
    trades_df['enter_time'] = trades_df['datetime']
    trades_df['exit_time'] = trades_df['datetime'].shift(-1)
    trades_df['enter_price'] = trades_df['price']
    trades_df['exit_price'] = trades_df['price'].shift(-1)
    trades_df['direction'] = (trades_df['direction'].astype(str)=='Direction.LONG')*1 + (trades_df['direction'].astype(str)=='Direction.SHORT')*-1
    trades_df['pnl'] = ((trades_df['exit_price']-trades_df['enter_price'])*trades_df['direction']*size - slippage)*trades_df.volume
    td_df = trades_df[trades_df.offset.astype(str)=='Offset.OPEN']
    return td_df[['symbol','enter_time','exit_time','direction','volume','enter_price','exit_price','pnl']]

def get_ex_factor():
    ex_factor_file_path = 'ex_factor.json'
    data = pd.read_json(ex_factor_file_path)
    data['ex_date'] = pd.to_datetime(data['ex_date'], unit='ms').dt.strftime("%Y-%m-%d")       # 这里用str速度比较快
    data['announcement_date'] = pd.to_datetime(data['announcement_date'], unit='ms').dt.strftime("%Y-%m-%d")
    return data

def run(strategy, params):
    # print(vt_symbol)
    engine = BacktestingEngine()
    engine.set_parameters(
        vt_symbol=params['vt_symbol'],
        interval=params['interval'],
        start=params['start'],
        end=params['end'],
        rate=params['rate'],
        slippage = params['slippage'],
        size=params['size'],
        pricetick=params['pricetick'],
        capital=params['capital'],
    )
    engine.add_strategy(strategy, {})
    engine.load_data()
    print(params['ex_date'], params['ex_factor'])
    engine.run_backtesting_sectional(ex_date=params['ex_date'], ex_factor=params['ex_factor'])
    return engine.trades

def convert_symbol(vt_symbol):
    if '.SZSE' in vt_symbol:
        order_book_id = vt_symbol[:6]+'.XSHE'
    elif '.SSE' in vt_symbol:
        order_book_id = vt_symbol[:6]+'.XSHG'
    return order_book_id

def get_date_cuts(vt_symbol, sdate, edate):
    ex_data = get_ex_factor()
    order_book_id = convert_symbol(vt_symbol)
    ex_symbol = ex_data[ex_data.order_book_id==order_book_id]
    # print(ex_symbol)
    ex_factor_dict = ex_symbol[['ex_date', 'ex_factor']].set_index('ex_date')['ex_factor'].to_dict()
    date_cuts = ex_symbol[(ex_symbol.ex_date>str(sdate))&(ex_symbol.ex_date<str(edate))].ex_date.to_list()
    date_cuts = [str(sdate)[:10]] + date_cuts + [str(edate)[:10]]
    date_cuts = list(set(date_cuts))
    date_cuts.sort()
    return date_cuts, ex_factor_dict

def merge_trades(all_trades, trades_tmp):
    if trades_tmp:
        start = len(all_trades)
        add_num = len(trades_tmp)
        keys_tmp = list(trades_tmp.keys())
        # keys_tmp.sort()
        # print(keys_tmp)
        for i in range(add_num):
            # TODO 后续如果要传到下一笔交易，这边需要修改
            if i==add_num-1 and str(trades_tmp[keys_tmp[i]].offset) == 'Offset.OPEN':    
                pass
            else:
                all_trades['BACKTESTING.'+str(start+i)] = trades_tmp[keys_tmp[i]]
    return all_trades
    
def backtest_sectional(strategy, vt_symbol, strategy_params):
    date_cuts, ex_factor_dict = get_date_cuts(vt_symbol, strategy_params['start'], strategy_params['end'])
    all_trades = dict()
    backtest_info = BacktestInfo()
    for i in range(len(date_cuts)-1):
        set_dict = strategy_params.copy()
        set_dict['vt_symbol'] = vt_symbol
        set_dict['start']  = datetime.strptime(date_cuts[i], "%Y-%m-%d") - dt.timedelta(days=20)
        set_dict['end']  = datetime.strptime(date_cuts[i+1], "%Y-%m-%d")
        set_dict['ex_date'] = datetime.strptime(date_cuts[i], "%Y-%m-%d")
        if date_cuts[i] in ex_factor_dict.keys():
            set_dict['ex_factor'] = ex_factor_dict[date_cuts[i]]
        else:
            set_dict['ex_factor'] = 1
        print(set_dict)
        trades_tmp = run(strategy, set_dict)
        if trades_tmp:
            print(trades_tmp['BACKTESTING.1'])
        all_trades = merge_trades(all_trades, trades_tmp)
    backtest_info.trades = all_trades
    backtest_info.capital = strategy_params['capital']
    backtest_info.rate = strategy_params['rate']
    backtest_info.size = strategy_params['size']
    backtest_info.slippage = strategy_params['slippage']
    backtest_info.vt_symbol = vt_symbol
    return backtest_info


if __name__ == '__main__':
    from tools.TradeAnalyse import TradeAnalyse
    from DoubleMA_simple import DoubleMA
    # from DoubleMA import DoubleMA
    import time
    import os
    print(os.path.dirname(__file__))
    os.chdir(os.path.dirname(__file__))
    t0 = time.time()
    # vt_symbols = ['adj006.SZSE','000006.SZSE']
    # vt_symbols = ['FG888.CZCE']
    vt_symbols = ['000006.SZSE']
    strategy_params = {
        'interval': '1m',
        'start': datetime(2022, 1, 1),
        'end': datetime(2023, 8, 15),
        'rate': 1.5e-3,
        'slippage': 0,
        'size': 100,
        'pricetick': 0.01,
        'capital': 100e4
    }
    
    for vt_symbol in vt_symbols:
        backtest_info = backtest_sectional(DoubleMA, vt_symbol, strategy_params)
        # print(backtest_info.trades)
        user = '胡'
        ta = TradeAnalyse()
        ta.summary(user, backtest_info)
    t1 = time.time()
    print("耗时: %.1f"%(t1-t0))

