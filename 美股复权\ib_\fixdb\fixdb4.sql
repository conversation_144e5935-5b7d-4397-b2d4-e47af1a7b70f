
-- 从源表读取数据插入到目标表，索引冲突时覆盖
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '663134311' AND datetime < '2023-11-01 12:00:00';
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '663134311' AND datetime < '2023-11-01 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '741203871' AND datetime < '2024-01-17 12:00:00';
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '741203871' AND datetime < '2024-01-17 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- 重置overview
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
            SELECT
                symbol,
                exchange,
                `interval`,
                COUNT(id) AS `count`,
                MIN(datetime) AS `start`,
                MAX(datetime) AS `end`
            FROM
                vnpy_stk_us_ib_d_2206_250814__.dbbardata
            GROUP BY
                symbol, exchange, `interval`
            ON DUPLICATE KEY UPDATE
                `count` = VALUES(`count`),
                `start` = VALUES(`start`),
                `end` = VALUES(`end`);