【查看网络】
docker network inspect bridge
docker inspect dbserver-db_his-1 | grep NetworkMode

【创建phpmyadmin】
docker rm -f my_phpmyadmin
docker run -d \
  --name my_phpmyadmin \
  --network dbserver_zhdb \
  -e PMA_HOST=dbserver-db_his-1 \
  -p 9090:80 \
  phpmyadmin

【创建用户】
CREATE USER 'CNstk'@'%' IDENTIFIED BY '*************';
GRANT SELECT, INSERT, UPDATE ON common_info.stock_adjustment_cn TO 'CNstk'@'%';
FLUSH PRIVILEGES;

【trigger】
DROP TRIGGER IF EXISTS trigger_insert_restrict;
DROP TRIGGER IF EXISTS trigger_update_restrict;
DELIMITER $$

CREATE TRIGGER trigger_insert_restrict
BEFORE INSERT ON stock_adjustment_cn
FOR EACH ROW
BEGIN
    IF NEW.old_symbol <> NEW.new_symbol THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'INSERT 错误：old_symbol 与 new_symbol 必须相同！';
    END IF;
    
    IF NEW.exchange NOT IN ('SSE', 'SZSE', 'BSE') THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'INSERT 错误：exchange 字段必须为 SSE、SZSE 或 BSE 之一！';
    END IF;
END $$

-- 触发器：UPDATE 时验证：
--  1. 只能在记录创建当天进行修改
--  2. 不允许修改 create_date 字段
--  3. old_symbol 与 new_symbol 必须相同
--  4. exchange 字段必须为 SSE、SZSE 或 BSE 之一
CREATE TRIGGER trigger_update_restrict
BEFORE UPDATE ON stock_adjustment_cn
FOR EACH ROW
BEGIN
    -- 检查原记录创建日期是否为当前日期
    IF DATE(OLD.create_date) <> CURDATE() THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'UPDATE 错误：只能在记录创建当天进行修改！';
    END IF;
    
    -- 不允许修改 create_date 字段
    IF NEW.create_date <> OLD.create_date THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'UPDATE 错误：不允许修改 create_date 字段！';
    END IF;
    
    -- 检查 old_symbol 与 new_symbol 是否相同
    IF NEW.old_symbol <> NEW.new_symbol THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'UPDATE 错误：old_symbol 与 new_symbol 必须相同！';
    END IF;
    
    -- 检查 exchange 字段是否有效
    IF NEW.exchange NOT IN ('SSE', 'SZSE', 'BSE') THEN
        SIGNAL SQLSTATE '45000'
            SET MESSAGE_TEXT = 'UPDATE 错误：exchange 字段必须为 SSE、SZSE 或 BSE 之一！';
    END IF;
END $$

DELIMITER ;
