# %%
import requests

headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6", "cache-control": "no-cache",
    "pragma": "no-cache", "sec-ch-ua": "\"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"",
    "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate", "sec-fetch-site": "none", "sec-fetch-user": "?1", "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"}
url = "https://companiesmarketcap.com/assets-by-market-cap/"
response = requests.get(url, headers=headers)

# print(response)
# %%
# print(response.text)
# %%
# response.text数据：列名：Rank, Name, Market Cap, Price, Today, Price(30 days), Country
# 不硬编码表头：//*[@id="cmkt"]/div[4]/table/thead/tr/th[1], /th[2], /th[3], /th[4], /th[5], /th[6], /th[7]
# 第一行各个元素：//*[@id="cmkt"]/div[4]/table/tbody/tr[1]/td[1]、/td[2]、/td[3]、/td[4]、/td[5]、/td[6]、/td[7]
# 第一列各个元素：//*[@id="cmkt"]/div[4]/table/tbody/tr/td[1]

# 提取数据到DataFrame
import pandas as pd
from lxml import etree

# 1. 提取表头
html = etree.HTML(response.text)
# columns = html.xpath('//*[@id="cmkt"]/div[4]/table/thead/tr/th/text()')
columns = ['Name', 'Company Code', 'Market Cap', 'Price', 'Today']

# 2. 提取数据
data = []
last_rank = int(html.xpath('//*[@id="cmkt"]/div[4]/table/tbody/tr[last()]/td[1]')[0].text)
for i in range(1, last_rank + 1):
    # rank = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[1]')[0].text

    # name = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[2]/a/div[1]')[0].text
    # //*[@id="cmkt"]/div[4]/table/tbody/tr[1]/td[2]/div[2]/a/div[1]是公司名，//*[@id="cmkt"]/div[4]/table/tbody/tr[1]/td[2]/div[2]/a/div[2]/text()是公司代码
    name = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[2]/div[2]//div[1]')[0].text
    company_code = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[2]/div[2]//div[2]/text()')[0]

    market_cap = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[3]')[0].text
    price = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[4]')[0].text
    # today：百分比格式涨跌幅字符串
    today = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[5]/span')[0].text

    # price_30_days = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[6]')[0].text

    # country = html.xpath(f'//*[@id="cmkt"]/div[4]/table/tbody/tr[{i}]/td[7]')[0].text
    data.append([name, company_code, market_cap, price, today])

df = pd.DataFrame(data, columns=columns)
# 增加一列当前日期
from datetime import datetime

df['Date'] = datetime.now().strftime('%Y-%m-%d')
df
# %%
# print(df.head())
'''
           Name Company Code Market Cap    Price   Today        Date
0          Gold         GOLD  $16.130 T   $2,402   0.80%  2024-04-16
1     Microsoft         MSFT   $3.073 T  $413.64  -1.96%  2024-04-16
2         Apple         AAPL   $2.666 T  $172.69  -2.19%  2024-04-16
3        NVIDIA         NVDA   $2.150 T  $860.01  -2.48%  2024-04-16
4  Saudi Aramco      2222.SR   $1.955 T    $8.08  -0.98%  2024-04-16
'''
# %%
from sqlalchemy import create_engine
from urllib.parse import quote_plus as urlquote

userName = 'root'
password = '^zhP@55word$'
dbHost = 'localhost'
dbPort = 3306
dbName = 'market_data'
DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'
engine = create_engine(DB_CONNECT, max_overflow=50,  # 超过连接池大小外最多创建的连接
                       pool_size=50,  # 连接池大小
                       pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错
                       pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
                    #    encoding='utf-8', 
                       echo=False)
# sql_query2 = "select t.* from vnpyzh.dbbardata t where symbol='fu99' and t.interval='1m'"
# fu = pd.read_sql_query(sql_query2, engine)

# df_all.to_sql('symbol_mapping_his', engine, if_exists='replace', index=False)

# %%
# mysql建表语句（以Date和Company Code为联合主键）：
# create_table_sql = '''
# CREATE TABLE IF NOT EXISTS company_market_cap(
#     Name VARCHAR(255),
#     Company_Code VARCHAR(255),
#     Market_Cap VARCHAR(255),
#     Price VARCHAR(255),
#     Today VARCHAR(255),
#     Date DATE,
#     PRIMARY KEY (Date, Company_Code)
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
# '''
#
# # 如果不存在表，则创建表
# with engine.connect() as conn:
#     conn.execute(create_table_sql)
# %%
# 插入数据，如果索引重复则替换
df.to_sql('company_market_cap', engine, if_exists='append', index=False)
