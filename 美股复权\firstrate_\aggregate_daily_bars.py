import os
import sys
import warnings
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
import traceback
from urllib.parse import quote_plus

import polars as pl
import typer
from loguru import logger
from tqdm import tqdm

warnings.filterwarnings("ignore")

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from vnpy.trader.utility import ZoneInfo
from vnpy.trader.setting import SETTINGS
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData
from vnpy.trader.database import DB_TZ

from utils.mysql_database import create_mysql_database

# 时区定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时间

# 日志配置
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)


class DailyBarAggregator:
    """分钟线聚合为日线的处理器"""
    
    def __init__(self, source_database: Optional[str] = None, target_database: Optional[str] = None):
        """初始化
        
        Args:
            source_database: 源数据库名称（读取分钟线），如果为None则使用默认值
            target_database: 目标数据库名称（写入日线），如果为None则使用默认值
        """
        # 创建源数据库连接（读取分钟线）
        source_settings = SETTINGS.copy()
        if source_database:
            source_settings['database.database'] = source_database
        else:
            source_settings['database.database'] = 'vnpy_stk_us_frt_m'
        
        logger.info(f"源数据库（读取分钟线）: {source_settings['database.database']}")
        
        # 创建源数据库实例
        self.source_database, self.SourceDbBarData, self.SourceDbTickData, self.SourceDbBarOverview, self.SourceDbTickOverview = create_mysql_database(source_settings)
        
        # 创建数据库连接URI，用于polars直接读取
        db_user = source_settings['database.user']
        db_pass = quote_plus(source_settings['database.password'])
        db_host = source_settings['database.host']
        db_port = source_settings['database.port']
        db_name = source_settings['database.database']
        self.source_db_uri = f"mysql://{db_user}:{db_pass}@{db_host}:{db_port}/{db_name}"
        
        # 创建目标数据库连接（写入日线）
        target_settings = SETTINGS.copy()
        if target_database:
            target_settings['database.database'] = target_database
        else:
            target_settings['database.database'] = 'vnpy_stk_us_frt_d'
        
        logger.info(f"目标数据库（写入日线）: {target_settings['database.database']}")
        
        # 创建目标数据库实例
        self.target_database, self.TargetDbBarData, self.TargetDbTickData, self.TargetDbBarOverview, self.TargetDbTickOverview = create_mysql_database(target_settings)
        
        logger.info(f"数据库时区: {DB_TZ}")
        logger.info(f"美东时区: {ET_TZ}")
    
    def get_target_overview(self, symbol: str, exchange: str) -> Optional[datetime]:
        """获取目标数据库中指定标的的日线数据结束时间
        
        Args:
            symbol: 标的代码
            exchange: 交易所
            
        Returns:
            Optional[datetime]: 日线数据的结束时间(DB_TZ时区无时区信息),如果没有数据则返回None
        """
        # 查询日线数据overview
        overview = self.TargetDbBarOverview.get_or_none(
            self.TargetDbBarOverview.symbol == symbol,
            self.TargetDbBarOverview.exchange == exchange,
            self.TargetDbBarOverview.interval == Interval.DAILY.value
        )
        
        if overview is None:
            logger.info(f"目标数据库中没有 {symbol} 的日线数据")
            return None
            
        # overview.end已经是DB_TZ时区且无时区信息
        return overview.end
    
    def get_symbols_to_process(self) -> List[Tuple[str, str]]:
        """获取需要处理的标的列表
        
        Returns:
            List[Tuple[str, str]]: (symbol, exchange) 列表
        """
        # 查询所有有分钟线数据的标的（从源数据库）
        query = (self.SourceDbBarOverview
                .select(self.SourceDbBarOverview.symbol, self.SourceDbBarOverview.exchange)
                .where(self.SourceDbBarOverview.interval == Interval.MINUTE.value)
                .distinct())
        
        symbols = [(row.symbol, row.exchange) for row in query]
        logger.info(f"找到 {len(symbols)} 个标的需要处理")
        return symbols
    
    def load_minute_bars(self, symbol: str, exchange: str,
                        start_date: Optional[datetime] = None) -> pl.DataFrame:
        """加载分钟线数据
        
        Args:
            symbol: 标的代码
            exchange: 交易所
            start_date: 开始日期,需要是DB_TZ时区且无时区信息的datetime
            
        Returns:
            pl.DataFrame: 分钟线数据
        """
        # 构建查询条件（从源数据库读取）
        query = (self.SourceDbBarData
                .select()
                .where(
                    (self.SourceDbBarData.symbol == symbol) &
                    (self.SourceDbBarData.exchange == exchange) &
                    (self.SourceDbBarData.interval == Interval.MINUTE.value)
                ))
        
        if start_date:
            query = query.where(self.SourceDbBarData.datetime > start_date)
        query = query.order_by(self.SourceDbBarData.datetime)
        
        # 转换为字典列表
        data = []
        for bar in query:
            # 数据库时间转换为美东时间
            et_datetime = bar.datetime.replace(tzinfo=DB_TZ).astimezone(ET_TZ)
            data.append({
                'datetime': et_datetime,
                'open_price': bar.open_price,
                'high_price': bar.high_price,
                'low_price': bar.low_price,
                'close_price': bar.close_price,
                'volume': bar.volume,
                'turnover': bar.turnover,
                'open_interest': bar.open_interest
            })
        
        if not data:
            return pl.DataFrame()
        
        # 创建Polars DataFrame
        df = pl.DataFrame(data)
        
        # 确保datetime列是正确的类型
        df = df.with_columns([
            pl.col('datetime').cast(pl.Datetime(time_zone=str(ET_TZ)))
        ])
        
        logger.info(f"加载了 {symbol} 的 {len(df)} 条分钟线数据")
        return df
    
    def aggregate_to_daily(self, df: pl.DataFrame) -> pl.DataFrame:
        """将分钟线聚合为日线
        
        Args:
            df: 分钟线数据
            
        Returns:
            pl.DataFrame: 日线数据
        """
        if df.is_empty():
            return df
        
        # 按交易日分组聚合
        daily_df = (df
                   .with_columns([
                       # 提取交易日期
                       pl.col('datetime').dt.date().alias('trade_date')
                   ])
                   .group_by('trade_date')
                   .agg([
                       # 开盘价：当日第一条记录的开盘价
                       pl.col('open_price').first().alias('open_price'),
                       # 最高价：当日所有记录的最高价
                       pl.col('high_price').max().alias('high_price'),
                       # 最低价：当日所有记录的最低价
                       pl.col('low_price').min().alias('low_price'),
                       # 收盘价：当日最后一条记录的收盘价
                       pl.col('close_price').last().alias('close_price'),
                       # 成交量：当日所有记录的成交量之和
                       pl.col('volume').sum().alias('volume'),
                       # 成交额：当日所有记录的成交额之和
                       pl.col('turnover').sum().alias('turnover'),
                       # 持仓量：当日最后一条记录的持仓量
                       pl.col('open_interest').last().alias('open_interest'),
                       # 将datetime设置为当天00:00:00
                       pl.col('datetime').first().dt.truncate("1d").alias('datetime')
                   ])
                   .sort('trade_date')
                   .drop('trade_date')
                   )
        
        logger.info(f"聚合得到 {len(daily_df)} 条日线数据")
        return daily_df
    
    def save_daily_bars(self, df: pl.DataFrame, symbol: str, exchange: str) -> bool:
        """保存日线数据到数据库
        
        Args:
            df: 日线数据
            symbol: 标的代码
            exchange: 交易所
            
        Returns:
            bool: 是否保存成功
        """
        if df.is_empty():
            logger.warning(f"没有日线数据需要保存: {symbol}")
            return False
        
        # 转换为BarData对象列表
        bars = []
        for row in df.iter_rows(named=True):
            # 时间戳需要转换回数据库时区
            bar_datetime = row['datetime']
            if hasattr(bar_datetime, 'astimezone'):
                # 如果已经有时区信息，直接转换
                db_datetime = bar_datetime.astimezone(DB_TZ)
            else:
                # 如果没有时区信息，先设置为美东时区再转换
                et_datetime = bar_datetime.replace(tzinfo=ET_TZ)
                db_datetime = et_datetime.astimezone(DB_TZ)
            
            bar = BarData(
                symbol=symbol,
                exchange=Exchange(exchange),
                datetime=db_datetime.replace(tzinfo=None),  # 数据库存储时不带时区信息
                interval=Interval.DAILY,
                volume=float(row['volume']),
                turnover=float(row['turnover']),
                open_interest=float(row['open_interest']),
                open_price=float(row['open_price']),
                high_price=float(row['high_price']),
                low_price=float(row['low_price']),
                close_price=float(row['close_price']),
                gateway_name="AGGREGATED"
            )
            bars.append(bar)
        
        try:
            # 保存到目标数据库
            self.target_database.save_bar_data(bars)
            logger.info(f"成功保存 {symbol} 的 {len(bars)} 条日线数据")
            return True
        except Exception as e:
            logger.error(f"保存日线数据失败 {symbol}: {str(e)}")
            return False

    def load_all_minute_bars(self) -> pl.DataFrame:
        """使用polars直接从数据库加载所有分钟线数据用于初始化"""
        logger.info("开始从源数据库加载所有分钟线数据 (using polars.read_database)...")
        
        table_name = self.SourceDbBarData._meta.table_name
        query = f"SELECT symbol, exchange, datetime, open_price, high_price, low_price, close_price, volume, turnover, open_interest FROM {table_name} WHERE `interval` = '{Interval.MINUTE.value}'"

        # pip install connectorx pyarrow
        df = pl.read_database_uri(query=query, uri=self.source_db_uri)

        if df.is_empty():
            logger.warning("源数据库中没有找到任何分钟线数据")
            return pl.DataFrame()
            
        # 时区转换: 数据库存储的是DB_TZ时区的naive datetime.
        # 需要先为数据附加DB_TZ时区, 然后再转换为ET_TZ时区.
        df = df.with_columns([
            pl.col('datetime').dt.replace_time_zone(str(DB_TZ)).dt.convert_time_zone(str(ET_TZ))
        ])
        
        logger.info(f"成功加载了 {len(df)} 条分钟线数据")
        return df

    def aggregate_all_to_daily(self, df: pl.DataFrame) -> pl.DataFrame:
        """将所有分钟线数据一次性聚合为日线"""
        if df.is_empty():
            return df
        
        logger.info("开始将所有分钟线聚合为日线...")
        
        daily_df = (df
            .with_columns([
                pl.col('datetime').dt.date().alias('trade_date')
            ])
            .group_by(['symbol', 'exchange', 'trade_date'])
            .agg([
                pl.col('open_price').first().alias('open_price'),
                pl.col('high_price').max().alias('high_price'),
                pl.col('low_price').min().alias('low_price'),
                pl.col('close_price').last().alias('close_price'),
                pl.col('volume').sum().alias('volume'),
                pl.col('turnover').sum().alias('turnover'),
                pl.col('open_interest').last().alias('open_interest'),
                pl.col('datetime').first().dt.truncate("1d").alias('datetime')
            ])
            .sort(['symbol', 'exchange', 'trade_date'])
            .drop('trade_date')
        )
        
        logger.info(f"成功聚合得到 {len(daily_df)} 条日线数据")
        return daily_df

    def save_all_daily_bars(self, df: pl.DataFrame) -> bool:
        """一次性保存所有日线数据到数据库（不更新overview）"""
        if df.is_empty():
            logger.warning("没有聚合后的日线数据需要保存")
            return False

        logger.info("开始将所有日线数据写入目标数据库...")

        try:
            # 直接从DataFrame构建数据库插入数据，避免不必要的BarData对象创建
            data_to_insert = []
            for row in tqdm(df.iter_rows(named=True), total=len(df), desc="转换数据"):
                # 时区转换：从美东时区转换为数据库时区
                bar_datetime = row['datetime']
                db_datetime = bar_datetime.astimezone(DB_TZ).replace(tzinfo=None)

                # 直接构建数据库记录字典
                record = {
                    'symbol': row['symbol'],
                    'exchange': row['exchange'],  # 已经是字符串
                    'datetime': db_datetime,
                    'interval': Interval.DAILY.value,
                    'volume': float(row['volume']),
                    'turnover': float(row['turnover']),
                    'open_interest': float(row['open_interest']),
                    'open_price': float(row['open_price']),
                    'high_price': float(row['high_price']),
                    'low_price': float(row['low_price']),
                    'close_price': float(row['close_price'])
                }
                data_to_insert.append(record)

            # 批量插入数据库
            with self.target_database.db.atomic():
                from peewee import chunked
                for c in chunked(data_to_insert, 100):
                    self.TargetDbBarData.insert_many(c).on_conflict_replace().execute()

            logger.info(f"成功保存 {len(data_to_insert)} 条日线数据到数据库")
            return True
        except Exception as e:
            logger.error(f"批量保存日线数据失败: {str(e)}\n{traceback.format_exc()}")
            return False

    def run_initial_aggregation(self, batch_size: int = 20) -> bool:
        """执行初始化全量聚合（分批处理以避免内存不足）

        Args:
            batch_size: 每批处理的symbol,exchange对数量，默认20
        """
        logger.info("========== 开始执行初始化全量聚合（分批处理） ==========")

        # 1. 获取所有需要处理的symbol,exchange对
        all_symbols = self.get_symbols_to_process()
        if not all_symbols:
            logger.warning("没有找到需要处理的标的")
            return False

        logger.info(f"总共需要处理 {len(all_symbols)} 个标的，每批处理 {batch_size} 个")

        # 2. 分批处理
        total_batches = (len(all_symbols) + batch_size - 1) // batch_size
        success_count = 0
        fail_count = 0

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(all_symbols))
            batch_symbols = all_symbols[start_idx:end_idx]

            logger.info(f"处理第 {batch_idx + 1}/{total_batches} 批，包含 {len(batch_symbols)} 个标的")

            # 处理当前批次
            batch_success, batch_fail = self._process_batch_initial(batch_symbols)
            success_count += batch_success
            fail_count += batch_fail

            logger.info(f"第 {batch_idx + 1} 批完成，成功: {batch_success}, 失败: {batch_fail}")

        # 3. 初始化Overview表
        try:
            logger.info("开始初始化目标数据库的BarOverview...")
            self.target_database.init_bar_overview()
            logger.success("BarOverview初始化完成")
        except Exception as e:
            logger.error(f"初始化BarOverview失败: {str(e)}\n{traceback.format_exc()}")
            return False

        logger.success(f"========== 初始化全量聚合完成，总计成功: {success_count}, 失败: {fail_count} ==========")
        return fail_count == 0

    def _process_batch_initial(self, batch_symbols: List[Tuple[str, str]]) -> Tuple[int, int]:
        """处理一批symbol,exchange对的初始化聚合

        Args:
            batch_symbols: 当前批次的(symbol, exchange)列表

        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        if not batch_symbols:
            return 0, 0

        try:
            # 1. 加载当前批次的分钟线数据
            minute_df = self._load_batch_minute_bars(batch_symbols)
            if minute_df.is_empty():
                logger.warning(f"当前批次没有找到分钟线数据")
                return 0, len(batch_symbols)

            # 2. 聚合为日线
            daily_df = self.aggregate_all_to_daily(minute_df)
            if daily_df.is_empty():
                logger.warning(f"当前批次聚合后没有日线数据")
                return 0, len(batch_symbols)

            # 3. 保存日线数据
            success = self.save_all_daily_bars(daily_df)
            if success:
                return len(batch_symbols), 0
            else:
                return 0, len(batch_symbols)

        except Exception as e:
            logger.error(f"处理批次时出错: {str(e)}\n{traceback.format_exc()}")
            return 0, len(batch_symbols)

    def _load_batch_minute_bars(self, batch_symbols: List[Tuple[str, str]]) -> pl.DataFrame:
        """加载指定批次symbol,exchange对的分钟线数据

        Args:
            batch_symbols: (symbol, exchange)列表

        Returns:
            pl.DataFrame: 分钟线数据
        """
        if not batch_symbols:
            return pl.DataFrame()

        logger.info(f"开始加载批次数据，包含 {len(batch_symbols)} 个标的...")

        # 构建WHERE条件
        symbol_exchange_conditions = []
        for symbol, exchange in batch_symbols:
            symbol_exchange_conditions.append(f"(symbol = '{symbol}' AND exchange = '{exchange}')")

        where_clause = " OR ".join(symbol_exchange_conditions)

        table_name = self.SourceDbBarData._meta.table_name
        query = f"""
        SELECT symbol, exchange, datetime, open_price, high_price, low_price, close_price, volume, turnover, open_interest
        FROM {table_name}
        WHERE `interval` = '{Interval.MINUTE.value}' AND ({where_clause})
        """

        try:
            # 使用polars直接从数据库读取
            df = pl.read_database_uri(query=query, uri=self.source_db_uri)

            if df.is_empty():
                logger.warning(f"当前批次没有找到任何分钟线数据")
                return pl.DataFrame()

            # 时区转换
            df = df.with_columns([
                pl.col('datetime').dt.replace_time_zone(str(DB_TZ)).dt.convert_time_zone(str(ET_TZ))
            ])

            logger.info(f"成功加载当前批次 {len(df)} 条分钟线数据")
            return df

        except Exception as e:
            logger.error(f"加载批次分钟线数据失败: {str(e)}\n{traceback.format_exc()}")
            return pl.DataFrame()

    def process_symbol(self, symbol: str, exchange: str,
                      start_date: Optional[datetime] = None) -> bool:
        """处理单个标的的日线聚合
        
        Args:
            symbol: 标的代码
            exchange: 交易所
            start_date: 开始日期(美东时区)
            
        Returns:
            bool: 是否处理成功
        """
        try:
            logger.info(f"开始处理标的: {symbol} ({exchange})")
            
            # 0. 确定开始日期
            if start_date:
                # 将美东时间转换为DB_TZ时区并删除时区信息
                db_start = start_date.astimezone(DB_TZ).replace(tzinfo=None)
            else:
                db_start = None
            
            # 获取目标数据库中的最新日期
            last_end = self.get_target_overview(symbol, exchange)
            if last_end:
                # 如果存在历史数据,使用最新日期作为开始日期的下限
                if db_start is None or last_end > db_start:
                    db_start = last_end
                    logger.info(f"使用上次处理结束时间作为开始时间: {db_start}")
                else:
                    logger.info(f"使用指定的开始时间: {db_start}")
            
            # 1. 加载分钟线数据
            minute_df = self.load_minute_bars(symbol, exchange, db_start)
            if minute_df.is_empty():
                logger.warning(f"没有找到分钟线数据: {symbol}")
                return False
            
            # 2. 聚合为日线
            daily_df = self.aggregate_to_daily(minute_df)
            if daily_df.is_empty():
                logger.warning(f"聚合后没有日线数据: {symbol}")
                return False
            
            # 3. 保存日线数据
            success = self.save_daily_bars(daily_df, symbol, exchange)
            
            if success:
                logger.success(f"完成处理标的: {symbol}")
            else:
                logger.error(f"处理失败: {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"处理标的时出错 {symbol}: {str(e)}\n{traceback.format_exc()}")
            return False
    
    def process_all(self, symbol_list: Optional[List[str]] = None,
                   start_date: Optional[datetime] = None) -> Tuple[int, int]:
        """处理所有标的的日线聚合
        
        Args:
            symbol_list: 要处理的标的列表，如果为None则处理所有标的
            start_date: 开始日期,需要是美东时区的datetime
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        # 获取要处理的标的列表
        all_symbols = self.get_symbols_to_process()
        
        if symbol_list:
            # 过滤指定的标的
            all_symbols = [(s, e) for s, e in all_symbols if s in symbol_list]
            logger.info(f"将处理指定的 {len(all_symbols)} 个标的")
        
        if not all_symbols:
            logger.warning("没有找到需要处理的标的")
            return 0, 0
        
        success_count = 0
        fail_count = 0
        
        # 逐个处理标的
        for symbol, exchange in tqdm(all_symbols, desc="处理标的"):
            try:
                if self.process_symbol(symbol, exchange, start_date):
                    success_count += 1
                else:
                    fail_count += 1
            except Exception as e:
                logger.error(f"处理标的时出错 {symbol}: {str(e)}")
                fail_count += 1
        
        logger.success(f"处理完成. 成功: {success_count}, 失败: {fail_count}")
        return success_count, fail_count


app = typer.Typer()

@app.command()
def aggregate(
    source_database: Optional[str] = typer.Option(
        None,
        "--source-db",
        "-sdb",
        help="源数据库名称（读取分钟线），如果不指定则使用默认值'vnpy_stk_us_frt_m'"
    ),
    target_database: Optional[str] = typer.Option(
        None,
        "--target-db",
        "-tdb",
        help="目标数据库名称（写入日线），如果不指定则使用默认值'vnpy_stk_us_frt_d'"
    ),
    symbol_list: Optional[str] = typer.Option(
        None,
        "--symbols",
        "-s",
        help="要处理的标的列表，用逗号分隔，例如：95514904,548309229"
    ),
    start_date: Optional[datetime] = typer.Option(
        None,
        "--start-date",
        "-sd",
        help="开始日期(美东时区)，格式：YYYY-MM-DD。如果不指定则从目标数据库中最新日期的下一天开始增量更新",
        formats=["%Y-%m-%d"]
    ),
    batch_size: int = typer.Option(
        20,
        "--batch-size",
        "-bs",
        help="初始化聚合时每批处理的symbol,exchange对数量，默认20。仅在初始化全量聚合时生效"
    )
):
    """分钟线聚合为日线工具

    示例用法：
    # 聚合所有标的的日线（默认从vnpy_stk_us_frt_m读取，写入vnpy_stk_us_frt_d）
    python aggregate_daily_bars.py

    # 聚合指定标的的日线
    python aggregate_daily_bars.py -s "95514904,548309229"

    # 从指定日期开始增量聚合日线
    python aggregate_daily_bars.py -sd 2024-01-01

    # 使用指定的源和目标数据库
    python aggregate_daily_bars.py -sdb vnpy_stk_us_frt_m_2206 -tdb vnpy_stk_us_frt_m2d

    # 初始化聚合时指定批次大小（避免内存不足）
    python aggregate_daily_bars.py -bs 10
    """
    try:
        # 创建聚合器
        aggregator = DailyBarAggregator(source_database, target_database)

        # 检查目标数据库是否为空，以决定执行初始化聚合还是增量更新
        overviews = aggregator.target_database.get_bar_overview()
        if not overviews and not symbol_list and not start_date:
            logger.info(f"目标数据库为空且未指定特定标的或开始日期，执行初始化全量聚合（批次大小: {batch_size}）。")
            aggregator.run_initial_aggregation(batch_size)
        else:
            if not overviews:
                logger.warning("目标数据库为空，但指定了标的或开始日期，将执行增量模式。")
            else:
                logger.info("目标数据库已有数据，执行增量更新。")

            # 处理标的列表
            symbols = None
            if symbol_list:
                symbols = [s.strip() for s in symbol_list.split(',')]
                logger.info(f"将处理指定标的: {symbols}")
            
            # 设置时区信息
            if start_date:
                start_date = start_date.replace(tzinfo=ET_TZ)
                logger.info(f"使用指定的开始时间(美东): {start_date}")
            
            # 执行聚合
            success, fail = aggregator.process_all(symbols, start_date)
            
            logger.success(f"聚合完成. 成功: {success}, 失败: {fail}")
        
    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}\n{traceback.format_exc()}")


if __name__ == "__main__":
    app()