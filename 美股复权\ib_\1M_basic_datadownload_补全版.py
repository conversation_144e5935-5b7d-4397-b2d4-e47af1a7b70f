from dataclasses import dataclass
from datetime import datetime, timedelta, date
from typing import List, Optional
from functools import lru_cache
import json
import asyncio
import traceback
import pandas as pd
import math
import csv
import aiofiles
from zoneinfo import ZoneInfo
SH_TZ = ZoneInfo('Asia/Shanghai')
NY_TZ = ZoneInfo('America/New_York')  # 美东时区
import pandas_market_calendars as mcal

from ib_async import IB, Contract, util, Stock
from ib_async.wrapper import RequestError
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.object import BarData
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.utility import load_json
from vnpy_ib.ib_gateway import INTERVAL_VT2IB
from sqlalchemy import create_engine
from utils.database_manager import db_manager, IbProduct, IbContractDetail

# 定义最大并发下载数
MAX_CONCURRENT_DOWNLOADS = 1

import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

# CSV文件的锁，确保异步安全写入
csv_write_lock = asyncio.Lock()

async def record_lack_data(conid: str, last_time: datetime, csv_filename: str = "lack_data.csv"):
    """异步安全地记录缺失数据到CSV文件
    
    Args:
        conid: 合约ID
        last_time: 最后一条数据的时间
        csv_filename: CSV文件名，默认为 lack_data.csv
    """
    async with csv_write_lock:
        file_exists = os.path.exists(csv_filename)
        
        async with aiofiles.open(csv_filename, mode='a', newline='', encoding='utf-8') as csvfile:
            # 如果文件不存在，先写入表头
            if not file_exists:
                await csvfile.write("conid,last_time\n")
            
            # 格式化时间为字符串
            last_time_str = last_time.strftime('%Y-%m-%d %H:%M:%S')
            await csvfile.write(f"{conid},{last_time_str}\n")
            
        logger.info(f"记录缺失数据: conid={conid}, last_time={last_time_str}")

@dataclass
class SymbolDownloadResult:
    """单个合约下载结果"""
    vt_symbol: str
    success: bool
    message: str

@dataclass
class OverallDownloadResult:
    """整体下载结果"""
    success: bool
    message: str
    failed_vt_symbols: List[str]

@lru_cache(maxsize=128)
def get_nyse_trading_days(start_date: datetime, end_date: datetime) -> pd.DataFrame:
    """获取NYSE交易日历，使用lru_cache缓存结果
    
    Args:
        start_date (datetime): 开始时间，支持任意时区
        end_date (datetime): 结束时间，支持任意时区
        
    Returns:
        pd.DataFrame: 交易日历数据
    """
    # 转换为美东时间并提取日期
    ny_start_date = start_date.astimezone(NY_TZ).date()
    ny_end_date = end_date.astimezone(NY_TZ).date()
    
    nyse = mcal.get_calendar('NYSE')
    return nyse.schedule(start_date=ny_start_date, end_date=ny_end_date)

class IBDataDownloader:
    """IB数据下载器"""
    def __init__(self):
        """
        初始化下载器
        """
        self.database = get_database()
        
        # 加载配置
        config = load_json("connect_ib.json")
        self.host = config['TWS地址']
        self.port = config['TWS端口']
        self.client_id = config['客户号']

        self.ib: IB = IB() # 初始化IB实例
        self.ib.RaiseRequestErrors = True # 启用请求错误抛出
        
        # 初始化交易日历相关属性
        self.nyse = mcal.get_calendar('NYSE')
        self.trading_minutes_cache = {}  # 缓存每日的交易分钟

    def init_trading_minutes(self, target_date: datetime):
        """初始化指定日期的交易分钟"""
        date_str = target_date.strftime("%Y-%m-%d")
        
        # 检查缓存
        if date_str in self.trading_minutes_cache:
            return self.trading_minutes_cache[date_str]
            
        schedule = self.nyse.schedule(start_date=date_str, end_date=date_str)
        if not schedule.empty:
            minutes = mcal.date_range(schedule, frequency='1Min', closed='left', force_close=False)
            trading_minutes = pd.DatetimeIndex([ts.astimezone(NY_TZ) for ts in minutes])
        else:
            return None
            
        # 缓存结果
        self.trading_minutes_cache[date_str] = trading_minutes
        return trading_minutes

    async def connect(self) -> None:
        """连接到IB TWS/Gateway"""
        if self.ib.isConnected():
            logger.info("已连接到IB (重复连接)")
            return
        
        await self.ib.connectAsync(self.host, self.port, clientId=self.client_id)
        logger.info("已连接到IB")

    async def download_bars(self, vt_symbol: str, start_date: datetime, end_date: datetime, interval: Interval) -> SymbolDownloadResult:
        """下载单个合约数据"""
        try:
            whatToShow = "TRADES"
            # 解析vt_symbol，支持conId.exchange格式
            if '.' in vt_symbol:
                conid_str, exchange_str = vt_symbol.split(".")
                
                contract = Contract(
                    conId=int(conid_str),
                    exchange=exchange_str
                )
                # 如果exchange_str是IDEALPRO则直接用MIDPOINT免得请求ib
                if exchange_str == "IDEALPRO":
                    whatToShow = "MIDPOINT"
                else:
                    await self.ib.qualifyContractsAsync(contract)
                    if contract.secType in ('CASH', 'CMDTY'):
                        whatToShow = "MIDPOINT"
            else:
                message = "vt_symbol格式不正确，应为conId.exchange"
                logger.error(f"{vt_symbol}: {message}")
                return SymbolDownloadResult(vt_symbol, False, message)

            # 根据interval调整end_date，确保包含最后一根K线
            if interval == Interval.MINUTE:
                adjusted_end_date = end_date + timedelta(minutes=1)
            elif interval == Interval.HOUR:
                adjusted_end_date = end_date + timedelta(hours=1)
            else:  # Interval.DAILY
                adjusted_end_date = end_date + timedelta(days=1)

            # 计算durationStr
            if exchange_str == "SMART" and interval != Interval.DAILY:
                # 使用缓存的函数获取交易日历，直接传入datetime对象
                trading_days = get_nyse_trading_days(start_date, adjusted_end_date)
                # 计算交易日数量（向上取整）
                total_days = len(trading_days) + 1  # 加1是为了确保获取最后一天数据
                if total_days < 365:
                    duration_str = f"{max(1, math.ceil(total_days))} D"  # 用math.ceil向上取整
                else:
                    duration_str = f"{math.ceil(total_days/365)} Y"
            else:
                # 非美股市场使用日历日
                delta = adjusted_end_date - start_date
                total_days = delta.total_seconds() / 86400
                if total_days < 365:
                    duration_str = f"{max(1, math.ceil(total_days))} D"  # 用math.ceil向上取整
                else:
                    duration_str = f"{total_days/365:.0f} Y"
                
            bar_size_setting = INTERVAL_VT2IB[interval]

            logger.info(f"开始下载 {vt_symbol} 的历史数据，从 {start_date} 到 {end_date}，周期 {interval.value}，持续时间 {duration_str}")

            try:
                bars = await self.ib.reqHistoricalDataAsync(
                    contract,
                    endDateTime=adjusted_end_date,
                    durationStr=duration_str,
                    barSizeSetting=bar_size_setting,
                    whatToShow=whatToShow,
                    useRTH=True,
                    timeout=0
                )
                
                if not bars:
                    message = "没有数据"
                    logger.info(f"{vt_symbol}: {message}")
                    return SymbolDownloadResult(vt_symbol, False, message)

                # 转换数据
                df = util.df(bars)
                
                # 记录过滤前的数据范围
                if not df.empty:
                    logger.info(f"{vt_symbol}: 过滤前数据范围 {df['date'].min()} - {df['date'].max()}, 总数据量: {len(df)}")
                
                # 根据时间过滤DataFrame
                if interval == Interval.DAILY:
                    df['date'] = pd.to_datetime(df['date'])
                    df['date'] = df['date'].dt.tz_localize(NY_TZ)

                df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                
                # 记录过滤后的数据范围
                if not df.empty:
                    logger.info(f"{vt_symbol}: 过滤后数据范围 {df['date'].min()} - {df['date'].max()}, 总数据量: {len(df)}")
                
                # 补全bar数据（仅对分钟线进行完整补全，按天分别处理）
                if not df.empty and interval == Interval.MINUTE and exchange_str == "SMART":
                    if len(df) < 117060:
                        # 设置date为索引
                        df.set_index('date', inplace=True)
                        
                        # 按天分组处理补全
                        daily_groups = df.groupby(df.index.date)
                        completed_dfs = []
                        
                        for trade_date, day_df in daily_groups:
                            if day_df.empty:
                                continue
                                
                            # 直接从缓存获取当天的交易分钟
                            date_str = trade_date.strftime("%Y-%m-%d")
                            trading_minutes = self.trading_minutes_cache.get(date_str, pd.DatetimeIndex([]))
                            
                            # 如果当天不是交易日，跳过
                            if len(trading_minutes) == 0:
                                logger.info(f"{vt_symbol}: {trade_date} 非交易日，跳过补全")
                                continue
                            
                            # 检查当天数据是否已经完整，如果完整则直接跳过补全
                            if len(day_df) >= len(trading_minutes):
                                # logger.info(f"{vt_symbol}: {trade_date} 数据已完整 ({len(day_df)}/{len(trading_minutes)})，跳过补全")
                                completed_dfs.append(day_df)
                                continue
                            
                            # 使用当天的交易分钟进行补全
                            full_day_df = day_df.reindex(trading_minutes)
                            
                            # 按照严格顺序进行价格填充（仅在当天内）：
                            # 1. 首先对close进行前向填充 (ffill)
                            full_day_df['close'] = full_day_df['close'].ffill()
                            
                            # 2. 用close填充OHL（必须先于open bfill，保证中间缺的是ffill的）
                            for col in ['open', 'high', 'low']:
                                full_day_df[col] = full_day_df[col].fillna(full_day_df['close'])
                            
                            # 3. 对open进行后向填充 (bfill)
                            full_day_df['open'] = full_day_df['open'].bfill()
                            
                            # 4. 用open填充HLC
                            for col in ['high', 'low', 'close']:
                                full_day_df[col] = full_day_df[col].fillna(full_day_df['open'])
                            
                            # 5. 删除仍然没有数据的行（全天无数据的情况）
                            full_day_df = full_day_df.dropna(subset=['close'])
                            
                            # 6. 交易量用0填充
                            if not full_day_df.empty:
                                for col in ['volume']:
                                    if col in full_day_df.columns:
                                        full_day_df[col] = full_day_df[col].fillna(0)
                                
                                if len(full_day_df) > len(day_df):
                                    logger.info(f"{vt_symbol}: {trade_date} 补全分钟线数据，从{len(day_df)}条增加到{len(full_day_df)}条 (交易分钟数: {len(trading_minutes)})")
                                
                                completed_dfs.append(full_day_df)
                            else:
                                logger.warning(f"{vt_symbol}: {trade_date} 全天无数据，跳过补全")
                        
                        # 合并所有补全后的天数据
                        if completed_dfs:
                            df = pd.concat(completed_dfs)
                            df = df.sort_index()  # 按时间排序
                            logger.info(f"{vt_symbol}: 完成所有交易日的分钟线补全，最终数据量: {len(df)}")
                        else:
                            logger.warning(f"{vt_symbol}: 所有交易日都无数据，使用原始数据")
                            df = df.reset_index()
                        
                        # 确保最终的df有正确的结构（date作为列）
                        if df.index.name == 'date' or 'date' not in df.columns:
                            df = df.reset_index()
                            if 'index' in df.columns:
                                df.rename(columns={'index': 'date'}, inplace=True)
                else:
                    # 非分钟线或非SMART交易所，不进行补全
                    pass

                bar_data = []
                for _, row in df.iterrows():
                    # IB返回的时区是TWS登录界面选择的时区，需转换成DB_TZ
                    bar = BarData(
                        symbol=conid_str, # Use conId as symbol
                        exchange=Exchange(exchange_str),
                        datetime=row['date'],
                        interval=interval, # Use Interval enum directly
                        volume=float(row['volume']),
                        open_price=float(row['open']),
                        high_price=float(row['high']),
                        low_price=float(row['low']),
                        close_price=float(row['close']),
                        gateway_name='IB'
                    )

                    bar_data.append(bar)

                if not bar_data:
                    message = "数据为空"
                    logger.info(f"{vt_symbol}: {message}")
                    return SymbolDownloadResult(vt_symbol, False, message)

                # 保存数据
                self.database.save_bar_data(bar_data)
                
                message = f"成功保存{len(bar_data)}条数据，{bar_data[0].datetime.replace(tzinfo=DB_TZ)}-{bar_data[-1].datetime.replace(tzinfo=DB_TZ)}"
                logger.info(f"{vt_symbol}: {message}")
                
                # 检查数据是否完整，如果不完整则记录到CSV
                if bar_data[-1].datetime.replace(tzinfo=DB_TZ) + timedelta(hours=1) < end_date:
                    await record_lack_data(conid_str, bar_data[-1].datetime.replace(tzinfo=DB_TZ))
                    
                return SymbolDownloadResult(vt_symbol, True, message)
                
            except RequestError as e:
                error_message = f"IB请求错误: [{e.code}] {e.message}"
                logger.error(f"{vt_symbol}: {error_message}")
                return SymbolDownloadResult(vt_symbol, False, error_message)
                
        except Exception as e:
            error_message = f"处理错误: {str(e)} - {traceback.format_exc()}" # 增加traceback信息
            logger.error(f"{vt_symbol}: {error_message}")
            return SymbolDownloadResult(vt_symbol, False, error_message)

    async def download(self, vt_symbols: List[str], start_date: dict, end_date: datetime, interval: Interval) -> OverallDownloadResult:
        """批量下载数据"""
        
        overall_success = True
        all_failed_vt_symbols = []

        try:
            await self.connect()
            
            # 如果是分钟线下载，预初始化所有可能需要的交易分钟（仅针对SMART交易所）
            if interval == Interval.MINUTE:
                logger.info("预初始化交易分钟数据...")
                # 获取所有需要的日期范围
                all_start_dates = list(start_date.values())
                min_start = min(all_start_dates)
                current_date = min_start.date()
                end_date_only = end_date.date()
                min_count = 0
                # 预加载所有交易日的分钟数据
                while current_date <= end_date_only:
                    trade_datetime = datetime.combine(current_date, datetime.min.time()).replace(tzinfo=NY_TZ)
                    if self.init_trading_minutes(trade_datetime) is not None:
                        min_count += len(self.trading_minutes_cache[trade_datetime.strftime("%Y-%m-%d")])
                    current_date += timedelta(days=1)
                logger.info(f"交易分钟初始化完成，缓存了 {len(self.trading_minutes_cache)} 个交易日, {min_count} 个交易分钟")
            
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
            download_tasks = []

            async def download_single_symbol(vt_symbol: str) -> SymbolDownloadResult:
                async with semaphore:
                    return await self.download_bars(vt_symbol, start_date[vt_symbol], end_date, interval)

            for vt_symbol in vt_symbols:
                task = asyncio.create_task(download_single_symbol(vt_symbol))
                download_tasks.append(task)

            # 执行所有任务并收集结果
            symbol_results: List[SymbolDownloadResult] = await asyncio.gather(*download_tasks)

            # 汇总结果
            for res in symbol_results:
                if not res.success:
                    overall_success = False
                    all_failed_vt_symbols.append(res.vt_symbol)

            if overall_success:
                return OverallDownloadResult(True, "所有合约下载成功", [])
            else:
                return OverallDownloadResult(False, "部分合约下载失败", all_failed_vt_symbols)
            
        except Exception as e:
            # 连接错误发生在connect()中，这里捕获的是connect()或download_bars()中的其他非RequestError
            error_message = f"批量下载过程中发生错误: {str(e)} - {traceback.format_exc()}" # 增加traceback信息
            logger.error(error_message)
            # 如果是连接错误导致整体失败，则所有请求的vt_symbols都算作失败
            return OverallDownloadResult(False, error_message, vt_symbols)
        finally:
            if self.ib and self.ib.isConnected(): # 确保连接存在且处于连接状态才断开
                self.ib.disconnect()
                logger.info("已断开IB连接")

async def sync_download(vt_symbols: List[str], start_date: dict, end_date: datetime, interval: Interval) -> OverallDownloadResult:
    """同步下载接口"""
    downloader = IBDataDownloader()
    return await downloader.download(vt_symbols, start_date, end_date, interval)


def fetch_symbol_all(engine):
    query = """
    SELECT conid, is_latest,created_time FROM ib_product 
    """

    df = pd.read_sql(query, engine)
    df = df[df['is_latest'] == 1]
    df['created_time'] = pd.to_datetime(df['created_time'])
    latest_time = df['created_time'].max()
    df = df[df['created_time'] >= latest_time.replace(hour=0, minute=0,second=0)]
    symbol_list = df['conid'].tolist()
    return symbol_list

def fetch_symbol_all_full(engine):
    query = """
    SELECT conid,symbol,is_latest,created_time FROM ib_product 
    """

    df = pd.read_sql(query, engine)
    # df = df[df['is_latest'] == 1]
    # df['created_time'] = pd.to_datetime(df['created_time'])
    # latest_time = df['created_time'].max()
    # df = df[df['created_time'] >= latest_time.replace(hour=0, minute=0,second=0)]
    # symbol_list = []
    # for index, row in df.iterrows():
    #     if 'OLD' not in row['symbol']:
    #         symbol_list.append(row['symbol'])

    # query2 = """
    # SELECT conid,valid_exchanges,primary_exchange FROM ib_contract_detail 
    # """
    # df2 = pd.read_sql(query2, engine)
    # contract_detail_list = df2[(df2['valid_exchanges']!='VALUE')&(df2['primary_exchange']!='PINK')]['conid'].tolist()
    # for conid in symbol_list:
    #     if conid not in contract_detail_list:
    #         symbol_list.remove(conid)
    return df
    
if __name__ == "__main__":
    # 示例用法，不再使用命令行参数解析
    # 请自行修改以下参数进行测试
    from datetime import datetime, timedelta # Ensure timedelta is imported
    import sys # Keep sys for exit

    host2 = "*************"
    port2 = 3308
    user2 = "zh"
    password2 = "zhP%4055word"
    database2_standard = "common_info"
    engine2 = create_engine(f"mysql+pymysql://{user2}:{password2}@{host2}:{port2}/{database2_standard}")

    ib_product_df = fetch_symbol_all_full(engine2)
    csv_df = pd.read_csv('scanner_unique_stk_us_all.csv')


    common_conids = set(ib_product_df['conid']).intersection(set(csv_df['conId']))
    symbol_list = ib_product_df[ib_product_df['conid'].isin(common_conids)]['symbol'].tolist()
    csv_symbols = csv_df['symbol'].to_list()+symbol_list
    common_symbols = set(csv_symbols).intersection(set(ib_product_df['symbol']))
    result_conids = set(ib_product_df[
        (ib_product_df['symbol'].isin(common_symbols)) & 
        (ib_product_df['is_latest'] == 1)
    ]['conid'].tolist())

    test_vt_symbols = [f"{i}.SMART" for i in result_conids]
    # test_vt_symbols = test_vt_symbols[1:]
    # test_vt_symbols = ['265598.SMART']
    logger.info(f'总共有{len(test_vt_symbols)}个合约')
    # 注意：这里的时间必须是美东时间，否则IB可能返回错误或空数据
    test_start_date = datetime(2024, 6, 1, 9, 30, 0, tzinfo=NY_TZ) # 示例开始时间，带时区
    test_end_date = datetime(2025, 8, 15, 16, 0, 0, tzinfo=NY_TZ) # 示例结束时间，带时区
    # test_interval = Interval.DAILY
    test_interval = Interval.MINUTE
    
    start_date_record = {i:test_start_date for i in test_vt_symbols}
    # start_date_record = {}
    database = get_database()
    overview = database.get_bar_overview()
    if os.path.exists('lack_data.csv'):
        lack_data_csv = pd.read_csv('lack_data.csv')
        lack_data = lack_data_csv['conid'].tolist()
        lack_data = [str(i) for i in lack_data]
    else:
        lack_data = []

    for s in overview:
        if s.interval.value !=test_interval.value:
            continue
        if f'{s.symbol}' in lack_data:
            test_vt_symbols.remove(f'{s.symbol}.SMART')
            continue
        if f'{s.symbol}.SMART' not in test_vt_symbols:
            continue
        else:
            test_vt_symbols.remove(f'{s.symbol}.SMART')
        # test_vt_symbols.append(f'{s.symbol}.SMART')
        # start_date_record[f'{s.symbol}.SMART'] = test_start_date

        # if test_interval == Interval.MINUTE:
        #     adjusted_end_date = s.end.replace(tzinfo=DB_TZ).astimezone(NY_TZ) + timedelta(minutes=1)
        # elif test_interval == Interval.HOUR:
        #     adjusted_end_date = s.end.replace(tzinfo=DB_TZ).astimezone(NY_TZ) + timedelta(hours=1)
        # elif test_interval == Interval.DAILY:
        #     adjusted_end_date = s.end.replace(tzinfo=DB_TZ).astimezone(NY_TZ) + timedelta(days=1)

        # if adjusted_end_date < test_end_date:
        #     if test_interval == Interval.DAILY:
        #         start_date_record[f'{s.symbol}.SMART'] = adjusted_end_date.replace(hour=0, minute=0,second=0)
        #     else:
        #         start_date_record[f'{s.symbol}.SMART'] = adjusted_end_date.replace(hour=9, minute=30,second=0)
        # else:
        #     test_vt_symbols.remove(f'{s.symbol}.SMART')

    if test_vt_symbols:
        logger.info(f'共有{len(test_vt_symbols)}个合约需要下载')
        result = util.run(sync_download(test_vt_symbols, start_date_record, test_end_date, test_interval))
    else:
        logger.info("没有需要下载的合约")
        exit()

    # 打印下载结果
    if result.success:
        logger.info(f"下载成功: {result.message}")
    else:
        logger.error(f"下载失败: {result.message}")
        if result.failed_vt_symbols:
            logger.error(f"失败的合约: {', '.join(result.failed_vt_symbols)}")

