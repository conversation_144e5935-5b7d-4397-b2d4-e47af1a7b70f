from datetime import datetime, timed<PERSON>ta
from typing import List, Tuple

from vnpy.trader.database import get_database


def get_symbol_list(symbol: str, global_start: datetime = datetime(2024, 3, 1)) -> List[Tuple[str, datetime, datetime]]:
    """
    根据输入的symbol获取相关的symbol列表及其对应的时间区间
    参数:
        symbol (str): 输入的symbol，例如 "000009.SZSE"
        global_start (datetime, optional): 起始时间，默认为2024.11.1
    返回:
        List[Tuple[str, datetime, datetime]]: 包含(symbol, start_date, end_date)的列表
    """
    # 解析输入的symbol
    base_symbol, exchange = symbol.split('.')

    # 获取数据库连接
    database = get_database()
    db = database.db

    # 使用LIKE和索引优化的查询，获取带后缀的symbols
    query = (f"SELECT symbol, start, end FROM dbbaroverview "
            f"WHERE symbol LIKE '{base_symbol}_%%' "  # 注意这里使用%%转义%
            f"AND exchange = '{exchange}' "
            f"AND end >= '{global_start}' "
            f"ORDER BY SUBSTRING_INDEX(symbol, '_', -1)")

    cursor = db.execute_sql(query)
    symbol_data = [(row[0], row[1], row[2]) for row in cursor.fetchall()]

    # 查询原始symbol（不带后缀的）并追加到symbol_data
    query_original = (f"SELECT symbol, start, end FROM dbbaroverview "
                    f"WHERE symbol = '{base_symbol}' "
                    f"AND exchange = '{exchange}' "
                    f"AND end >= '{global_start}'")

    cursor = db.execute_sql(query_original)
    original_data = cursor.fetchone()
    if original_data:
        symbol_data.append(original_data)

    if not symbol_data:
        return []

    result = []
    prev_end = None
    global_end = datetime.now()
    prev_symbol = None  # 初始化前一个symbol变量

    # 统一处理所有symbols
    for i, (sym, data_start, data_end) in enumerate(symbol_data):
        # 确定start_date
        if i == 0:
            # 第一个symbol：取全局开始时间和数据实际开始时间的较大值
            start_date = max(global_start, data_start)
        else:
            # 非第一个symbol：取前一个symbol结束时间减3个月、数据实际开始时间、全局开始时间三者的最大值
            three_months_before = prev_end - timedelta(days=120) if prev_end else None
            start_candidates = [x for x in [three_months_before, data_start, global_start] if x is not None]
            start_date = max(start_candidates)

        # 确定end_date：所有symbol都取实际结束时间和全局结束时间的较小值
        end_date = min(global_end, data_end)

        result.append((f"{sym}.{exchange}", start_date, end_date, prev_symbol))  # 新增prev_symbol字段
        prev_end = end_date
        prev_symbol = f"{sym}.{exchange}"  # 保存当前symbol，供下一条记录使用

    return result


def test_get_symbol_list():
    """
    测试函数
    """
    test_symbols = ["600633.SSE", ]

    for test_symbol in test_symbols:
        print(f"\n测试symbol: {test_symbol}")
        result = get_symbol_list(test_symbol)
        print(f"result: {result}")
        print("--------------------------------")
        # for symbol, start, end in result:
        for symbol, start, end, prev_symbol in result:
            print(f"Symbol: {symbol}")
            print(f"Start Date: {start}")
            print(f"End Date: {end}")
            print(f"Previous Symbol: {prev_symbol}")
            print("---")


if __name__ == "__main__":
    test_get_symbol_list()
