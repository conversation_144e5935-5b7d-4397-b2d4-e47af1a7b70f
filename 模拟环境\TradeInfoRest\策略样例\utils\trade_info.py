from datetime import datetime
import requests
from vnpy.trader.constant import Interval
# 如果版本大于3.0.0
import vnpy
version = vnpy.__version__
if version >= '3.0.0':
    from vnpy_spreadtrading import SpreadData, LegData
    from vnpy_spreadtrading.backtesting import BacktestingEngine as SpreadBacktestingEngine
    from vnpy_portfoliostrategy import StrategyTemplate, StrategyEngine
    from vnpy_portfoliostrategy.utility import PortfolioBarGenerator
    from vnpy_portfoliostrategy.backtesting import BacktestingEngine as PortfolioBacktestingEngine
    from vnpy_ctastrategy import (CtaTemplate, TickData, BarData, TradeData, OrderData, BarGenerator, ArrayManager, StopOrder, TargetPosTemplate)
    from vnpy_ctastrategy.backtesting import BacktestingEngine
else:
    from vnpy.app.spread_trading import SpreadData, LegData
    from vnpy.app.spread_trading.backtesting import BacktestingEngine as SpreadBacktestingEngine
    from vnpy.app.portfolio_strategy import StrategyTemplate, StrategyEngine
    from vnpy.app.portfolio_strategy.utility import PortfolioBarGenerator
    from vnpy.app.portfolio_strategy.backtesting import BacktestingEngine as PortfolioBacktestingEngine
    from vnpy.app.cta_strategy import (CtaTemplate, TickData, BarData, TradeData, OrderData, BarGenerator, ArrayManager, StopOrder, TargetPosTemplate)
    from vnpy.app.cta_strategy.backtesting import BacktestingEngine

from .symbol_info import extract_symbol_pre, all_symbol_pres, all_priceticks, all_sizes, dbsymbols


class TradeInfo:
    symbol: str
    author: str
    direction: str
    offset: str
    price: float = 0
    volume: float = 0
    create_date: str
    datetime: str
    pnl: float = 0
    remarks: str = None
    strategy_name: str = None

ip = '*************'
# ip = 'localhost'
def send_trades_info(trades_dict, strategy_class, flask_backend_url=f'http://{ip}:5000/api/v1/update/'):
    # {}为空，{'FG888.CZCE': {}, 'SR888.CZCE': {}, 'p888.DCE': {}, 'y888.DCE': {}}也是为空
    if trades_dict and sum(len(v) for v in trades_dict.values()) > 0:
        trades_infos = {}
        for elem, trades in trades_dict.items():
            trades_info = {}
            try:
                create_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                for i, v in enumerate(trades.values()):
                    trade_info = TradeInfo()
                    trade_info.symbol = v.symbol
                    trade_info.author = strategy_class.author
                    trade_info.direction = v.direction.value  # name属性返回枚举成员的名称，value属性返回枚举成员的值
                    trade_info.offset = v.offset.value
                    trade_info.price = v.price
                    trade_info.volume = v.volume
                    trade_info.create_date = create_date
                    trade_info.datetime = v.datetime.strftime('%Y-%m-%d %H:%M:%S')
                    trade_info.pnl = 0
                    trade_info.remarks = None
                    trade_info.strategy_name = strategy_class.strategy_name
                    trades_info[i] = trade_info.__dict__
            except Exception as e:
                print(f'Error occurred while creating trades info for {elem}: {str(e)}')
            trades_infos[elem] = trades_info
        response = requests.post(flask_backend_url, json=trades_infos)
        if response.status_code == 200:
            print(f'Successfully updated {strategy_class.strategy_name} trades info.')
        else:
            print(f'Failed to update {strategy_class.strategy_name} trades info.')
    else:
        # 向f'http://{ip}:5000/api/v1/delete/'发送strategy_name
        strategy_name = strategy_class.strategy_name
        flask_backend_delete_url = flask_backend_url.replace('update', 'delete')
        response = requests.post(flask_backend_delete_url, json={'strategy_name': strategy_name})
        if response.status_code == 200:
            print(f'Successfully deleted {strategy_class.strategy_name} trades info.')
        else:
            print(f'Failed to delete {strategy_class.strategy_name} trades info.')


def get_888trades_dict(strategy_class, symbols=dbsymbols, standard=1, start=datetime(2022, 7, 1),
                    end=datetime(2099, 12, 20),
                    data_interval=Interval.MINUTE):
    # 将symbols转换为vt_symbols，如rb转换为rb888.SHFE，其中SHFE从all_symbol_pres中获取
    vt_symbols = [f"{s}888.{exchange}" for s in symbols for exchange in all_symbol_pres if
                  s in all_symbol_pres[exchange]]
    sizes = {f"{s}888.{exchange}": all_sizes[s] for s in symbols for exchange in all_symbol_pres if
             s in all_symbol_pres[exchange]}
    priceticks = {f"{s}888.{exchange}": all_priceticks[s] for s in symbols for exchange in all_symbol_pres if
                  s in all_symbol_pres[exchange]}
    rates = {s: 2.5e-5 for s in vt_symbols}
    slippages = {s: 2 * priceticks[s] for s in vt_symbols}  # 2倍的priceticks作为滑点

    if standard == 0:
        zeros = {s: 0. for s in vt_symbols}
        rates = zeros
        slippages = zeros

    # 保存成交记录
    trades_dict = {}
    for vt_symbol in vt_symbols:
        engine = BacktestingEngine()
        engine.set_parameters(vt_symbol=vt_symbol, interval=data_interval, start=start, end=end, rate=rates[vt_symbol],
                              slippage=slippages[vt_symbol], size=sizes[vt_symbol], pricetick=priceticks[vt_symbol],
                              capital=1_000_000, )
        engine.add_strategy(strategy_class, {})

        engine.load_data()
        engine.run_backtesting()
        engine.calculate_result()
        engine.calculate_statistics()
        trades_dict[vt_symbol] = engine.trades
        # 打印策略名称和交易品种
        print(f'策略名称：{strategy_class.strategy_name}，交易品种：{vt_symbol}绩效如上')
    return trades_dict
