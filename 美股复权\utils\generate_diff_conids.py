#!/usr/bin/env python3
"""
生成filtered_conid2.txt中但不在filtered_conid.txt中的conid列表
保存为diff_conids.txt，以逗号分隔
"""

import os
import typer
from pathlib import Path
from loguru import logger
from vnpy.trader.utility import get_file_path

def load_conid_list(file_path: str) -> list:
    """从文件中读取conid列表（兼容原有接口）"""
    conids = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                conids.append(line)
    return conids

def load_conid_set(file_path: str) -> set:
    """从文件中读取conid集合"""
    return set(load_conid_list(file_path))

app = typer.Typer()

@app.command()
def get_diff_conids(
    file1: str = typer.Option("filtered_conid.txt", help="第一个conid文件路径"),
    file2: str = typer.Option("filtered_conid2.txt", help="第二个conid文件路径"),
    output: str = typer.Option("diff_conids.txt", help="输出差异文件路径")
) -> list:
    """获取在file2中但不在file1中的conid列表，保存到文件"""
    # 获取文件路径
    file1_path = get_file_path(file1)
    file2_path = get_file_path(file2)

    conids1 = load_conid_set(file1_path)
    conids2 = load_conid_set(file2_path)

    diff_conids = conids2 - conids1
    diff_list = sorted(list(diff_conids))

    logger.info(f"文件1({file1_path})有{len(conids1)}个conid")
    logger.info(f"文件2({file2_path})有{len(conids2)}个conid")
    logger.info(f"差异conid数量: {len(diff_list)}")

    # 保存差异conids
    if diff_list:
        output_path = get_file_path(output)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(','.join(diff_list))
        logger.info(f"差异conids已保存到: {output_path}")
        logger.info(f"前10个差异conid: {diff_list[:10]}")
    else:
        logger.info("没有发现差异conid")

    return diff_list

def get_diff_conids_programmatic(file1_path: str, file2_path: str, output_file: str = None) -> list:
    """程序化调用的版本，用于其他脚本导入"""
    conids1 = load_conid_set(file1_path)
    conids2 = load_conid_set(file2_path)

    diff_conids = conids2 - conids1
    diff_list = sorted(list(diff_conids))

    logger.info(f"文件1({file1_path})有{len(conids1)}个conid")
    logger.info(f"文件2({file2_path})有{len(conids2)}个conid")
    logger.info(f"差异conid数量: {len(diff_list)}")

    # 如果指定了输出文件，保存差异conids
    if output_file and diff_list:
        output_path = get_file_path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(','.join(diff_list))
        logger.info(f"差异conids已保存到: {output_path}")

    return diff_list

if __name__ == "__main__":
    app()
