#!/usr/bin/env python3
"""
生成filtered_conid2.txt中但不在filtered_conid.txt中的conid列表
保存为diff_conids.txt，以逗号分隔
"""

import os
from pathlib import Path
from loguru import logger
from vnpy.trader.utility import get_file_path

def load_conid_set(file_path: str) -> set:
    """从文件中读取conid集合"""
    conids = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                conids.add(line)
    return conids

def main():
    """生成差异conids文件"""
    try:
        # 获取文件路径
        file1_path = get_file_path("filtered_conid.txt")
        file2_path = get_file_path("filtered_conid2.txt")
        output_path = get_file_path("diff_conids.txt")
        
        logger.info(f"读取文件1: {file1_path}")
        logger.info(f"读取文件2: {file2_path}")
        
        # 读取两个文件的conid集合
        conids1 = load_conid_set(file1_path)
        conids2 = load_conid_set(file2_path)
        
        logger.info(f"文件1有{len(conids1)}个conid")
        logger.info(f"文件2有{len(conids2)}个conid")
        
        # 计算差异：在file2中但不在file1中的conid
        diff_conids = conids2 - conids1
        diff_list = sorted(list(diff_conids))  # 排序便于查看
        
        logger.info(f"差异conid数量: {len(diff_list)}")
        
        if diff_list:
            # 保存为逗号分隔的格式
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(','.join(diff_list))
            
            logger.info(f"差异conids已保存到: {output_path}")
            logger.info(f"前10个差异conid: {diff_list[:10]}")
        else:
            logger.info("没有发现差异conid")
            
    except Exception as e:
        logger.error(f"生成差异文件失败: {e}")
        raise

if __name__ == "__main__":
    main()
