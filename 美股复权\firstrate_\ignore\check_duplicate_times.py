import pandas as pd
import os
import glob
from datetime import datetime
import typer
from typing import Optional

app = typer.Typer()

def check_file(file_path: str, print_duplicates: bool = True) -> tuple:
    """检查单个文件的重复时间
    
    Args:
        file_path: 文件路径
        print_duplicates: 是否打印重复的时间点
        
    Returns:
        tuple: (重复时间点数量, 总行数)
    """
    # 读取数据，格式：DateTime,Open,High,Low,Close,Volume
    df = pd.read_csv(
        file_path,
        header=None,
        names=["datetime", "open", "high", "low", "close", "volume"],
        parse_dates=["datetime"]
    )
    
    # 检查重复的时间点
    duplicates = df[df['datetime'].duplicated(keep=False)].sort_values('datetime')
    duplicate_count = len(duplicates)
    
    if duplicate_count > 0 and print_duplicates:
        symbol = os.path.basename(file_path).split('_')[0]
        print(f"\n文件: {symbol}")
        print(f"发现 {duplicate_count} 个重复时间点:")
        
        # 按时间分组显示重复数据
        for dt, group in duplicates.groupby('datetime'):
            print(f"\n时间: {dt}")
            print(group.to_string(index=False))
    
    return duplicate_count, len(df)

@app.command()
def main(
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock\\full_1min",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    symbol: Optional[str] = typer.Option(
        None,
        "--symbol",
        "-s",
        help="指定要检查的股票代码"
    )
):
    """检查FirstRate数据文件中的重复时间"""
    start_time = datetime.now()
    print(f"开始检查: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确定要检查的文件
    if symbol:
        files = [os.path.join(data_dir, f"{symbol}_full_1min_UNADJUSTED.txt")]
    else:
        files = glob.glob(os.path.join(data_dir, "*_full_1min_UNADJUSTED.txt"))
    
    total_files = len(files)
    files_with_duplicates = 0
    total_duplicates = 0
    
    print(f"开始检查 {total_files} 个文件...")
    
    for i, file_path in enumerate(files, 1):
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue
            
        duplicate_count, total_rows = check_file(
            file_path, 
            print_duplicates=(symbol is not None)  # 只有检查单个股票时才打印详细信息
        )
        
        if duplicate_count > 0:
            symbol = os.path.basename(file_path).split('_')[0]
            files_with_duplicates += 1
            total_duplicates += duplicate_count
            if not symbol:  # 如果不是检查单个股票，则只打印汇总信息
                print(f"[{i}/{total_files}] {symbol}: {duplicate_count} 个重复时间点 (总行数: {total_rows})")
    
    print(f"\n检查完成!")
    print(f"发现 {files_with_duplicates} 个文件存在重复时间点")
    print(f"共发现 {total_duplicates} 个重复时间点")
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"检查用时: {duration}")

if __name__ == "__main__":
    app() 