DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '751479892' AND datetime < '2025-01-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '696511312' AND datetime < '2025-07-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '52288706' AND datetime < '2022-09-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '47534541' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '114644333' AND datetime < '2022-12-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '47599263' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '51214241' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '7341' AND datetime < '2025-05-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '7341' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '7278' AND datetime < '2025-05-23 12:00:00';

-- ZURVY
SET @multiplier = 0.5;
UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata
SET open_price = open_price / @multiplier,
    high_price = high_price / @multiplier,
    low_price = low_price / @multiplier,
    close_price = close_price / @multiplier,
    volume = volume * @multiplier
WHERE symbol = '105653547'
AND datetime < '2024-08-21 12:00:00';

-- WHLR	786920014 IB漏股数复权
SET @multiplier = 10;
UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata
SET open_price = open_price / @multiplier,
    high_price = high_price / @multiplier,
    low_price = low_price / @multiplier,
    close_price = close_price / @multiplier,
    volume = volume * @multiplier
WHERE symbol = '786920014'
AND datetime < '2023-08-17 12:00:00';

-- IVF	    800320224   无需处理
-- WRD	    723467866   无需处理
-- LFVN	    209744748   无需处理
-- ZION	    276329      无需处理
-- GLNK	    563316664   无需处理
-- HGRAF	607327469   无需处理
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '712378913' AND datetime < '2023-09-25 12:00:00';

-- 数据迁移：从vnpy_stk_us_frt_d_2206_250814.dbbardata读取数据写入vnpy_stk_us_ib_d_2206_250814__.dbbardata
-- 先删除目标表中的旧数据
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '700735038' AND datetime < '2023-09-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '14487733' AND datetime < '2022-11-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '800606097' AND datetime < '2025-08-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '798837403' AND datetime < '2025-07-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '768815582' AND datetime < '2025-03-18 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '768545231' AND datetime < '2025-03-18 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715338' AND datetime < '2025-02-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715312' AND datetime < '2023-12-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715312' AND datetime < '2023-12-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715312' AND datetime < '2023-12-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715312' AND datetime < '2023-12-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '761715312' AND datetime < '2023-12-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '650217279' AND datetime < '2023-10-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-10-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '733716803' AND datetime < '2023-09-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '647316948' AND datetime < '2023-05-31 12:00:00';

-- 从源表读取数据插入到目标表，索引冲突时覆盖
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '700735038' AND datetime < '2023-09-29 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '14487733' AND datetime < '2022-11-11 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '800606097' AND datetime < '2025-08-15 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '798837403' AND datetime < '2025-07-29 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '768815582' AND datetime < '2025-03-18 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '768545231' AND datetime < '2025-03-18 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715338' AND datetime < '2025-02-19 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715312' AND datetime < '2023-12-13 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715312' AND datetime < '2023-12-12 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715312' AND datetime < '2023-12-09 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715312' AND datetime < '2023-12-08 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '761715312' AND datetime < '2023-12-07 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-13 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-12 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-11 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-10 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-07 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-06 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-05 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-04 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '650217279' AND datetime < '2023-10-03 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-10-03 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '733716803' AND datetime < '2023-09-30 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '647316948' AND datetime < '2023-05-31 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
            SELECT
                symbol,
                exchange,
                `interval`,
                COUNT(id) AS `count`,
                MIN(datetime) AS `start`,
                MAX(datetime) AS `end`
            FROM
                vnpy_stk_us_ib_d_2206_250814__.dbbardata
            GROUP BY
                symbol, exchange, `interval`
            ON DUPLICATE KEY UPDATE
                `count` = VALUES(`count`),
                `start` = VALUES(`start`),
                `end` = VALUES(`end`);