# 历史分钟级数据同步工具
# 开发程序功能：每日定时自动从主数据库同步当日分钟级数据至多个备用库
# 要求:
# 尽量采用vn.py原生api
# 同步前先核查：主库数据是否完整，以防数据下载程序失效；备库与主库历史数据时间差确定，下载当日数据还是全部数据
# 每日要有日志文件

import glob
import json
# step1: 获取主数据库实例database_main，备用数据库实例database_backup
# step2: 获取主数据库中所有品种的最新日期，与当前日期比较，如果存在当日数据，则主库数据完整，否则警告主库数据不完整，停止同步。如果主库数据完整，则获取备用数据库中所有品种的最新日期，与主库最新日期比较，如果备用库最新日期小于主库最新日期，则选择下载相差的数据到备用库，或者下载当日数据到备用库。
# step3: 每日定时自动更新，每日17:00开始更新，每分钟检查一次，如果当前时间为17:00，则开始更新，更新前先核查主库数据是否完整，如果主库数据不完整，则停止更新，否则更新备用库数据。
# step4：保存日志文件，记录每日更新情况。格式：时间，主库数据不完整。或者：时间，更新品种，更新数据条数，更新耗时，更新状态（成功/失败）。
import logging
import os
import re
from datetime import datetime, time, timedelta
from itertools import product

from typing import List
from vnpy.trader.constant import Interval
# 获取vnpy版本
import vnpy
from vnpy.trader.object import BarData

version = vnpy.__version__
if version < '3.0.0':  # vnpy 2.*版本
    from utils.mysql_database import create_mysql_database2 as create_mysql_database
else:  # vnpy 3.*版本
    from utils.mysql_database import create_mysql_database3 as create_mysql_database

# 从setting/config.json中读取配置信息
try:
    with open('setting/config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
        symbol_prefixes = config['symbol_prefixes']
        user_task = config['user_task']
        task_time = config['task_time']
        # symbols = config.get('symbols')  # 获取symbols，如果不存在则返回None
        from vnpy.utils.symbol_info import *
        symbols = [f"{s}888" for s in all_symbols]
        # symbols = [f"{s}888" for s in liquid_ind_symbol['black']]
except FileNotFoundError:
    print('setting/config.json文件不存在')
    exit()

# 设置每个interval对应的endtime
# 每日下午收盘后核查版本：
endtime = {
    Interval.DAILY: time.min,
    Interval.MINUTE: time(hour=14, minute=59),
    Interval.HOUR: time(hour=14)
}


# 日志记录
def get_logger(td):
    # 获取当前日期
    td = datetime.now().date()
    # 检查并创建目录
    log_dir = f'log_files/logfiles-{td.year}/{td.month}'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    # 导入logging模块
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 创建一个handler，用于写入日志文件
    log_file = os.path.join(log_dir, f'task-{td}.log')
    # 若不存在日志文件，则创建，否则追加
    fh = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    fh.setLevel(logging.INFO)

    # 创建一个handler，用于输出到控制台
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)

    # 定义handler的输出格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)

    # 给logger添加handler
    logger.addHandler(fh)
    logger.addHandler(ch)

    return logger


# 返回一个database_main对象和多个database_backup对象的列表
def get_databases():
    """获取数据库实例"""
    # 从setting/main/vt_setting.json中获取主数据库信息，编码为utf-8
    with open('setting/main/vt_setting.json', 'r', encoding='utf-8') as f:
        main_setting = json.load(f)
        db_main = create_mysql_database(main_setting)
    # 使用glob，从setting/backup/*.json中获取备用数据库信息
    backup_settings = glob.glob('setting/backup/*.json')
    db_backups = []
    for setting in backup_settings:
        with open(setting, 'r', encoding='utf-8') as f:
            backup_setting = json.load(f)
            db_backups.append(create_mysql_database(backup_setting))
    return db_main, db_backups


# 主库数据完整性检查函数
def check_main(overview_main, today, logger):
    # 匹配symbol前缀部分，如PF304匹配出字母PF，pg2210匹配出字母pg，p2305匹配出p，纯数字则匹配为纯数字
    symbol_regex = r'^([a-zA-Z]+|\d+)'
    main_prefixes = [re.findall(symbol_regex, item.symbol)[0] for item in overview_main if
                     re.findall(symbol_regex, item.symbol)]

    # 获取overview_main所有条目的[symbol、interval、endtime]的列表
    main_items = [(symbol_prefix, item.interval, item.end) for symbol_prefix, item in zip(main_prefixes, overview_main)]
    logger.info(f"主库数据量：{len(main_items)}")

    # 组合[symbol、interval、endtime]的笛卡尔积组合的列表
    symbol_interval_endtime = [(symbol_prefix, interval, datetime.combine(today, endtime[interval])) for
                               symbol_prefix, interval in
                               product(symbol_prefixes, [Interval.DAILY, Interval.MINUTE, Interval.HOUR])]
    # 检查symbol_interval_endtime是否是main_items的子集，用set，记录日志
    if set(symbol_interval_endtime).issubset(set(main_items)):
        logger.info("主库数据完整")
    else:
        logger.info("主库数据不完整")
        # 记录理论需要数据量即symbol_interval_endtime的数量，main_items在symbol_interval_endtime子集中的数量，以及差异数据数量及差异数据
        # 理论需要数据量
        need_count = len(symbol_interval_endtime)
        # main_items在symbol_interval_endtime子集中的数量
        exist_count = len(set(symbol_interval_endtime).intersection(set(main_items)))
        # 差异数据量及差异数据
        diff_count = len(set(symbol_interval_endtime).difference(set(main_items)))
        diff_items = set(symbol_interval_endtime).difference(set(main_items))
        # 差异合约名（集合）
        diff_symbols = set([item[0] for item in diff_items])
        # 记录各数量到日志
        logger.warning(
            f"主库当日理论更新数据量：{need_count}，实际更新数据量：{exist_count}，差异数据量：{diff_count}，差异合约：{diff_symbols}")
        logger.warning(f"差异数据：{diff_items}")


def backup_all(db_main, db_backups, view_main, logger, symbols=None):
    """备份所有数据"""
    # start_time为最小时间
    start_time = datetime.min
    end_time = datetime.max
    # 初始化备份数据列表，用于日志记录
    backup_data = 0
    # 从overview_main中获取全部数据的symbol、exchange、interval
    for item in view_main:
        symbol = item.symbol
        exchange = item.exchange
        interval = item.interval
        if symbols is None or symbol in symbols:
            data = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=start_time,
                                         end=end_time)
            # 意外处理：任何一个副库出现问题，不能影响其他副库的同步
            for db_backup in db_backups:
                try:
                    db_backup.save_bar_data(data)
                except Exception as e:
                    logger.error(f"备份数据出错，错误信息：{e}")
            backup_data += 1
            logger.info(f"finish backup {symbol} interval:{interval} start:{item.start} end:{item.end}")
    # 记录备份数据到日志
    logger.info(f"已备份所有数据，备份品种数量：{backup_data}")


def backup_today(db_main, db_backups, view_main, today, logger, symbols=None):
    """备份当日数据"""
    # load_bar_data需要完整参数：load_bar_data(symbol,exchange,interval,start,end)
    # start_time为前一天下午5点
    start_time = datetime.combine(today - timedelta(days=7), time(hour=17))
    end_time = datetime.combine(today, time.max)
    # 初始化备份数据列表，用于日志记录
    backup_data = 0
    # 从overview_main中获取当日数据的symbol、exchange、interval
    for item in view_main:
        if item.end >= start_time:
            symbol = item.symbol
            exchange = item.exchange
            interval = item.interval
            if symbols is None or symbol in symbols:
                data = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=start_time,
                                             end=end_time)
                # 意外处理：任何一个副库出现问题，不能影响其他副库的同步
                for db_backup in db_backups:
                    try:
                        db_backup.save_bar_data(data)
                    except Exception as e:
                        logger.error(f"备份数据出错，错误信息：{e}")
                backup_data += 1
                # start为前一天下午5点，其他与bakcup_all相同
                logger.info(f"finish backup {symbol} interval:{interval} start:{start_time} end:{item.end}")
    # 记录备份数据到日志
    logger.info(f"已备份当日数据，备份品种数量：{backup_data}")

# 增量备份
def backup_new(db_main, db_backups, view_main, today, logger, symbols=None, ignore_main=False):
    """根据每个品种在主库和副库的最新时间，决定是否备份主库更新的时间的数据到副库"""
    # 初始化备份数据列表，用于日志记录
    backup_data = 0

    # 获取view_backup
    view_backups = [i.get_bar_overview() for i in db_backups]

    for item in view_main:
        symbol = item.symbol
        exchange = item.exchange
        interval = item.interval
        latest_time_main = item.end
        if symbols is None or symbol in symbols:
            # 若主库最新时间大于备用库最新时间，则备份主库的数据至备用库
            for i, view_backup in enumerate(view_backups):
                # 获取备用库中该品种最新的时间
                # 默认为1970年1月1日
                latest_time_backup = datetime(1970,1,1)
                for k in view_backup:
                    # for k in j:
                    if k.symbol == symbol and k.interval == interval:
                        latest_time_backup = k.end
                        break

                if latest_time_backup < latest_time_main or ignore_main:
                    start_time = latest_time_backup
                    end_time = datetime.combine(today, time.max)
                    data = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=start_time, end=end_time)
                    try:
                        db_backups[i].save_bar_data(data)
                    except Exception as e:
                        logger.error(f"备份数据出错，错误信息：{e}")
                    backup_data += 1
                    logger.info(f"完成增量备份 {symbol} {interval} 起始时间:{start_time} 结束时间:{end_time}")

def update_old_backup_new(db_main, db_backups, view_main, today, logger, symbols=None):
    """根据每个品种在主库和副库的最新时间，决定是否备份主库更新的时间的数据到副库。
    与backup_new不同的是，在备份前会先检查主库该品种副库最新时间的数据是否与主库对应时间的数据一致，不一致则先删除副库该品种全部数据，再备份主库该品种全部数据"""
    # 初始化备份数据列表，用于日志记录
    backup_data = 0

    # 获取view_backup
    view_backups = [i.get_bar_overview() for i in db_backups]
    logger.info(f"成功获取{len(db_backups)}个备用库的bar_overview")

    for item in view_main:
        symbol = item.symbol
        exchange = item.exchange
        interval = item.interval
        latest_time_main = item.end
        if symbols is None or symbol in symbols:
            logger.info(f"处理合约: {symbol}, 交易所: {exchange}, 周期: {interval}, 主库最新时间: {latest_time_main}")
            # 若主库最新时间大于备用库最新时间，则备份主库的数据至备用库
            for i, view_backup in enumerate(view_backups):
                # 获取备用库中该品种最新的时间
                # 默认为1970年1月1日
                latest_time_backup = datetime(1970,1,1)
                old_time_backup = datetime(2019,1,1)
                for k in view_backup:
                    # for k in j:
                    if k.symbol == symbol and k.interval.value == interval.value:
                        latest_time_backup = k.end
                        old_time_backup = k.start
                        logger.info(f"备用库{i+1}中找到合约{symbol}数据，起始时间:{old_time_backup}，最新时间:{latest_time_backup}")
                        break

                if latest_time_backup <= latest_time_main:
                    logger.info(f"备用库{i+1}的{symbol} {interval}最新时间{latest_time_backup}小于主库最新时间{latest_time_main}，需要备份")
                    # 先检查主库该品种副库最新时间的数据是否与主库对应时间的数据一致
                    start_time = latest_time_backup
                    old_time_backup_3 = old_time_backup + timedelta(days=3)
                    end_time = datetime.combine(today, time.max)
                    logger.info(f"加载主库数据，起始时间:{old_time_backup}，结束时间:{old_time_backup_3}")
                    data_main: List[BarData] = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=old_time_backup, end=old_time_backup_3)
                    logger.info(f"主库加载到{len(data_main) if data_main else 0}条数据")
                    data_backup: List[BarData] = db_backups[i].load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=old_time_backup, end=old_time_backup_3)
                    logger.info(f"备用库加载到{len(data_backup) if data_backup else 0}条数据")

                    if not data_main:
                        logger.warning(f"主库未找到{symbol} {interval}在{old_time_backup}至{old_time_backup_3}期间的数据，跳过处理")
                        continue
                    elif data_backup and round(data_main[0].close_price, 2) != round(data_backup[0].close_price, 2):
                        main_price = round(data_main[0].close_price, 2)
                        backup_price = round(data_backup[0].close_price, 2) if data_backup else None
                        main_time = data_main[0].datetime
                        backup_time = data_backup[0].datetime if data_backup else None
                        logger.warning(f"{symbol} {interval}数据不一致，主库时间:{main_time} 收盘价:{main_price}，备用库时间:{backup_time} 收盘价:{backup_price}，将删除备用库数据并重新全量备份")
                        # 先删除副库该品种全部数据
                        db_backups[i].delete_bar_data(symbol=symbol, exchange=exchange, interval=interval)
                        logger.info(f"已删除备用库{i+1}中{symbol} {interval}的全部数据")
                        # 备份主库该品种全部数据
                        start_time = datetime(1970,1,1)
                    elif latest_time_backup == latest_time_main:
                        continue

                    logger.info(f"从主库加载{symbol} {interval}数据，起始时间:{start_time}，结束时间:{end_time}")
                    data = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=start_time, end=end_time)
                    try:
                        db_backups[i].save_bar_data(data)
                    except Exception as e:
                        logger.error(f"备份数据出错，错误信息：{e}")
                    backup_data += 1
                    logger.info(f"完成增量备份 {symbol} {interval} 起始时间:{start_time} 结束时间:{end_time}")

def backup_new_symbol(db_main, db_backups, view_main, today, logger, symbols=None):
    '''备份在view_main中存在，但在db_backups中不存在的品种。直接全量备份，因为view_backups中不存在该品种的数据'''
    # 初始化备份数据列表，用于日志记录
    backup_data = 0

    # 获取view_backup
    view_backups = [i.get_bar_overview() for i in db_backups]

    for item in view_main:
        symbol = item.symbol
        exchange = item.exchange
        interval = item.interval
        if symbols is None or symbol in symbols:
            for i, view_backup in enumerate(view_backups):
                symbol_exchange_interval_backup = [(k.symbol, k.exchange, k.interval) for k in view_backup]
                if (symbol, exchange, interval) not in symbol_exchange_interval_backup:
                    # 备份主库该品种全部数据
                    start_time = datetime(1970,1,1)
                    end_time = datetime.combine(today, time.max)
                    data = db_main.load_bar_data(symbol=symbol, exchange=exchange, interval=interval, start=start_time, end=end_time)
                    try:
                        db_backups[i].save_bar_data(data)
                    except Exception as e:
                        logger.error(f"备份数据出错，错误信息：{e}")
                    backup_data += 1
                    logger.info(f"完成增量备份 {symbol} {interval} 起始时间:{start_time} 结束时间:{end_time}")
    # 记录备份数据到日志
    logger.info(f"已备份新品种数据，备份品种数量：{backup_data}")




def task():
    # today = datetime(2023, 3, 23).date() # 测试用
    today = datetime.now().date()

    # 获取日志记录器
    logger = get_logger(today)

    # 执行任务并记录日志
    logger.info('开始执行任务')

    # 获取数据库实例
    database_main, database_backups = get_databases()

    # 获取主库overview
    overview_main = database_main.get_bar_overview()

    # 备份主库数据到备用库（等待用户输入：备份整个主数据库到备用库，或者备份当日的日线、小时线、分钟线数据（即查询主库中start大于前一天下午5点的数据）到备用库），记录日志
    # 等待用户输入操作指令
    # user_input = input("请输入操作指令：1.检查主库完整性并备份全部数据 2.检查主库完整性并备份当日数据 3.备份全部数据 4.备份当日数据")
    # 服务器运行，暂时用固定值
    user_input = user_task
    logger.info(f"用户输入操作指令：{user_input}")

    if user_input == '1':
        # 检查主库完整性并备份全部数据
        check_main(overview_main, today, logger)
        backup_all(database_main, database_backups, overview_main, logger, symbols=symbols)
    elif user_input == '2':
        # 检查主库完整性并备份当日数据
        check_main(overview_main, today, logger)
        backup_today(database_main, database_backups, overview_main, today, logger, symbols=symbols)
    elif user_input == '3':
        # 备份全部数据
        backup_all(database_main, database_backups, overview_main, logger, symbols=symbols)
    elif user_input == '4':
        # 备份当日数据
        backup_today(database_main, database_backups, overview_main, today, logger, symbols=symbols)
    elif user_input == '5':
        # 增量备份
        backup_new(database_main, database_backups, overview_main, today, logger, symbols=symbols, ignore_main=True)
    elif user_input == '6':
        # 增量备份+昨日换月品种全量备份
        update_old_backup_new(database_main, database_backups, overview_main, today, logger, symbols=symbols)
    else:
        print("输入错误，请重新输入")

    # backup_new_symbol(database_main, database_backups, overview_main, today, logger, symbols=symbols)
    logger.info('任务运行结束')


# 单次运行
task()
# # 服务器运行，每天下午5点执行一次
# import time as t, schedule
# #
# schedule.every().day.at(task_time).do(task)
# while True:
#     time_str = datetime.now().strftime('%Y%m%d%H%M%S')
#     print(f"============{time_str}===============")
#     schedule.run_pending()
#     t.sleep(60)
