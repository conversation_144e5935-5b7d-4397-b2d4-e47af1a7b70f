# Changelog

## [3.0.0] - 2025-05-30

### Added
- 新增 `update_conid.py` 脚本
  - 自动追踪和处理标的改名情况
  - 从数据库 overview 中获取所有旧 conid
  - 通过 stable_id 关联查找新 conid
  - 自动更新数据库中的历史数据，包括 dbbaroverview 和 dbbardata 表

### Changed
- 升级 `database_manager.py`
  - 在 `IbProduct` 表中新增 `stable_id` 字段，用于链式追踪同一标的的不同时期信息
  - 添加相关索引以优化查询性能

- 重构 `save_ib_products.py`
  - 新增 `FIELD_MAPPING` 统一管理 API 和数据库字段映射
  - 新增 `get_field` 函数统一处理字典和 ORM 对象的字段获取
  - 优化 `save_products` 函数，通过更新 `created_time` 支持标的改名后再改回old_conid -> new_conid -> old_conid的情况
  - 新增 `assign_stable_id_to_records` 函数处理 stable_id 的分配

### Technical Details
- stable_id 机制说明
  - 用于标识同一标的的不同版本（如改名前后）
  - 首次出现的标的会获得新的 stable_id
  - 当发现与已有记录的 conid 或 symbol 匹配时，会继承该记录的 stable_id
  - is_latest 字段会基于 created_time 标识每个 stable_id 组中的最新版本 