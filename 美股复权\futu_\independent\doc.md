# 文档地址
https://openapi.futunn.com/futu-api-doc/

# 获取复权因子
get_rehab(code)

介绍

获取股票的复权因子

参数

参数	类型	说明
code	str	股票代码
返回

参数	类型	说明
ret	RET_CODE	接口调用结果
data	pd.DataFrame	当 ret == RET_OK，返回复权数据
str	当 ret != RET_OK，返回错误描述
复权数据格式如下：

字段	类型	说明
ex_div_date	str	除权除息日
split_base	float	拆股分子
split_ert	float	拆股分母
join_base	float	合股分子
join_ert	float	合股分母
split_ratio	float	拆合股比例 
per_cash_div	float	每股派现
bonus_base	float	送股分子
bonus_ert	float	送股分母
per_share_div_ratio	float	送股比例 
transfer_base	float	转增股分子
transfer_ert	float	转增股分母
per_share_trans_ratio	float	转增股比例 
allot_base	float	配股分子
allot_ert	float	配股分母
allotment_ratio	float	配股比例 
allotment_price	float	配股价
add_base	float	增发股分子
add_ert	float	增发股分母
stk_spo_ratio	float	增发比例 
stk_spo_price	float	增发价格
forward_adj_factorA	float	前复权因子 A
forward_adj_factorB	float	前复权因子 B
backward_adj_factorA	float	后复权因子 A
backward_adj_factorB	float	后复权因子 B
前复权价格 = 不复权价格 × 前复权因子 A + 前复权因子 B
后复权价格 = 不复权价格 × 后复权因子 A + 后复权因子 B

Example

from futu import *
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

ret, data = quote_ctx.get_rehab("HK.00700")
if ret == RET_OK:
    print(data)
    print(data['ex_div_date'][0])    # 取第一条的除权除息日
    print(data['ex_div_date'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
 
        Copied!
    
Output
    ex_div_date  split_ratio  per_cash_div  per_share_div_ratio  per_share_trans_ratio  allotment_ratio  allotment_price  stk_spo_ratio  stk_spo_price  forward_adj_factorA  forward_adj_factorB  backward_adj_factorA  backward_adj_factorB
0   2005-04-19          NaN          0.07                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -0.07                   1.0                  0.07
..         ...          ...           ...                  ...                    ...              ...              ...            ...            ...                  ...                  ...                   ...                   ...
15  2019-05-17          NaN          1.00                  NaN                    NaN              NaN              NaN            NaN            NaN                  1.0                -1.00                   1.0                  1.00

[16 rows x 13 columns]
2005-04-19
['2005-04-19', '2006-05-15', '2007-05-09', '2008-05-06', '2009-05-06', '2010-05-05', '2011-05-03', '2012-05-18', '2013-05-20', '2014-05-15', '2014-05-16', '2015-05-15', '2016-05-20', '2017-05-19', '2018-05-18', '2019-05-17']
 
        Copied!

重要：实际测试api返回字段可能有变化：
```python
from futu import *
import pandas as pd

# 设置 pandas 显示选项，不限制列数和行数
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11211)

ret, data = quote_ctx.get_rehab("US.LDDFD")
if ret == RET_OK:
    print(data)
    print(data['ex_div_date'][0])    # 取第一条的除权除息日
    print(data['ex_div_date'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
```

(S:\Envs\test) PS S:\OneDrive - lancely\芷瀚同步\qt\futu> & S:/Envs/test/python.exe "s:/OneDrive - lancely/芷瀚同步/qt/futu/rehap.py"
2024-11-28 15:48:18,666 | 43904 | [open_context_base.py] _send_init_connect_sync:311: InitConnect ok: conn_id=1, host=127.0.0.1, port=11211, user_id=31230491
   ex_div_date  split_base  split_ert    join_base     join_ert  split_ratio  per_cash_div  special_dividend    bonus_base   bonus_ert  per_share_div_ratio  transfer_base  transfer_ert  per_share_trans_ratio  allot_base  allot_ert  allotment_ratio  allotment_price  add_base  add_ert  stk_spo_ratio  stk_spo_price  forward_adj_factorA  forward_adj_factorB  backward_adj_factorA  backward_adj_factorB
0   1997-12-01         NaN        NaN          NaN          NaN          NaN       1.10000               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.96400                  0.0               1.00000               1.10000
1   1998-12-23         NaN        NaN          NaN          NaN          NaN       0.06000               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.99785                  0.0               1.00000               0.06000
2   1999-04-20         NaN        NaN          NaN          NaN          NaN       0.54000               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.98275                  0.0               1.00000               0.54000
3   1999-11-12         NaN        NaN          NaN          NaN          NaN       0.76000               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.97629                  0.0               1.00000               0.76000
4   1999-11-22         NaN        NaN          NaN          NaN          NaN       1.01161               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.96662                  0.0               1.00000               1.01161
5   2000-11-07         NaN        NaN          NaN          NaN          NaN           NaN               NaN  5.000000e+04      5419.0             9.226795            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.90221                  0.0               1.10838               0.00000
6   2002-12-18         NaN        NaN          NaN          NaN          NaN           NaN               NaN  2.500000e+04       553.0            45.207957            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.97835                  0.0               1.02212               0.00000
7   2009-11-12         NaN        NaN          NaN          NaN          NaN       0.14371               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.98340                  0.0               1.00000               0.14371
8   2010-11-16         NaN        NaN          NaN          NaN          NaN       0.52195               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.96913                  0.0               1.00000               0.52195
9   2011-06-08         NaN        NaN          NaN          NaN          NaN       0.42231               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.96705                  0.0               1.00000               0.42231
10  2011-11-22         NaN        NaN          NaN          NaN          NaN       0.85886               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.91661                  0.0               1.00000               0.85886
11  2012-07-02         NaN        NaN          NaN          NaN          NaN       0.37824               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.94665                  0.0               1.00000               0.37824
12  2012-11-27         NaN        NaN          NaN          NaN          NaN       0.58900               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.92340                  0.0               1.00000               0.58900
13  2013-11-18         NaN        NaN          NaN          NaN          NaN       0.47163               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.96066                  0.0               1.00000               0.47163
14  2014-07-10         NaN        NaN          NaN          NaN          NaN       0.04415               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.99745                  0.0               1.00000               0.04415
15  2017-11-10         NaN        NaN          NaN          NaN          NaN       1.36246               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.95458                  0.0               1.00000               1.36246
16  2022-11-25         NaN        NaN          NaN          NaN          NaN           NaN           0.18696           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.95372                  0.0               1.00000               0.18696
17  2023-06-02         NaN        NaN          NaN          NaN          NaN           NaN           0.56899           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.91109                  0.0               1.00000               0.56899
18  2023-10-03         NaN        NaN  500000000.0  453902257.0     1.101559           NaN               NaN           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              1.10156                  0.0               0.90780               0.00000
19  2023-12-06         NaN        NaN          NaN          NaN          NaN           NaN           0.97511  1.000000e+09  17478491.0            57.213177            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.88204                  0.0               1.01748               0.97511
20  2024-06-03         NaN        NaN          NaN          NaN          NaN           NaN           0.65025           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.94365                  0.0               1.00000               0.65025
21  2024-11-25         NaN        NaN          NaN          NaN          NaN           NaN           1.01832           NaN         NaN                  NaN            NaN           NaN                    NaN         NaN        NaN              NaN              NaN       NaN      NaN            NaN            NaN              0.93550                  0.0               1.00000               1.01832
1997-12-01
['1997-12-01', '1998-12-23', '1999-04-20', '1999-11-12', '1999-11-22', '2000-11-07', '2002-12-18', '2009-11-12', '2010-11-16', '2011-06-08', '2011-11-22', '2012-07-02', '2012-11-27', '2013-11-18', '2014-07-10', '2017-11-10', '2022-11-25', '2023-06-02', '2023-10-03', '2023-12-06', '2024-06-03', '2024-11-25']
2024-11-28 15:48:18,695 | 43904 | [open_context_base.py] on_disconnect:383: Disconnected: conn_id=1
    
接口限制

每 30 秒内最多请求 60 次获取复权因子接口。

# Q16：复权因子相关
A：

#概述
所谓 复权 就是对股价和成交量进行权息修复，按照股票的实际涨跌绘制股价走势图，并把成交量调整为相同的股本口径。
公司行动（如：拆股、合股、送股、转增股、配股、增发股、分红）均可能对股价产生影响，而复权计算可对量价进行调整，剔除公司行动的影响，保持股价走势的连续性。

#名词解释
公司行动：上市公司进行一些股权、股票等影响公司股价和股东持仓变化的行为。
前复权：保持现有的股价不变，以当前的股价为基准，对以前的股价进行复权计算。
后复权：保持先前的股价不变，以过去的股价为基准，对以后的股价进行复权计算。
复权因子：即权息修复比例，用于计算复权后的价格及持仓数量。
除权除息日：即股权登记日下一个交易日。在股票的除权除息日，证券交易所都要计算出股票的除权除息价，以作为股民在除权除息日开盘的参考。其意义是股票股利分配给股东的日期。
#复权方法
主流的复权计算方法分为两种：事件法和连乘法；而 OpenAPI 针对不同市场使用不同的计算方法。

事件复权法：通过还原除权除息的各类事件进行复权；存在两个复权因子（复权因子 A 和 复权因子 B），复权因子 B 主要调整现金分红对股价的影响，而复权因子 A 调整其他公司行动对股价的影响。
连乘复权法：通过复权因子连乘的方式进行复权，只保留 复权因子 A（或将 复权因子 B 置为0），复权因子 A 为 除权除息日前收盘价/该日经权息调整后的前收盘价。
提示

OpenAPI 对美股前复权使用连乘法，即将 复权因子 B 置为0。
OpenAPI 对除美股以外的标的（A股、港股、新加坡股票等）及美股后复权使用事件法。
#计算公式
#单次复权
前复权：
前复权价格 = 不复权价格 × 前复权因子 A + 前复权因子 B
后复权：
后复权价格 = 不复权价格 × 后复权因子 A + 后复权因子 B
#多次复权
前复权：按照时间顺序，筛选出大于计算日期的复权因子，优先使用时间较早的复权因子进行复权计算。以两次复权为例：

code

后复权：按照时间倒序，筛选出小于等于计算日期的复权因子，优先使用时间较晚的复权因子进行复权计算。以两次复权为例：

code

#示例
#单次前复权示例
以牧原股份为例：

筛选复权因子如下：
除权除息日	股票代码	方案说明	前复权因子 A	前复权因子 B
2021/06/03	SZ.002714	10转4.0股派14.61元（含税）	0.71429	-1.04357
不复权数据如下：
日期	股票代码	不复权收盘价
2021/06/02	SZ.002714	93.11
2021/06/03	SZ.002714	66.25
前复权数据如下：
日期	股票代码	前复权收盘价
2021/06/02	SZ.002714	65.4639719
2021/06/03	SZ.002714	66.25
前复权数据计算方法：
牧原股份在 2021/06/03 进行拆股及现金分红行动（10转4.0股派14.61元），根据前复权计算公式对 2021/06/02 的收盘价进行调整计算，则：前复权价格（65.4639719） = 不复权价格（93.11） × 前复权因子 A（0.71429） + 前复权因子 B（-1.04357）

code

#多次后复权示例
接上一个例子，计算牧原股份在 2021/06/02 的后复权价格：

筛选复权因子如下：
除权除息日	股票代码	方案说明	后复权因子 A	后复权因子 B
2014/07/04	SZ.002714	10派2.34元（含税）	1	0.234
2015-06-10	SZ.002714	10转10.0股派0.61元（含税）	2	0.061
2016-07-08	SZ.002714	10转10.0股派3.53元（含税）	2	0.353
2017-07-11	SZ.002714	10派6.91元（含税）	1	0.691
2018-07-03	SZ.002714	10转8.0股派6.9元（含税）	1.8	0.69
2019-07-04	SZ.002714	10派0.5元（含税）	1	0.05
2020-06-04	SZ.002714	10转7.0股派5.5元（含税）	1.7	0.55
不复权数据如下：
日期	股票代码	不复权收盘价
2021/06/02	SZ.002714	93.11
后复权数据如下：
日期	股票代码	后复权收盘价
2021/06/02	SZ.002714	1150.5114
后复权数据计算方法：
为了计算牧原股份在 2021/06/02 的后复权价格，需要将早于 2021/06/02 的复权事件进行一一复权，得到最后的后复权价格，具体计算如下：

code


# 获取板块列表
get_plate_list(market, plate_class)

介绍

获取板块列表

参数

参数	类型	说明
market	Market	市场标识 
plate_class	Plate	板块分类
返回

参数	类型	说明
ret	RET_CODE	接口调用结果
data	pd.DataFrame	当 ret == RET_OK，返回板块列表数据
str	当 ret != RET_OK，返回错误描述
板块列表数据格式如下：
字段	类型	说明
code	str	板块代码
plate_name	str	板块名字
plate_id	str	板块 ID
Example

from futu import *
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

ret, data = quote_ctx.get_plate_list(Market.HK, Plate.CONCEPT)
if ret == RET_OK:
    print(data)
    print(data['plate_name'][0])    # 取第一条的板块名称
    print(data['plate_name'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
 
        Copied!
    
Output
    code plate_name plate_id
0   HK.BK1000      做空集合股   BK1000
..        ...        ...      ...
77  HK.BK1999       殡葬概念   BK1999

[78 rows x 3 columns]
做空集合股
['做空集合股', '阿里概念股', '雄安概念股', '苹果概念', '一带一路', '5G概念', '夜店股', '粤港澳大湾区', '特斯拉概念股', '啤酒', '疑似财技股', '体育用品', '稀土概念', '人民币升值概念', '抗疫概念', '新股与次新股', '腾讯概念', '云办公', 'SaaS概念', '在线教育', '汽车经销商', '挪威政府全球养老基金持仓', '武汉本地概念股', '核电', '内地医药股', '化妆美容股', '科网股', '公用股', '石油股', '电讯设备', '电力股', '手游股', '婴儿及小童用品股', '百货业股', '收租股', '港口运输股', '电信股', '环保', '煤炭股', '汽车股', '电池', '物流', '内地物业管理股', '农业股', '黄金股', '奢侈品股', '电力设备股', '连锁快餐店', '重型机械股', '食品股', '内险股', '纸业股', '水务股', '奶制品股', '光伏太阳能股', '内房股', '内地教育股', '家电股', '风电股', '蓝筹地产股', '内银股', '航空股', '石化股', '建材水泥股', '中资券商股', '高铁基建股', '燃气股', '公路及铁路股', '钢铁金属股', '华为概念', 'OLED概念', '工业大麻', '香港本地股', '香港零售股', '区块链', '猪肉概念', '节假日概念', '殡葬概念']
 
        Copied!
    
接口限制

每 30 秒内最多请求 10 次获取板块列表接口



# 获取板块内股票列表
get_plate_stock(plate_code, sort_field=SortField.CODE, ascend=True)

介绍

获取指定板块内的股票列表，获取股指的成分股

参数

参数	类型	说明
plate_code	str	板块代码 
sort_field	SortField	排序字段
ascend	bool	排序方向 
返回

参数	类型	说明
ret	RET_CODE	接口调用结果
data	pd.DataFrame	当 ret == RET_OK，返回板块股票数据
str	当 ret != RET_OK，返回错误描述
板块股票数据
字段	类型	说明
code	str	股票代码
lot_size	int	每手股数，期货表示合约乘数
stock_name	str	股票名称
stock_type	SecurityType	股票类型
list_time	str	上市时间 
stock_id	int	股票 ID
main_contract	bool	是否主连合约 
last_trade_time	str	最后交易时间 
Example

from futu import *
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

ret, data = quote_ctx.get_plate_stock('HK.BK1001')
if ret == RET_OK:
    print(data)
    print(data['stock_name'][0])    # 取第一条的股票名称
    print(data['stock_name'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
 
        Copied!
    
Output
    code  lot_size stock_name  stock_owner  stock_child_type stock_type   list_time        stock_id  main_contract last_trade_time
0   HK.00462      4000       天然乳品          NaN               NaN      STOCK  2005-06-10  55589761712590          False                
..       ...       ...        ...          ...               ...        ...         ...             ...            ...             ...
9   HK.06186      1000       中国飞鹤          NaN               NaN      STOCK  2019-11-13  78159814858794          False               

[10 rows x 10 columns]
天然乳品
['天然乳品', '现代牧业', '雅士利国际', '原生态牧业', '中国圣牧', '中地乳业', '庄园牧场', '澳优', '蒙牛乳业', '中国飞鹤']
 
        Copied!
    
接口限制

每 30 秒内最多请求 10 次获取板块内股票列表接口
常用的板块、指数代码
代码	说明
HK.HSI Constituent	恒指成份股
HK.HSCEI Stock	国指成份股
HK.Motherboard	港股主板
HK.GEM	港股创业板
HK.LIST1910	所有港股
HK.LIST1911	主板 H 股
HK.LIST1912	创业板 H 股
HK.Fund	ETF（港股基金）
HK.LIST1600	热度榜（港）
HK.LIST1921	已上市新股-港股
SH.LIST3000000	上海主板
SH.LIST0901	上证 B 股
SH.LIST0902	深证 B 股
SH.LIST3000002	沪深指数
SH.LIST3000005	全部 A 股（沪深）
SH.LIST0600	热度榜（沪深）
SH.LIST0992	科创板
SH.LIST0921	已上市新股-A 股
SZ.LIST3000001	深证主板
SZ.LIST3000003	中小板
SZ.LIST3000004	创业板（深）
US.USAALL	全部美股


# 条件选股
get_stock_filter(market, filter_list, plate_code=None, begin=0, num=200)

介绍

条件选股

参数

参数	类型	说明
market	Market	市场标识 
filter_list	list	筛选条件的列表 
plate_code	str	板块代码
begin	int	数据起始点
num	int	请求数据个数
SimpleFilter 对象相关参数如下：

字段	类型	说明
stock_field	StockField	简单属性
filter_min	float	区间下限 
filter_max	float	区间上限 
is_no_filter	bool	该字段是否不需要筛选 
sort	SortDir	排序方向 
AccumulateFilter 对象相关参数如下：

字段	类型	说明
stock_field	StockField	累积属性
filter_min	float	区间下限 
filter_max	float	区间上限 
is_no_filter	bool	该字段是否不需要筛选 
sort	SortDir	排序方向 
days	int	所筛选的数据的累计天数
FinancialFilter 对象相关参数如下：

字段	类型	说明
stock_field	StockField	财务属性
filter_min	float	区间下限 
filter_max	float	区间上限 
is_no_filter	bool	该字段是否不需要筛选 
sort	SortDir	排序方向 
quarter	FinancialQuarter	财报累积时间
CustomIndicatorFilter 对象相关参数如下：

字段	类型	说明
stock_field1	StockField	自定义技术指标属性
stock_field1_para	list	自定义技术指标属性参数 
relative_position	RelativePosition	相对位置
stock_field2	StockField	自定义技术指标属性
stock_field2_para	list	自定义技术指标属性参数 
value	float	自定义数值 
ktype	KLType	K线类型 KLType 
consecutive_period	int	筛选连续周期（consecutive_period）都符合条件的数据 
is_no_filter	bool	该字段是否不需要筛选 
PatternFilter 对象相关参数如下：

字段	类型	说明
stock_field	StockField	形态技术指标属性
ktype	KLType	K线类型 KLType （仅支持K_60M，K_DAY，K_WEEK，K_MON 四种时间周期）
consecutive_period	int	筛选连续周期（consecutive_period）都符合条件的数据 
is_no_filter	bool	该字段是否不需要筛选 
返回

参数	类型	说明
ret	RET_CODE	接口调用结果
data	tuple	当 ret == RET_OK，返回选股数据
str	当 ret != RET_OK，返回错误描述
选股数据元组组成如下：

字段	类型	说明
last_page	bool	是否是最后一页
all_count	int	列表总数量
stock_list	list	选股数据 
FilterStockData 类型的字段格式：

字段	类型	说明
stock_code	str	股票代码
stock_name	str	股票名字
cur_price	float	最新价
cur_price_to_highest_52weeks_ratio	float	(现价 - 52周最高)/52周最高 
cur_price_to_lowest_52weeks_ratio	float	(现价 - 52周最低)/52周最低 
high_price_to_highest_52weeks_ratio	float	(今日最高 - 52周最高)/52周最高 
low_price_to_lowest_52weeks_ratio	float	(今日最低 - 52周最低)/52周最低 
volume_ratio	float	量比
bid_ask_ratio	float	委比 
lot_price	float	每手价格
market_val	float	市值
pe_annual	float	市盈率
pe_ttm	float	市盈率 TTM
pb_rate	float	市净率
change_rate_5min	float	五分钟价格涨跌幅 
change_rate_begin_year	float	年初至今价格涨跌幅 
ps_ttm	float	市销率 TTM 
pcf_ttm	float	市现率 TTM 
total_share	float	总股数 
float_share	float	流通股数 
float_market_val	float	流通市值 
change_rate	float	涨跌幅 
amplitude	float	振幅 
volume	float	日均成交量
turnover	float	日均成交额
turnover_rate	float	换手率 
net_profit	float	净利润
net_profix_growth	float	净利润增长率 
sum_of_business	float	营业收入
sum_of_business_growth	float	营业同比增长率 
net_profit_rate	float	净利率 
gross_profit_rate	float	毛利率 
debt_asset_rate	float	资产负债率 
return_on_equity_rate	float	净资产收益率 
roic	float	投入资本回报率 
roa_ttm	float	资产回报率 TTM 
ebit_ttm	float	息税前利润 TTM 
ebitda	float	税息折旧及摊销前利润 
operating_margin_ttm	float	营业利润率 TTM 
ebit_margin	float	EBIT 利润率 
ebitda_margin	float	EBITDA 利润率 
financial_cost_rate	float	财务成本率 
operating_profit_ttm	float	营业利润 TTM 
shareholder_net_profit_ttm	float	归属于母公司的净利润 
net_profit_cash_cover_ttm	float	盈利中的现金收入比例 
current_ratio	float	流动比率 
quick_ratio	float	速动比率 
current_asset_ratio	float	流动资产率 
current_debt_ratio	float	流动负债率 
equity_multiplier	float	权益乘数
property_ratio	float	产权比率 
cash_and_cash_equivalents	float	现金和现金等价 
total_asset_turnover	float	总资产周转率 
fixed_asset_turnover	float	固定资产周转率 
inventory_turnover	float	存货周转率 
operating_cash_flow_ttm	float	经营活动现金流 TTM 
accounts_receivable	float	应收账款净额 
ebit_growth_rate	float	EBIT 同比增长率 
operating_profit_growth_rate	float	营业利润同比增长率 
total_assets_growth_rate	float	总资产同比增长率 
profit_to_shareholders_growth_rate	float	归母净利润同比增长率 
profit_before_tax_growth_rate	float	总利润同比增长率 
eps_growth_rate	float	EPS 同比增长率 
roe_growth_rate	float	ROE 同比增长率 
roic_growth_rate	float	ROIC 同比增长率 
nocf_growth_rate	float	经营现金流同比增长率 
nocf_per_share_growth_rate	float	每股经营现金流同比增长率 
operating_revenue_cash_cover	float	经营现金收入比 
operating_profit_to_total_profit	float	营业利润占比 
basic_eps	float	基本每股收益 
diluted_eps	float	稀释每股收益 
nocf_per_share	float	每股经营现金净流量 
price	float	最新价格
ma	float	简单均线 
ma5	float	5日简单均线
ma10	float	10日简单均线
ma20	float	20日简单均线
ma30	float	30日简单均线
ma60	float	60日简单均线
ma120	float	120日简单均线
ma250	float	250日简单均线
rsi	float	RSI的值 
ema	float	指数移动均线 
ema5	float	5日指数移动均线
ema10	float	10日指数移动均线
ema20	float	20日指数移动均线
ema30	float	30日指数移动均线
ema60	float	60日指数移动均线
ema120	float	120日指数移动均线
ema250	float	250日指数移动均线
kdj_k	float	KDJ 指标的 K 值 
kdj_d	float	KDJ 指标的 D 值 
kdj_j	float	KDJ 指标的 J 值 
macd_diff	float	MACD 指标的 DIFF 值 
macd_dea	float	MACD 指标的 DEA 值 
macd	float	MACD 指标的 MACD 值 
boll_upper	float	BOLL 指标的 UPPER 值 
boll_middler	float	BOLL 指标的 MIDDLER 值 
boll_lower	float	BOLL 指标的 LOWER 值 
Example

from futu import *
import time

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
simple_filter = SimpleFilter()
simple_filter.filter_min = 2
simple_filter.filter_max = 1000
simple_filter.stock_field = StockField.CUR_PRICE
simple_filter.is_no_filter = False
# simple_filter.sort = SortDir.ASCEND

financial_filter = FinancialFilter()
financial_filter.filter_min = 0.5
financial_filter.filter_max = 50
financial_filter.stock_field = StockField.CURRENT_RATIO
financial_filter.is_no_filter = False
financial_filter.sort = SortDir.ASCEND
financial_filter.quarter = FinancialQuarter.ANNUAL

custom_filter = CustomIndicatorFilter()
custom_filter.ktype = KLType.K_DAY
custom_filter.stock_field1 = StockField.MA10
custom_filter.stock_field2 = StockField.MA60
custom_filter.relative_position = RelativePosition.MORE
custom_filter.is_no_filter = False

nBegin = 0
last_page = False
ret_list = list()
while not last_page:
    nBegin += len(ret_list)
    ret, ls = quote_ctx.get_stock_filter(market=Market.HK, filter_list=[simple_filter, financial_filter, custom_filter], begin=nBegin)  # 对香港市场的股票做简单、财务和指标筛选
    if ret == RET_OK:
        last_page, all_count, ret_list = ls
        print('all count = ', all_count)
        for item in ret_list:
            print(item.stock_code)  # 取股票代码
            print(item.stock_name)  # 取股票名称
            print(item[simple_filter])   # 取 simple_filter 对应的变量值
            print(item[financial_filter])   # 取 financial_filter 对应的变量值
            print(item[custom_filter])  # 获取 custom_filter 的数值
    else:
        print('error: ', ls)
    time.sleep(3)  # 加入时间间隔，避免触发限频

quote_ctx.close()  # 结束后记得关闭当条连接，防止连接条数用尽
 
        Copied!
    
Output
39 39 [ stock_code:HK.08103  stock_name:HMVOD视频  cur_price:2.69  current_ratio(annual):4.413 ,  stock_code:HK.00376  stock_name:云锋金融  cur_price:2.96  current_ratio(annual):12.585 ,  stock_code:HK.09995  stock_name:荣昌生物-B  cur_price:92.65  current_ratio(annual):16.054 ,  stock_code:HK.80737  stock_name:湾区发展-R  cur_price:2.8  current_ratio(annual):17.249 ,  stock_code:HK.00737  stock_name:湾区发展  cur_price:3.25  current_ratio(annual):17.249 ,  stock_code:HK.03939  stock_name:万国国际矿业  cur_price:2.22  current_ratio(annual):17.323 ,  stock_code:HK.01055  stock_name:中国南方航空股份  cur_price:5.17  current_ratio(annual):17.529 ,  stock_code:HK.02638  stock_name:港灯-SS  cur_price:7.68  current_ratio(annual):21.255 ,  stock_code:HK.00670  stock_name:中国东方航空股份  cur_price:3.53  current_ratio(annual):25.194 ,  stock_code:HK.01952  stock_name:云顶新耀-B  cur_price:69.5  current_ratio(annual):26.029 ,  stock_code:HK.00089  stock_name:大生地产  cur_price:4.22  current_ratio(annual):26.914 ,  stock_code:HK.00728  stock_name:中国电信  cur_price:2.81  current_ratio(annual):27.651 ,  stock_code:HK.01372  stock_name:比速科技  cur_price:5.1  current_ratio(annual):28.303 ,  stock_code:HK.00753  stock_name:中国国航  cur_price:6.38  current_ratio(annual):31.828 ,  stock_code:HK.01997  stock_name:九龙仓置业  cur_price:43.75  current_ratio(annual):33.239 ,  stock_code:HK.02158  stock_name:医渡科技  cur_price:39.0  current_ratio(annual):34.046 ,  stock_code:HK.02588  stock_name:中银航空租赁  cur_price:77.0  current_ratio(annual):34.531 ,  stock_code:HK.01330  stock_name:绿色动力环保  cur_price:3.36  current_ratio(annual):35.028 ,  stock_code:HK.01525  stock_name:建桥教育  cur_price:6.28  current_ratio(annual):36.989 ,  stock_code:HK.09908  stock_name:嘉兴燃气  cur_price:10.02  current_ratio(annual):37.848 ,  stock_code:HK.06078  stock_name:海吉亚医疗  cur_price:49.8  current_ratio(annual):39.0 ,  stock_code:HK.01071  stock_name:华电国际电力股份  cur_price:2.16  current_ratio(annual):39.507 ,  stock_code:HK.00357  stock_name:美兰空港  cur_price:34.15  current_ratio(annual):39.514 ,  stock_code:HK.00762  stock_name:中国联通  cur_price:5.15  current_ratio(annual):40.74 ,  stock_code:HK.01787  stock_name:山东黄金  cur_price:15.56  current_ratio(annual):41.604 ,  stock_code:HK.00902  stock_name:华能国际电力股份  cur_price:2.66  current_ratio(annual):42.919 ,  stock_code:HK.00934  stock_name:中石化冠德  cur_price:2.96  current_ratio(annual):43.361 ,  stock_code:HK.01117  stock_name:现代牧业  cur_price:2.3  current_ratio(annual):45.037 ,  stock_code:HK.00177  stock_name:江苏宁沪高速公路  cur_price:8.78  current_ratio(annual):45.93 ,  stock_code:HK.01379  stock_name:温岭工量刃具  cur_price:5.71  current_ratio(annual):46.774 ,  stock_code:HK.01876  stock_name:百威亚太  cur_price:22.5  current_ratio(annual):46.917 ,  stock_code:HK.01907  stock_name:中国旭阳集团  cur_price:4.38  current_ratio(annual):47.129 ,  stock_code:HK.02160  stock_name:心通医疗-B  cur_price:15.54  current_ratio(annual):47.384 ,  stock_code:HK.00293  stock_name:国泰航空  cur_price:7.1  current_ratio(annual):47.983 ,  stock_code:HK.00694  stock_name:北京首都机场股份  cur_price:6.34  current_ratio(annual):47.985 ,  stock_code:HK.09922  stock_name:九毛九  cur_price:26.65  current_ratio(annual):48.278 ,  stock_code:HK.01083  stock_name:港华燃气  cur_price:3.39  current_ratio(annual):49.2 ,  stock_code:HK.00291  stock_name:华润啤酒  cur_price:58.0  current_ratio(annual):49.229 ,  stock_code:HK.00306  stock_name:冠忠巴士集团  cur_price:2.29  current_ratio(annual):49.769 ]
HK.08103
HMVOD视频
2.69
2.69
4.413
...
HK.00306
冠忠巴士集团
2.29
2.29
49.769
 
        Copied!
    
提示

利用获取子板块列表函数 获取子板块代码，条件选股支持的板块分别为
港股的行业板块和概念板块。
美股的行业板块
沪深的行业板块，概念板块和地域板块
支持的板块指数代码
代码	说明
HK.Motherboard	港股主板
HK.GEM	港股创业板
HK.BK1911	H 股主板
HK.BK1912	H 股创业板
US.NYSE	纽交所
US.AMEX	美交所
US.NASDAQ	纳斯达克
SH.3000000	上海主板
SZ.3000001	深证主板
SZ.3000004	深证创业板
接口限制

港股 BMP 权限不支持条件选股功能
每 30 秒内最多请求 10 次条件选股接口
每页返回的筛选结果最多 200 个
建议筛选条件不超过 250 个，否则可能会出现“业务处理超时没返回”
累积属性的同一筛选条件数量上限 10 个
如果使用“最新价”等动态数据作为排序字段，在多页获取的间隙，数据的排序有可能发生变化
非同类指标不支持比较，仅限于同类指标之间建立比较关系，跨不同类型的指标比较会报错。例如：MA5 和 MA10 可以建立关系。MA5和EMA10不能建立关系。
自定义指标属性的同一类筛选条件超出数量上限10个
简单属性，财务属性，形态属性不支持对同一字段重复指定筛选条件