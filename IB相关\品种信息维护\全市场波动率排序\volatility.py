import math
import numpy as np

def standard_deviation(price_data, window=14, trading_periods=252, clean=True):
    log_return = (price_data["close_price"] / price_data["close_price"].shift(1)).apply(np.log)
    result = log_return.rolling(window=window, center=False).std() * math.sqrt(
        trading_periods
    )
    if clean:
        return result.dropna()
    else:
        return result


def parkinson(price_data, window=14, trading_periods=252, clean=True):
    rs = (1.0 / (4.0 * math.log(2.0))) * (
        (price_data["high_price"] / price_data["low_price"]).apply(np.log)
    ) ** 2.0
    def f(v):
        return (trading_periods * v.mean()) ** 0.5
    result = rs.rolling(window=window, center=False).apply(func=f)
    if clean:
        return result.dropna()
    else:
        return result


def garman_klass(price_data, window=14, trading_periods=252, clean=True):
    log_hl = (price_data["high_price"] / price_data["low_price"]).apply(np.log)
    log_co = (price_data["close_price"] / price_data["open_price"]).apply(np.log)
    rs = 0.5 * log_hl ** 2 - (2 * math.log(2) - 1) * log_co ** 2
    def f(v):
        return (trading_periods * v.mean()) ** 0.5
    result = rs.rolling(window=window, center=False).apply(func=f)
    if clean:
        return result.dropna()
    else:
        return result


def hodges_tompkins(price_data, window=14, trading_periods=252, clean=True):
    log_return = (price_data["close_price"] / price_data["close_price"].shift(1)).apply(np.log)
    vol = log_return.rolling(window=window, center=False).std() * math.sqrt(
        trading_periods
    )
    h = window
    n = (log_return.count() - h) + 1
    adj_factor = 1.0 / (1.0 - (h / n) + ((h ** 2 - 1) / (3 * n ** 2)))
    result = vol * adj_factor
    if clean:
        return result.dropna()
    else:
        return


def rogers_satchell(price_data, window=14, trading_periods=252, clean=True):
    log_ho = (price_data["high_price"] / price_data["open_price"]).apply(np.log)
    log_lo = (price_data["low_price"] / price_data["open_price"]).apply(np.log)
    log_co = (price_data["close_price"] / price_data["open_price"]).apply(np.log)
    rs = log_ho * (log_ho - log_co) + log_lo * (log_lo - log_co)
    def f(v):
        return (trading_periods * v.mean()) ** 0.5
    result = rs.rolling(window=window, center=False).apply(func=f)
    if clean:
        return result.dropna()
    else:
        return result


def yang_zhang(price_data, window=14, trading_periods=252, clean=True):
    log_ho = (price_data["high_price"] / price_data["open_price"]).apply(np.log)
    log_lo = (price_data["low_price"] / price_data["open_price"]).apply(np.log)
    log_co = (price_data["close_price"] / price_data["open_price"]).apply(np.log)
    log_oc = (price_data["open_price"] / price_data["close_price"].shift(1)).apply(np.log)
    log_oc_sq = log_oc ** 2
    log_cc = (price_data["close_price"] / price_data["close_price"].shift(1)).apply(np.log)
    log_cc_sq = log_cc ** 2
    rs = log_ho * (log_ho - log_co) + log_lo * (log_lo - log_co)
    close_vol = log_cc_sq.rolling(window=window, center=False).sum() * (
        1.0 / (window - 1.0)
    )
    open_vol = log_oc_sq.rolling(window=window, center=False).sum() * (
        1.0 / (window - 1.0)
    )
    window_rs = rs.rolling(window=window, center=False).sum() * (1.0 / (window - 1.0))
    k = 0.34 / (1.34 + (window + 1) / (window - 1))
    result = (open_vol + k * close_vol + (1 - k) * window_rs).apply(
        np.sqrt
    ) * math.sqrt(trading_periods)
    if clean:
        return result.dropna()
    else:
        return result

# 3年期简单波动率：(期间最高价-期间最低价)/期间平均价
def simple_vol(price_data, window=14, trading_periods=252, clean=True):
    rolling_high = price_data["high_price"].rolling(window=window, center=False).max()
    rolling_low = price_data["low_price"].rolling(window=window, center=False).min()
    rolling_mean = price_data["close_price"].rolling(window=window, center=False).mean()
    result = (rolling_high - rolling_low) / rolling_mean
    # print(f'{price_data.index[-1]}: {window}日滚动波动率：{result[-1]:.2f} = (最高价: {rolling_high[-1]} - 最低价: {rolling_low[-1]}) / 均价：{rolling_mean[-1]}')
    if clean:
        return result.dropna()
    else:
        return result