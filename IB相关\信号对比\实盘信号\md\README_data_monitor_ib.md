# IBKR数据监控器使用说明

## 概述

修改后的 `data_monitor.py` 现在支持IBKR交易的标的（外汇、美股等），主要改进包括：

1. **交易时段感知**: 只在交易时段内检查数据完整性
2. **IB网关集成**: 自动获取合约交易时段信息
3. **智能数据缺失检测**: 基于实际交易时段计算预期数据量

## 主要功能

### 1. 交易时段管理
- 自动连接IB网关获取合约详情
- 解析交易时段信息（tradingPeriod）
- 支持多个交易时段（如外汇24小时交易）
- 定期更新交易时段信息（24小时间隔）

### 2. 智能数据检查
- **交易时段内检查**: 只在交易时段内检查数据滞后
- **精确缺失计算**: 基于实际交易分钟数计算数据缺失
- **时区处理**: 正确处理不同时区的交易时间

### 3. 告警优化
- 区分交易时段内外的数据状态
- 提供更准确的数据缺失信息
- 支持指数退避告警策略

## 配置文件

### 1. connect_ib_quote.json
```json
{
    "TWS地址": "localhost",
    "TWS端口": 4002,
    "客户号": 2,
    "交易账户": ""
}
```

### 2. data_monitor_setting.json
```json
{
    "symbol_list": ["265598.SMART", "12087792.IDEALPRO"],
    "wecom_key": "your-wecom-key",
    "delay_threshold": 3
}
```

## 交易时段示例

### 美股 (265598.SMART)
```
交易时段: 09:30-16:00 (美东时间)
时区: US/Eastern
```

### 外汇 (12087792.IDEALPRO)
```
交易时段: 17:15(周日)-17:00(周五) (美东时间)
时区: US/Eastern
几乎24小时交易，除了每日17:00-17:15的维护时间
```

## 运行方式

```bash
python data_monitor.py
```

## 测试

### 基础功能测试
运行测试脚本验证交易时段计算功能：
```bash
python data_monitor_test.py
```

### 完整监控测试流程

1. **启动数据监控器**（在一个终端中）：
```bash
python data_monitor.py
```

2. **生成测试数据**（在另一个终端中）：
```bash
python data_monitor_test.py
# 选择 'y' 运行数据生成测试
```

3. **观察监控结果**：
   - 测试脚本会生成带有延迟和缺失的数据
   - 数据监控器会在下一个检查周期（60秒内）检测到异常
   - 企业微信会收到告警通知

### 测试数据说明
- 随机选择一个合约作为数据缺失测试对象
- 在第3分钟位置跳过数据生成（模拟数据缺失）
- 设置不同的数据延迟（1-5分钟）
- 生成30分钟的历史数据用于测试

## 主要修改点

1. **添加IB网关连接**
   - 自动连接IB网关获取合约信息
   - 定期查询合约详情更新交易时段

2. **交易时段检查**
   - `is_in_trading_period()`: 判断是否在交易时段内
   - `calculate_trading_minutes()`: 计算交易时段内的分钟数

3. **数据检查逻辑优化**
   - 只在交易时段内检查数据滞后
   - 基于实际交易分钟数检查数据缺失

4. **告警消息改进**
   - 显示交易时段内应有的数据量
   - 更准确的缺失数据统计

## 注意事项

1. 确保IB TWS或Gateway正在运行
2. 确保网关配置正确（端口、客户号等）
3. 首次运行需要等待合约信息查询完成
4. 交易时段信息会定期更新（24小时间隔）

## 依赖项

- vnpy框架
- IB网关模块
- MySQL数据库
- 企业微信通知模块