# %% md
# # 使用迅投研xtquant和vnpy_xt
# %%
# Token连接（若通过客户端连接则无需运行此单元格）
from urllib.parse import quote as urlquote

from sqlalchemy import create_engine
from vnpy.trader.utility import TEMP_DIR
from xtquant import xtdata
from xtquant import xtdatacenter as xtdc

xtdc.set_token("18988f50ee15266299a4b309eaf984210c07cd35")  # 换成自己的Token
xtdc.set_data_home_dir(str(TEMP_DIR) + "/xt")
# token模式下期货周末夜盘数据时间模式可选,可以选择展示为周一凌晨时间或真实的周六凌晨时间
xtdc.set_future_realtime_mode(True)
xtdc.init(False)
xtdc.listen(port=(58600, 58610))
# %%
xtdata.download_history_data("", "historycontract")
xtdata.download_history_data("", "historymaincontract")
# %%
from datetime import datetime
from dateutil.relativedelta import relativedelta

start_time = datetime(2021, 1, 1)
contract_start_time = datetime(2021, 1, 1) - relativedelta(years=1)
start_str = start_time.strftime('%Y%m%d')
contract_start_str = contract_start_time.strftime('%Y%m%d')
end_str = datetime.now().strftime('%Y%m%d')

calendar = xtdata.get_trading_calendar(market='SH', start_time=start_str, end_time=end_str)
# calendar 从%Y%m%d转为datetime
calendar = [datetime.strptime(i, '%Y%m%d') for i in calendar]
calendar
# %%
from vnpy.utils.symbol_info import all_symbol_pres
from vnpy.trader.constant import Exchange

EXCHANGE_VT2XT: dict[Exchange, str] = {Exchange.SSE: "SH", Exchange.SZSE: "SZ", Exchange.BSE: "BJ", Exchange.SHFE: "SF",
                                       Exchange.CFFEX: "IF", Exchange.INE: "INE", Exchange.DCE: "DF",
                                       Exchange.CZCE: "ZF", Exchange.GFEX: "GF", }

EXCHANGE_XT2VT = {v: k for k, v in EXCHANGE_VT2XT.items()}

xt_main_symbols = [f'{symbol}00.{EXCHANGE_VT2XT[Exchange(exchange)]}' for exchange, symbols in all_symbol_pres.items()
                   # for symbol in symbols if symbol =='y'
                   for symbol in symbols if symbol != 'sctas']
# %%
import pandas as pd


# 将xt_symbol转换为vt_symbol
def xt2vt_symbol(xt_symbol):
    symbol, exchange = xt_symbol.rsplit('.', 1)
    return f'{symbol}.{EXCHANGE_XT2VT[exchange].value}'


# 获取主要合约数据
def get_main_ticker(code_market):
    try:
        pd_series = xtdata.get_main_contract(code_market, contract_start_str, end_str)
    except:
        return pd.DataFrame()

    if not isinstance(pd_series, pd.Series) or pd_series.empty:
        return pd.DataFrame()

    pd_series.index = [datetime.fromtimestamp(i / 1000) for i in pd_series.index]
    pd_series = pd_series.reindex(calendar, method='ffill').dropna()

    df = pd_series.to_frame().reset_index()
    df.rename(columns={'index': 'orig_date', 0: 'specific_symbol'}, inplace=True)

    symbol, exchange = code_market.rsplit('.', 1)
    df['main_symbol'] = f'{symbol[:-2]}88.{EXCHANGE_XT2VT[exchange].value}'
    df['specific_symbol'] = df['specific_symbol'].apply(xt2vt_symbol)

    return df[['main_symbol', 'specific_symbol', 'orig_date']]


get_main_ticker("zn00.SF")


# %%
# 获取符号映射历史
def get_symbol_mapping_his():
    return pd.concat([get_main_ticker(symbol) for symbol in xt_main_symbols])


symbol_mapping_his = get_symbol_mapping_his()
symbol_mapping_his


# %%


# 获取特定时间的价格
def get_price(vt_symbol, date, change_hour=10, change_minute=0):
    symbol, exchange = vt_symbol.split('.')
    xt_symbol = f'{symbol}.{EXCHANGE_VT2XT[Exchange(exchange)]}'

    change_time = datetime(date.year, date.month, date.day, change_hour, change_minute).strftime('%Y%m%d%H%M%S')
    xtdata.download_history_data(xt_symbol, "1m", change_time, change_time)
    data = xtdata.get_local_data(["close"], [xt_symbol], "1m", change_time, change_time, -1, 'none', False)

    # # 生成转换时间和加10分钟的时间
    # change_time = datetime(date.year, date.month, date.day, change_hour, change_minute)
    # change_time_str = change_time.strftime('%Y%m%d%H%M%S')
    # change_time_plus_10_min = (change_time + timedelta(minutes=10)).strftime('%Y%m%d%H%M%S')
    # 
    # # 下载历史数据（假设你需要10分钟的范围）
    # xtdata.download_history_data(xt_symbol, "1m", change_time_str, change_time_plus_10_min)
    # 
    # # 获取市场数据
    # data = xtdata.get_local_data(["close"], [xt_symbol], "1m", change_time_str, change_time_str, -1, 'none', False)

    try:
        close = data[xt_symbol]['close'].values[0]
    except:
        print(f"xtdata no price\n"
              f"xtdata.download_history_data('{xt_symbol}', '1m', '{change_time}', '{change_time}')\n"
              f"data = xtdata.get_local_data(['close'], ['{xt_symbol}'], '1m', '{change_time}', '{change_time}')\n")
        close = None

    return close


# %%
# xtdata.download_history_data('','historymaincontract')
xtdata.get_main_contract('y00.DF', '20231028', '20240408')


# %%
# 获取符号切换点和相应的价格
def get_symbol_mapping_switch(df):
    df = df.rename(columns={'specific_symbol': 'new_symbol'})
    df['old_symbol'] = df.groupby('main_symbol')['new_symbol'].shift(1)
    df = df[df['new_symbol'] != df['old_symbol']].dropna()
    df = df[df['orig_date'] >= '2023-10-28']

    df['old_price'] = df.apply(lambda row: get_price(row['old_symbol'], row['orig_date']), axis=1)
    df['new_price'] = df.apply(lambda row: get_price(row['new_symbol'], row['orig_date']), axis=1)

    return df.dropna()[['main_symbol', 'old_symbol', 'new_symbol', 'old_price', 'new_price', 'orig_date']]


symbol_mapping_switch = get_symbol_mapping_switch(symbol_mapping_his)
symbol_mapping_switch
# %%
'''
CREATE TABLE `symbol_mapping_his` (
  `id` int NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '888合约',
  `specific_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '具体合约',
  `orig_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
'''
# 新结构：
'''
CREATE TABLE `symbol_mapping_his` (
  `id` int NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '888合约',
  `specific_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '具体合约',
  `orig_date` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uc_main_symbol_orig_date` (`main_symbol`,`orig_date`)
) ENGINE=InnoDB AUTO_INCREMENT=39285 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
'''
# 主要变化：1.将orig_date的类型改为date；2.添加唯一索引
# 保存到数据库（scout表的symbol_mapping_his中）
# 新新结构
'''
CREATE TABLE `symbol_mapping_switch` (
  `index` bigint NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(255) NOT NULL,
  `old_symbol` varchar(255) NOT NULL,
  `new_symbol` varchar(255) NOT NULL,
  `old_price` float NOT NULL,
  `new_price` float NOT NULL,
  `orig_date` datetime NOT NULL,
  PRIMARY KEY (`index`),
  UNIQUE KEY `unique_main_symbol_orig_date` (`main_symbol`, `orig_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
'''


# 主要变化：增加old_symbol, old_price, new_price，specific_symbol改为new_symbol
# 表中存储换月信息，记录每个main_symbol的换月日的 原合约名，新合约名。原合约价格，新合约价格。 
#    价格取orig_date当日两个合约的10：00的  close值【原始值，不赋权】

# 获取mysql引擎
def get_mysql_engine():
    userName = 'root'
    password = 'p0o9i8u7'
    dbHost = 'localhost'
    dbPort = 3306
    dbName = 'common_info'
    DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'
    mysql_engine = create_engine(DB_CONNECT, max_overflow=50,  # 超过连接池大小外最多创建的连接
                                 pool_size=50,  # 连接池大小
                                 pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错
                                 pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
                                 # encoding='utf-8',
                                 echo=False)
    return mysql_engine


# %%
def main():
    symbol_mapping_his = get_symbol_mapping_his()

    if symbol_mapping_his.empty:
        print('[symbol_mapping_his]无数据，请检查')
        raise ValueError
    else:
        mysql_engine = get_mysql_engine()
        symbol_mapping_his.to_sql('symbol_mapping_his', mysql_engine, if_exists='replace',  # index=False
                                  )

        symbol_mapping_switch = get_symbol_mapping_switch(symbol_mapping_his)
        symbol_mapping_switch.to_sql('symbol_mapping_switch', mysql_engine, if_exists='replace',  # index=False
                                     )


# %%
if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(
            e)  # from send_to_wechat import WeChat  # wx = WeChat()  # wx.send_data(f"118.89.200.89:{__file__}: An error occurred! ", touser='liaoyuan')
