# # Scanners

from ib_async import *

util.startLoop()

ib = IB()
ib.connect('localhost', 4002, clientId=1)

sub = ScannerSubscription(instrument='STK', locationCode='STK.US', scanCode='MOST_ACTIVE_AVG_USD')

tagValues = [TagValue("avgUsdVolumeAbove", "1e8"), ]

# the tagValues are given as 3rd argument; the 2nd argument must always be an empty list
# (IB has not documented the 2nd argument and it's not clear what it does)
scanData = ib.reqScannerData(sub, [], tagValues)
'''
class ScanData:
    rank: int
    contractDetails: ContractDetails
    distance: str
    benchmark: str
    projection: str
    legsStr: str
'''
print(scanData[0])
symbols = [sd.contractDetails.contract.symbol for sd in scanData]
print(len(symbols), symbols)

path = 'scanner_parameters.xml'

xml = ib.reqScannerParameters()

print(len(xml), 'bytes')
with open(path, 'w', encoding='utf-8') as f:
    f.write(xml)

# import webbrowser
# webbrowser.open(path)

with open(path, 'r', encoding='utf-8') as f:
    xml = f.read()

# parse XML document
import xml.etree.ElementTree as ET

tree = ET.fromstring(xml)

# find all tags that are available for filtering
tags = [elem.text for elem in tree.findall('.//AbstractField/code')]
print(len(tags), 'tags, showing first 100:')
print(tags[:100])

scanCodes = [e.text for e in tree.findall('.//scanCode')]

print(len(scanCodes), 'scan codes, showing the ones starting with "TOP":')
print(sorted(set([sc for sc in scanCodes if not sc.startswith('SCAN')])))
# print([sc for sc in scanCodes if sc.startswith('TOP')])
print(sorted(set([sc for sc in scanCodes if sc.startswith('MOST')])))

instrumentTypes = set(e.text for e in tree.findall('.//Instrument/type'))
print(instrumentTypes)
locationCodes = [e.text for e in tree.findall('.//locationCode')]
print(locationCodes)

# 以.拆分，取instrumentTypes, locationCodes第一个元素
instrumentTypes_pre = set([it.split('.')[0] for it in instrumentTypes])
locationCodes_pre = set([lc.split('.')[0] for lc in locationCodes])
print(instrumentTypes_pre, locationCodes_pre)
print(instrumentTypes_pre - locationCodes_pre)
print(locationCodes_pre - instrumentTypes_pre)

# 打印instrumentTypes中以Global开头的元素
print([it for it in instrumentTypes if it.startswith('Global')])

stk_instruments = {it for it in instrumentTypes if it.startswith('STK') or it.startswith('STOCK')}
print(stk_instruments)
stk_locationCodes = [lc for lc in locationCodes if lc.startswith('STK') and lc.count('.') == 1]
print(stk_locationCodes)
from itertools import product

stk_product = list(product(stk_instruments, stk_locationCodes))
print(len(stk_product))

# FUT
fut_instruments = {it for it in instrumentTypes if it.startswith('FUT')}
print(fut_instruments)
fut_locationCodes = {lc for lc in locationCodes if lc.startswith('FUT')}
print(fut_locationCodes)
fut_product = list(product(fut_instruments, fut_locationCodes))
print(len(fut_product))

# ETF
etf_instruments = {it for it in instrumentTypes if it.startswith('ETF')}
print(etf_instruments)  # {'ETF.EQ.US', 'ETF.FI.US'}
etf_locationCodes = {lc for lc in locationCodes if lc.startswith('ETF')}
print(
    etf_locationCodes)  # {'ETF.EQ.US.MAJOR', 'ETF.FI.NASDAQ.NMS', 'ETF.FI.US.MAJOR', 'ETF.EQ.NASDAQ.NMS', 'ETF.FI.BATS', 'ETF.FI.ARCA', 'ETF.EQ.ARCA', 'ETF.FI.US', 'ETF.EQ.BATS', 'ETF.EQ.US'}
etf_product = list(product(etf_instruments, etf_locationCodes))
print(len(etf_product))
# 删除以.拆分后，每组中第一个元素不同或第二个元素不同的
for i in etf_product:
    if i[0].split('.')[0] != i[1].split('.')[0] or i[0].split('.')[1] != i[1].split('.')[1]:
        etf_product.remove(i)  # print(i)
print(len(etf_product))

'''
# Single-Stock Futures, SSF
ssf_instruments = {it for it in instrumentTypes if it.startswith('SSF')}
print(ssf_instruments)
ssf_locationCodes = {lc for lc in locationCodes if lc.startswith('SSF')}
print(ssf_locationCodes)
ssf_product = list(product(ssf_instruments, ssf_locationCodes))
print(len(ssf_product))
# 删除以.拆分后，每组中第一个元素不同或第二个元素不同的
for i in ssf_product:
    if i[0].split('.')[0] != i[1].split('.')[0] or i[0].split('.')[1] != i[1].split('.')[1]:
        ssf_product.remove(i)
print(len(ssf_product))
exchange_rate = ['AUD-CAD', 'AUD-CHF', 'AUD-CNH', 'AUD-HKD', 'AUD-JPY', 'AUD-NZD', 'AUD-SGD', 'AUD-USD', 'AUD-ZAR', 'BGN-USD', 'CAD-CHF', 'CAD-CNH', 'CAD-JPY', 'CHF-CNH', 'CHF-CZK', 'CHF-DKK', 'CHF-HUF', 'CHF-JPY', 'CHF-NOK', 'CHF-PLN', 'CHF-SEK', 'CHF-TRY', 'CHF-ZAR', 'CNH-HKD', 'CNH-JPY', 'DKK-JPY', 'DKK-NOK', 'DKK-SEK', 'EUR-AED', 'EUR-AUD', 'EUR-CAD', 'EUR-CHF', 'EUR-CNH', 'EUR-CZK', 'EUR-DKK', 'EUR-GBP', 'EUR-HKD', 'EUR-HUF', 'EUR-ILS', 'EUR-JPY', 'EUR-MXN', 'EUR-NOK', 'EUR-NZD', 'EUR-PLN', 'EUR-RUB', 'EUR-SAR', 'EUR-SEK', 'EUR-SGD', 'EUR-TRY', 'EUR-USD', 'EUR-ZAR', 'GBP-AUD', 'GBP-CAD', 'GBP-CHF', 'GBP-CNH', 'GBP-CZK', 'GBP-DKK', 'GBP-HKD', 'GBP-HUF', 'GBP-JPY', 'GBP-MXN', 'GBP-NOK', 'GBP-NZD', 'GBP-PLN', 'GBP-SEK', 'GBP-SGD', 'GBP-TRY', 'GBP-USD', 'GBP-ZAR', 'HKD-JPY', 'KRW-AUD', 'KRW-CAD', 'KRW-CHF', 'KRW-EUR', 'KRW-GBP', 'KRW-HKD', 'KRW-JPY', 'KRW-USD', 'MXN-JPY', 'MYR-MYR', 'MYR-USD', 'NOK-JPY', 'NOK-SEK', 'NZD-CAD', 'NZD-CHF', 'NZD-JPY', 'NZD-USD', 'RON-USD', 'SEK-JPY', 'SGD-CNH', 'SGD-HKD', 'SGD-JPY', 'THB-USD', 'USD-AED', 'USD-BGN', 'USD-CAD', 'USD-CHF', 'USD-CNH', 'USD-CZK', 'USD-DKK', 'USD-HKD', 'USD-HUF', 'USD-ILS', 'USD-JPY', 'USD-KRW', 'USD-MXN', 'USD-MYR', 'USD-NOK', 'USD-PLN', 'USD-RON', 'USD-RUB', 'USD-SAR', 'USD-SEK', 'USD-SGD', 'USD-THB', 'USD-TRY', 'USD-ZAR', 'ZAR-JPY']
exchange_rate = [f'{i}-CASH.IDEALPRO' for i in exchange_rate]# 改为ZAR-JPY-CASH.IDEALPRO格式
print(exchange_rate)
'''


# # 高流动性股票筛选

# 包装成函数，入参：instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow=None
def single_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow=None, avg=True):
    global data, pair
    sub = ScannerSubscription(instrument=instrument, locationCode=locationCode, scanCode='MOST_ACTIVE_AVG_USD')
    try:
        above_tag = "avgUsdVolumeAbove" if avg else "usdVolumeAbove"

        tagValues = [TagValue(above_tag, avgUsdVolumeAbove), ]
        if avgUsdVolumeBelow:
            below_tag = "avgUsdVolumeBelow" if avg else "usdVolumeBelow"
            tagValues.append(TagValue(below_tag, avgUsdVolumeBelow))

        scanData = ib.reqScannerData(sub, [], tagValues)
        num_scanData = len(scanData)
        if num_scanData > 0:
            pair.append((instrument, locationCode))

        for sd in scanData:
            temp = {'instrument': instrument, 'locationCode': locationCode  # , 'scanCode': 'MOST_ACTIVE'
                , "rank": sd.rank,
                    # "Symbol": sd.contractDetails.contract.symbol, "MarketName": sd.contractDetails.marketName,
                    #         "SecType": sd.contractDetails.contract.secType, "Conid": sd.contractDetails.contract.conId,
                    #         "Currency": sd.contractDetails.contract.currency,
                    }
            # 改为直接添加contractDetails的属性__dict__
            # for attr in dir(sd.contractDetails.contract):
            #     if not attr.startswith('_'):
            #         temp[attr] = getattr(sd.contractDetails.contract, attr)
            temp.update(sd.contractDetails.contract.__dict__)
            temp.update(sd.contractDetails.__dict__)

            data.append(temp)
        # print(f'{instrument} {locationCode} {num_scanData}')

        return num_scanData  # 最多返回50支，如果返回50支，说明还有更多，需要调整avgUsdVolumeAbove和avgUsdVolumeBelow，迭代缩小范围，直到返回小于50支

    except Exception as e:
        # print(e)
        return 0


def iter_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow=None, avg=True):
    min_above = avgUsdVolumeAbove
    num_scanData = single_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow, avg=avg)
    while num_scanData == 50:
        avgUsdVolumeAbove = str(int(avgUsdVolumeAbove) * 2)
        num_scanData = single_scanner(instrument, locationCode, avgUsdVolumeAbove, avgUsdVolumeBelow, avg=avg)

    if avgUsdVolumeAbove == min_above:
        return

    # 二分法
    max_below = avgUsdVolumeAbove  # max below
    iter_range = int(max_below) - int(min_above)
    last_range = iter_range // 2

    last_avgUsdVolumeBelow = max_below
    last_avgUsdVolumeAbove = str(int(max_below) - last_range)

    num_scanData = single_scanner(instrument, locationCode, last_avgUsdVolumeAbove, last_avgUsdVolumeBelow, avg=avg)
    print(f'{last_avgUsdVolumeBelow} > {instrument} {locationCode} > {last_avgUsdVolumeAbove} : {num_scanData}')

    while int(last_avgUsdVolumeAbove) >= int(min_above):
        if num_scanData == 50:  # 区间减半，当前区间需要再次扫描：上限不变，下限提升
            last_range //= 2
            last_avgUsdVolumeAbove = str(int(last_avgUsdVolumeAbove) + last_range)
        elif 0 < num_scanData <= 5:  # 区间翻倍，当前区间无需再次扫描：上限下限均降低翻倍后的区间
            last_range *= 2
            last_avgUsdVolumeBelow = last_avgUsdVolumeAbove
            last_avgUsdVolumeAbove = str(max(int(min_above), int(last_avgUsdVolumeAbove) - last_range))
        elif 5 < num_scanData < 50:  # 上限下限均降低原区间
            last_avgUsdVolumeBelow = last_avgUsdVolumeAbove
            last_avgUsdVolumeAbove = str(max(int(min_above), int(last_avgUsdVolumeAbove) - last_range))
        else:
            break

        if last_avgUsdVolumeAbove == last_avgUsdVolumeBelow:
            break
        num_scanData = single_scanner(instrument, locationCode, last_avgUsdVolumeAbove, last_avgUsdVolumeBelow, avg=avg)

        print(f'{last_avgUsdVolumeBelow} > {instrument} {locationCode} > {last_avgUsdVolumeAbove} : {num_scanData}')


'''
data = {"rank": rank, "Symbol": contractDetails.contract.symbol, "MarketName": contractDetails.marketName,
                    "SecType": contractDetails.contract.secType, "Conid": contractDetails.contract.conId,
                    "Currency": contractDetails.contract.currency}
'''
data = []
pair = []

# 合并stk_product和fut_product
stk_etf = stk_product + etf_product

for instrument, locationCode in stk_etf:
    #     print(instrument, locationCode)
    iter_scanner(instrument, locationCode, '100000000')

print(pair)

# fut_ssf = fut_product + ssf_product

for instrument, locationCode in fut_product:
    #     print(instrument, locationCode)
    iter_scanner(instrument, locationCode, '50000000', avg=False)

# print(pair)

print(pair)

import pandas as pd

# 将df以Conid去重，保留第一次出现的行
df = pd.DataFrame(data).drop_duplicates(subset=['conId'], keep='first')
df

# 删除全为nan的列
df = df.dropna(axis=1, how='all')
# 删除全为0的列
df = df.loc[:, (df != 0).any(axis=0)]
df = df.loc[:, (df != 1).any(axis=0)]
# 删除contract,rank列，如果有的话
df = df.drop(columns=['contract', 'rank', 'comboLegs', 'secIdList'], errors='ignore')
# 删除全为''的列
df = df.loc[:, (df != '').any(axis=0)]
df.to_csv('scanner_unique_stketffut.csv')
df

ib.disconnect()

from vnpy.trader.setting import SETTINGS

SETTINGS["database.database"] = "vnpyibhis_vol"
SETTINGS['database.host'] = 'localhost'
SETTINGS['database.user'] = 'root'
SETTINGS['database.password'] = 'p0o9i8u7'
import pandas as pd
from vnpy.trader.object import Interval
from vnpy_ib.ib_gateway import EXCHANGE_IB2VT


from datetime import datetime, timedelta
from time import sleep

from vnpy.trader.object import SubscribeRequest
from vnpy.trader.utility import extract_vt_symbol
import pandas as pd
import pytz
from vnpy.trader.object import HistoryRequest, Interval
from vnpy_ib.ib_gateway import IbGateway
from vnpy.event import EventEngine
from vnpy.trader.database import get_database
from vnpy.trader.engine import MainEngine
import matplotlib.pyplot as plt

#plt.style.use('seaborn-whitegrid')
#plt.rc('figure', autolayout=True)
#plt.rc('axes', labelweight='bold', labelsize='large', titleweight='bold', titlesize=18, titlepad=10)
#plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']  # 用来正常显示中文标签
#plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# mpl.rc('figure', figsize=(12,5), titlesize='xx-large', dpi=110)
# mpl.rc('font', size=10)
from copy import copy

# 定制画布风格
#import matplotlib as mpl

#mpl.style.use('bmh')
from sqlalchemy import create_engine
from urllib.parse import quote_plus as urlquote
# 显示全部列
pd.options.display.max_columns = None
# 行
pd.options.display.max_rows = None

# Define the trading periods in Eastern Time (ET) for US stock market
# US_DAY_START = time(9, 30)  # Regular trading hours start at 9:30 AM ET
# US_DAY_END = time(16, 0)  # Regular trading hours end at 4:00 PM ET

# connect_ib_api_setting = {"TWS地址": "localhost", "TWS端口": 4002, "客户号": 13, "交易账户": "", "查询期权": "否"}
# connect_ib_api_setting = {"TWS地址": "**************", "TWS端口": 4002, "客户号": 12, "交易账户": "", "查询期权": "否"}
connect_ib_tws_setting = {"TWS地址": "localhost", "TWS端口": 7497, "客户号": 20, "交易账户": "", "查询期权": "否"}


# 定义函数（查询历史数据并把历史数据BarData输出到csv），便于使用函数循环下载
# @retry(tries=3, delay=0.3)
def req_download_historydata(engine, database_manager, vt_symbol, start, end, interval=Interval.MINUTE):
    contract = engine.get_contract(vt_symbol)
    if not contract:
        # 打印时间、函数名、msg
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] 找不到合约：{vt_symbol}')
        return

    unsub_req = SubscribeRequest(contract.symbol, contract.exchange)
    engine.get_gateway('IB').api.unsubscribe(unsub_req)
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] 取消订阅：{vt_symbol}')

    historyreq = HistoryRequest(symbol=contract.symbol, exchange=contract.exchange, start=start, end=end,
                                interval=interval)
    bardatalist = engine.query_history(historyreq, contract.gateway_name)
    if bardatalist:  # 取消订阅
        unsub_req = SubscribeRequest(contract.symbol, contract.exchange)
        engine.get_gateway('IB').api.unsubscribe(unsub_req)
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] 取消订阅：{vt_symbol}')
    # 把历史数据BarData输出到csv，路径为当前目录的data文件夹下，文件名为合约名、start的日期、end的日期
    # pd.DataFrame(bardatalist).to_csv('data/' + str(historyreq.symbol) + "_" + str(historyreq.start.date()) + "_" + str(
    #     historyreq.end.date()) + ".csv", index=True, header=True)
    # print(
    #     f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] History data export to CSV: {vt_symbol}')

    import traceback
    # 把历史数据BarData放入数据库
    try:
        database_manager.save_bar_data(bardatalist)
        print(
        f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] History data export to DB: {vt_symbol} interval:{interval.value}')

    except:
        print('[save_bar_data]error')
        print(traceback.format_exc())

def clear_database(database_manager):
    overview = database_manager.get_bar_overview()
    for i in overview:
        database_manager.delete_bar_data(i.symbol, i.exchange, i.interval)
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [clear_database] 数据库清空完成')

def run_child():
    """
    Running in the child process.
    """
    # print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] 当前工作目录:{os.getcwd()}')
    # if not os.path.exists("data"):
    #     os.mkdir("data")
    # 订阅行情
    # 创建事件引擎
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    main_engine.add_gateway(IbGateway)
    # main_engine.connect(connect_ib_api_setting, "IB")
    main_engine.connect(connect_ib_tws_setting, "IB")

    database_manager = get_database()

    # 清空数据库
    clear_database(database_manager)

    # 使用script_engine订阅历史数据是从rqdata获取，vnpy v2.07已经提供历史数据获取，这里创建HistoryRequest来获取,
    # 查询如果没有endtime，默认当前。返回历史数据输出到数据库和csv文件
    # 关于api更多信息可以参见 https://interactivebrokers.github.io/tws-api/historical_bars.html
    print(
        f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] ***从IB读取历史数据, 返回历史数据输出到数据库和csv文件***')

    # 下载昨晚21：30到今天4：00的数据，IB的数据是UTC时间，所以需要转换
    today = datetime.now(pytz.timezone('Asia/Shanghai'))
    yesday = today - timedelta(days=60)
    # yesday = today - relativedelta(years=5)
    start = datetime(yesday.year, yesday.month, yesday.day, 4, 0)
    # start = datetime(yesday.year, yesday.month, yesday.day, 3, 59).astimezone(pytz.timezone('UTC'))
    end = datetime(today.year, today.month, today.day, 4, 0)
    # print(start, end)  # 注意下载的数据是左闭右开，不包含end时间点的数据
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] {start} - {end}')
    # 使用conid作为symbol
    df = pd.read_csv('scanner_unique_stketffut.csv')

    print(f'df.shape:{df.shape}')
    symbol_exchange = [(f'{row["conId"]}', EXCHANGE_IB2VT[row["exchange"]]) for i, row in df.iterrows()]
    # 一次订阅99支，等待get_contract100支都不为空，再同时下载50支；结束后再订阅并下载后100支，依次循环
    trunk = 50
    for i in range(0, len(symbol_exchange), trunk):
        symbol_exchange100 = symbol_exchange[i:i + trunk]
        for conId, exchange in symbol_exchange100:
            req1 = SubscribeRequest(conId, exchange)
            main_engine.subscribe(req1, "IB")
        for i in range(20):
            contracts = [main_engine.get_contract(f'{conId}.{exchange.value}') for conId, exchange in
                         symbol_exchange100]
            if all(contracts):
                break
            sleep(0.3)
        for conId, exchange in symbol_exchange100:
            req_download_historydata(main_engine, database_manager, f'{conId}.{exchange.value}', start, end,
                                     interval=Interval.DAILY)  # print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] vt_symbol:{vt_symbol} 下载完成')

    sleep(30)
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] 关闭子进程')
    main_engine.close()  # sys.exit(0)  # sys.exit(0)表示正常退出程序，sys.exit(1)表示异常退出程序


if __name__ == "__main__":
    # run_parent()
    # 只运行一次
    run_child()

df = pd.read_csv('scanner_unique_stketffut.csv', keep_default_na=False, na_values=[''])
# ,instrument,locationCode,secType,conId,symbol,exchange,currency,localSymbol,marketName
# print(df.head().append(df.tail()))
# df['symbol'] = df['symbol']+'-'+df['currency']+'-'+df['secType']
df


userName = 'root'
password = 'p0o9i8u7'
dbHost = 'localhost'
dbPort = 3306
dbName = 'vnpyibhis_vol'
DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'
engine = create_engine(DB_CONNECT, max_overflow=50,  # 超过连接池大小外最多创建的连接
                       pool_size=50,  # 连接池大小
                       pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错
                       pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
                       # encoding='utf-8',
                       echo=False)


# sql_query2 = "select t.* from vnpyzh.dbbardata t where symbol='fu99' and t.interval='1m'"
# fu = pd.read_sql_query(sql_query2, engine)

# symbol = df['symbol'].iloc[0]
# exchange = df['exchange'].iloc[0]
# sql_query = f"select t.* from vnpyibhis_vol.dbbardata t where symbol='{symbol}' and t.interval='d' and exchange='{exchange}'"
# data = pd.read_sql_query(sql_query, engine, index_col='datetime', parse_dates='datetime')
# data.drop(columns='id', inplace=True)
# data


# 获取单标的
def get_history(symbol, exchange):
    columns = ['symbol', 'exchange', 'datetime', 'volume', 'open_price', 'high_price', 'low_price', 'close_price']
    column_query = ', '.join([f't.{column}' for column in columns])
    day_of_60_days_before = (datetime.now() - timedelta(days=60)).date()
    # sql_query = f"select {column_query} from vnpyibhis_vol.dbbardata t where symbol='{symbol}' and t.interval='d' and exchange='{exchange}'"
    sql_query = f"select {column_query} from vnpyibhis_vol.dbbardata t where symbol='{symbol}' and t.interval='d' and exchange='{exchange}' and t.datetime >= '{day_of_60_days_before}'"
    # sql_query = f"-- select t.* from vnpyibhis_vol.dbbardata t where symbol='{symbol}' and t.interval='d'"# and exchange='{exchange}'"
    data = pd.read_sql_query(sql_query, engine, parse_dates='datetime')
    # 重命名symbol列为conid
    data.rename(columns={'symbol': 'conId'}, inplace=True)
    return data


# 获取所有数据
def get_all_history():
    all_data = pd.DataFrame()
    for i in range(len(df)):
        data = get_history(df['conId'].iloc[i], df['exchange'].iloc[i])
        data['symbol'] = df['symbol'].iloc[i]
        data['currency'] = df['currency'].iloc[i]
        data['secType'] = df['secType'].iloc[i]
        data['localSymbol'] = df['localSymbol'].iloc[i]
        all_data = pd.concat([all_data, data])
    return all_data


his_data = get_all_history()
# 以datetime、symbol为multiindex，并datetime排序
his_data = his_data.set_index(['datetime', 'conId']).sort_index()
his_data

import shelve


def get_shelve():
    with shelve.open(r'.vntrader\ib_contract_data.db') as f:
        contracts_details = f.get("contracts_details", {})
        ib_contracts = f.get("ib_contracts", {})

    # 将所有keys的multiplier取出来，放在DataFrame
    df_shelve = pd.DataFrame()
    for key in ib_contracts.keys():
        symbol, exchange = extract_vt_symbol(key)
        df_shelve.loc[symbol, 'multiplier'] = float(ib_contracts[key].multiplier)
        df_shelve.loc[symbol, 'secType'] = ib_contracts[
            key].secType  # df_shelve.loc[symbol, 'currency'] = ib_contracts[key].currency

    for key in contracts_details.keys():
        symbol, exchange = extract_vt_symbol(key)
        # if symbol.rsplit('-', 1)[-1] == 'STK':
        #     print(symbol)
        df_shelve.loc[symbol, 'longName'] = contracts_details[key].longName

    # 降序
    df_shelve.sort_values(by='multiplier', ascending=False, inplace=True)
    return df_shelve


df_shelve = get_shelve()
df_shelve

all_data = copy(his_data)

all_data['multiplier'] = all_data.index.get_level_values(1).map(df_shelve.multiplier)
all_data['secType'] = all_data.index.get_level_values(1).map(df_shelve.secType)
all_data['longName'] = all_data.index.get_level_values(1).map(df_shelve.longName)
secType_map = {'STK': '股票/ETF', 'CONTFUT': '期货'}
all_data['secType'] = all_data['secType'].map(secType_map)
all_data

# 查看multiplier列的unique值
print(f'multiplier列的unique值：{all_data.multiplier.unique()}')
# 查看currency列的unique值
currencies = all_data.currency.unique()
print(f'currency列的unique值：{currencies}')
# 查看exchange列的unique值
print(f'exchange列的unique值：{all_data.exchange.unique()}')
# 查看每期datetime的symbol数量的集合
print(f'每期datetime的symbol数量的集合：{set(all_data.index.get_level_values(0).value_counts())}')
# 查看每个symbol的datetime数量的集合
print(f'每个symbol的datetime数量的集合：{set(all_data.index.get_level_values(1).value_counts())}')
# 删除symbol的datetime数量少于10的symbol
all_data = all_data.groupby(level=1).filter(lambda x: len(x) >= 20)

# 获取汇率：currencies中货币对USD的汇率
import yfinance as yf

# currency_tickers = [f'{currency}=X' for currency in currencies if currency != 'USD']
currency_tickers = [f'{currency}USD=X' for currency in currencies]
currency_dict = {}
for currency in currency_tickers:
    ticker = yf.Ticker(currency)
    close = ticker.history(period='1d').Close[0]
    print(f'{currency}的最新收盘价：{close}')
    currency_dict[currency] = close
# 增加一列对USD的汇率
all_data['usd_rate'] = all_data.currency.apply(lambda x: currency_dict.get(f'{x}USD=X', 0.0))

# 计算成交额（volume*close_price*multiplier*usd_rate）列
all_data['turnover'] = all_data.volume * all_data.close_price * all_data.multiplier * all_data.usd_rate
# 删除currency列
# all_data.drop(columns='currency',inplace=True)
# 以symbol分组，计算每个symbol的日均成交额（10天）
# all_data['turnover_10d'] = all_data.groupby(level=1).turnover.rolling(10).mean().values

# 以期分组，计算每个标的的成交额/当期最大成交额的比例，放在turnover_ratio列
all_data['turnover_ratio'] = all_data.groupby(level=0).turnover.apply(lambda x: x / x.max())
# all_data['turnover_ratio'] = all_data.groupby(level=0).turnover_10d.apply(lambda x: x/x.max())
all_data

# 先切换索引顺序：symbol、datetime
# all_data = all_data.swaplevel().sort_index()
# import importlib
# importlib.reload(sys.modules['volatility'])
from volatility import simple_vol

# 以symbol分组，计算每个symbol的波动率（simple_vol, 10天）
# all_data['simple_vol_10d'] = all_data.groupby(level=1).apply(lambda x: simple_vol(x, 10,clean=False)).values# na以0填充

# 切换索引顺序：symbol、datetime
all_data = all_data.swaplevel().sort_index()
# 以symbol分组，计算每个symbol的波动率（simple_vol, 10天）
all_data['simple_vol_10d'] = all_data.groupby(level=0).apply(lambda x: simple_vol(x, 10, clean=False)).values  # na以0填充
# 再切换索引顺序：datetime、symbol
all_data = all_data.swaplevel().sort_index()
all_data.fillna(0, inplace=True)
# 计算vol_ratio
all_data['vol_ratio'] = all_data.groupby(level=0).simple_vol_10d.apply(lambda x: x / x.max())
# 加权两个ratio，放在ratio列
all_data['ratio'] = (all_data.turnover_ratio + all_data.vol_ratio) / 2
all_data

temp = all_data.copy()
temp.reset_index(inplace=True)
# symbol、currency、secType、exchange相同，可能conId不同，只保留最新一期ratio最大的那组对应的conId的数据
# 先查看相同symbol、currency、secType、exchange而存在conId不同的对应关系，保留最新datetime的数据
temp = temp.set_index(['symbol', 'currency', 'secType', 'exchange', 'conId']).sort_values('datetime')
temp = temp.groupby(level=[0, 1, 2, 3, 4]).last()
temp.reset_index(inplace=True)
# 选出turnover最大的那组对应的conId，记录被删除的conId，在all_data中删除
all_conId = temp.conId.unique()
temp = temp.loc[temp.groupby(['symbol', 'currency', 'secType', 'exchange']).turnover.idxmax()]
max_conId = temp.conId.unique()
delete_conId = list(set(all_conId) - set(max_conId))
print(f'被删除的conId：{delete_conId}')

temp = all_data.copy()
temp.reset_index(inplace=True)
# symbol、currency、secType、exchange相同，可能conId不同，只保留最新一期ratio最大的那组对应的conId的数据
# 先查看相同symbol、currency、secType、exchange而存在conId不同的对应关系，保留最新datetime的数据
temp = temp.set_index(['symbol', 'currency', 'secType', 'exchange', 'conId']).sort_values('datetime')
temp = temp.groupby(level=[0, 1, 2, 3, 4]).last()
temp.reset_index(inplace=True)

temp_different_month = pd.DataFrame()
temp_different_multiplier = pd.DataFrame()
# 查看delete_conId第i个conId所在'symbol', 'currency', 'secType', 'exchange'的数据
for i in delete_conId:
    symbol = temp.loc[temp.conId == i].symbol.iloc[0]
    currency = temp.loc[temp.conId == i].currency.iloc[0]
    secType = temp.loc[temp.conId == i].secType.iloc[0]
    exchange = temp.loc[temp.conId == i].exchange.iloc[0]
    temp_ = temp[
        (temp.symbol == symbol) & (temp.currency == currency) & (temp.secType == secType) & (temp.exchange == exchange)]
    # temp_all = pd.concat([temp_all, temp_])
    # 检查temp_的各行的multiplier列
    # 如果不同，放在temp_different_multiplier
    if len(temp_.multiplier.unique()) > 1:
        temp_different_multiplier = pd.concat([temp_different_multiplier, temp_])  # 否则放在temp_different_month
    else:
        temp_different_month = pd.concat([temp_different_month, temp_])

temp_different_month

temp_different_multiplier

# 删除all_data中的delete_conId
all_data = all_data[~all_data.index.get_level_values(1).isin(delete_conId)]
all_data

# 每期只保留ratio前500的数据
# all_data = all_data.groupby(level=0).apply(lambda x: x.nlargest(500, 'ratio'))# 会导致index重复，不需要groupby
all_data = all_data.groupby(level=0).apply(lambda x: x.nlargest(500, 'ratio')).reset_index(level=0, drop=True)
# 删除open_price、high_price、low_price列
all_data.drop(columns=['open_price', 'high_price', 'low_price'], inplace=True)
# 将longName列放在第一列（不包括索引）
cols = all_data.columns.tolist()
# cols = ['conId'] + cols[:cols.index('conId')] + cols[cols.index('conId')+1:]
cols = ['symbol'] + cols[:cols.index('symbol')] + cols[cols.index('symbol') + 1:]
cols = ['longName'] + cols[:cols.index('longName')] + cols[cols.index('longName') + 1:]
cols = ['secType'] + cols[:cols.index('secType')] + cols[cols.index('secType') + 1:]
all_data = all_data[cols]
all_data

latest_data = copy(all_data)
# 只保留2024-04-18及之前的数据
# latest_data = latest_data[latest_data.index.get_level_values(0) <= datetime(2024, 5, 13)]
# 删除任意单元格有0的行
# latest_data = latest_data[(latest_data != 0).all(1)]# all(1)表示行，all(0)表示列
# 删除任意单元格有0的行（除了secType列）
latest_data = latest_data[(latest_data.drop(columns='secType') != 0).all(1)]

# 以datetime分组，删除symbol数量不足500的组
latest_data = latest_data.groupby(level=0).filter(lambda x: len(x) >= 500)

# 格式处理:turnover除以10e8后以亿美元为单位保留2位小数，列名重命名为turnover（单位：亿美元）；turnover_ratio、simple_vol_10d、vol_ratio、ratio保留4位小数
latest_data['turnover'] = (latest_data.turnover / 1e8).round(2)
# latest_data['turnover_10d'] = (latest_data.turnover_10d/1e8).round(2)
latest_data.rename(columns={'turnover': 'turnover（单位：亿美元）'}, inplace=True)
# latest_data.rename(columns={'turnover':'turnover（亿美元）', 'turnover_10d':'turnover_10d（亿美元）'}, inplace=True)
latest_data['turnover_ratio'] = latest_data.turnover_ratio.round(4)
latest_data['simple_vol_10d'] = latest_data.simple_vol_10d.round(4)
latest_data['vol_ratio'] = latest_data.vol_ratio.round(4)
latest_data['ratio'] = latest_data.ratio.round(4)
# secType为0的值以'期货'填充
latest_data['secType'] = latest_data.secType.replace(0, '期货')

# 最新日期
latest_date = latest_data.index.get_level_values(0).max()
print(f'最新日期：{latest_date}')
# 只导出最新日期的数据
latest_data = latest_data.loc[latest_date]
# 移除索引
latest_data.reset_index(inplace=True)

# latest_data['symbol'] = latest_data.symbol.str.rsplit('-', 1).str[0].str[:-4]

# latest_data.to_excel(f'{latest_date.date()}.xlsx')
# 不要索引列
latest_data.to_excel(f'{latest_date.date()}.xlsx', index=False)
latest_data
