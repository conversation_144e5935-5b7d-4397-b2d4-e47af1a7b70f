import requests
import json
import os, sys

headers = {
    "referer": "https://www.interactivebrokers.com/cn/trading/products-exchanges.php",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
}
url = "https://www.interactivebrokers.com/webrest/search/products-by-exchangeId"
data = {
    "domain": "com",
    "exchangeId": "NYSE", # NASDAQ AMEX
    "pageNumber": 1,
    "pageSize": 100,
    "sortDirection": "asc",
    "sortField": "symbol"
}
data = json.dumps(data, separators=(',', ':'))

file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.proxy_utils import get_system_proxy
proxies=get_system_proxy()
print(f'proxies: {proxies}')
response = requests.post(url, headers=headers, data=data, proxies=proxies)

print(response.text)
print(response)