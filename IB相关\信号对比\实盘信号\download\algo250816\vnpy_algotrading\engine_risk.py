import sys, re
from time import sleep
from typing import Dict, Type
from vnpy.event import Event, EventEngine
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.object import LogData
from .database import Todo
from .base import EVENT_ALGO_LOG
from vnpy.trader.utility import load_json, save_json
from .template_risk import RiskPlugin

APP_NAME = "AlgoRiskManager"

class AlgoRiskEngine(BaseEngine):
    """算法交易风控引擎"""
    setting_filename: str = "algo_risk_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        super().__init__(main_engine, event_engine, APP_NAME)

        self.plugin_count: int = 0
        self.plugins: Dict[str, Type[RiskPlugin]] = {}
        self.plugin_instances: Dict[str, "RiskPlugin"] = {}
        self.balance_retry_count: int = 5  # 获取账户余额重试次数

        self.load_setting()

        # 加载风控插件
        self.load_risk_template()

    def load_setting(self) -> None:
        # 加载风控配置
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "enabled_plugins": ["SymbolRestriction", "OrderValueLimit"]
            }
            save_json(self.setting_filename, self.setting)

        # 重新加载所有已启动的风控插件配置
        for plugin in self.plugin_instances.values():
            plugin.load_setting()

    def load_risk_template(self) -> None:
        """载入风控插件"""
        from .internal_risk_plugins.order_value_limit import OrderValueLimit
        from .internal_risk_plugins.symbol_restriction import SymbolRestriction

        # 添加所有可用的风控插件
        self.add_risk_template(OrderValueLimit)
        self.add_risk_template(SymbolRestriction)

        self.write_log(f"风控插件加载完成，共加载{self.plugin_count}个插件")

    def add_risk_template(self, template: Type[RiskPlugin]) -> None:
        """添加风控插件"""
        # 记录插件类
        self.plugins[template.__name__] = template

        # 如果插件在启用列表中，则创建实例 
        if template.__name__ in self.setting["enabled_plugins"]:
            instance = template(self)
            self.plugin_instances[template.__name__] = instance
            self.plugin_count += 1
            self.write_log(f"风控插件加载成功:{template.__name__}")

    def check_risk(self, todo: Todo) -> bool:
        """执行风控检查"""
        for name, plugin in self.plugin_instances.items():
            if not plugin.check_risk(todo):
                self.write_log(f"风控插件[{name}]拦截订单")
                return False

        return True

    def get_balance(self, gateway_name: str = "") -> float:
        """获取账户权益"""
        if not gateway_name:
            gateway_names = [name for name in self.main_engine.gateways.keys() if name not in ("RPC", "quote")]
            if not gateway_names:
                self.write_log("没有可用的网关")
                return 0
            gateway_name = gateway_names[0]

        # 获取vt_accountid
        vt_accountid = f"{gateway_name}.USD"
        if not vt_accountid:
            return 0

        # 获取账户信息
        account = self.main_engine.get_account(vt_accountid)
        if not account:
            all_accounts = self.main_engine.get_all_accounts()
            if all_accounts:
                for account in all_accounts:
                    if account.vt_accountid.endswith(".USD") and account.gateway_name == gateway_name:
                        self.write_log(f"所有账户：{all_accounts}，风控使用{account.vt_accountid}")
                        return account.balance
            else:
                if self.balance_retry_count <= 0:
                    msg = "error 获取账户余额超过最大重试次数"
                    self.write_log(msg)
                    # raise RuntimeError(msg)
                    return 0
                    
                self.balance_retry_count -= 1
                self.write_log(f"获取账户余额重试剩余次数:{self.balance_retry_count}")
                sleep(1)
                return self.get_balance()

        return account.balance

    def write_log(self, msg: str, need_format: bool = True) -> None:
        """输出日志"""
        if need_format:
            func_name = sys._getframe(1).f_code.co_name
            class_name = self.__class__.__name__
            formatted_msg = f"[{class_name}.{func_name}] {msg}"
        else:
            formatted_msg: str = msg


        log: LogData = LogData(msg=formatted_msg, gateway_name=APP_NAME)
        event: Event = Event(EVENT_ALGO_LOG, data=log)
        self.event_engine.put(event)
