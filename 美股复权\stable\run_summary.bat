@echo off
setlocal enabledelayedexpansion

cd /d "E:\git\IntervalTools\美股复权\stable"

set offset=0
set python_path=S:\Envs\cta\python.exe
set script_name=stk_data_summary_Semaphore.py

:: 创建日期数组
set dates[0]=2024-06-01
set dates[1]=2024-07-01
set dates[2]=2024-08-01
set dates[3]=2024-09-01
set dates[4]=2024-10-01
set dates[5]=2024-11-01
set dates[6]=2024-12-01
set dates[7]=2025-01-01
set dates[8]=2025-02-01
set dates[9]=2025-03-01
set dates[10]=2025-04-01
set dates[11]=2025-05-01
set dates[12]=2025-06-01
set dates[13]=2025-07-01
set dates[14]=2025-08-01
set dates[15]=2025-08-15

:: 循环执行命令
for /L %%i in (0,1,15) do (
    %python_path% .\%script_name% --end_date !dates[%%i]! -f -s periods -o !offset!
    timeout /t 1 /nobreak >nul
    set /a offset+=1
)

echo All tasks completed.
pause