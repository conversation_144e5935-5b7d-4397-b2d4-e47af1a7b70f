# 北京时间 9.27 6    = utc时间 9.26 22

# 将UTC时间2023-09-26 22:00:00 转换为上海时间
import pytz
from datetime import datetime
utc_dt = datetime(2023, 9, 26, 22, 0, 0, tzinfo=pytz.utc)
print(f'utc_dt: {utc_dt}')
bj_dt = utc_dt.astimezone(pytz.timezone('Asia/Shanghai'))
print(f'bj_dt: {bj_dt}')
# 美中时间
uscentral_dt = utc_dt.astimezone(pytz.timezone('US/Central'))
print(f'uscentral_dt: {uscentral_dt}')
# 美东时间
useastern_dt = utc_dt.astimezone(pytz.timezone('US/Eastern'))
print(f'useastern_dt: {useastern_dt}')