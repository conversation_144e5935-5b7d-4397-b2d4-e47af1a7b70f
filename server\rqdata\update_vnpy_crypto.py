# # 获取指定数据服务实例
# from vnpy_rqdata.rqdata_datafeed import RqdataDatafeed
# from vnpy_mysql.mysql_database import MysqlDatabase
# datafeed = RqdataDatafeed()
# database = MysqlDatabase()
from vnpy_binance.binance_spot_gateway import INTERVAL_VT2BINANCE, TIMEDELTA_MAP, UTC_TZ  # , generate_datetime
from vnpy_evo.trader.setting import SETTINGS

SETTINGS["database.database"] = "vnpy_crypto"
SETTINGS["database.name"] = "mysql"
# # SETTINGS["database.host"] = "*************"
# SETTINGS["database.host"] = "*************"
# SETTINGS["database.port"] = 3308
# SETTINGS["database.user"] = "crypto"
# SETTINGS["database.password"] = "zhP@c4oword"

SETTINGS["database.host"] = "127.0.0.1"
SETTINGS["database.port"] = 3306
SETTINGS["database.user"] = "root"
SETTINGS["database.password"] = "p0o9i8u7"

from vnpy_evo.trader.database import get_database, DB_TZ

database = get_database()

# from vnpy_evo.trader.datafeed import get_datafeed
# datafeed = get_datafeed()
from vnpy_binance import BinanceSpotGateway
from datetime import datetime
from time import sleep

from vnpy_evo.trader.constant import (Interval)
from vnpy_evo.trader.object import (BarData)

from vnpy_rest import RestClient, Response

# 设置合约品种
symbols = {
    # "SHFE": ["AG", "AL", "AU", "BU", "CU", "FU", "HC", "LU", "NI", "NR", "PB", "RB", "RU", "SC", "SN", "SP", "SS", "WR",
    #          "ZN"],
    # "DCE": ["A","B","BB","C","CS","EG","EB","FB","I","J","JD","JM","L","LH","M","P","PG","PP","RR","V","Y"],
    # "CZCE": ["AP","CF","CJ","CY","FG","JR","LR","MA","OI","PF","PM","RI","RM","RS","SA","SF","SM","SR","TA","UR","WH","ZC"],
    # "CFFEX": ["IC", "IF", "IH", "T", "TF", "TS"],
    # "SSE": ["600941", "601186", "600233", "601878", "601666", "603087", "603737", "600320", "600808", "605090",
    #         "600975", "603013", "603367", "603328", "600400", "600897", "603360", "603098", "603275", "603666",
    #         "603669", "603190", "600706", "600892", "605169", "605288", "603326", "603280", "600241", "600243"],
    # "SZSE": ["002352", "000157", "000513", "002850", "000830", "002698", "001323", "002506", "000521", "001301",
    #          "002755", "000980", "002204", "002038", "000917", "000755", "003000", "002990", "002344", "002918",
    #          "002845", "002520", "000713", "000151", "001217", "002940", "002412", "002976", "002213", "003036",
    #          "002357", "002529", "000953"],
    "BINANCE": ['BTC', 'ETH', 'BNB', 'SOL', 'XRP', 'DOGE', 'ADA', 'AVAX', 'SHIB', 'BCH', 'DOT', 'WBTC', 'TRX',
                'LINK', 'MATIC', 'LTC', 'ICP', 'NEAR', 'DAI', 'UNI', 'ETC', 'APT', 'FDUSD', 'STX', 'ATOM', 'FIL', 'TAO',
                'PEPE', 'NOT']
}
spot = [i + '-SPOT' for i in symbols['BINANCE'] if i not in ['DAI']]
swap = [i + '-SWAP' for i in symbols['BINANCE'] if i not in ['PEPE', 'SHIB', 'DAI', 'FDUSD', 'WBTC']]
symbols['BINANCE'] = spot + swap

# 设置下载时间段
start_date = datetime(2023, 7, 1, tzinfo=DB_TZ)
# start_date = datetime(2024, 5, 17, tzinfo=DB_TZ)
end_date = datetime.now().astimezone(DB_TZ)

# interval = Interval.HOUR
interval = Interval.MINUTE

from datetime import datetime

from vnpy_evo.trader.constant import Exchange
from vnpy_evo.trader.object import HistoryRequest


class BinanceSpotRestAPi(RestClient):
    gateway_name = BinanceSpotGateway.default_name

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        if '-SPOT' in req.symbol:
            path = "/api/v3/klines"
        elif '-SWAP' in req.symbol:
            path = "/fapi/v1/klines"
        else:
            raise ValueError(f"Unknown type: {req.symbol}")

        """Query kline history data"""
        history: list[BarData] = []
        limit: int = 1000
        start_time: int = int(datetime.timestamp(req.start))

        while True:
            # Create query parameters
            usdt_symbol = req.symbol.replace('-SPOT', 'USDT').replace('-SWAP', 'USDT').upper()
            params: dict = {"symbol": usdt_symbol, "interval": INTERVAL_VT2BINANCE[req.interval], "limit": limit,
                            "startTime": start_time * 1000}

            if req.end:
                end_time: int = int(datetime.timestamp(req.end))
                params["endTime"] = end_time * 1000  # Convert to milliseconds

            resp: Response = self.request("GET", path, params=params)

            # Break the loop if request failed
            if resp.status_code // 100 != 2:
                msg: str = f"Query kline history failed, status code: {resp.status_code}, message: {resp.text}, symbol: {req.symbol}, usdt_symbol: {usdt_symbol}"
                # self.gateway.write_log(msg)
                print(msg)
                break
            else:
                data: dict = resp.json()
                if not data:
                    msg: str = f"No kline history data is received, start time: {start_time}, symbol: {req.symbol}"
                    # self.gateway.write_log(msg)
                    print(msg)
                    break

                buf: list[BarData] = []

                for row in data:
                    bar: BarData = BarData(symbol=req.symbol, exchange=req.exchange, datetime=generate_datetime(row[0]),
                                           interval=req.interval, volume=float(row[5]), turnover=float(row[7]),
                                           open_price=float(row[1]), high_price=float(row[2]), low_price=float(row[3]),
                                           close_price=float(row[4]), gateway_name=self.gateway_name)
                    buf.append(bar)

                history.extend(buf)

                begin: datetime = buf[0].datetime
                end: datetime = buf[-1].datetime
                msg: str = f"Query kline history finished, {req.symbol} - {req.interval.value}, {begin} - {end}\n\n"
                # self.gateway.write_log(msg)
                print(msg)

                # Break the loop if the latest data received
                if len(data) < limit:
                    break

                # Update query start time
                start_dt = bar.datetime + TIMEDELTA_MAP[req.interval]
                start_time = int(datetime.timestamp(start_dt))

            # Wait to meet request flow limit
            sleep(0.3)

        # Remove the unclosed kline
        if history:
            history.pop(-1)

        return history


def generate_datetime(timestamp: float) -> datetime:
    """Generate datetime object from Binance timestamp"""
    dt: datetime = datetime.fromtimestamp(timestamp / 1000, tz=UTC_TZ)
    return dt


# 批量下载
def query_save_data(req):
    # data = datafeed.query_bar_history(req)
    api = BinanceSpotRestAPi()
    api.init("https://www.binance.com")
    api.start()
    data = api.query_history(req)

    database.save_bar_data(data)
    print(f"{req.symbol}历史数据下载完成")

    api.stop()


def download_data(symbol_type="88"):
    bar_overview = database.get_bar_overview()
    overview_symbols = [(d.symbol, d.exchange.value, d.interval) for d in bar_overview]

    symbols_to_download = []
    for exchange, symbols_list in symbols.items():
        for symbol in symbols_list:
            if (symbol + symbol_type, exchange, interval) not in overview_symbols:
                symbols_to_download.append((symbol + symbol_type, Exchange(exchange), interval))
    print(f'symbols_to_download:{symbols_to_download}')

    for symbol, exchange, _ in symbols_to_download:
        req = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            start=start_date.astimezone(UTC_TZ),
            interval=interval,
            end=end_date.astimezone(UTC_TZ),
        )
        query_save_data(req)


# download_data()

# 增量更新
def update_data():
    end = datetime.now().astimezone(DB_TZ)
    data = database.get_bar_overview()
    for d in data:
        symbol = d.symbol
        exchange = d.exchange
        start = d.end.replace(tzinfo=DB_TZ)

        req = HistoryRequest(symbol=symbol, exchange=exchange, start=start, interval=interval, end=end, )
        query_save_data(req=req)

def fill_missing_intervals():
    '''
    根据omission interval.xlsx文件，找出缺失的时间间隔，然后补充数据。

    # mysql
    SELECT
        symbol,
        datetime,
        prev_datetime
    FROM (
        SELECT
            symbol,
            datetime,
            LAG(datetime) OVER (PARTITION BY symbol ORDER BY datetime) AS prev_datetime
        FROM
            vnpy_crypto.dbbardata
    ) AS subquery
    WHERE
        datetime > prev_datetime + INTERVAL 1 MINUTE;

    # omission interval.xlsx
    symbol	datetime	prev_datetime
    ADA-SPOT	2024/5/22 15:59	2024/5/22 7:59
    ADA-SPOT	2024/5/23 7:59	2024/5/22 23:59
    ADA-SPOT	2024/5/24 7:59	2024/5/23 23:59
    ADA-SPOT	2024/5/25 16:14	2024/5/25 8:14
    ADA-SPOT	2024/5/26 7:59	2024/5/25 23:59
    ADA-SPOT	2024/5/27 7:59	2024/5/26 23:59
    ADA-SPOT	2024/5/28 7:59	2024/5/27 23:59
    '''
    import pandas as pd
    df = pd.read_excel('omission interval.xlsx')
    for i in range(len(df)):
        symbol = df.loc[i, 'symbol']
        # datetime = df.loc[i, 'datetime']
        # datetime要加一个interval单位，因为history.pop(-1)
        datetime = pd.to_datetime(df.loc[i, 'datetime']) + pd.Timedelta(minutes=1)
        prev_datetime = df.loc[i, 'prev_datetime']
        req = HistoryRequest(
            symbol=symbol,
            exchange=Exchange.BINANCE,
            start=pd.to_datetime(prev_datetime).tz_localize(DB_TZ),
            interval=Interval.MINUTE,
            end=datetime.tz_localize(DB_TZ),
        )
        query_save_data(req)
        print(f"{symbol}历史数据补漏区间：{prev_datetime} - {datetime}")


def main():
    # download_data(symbol_type="")
    # update_data()
    fill_missing_intervals()


if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()

        # # 每日定时自动更新
        # current_time = datetime.now().time()
        # start_time = time(17, 0, 0)  # 每天17:00开始更新
        #
        # while True:
        #     sleep(60)  # 每分钟检查一次
        #     if current_time == start_time:
        #         # download_data()
        #         update_data()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(e)

        from send_to_wechat import WeChat

        wx = WeChat()
        import requests
        ip = requests.get('https://ifconfig.me').text
        wx.send_data(f"{ip}:{__file__}: An error occurred! ", touser='liaoyuan')
