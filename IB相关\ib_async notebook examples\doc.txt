Historical BarsCopy Location
Historical Bar data returns a candlestick value based on the requested duration and bar size. This will always return an open, high, low, and close values. Based on which whatToShow value is used, you may also receive volume data. See the whatToShow section for more details.

Requesting Historical BarsCopy Location
EClient.reqHistoricalData(
reqId: int, A unique identifier which will serve to identify the incoming data.

contract: Contract, The IBApi.Contract object you are working with.

endDateTime: String, The request’s end date and time. This should be formatted as “YYYYMMDD HH:mm:ss TMZ” or an empty string indicates current present moment).
Please be aware that endDateTime must be left as an empty string when requesting continuous futures contracts.

durationStr: String, The amount of time (or Valid Duration String units) to go back from the request’s given end date and time.

barSizeSetting: String, The data’s granularity or Valid Bar Sizes

whatToShow: String, The type of data to retrieve. See Historical Data Types

useRTH: bool, Whether (1) or not (0) to retrieve data generated only within Regular Trading Hours (RTH)

formatDate: bool, The format in which the incoming bars’ date should be presented. Note that for day bars, only yyyyMMdd format is available.

keepUpToDate: bool, Whether a subscription is made to return updates of unfinished real time bars as they are available (True), or all data is returned on a one-time basis (False). If True, and endDateTime cannot be specified.
Supported whatToShow values: Trades, Midpoint, Bid, Ask.

chartOptions: TagValueList, This is a field used exclusively for internal use.

)

Python
Java
C++
C#
VB.NET
self.reqHistoricalData(4102, contract, queryTime, "1 M", "1 day", "MIDPOINT", 1, 1, False, [])
 

DurationCopy Location
The Interactive Brokers Historical Market Data maintains a duration parameter which specifies the overall length of time that data can be collected. The duration specified will derive the bars of data that can then be collected.

Valid Duration String Units:
Unit	Description
S	Seconds
D	Day
W	Week
M	Month
Y	Year
 

Historical Bar SizesCopy Location
Bar sizes dictate the data returned by historical bar requests. The bar size will dictate the scale over which the OHLC/V is returned to the API.

Valid Bar Sizes:
Bar Unit	Bar Sizes
secs	1, 5, 10, 15, 30
mins	1, 2, 3, 5, 10, 15, 20, 30
hours	1, 2, 3, 4, 8
day	1
weeks	1
months	1
 

Step SizesCopy Location
The functionality of market data requests are predicated on preset step sizes. As such, not all bar sizes will work with all duration values. The table listed here will discuss the smallest to largest bar size value for each duration string.

Duration Unit	Bar units allowed	Bar size Interval (Min/Max)
S	secs | mins	1 secs -> 1mins
D	secs | mins | hrs	5 secs -> 1 hours
W	sec | mins | hrs	10 secs -> 4 hrs
M	sec | mins | hrs	30 secs -> 8 hrs
Y	mins | hrs   | d	1 mins-> 1 day
Max Duration Per Bar SizeCopy Location
The table below displays the maximum duration values allowed for a given bar.

As an example, the maximum duration for Seconds values supported for 5 seconds bars are 86400 S. This means that if I want to retrieve more than 1 day’s worth of 5 second bars, I will then need to request data in increments of D (days).

Bar Size	Max Second Duration	Max Day Duration	Max Week Duration	Max Month Duration	Max Year Duration
1 secs	2000 S	{Not Supported}	{Not Supported}	{Not Supported}	{Not Supported}
5 secs	86400 S	365 D	52 W	12 M	68 Y
10 secs	86400 S	365 D	52 W	12 M	68 Y
15 secs	86400 S	365 D	52 W	12 M	68 Y
30 secs	86400 S	365 D	52 W	12 M	68 Y
1 min	86400 S	365 D	52 W	12 M	68 Y
2 mins	86400 S	365 D	52 W	12 M	68 Y
3 mins	86400 S	365 D	52 W	12 M	68 Y
5 mins	86400 S	365 D	52 W	12 M	68 Y
10 mins	86400 S	365 D	52 W	12 M	68 Y
15 mins	86400 S	365 D	52 W	12 M	68 Y
20 mins	86400 S	365 D	52 W	12 M	68 Y
30 mins	86400 S	365 D	52 W	12 M	68 Y
1 hour	86400 S	365 D	52 W	12 M	68 Y
2 hours	86400 S	365 D	52 W	12 M	68 Y
3 hours	86400 S	365 D	52 W	12 M	68 Y
4 hours	86400 S	365 D	52 W	12 M	68 Y
8 hours	86400 S	365 D	52 W	12 M	68 Y
1 day	86400 S	365 D	52 W	12 M	68 Y
1M	86400 S	365 D	52 W	12 M	68 Y
1W	86400 S	365 D	52 W	12 M	68 Y
Format Date ReceivedCopy Location
Interactive Brokers will return historical market data based on the format set from the request. The formatDate parameter can be provided an integer value to indicate how data should be returned.

Note: Day bars will only return dates in the yyyyMMdd format. Time data is not available.

Value	Description	Example
1	String Time Zone Date	“20231019 16:11:48 America/New_York”
2	Epoch Date	1697746308
3	Day & Time Date	“1019 16:11:48 America/New_York”
Keep Up To DateCopy Location
When using keepUpToDate=True for historical data requests, you will see several bars returned with the same timestamp. This is because data is updated approximately every 1-2 seconds. These updates compound until the end of the specified bar size.

In our example to the below, 15 second bars are requested, and we can see the 30 second bar built out incrementally until 20231204 13:30:30 is completed. At which point, we move on to the 45th second bars. This same logic extends into minute, hourly, or daily bars.

Note:
keepUpToDate is only available for whatToShow: Trades, Midpoint, Bid, Ask

Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.55
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.55
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.55
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.55
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.55
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.56
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.56, Low: 188.54, Close: 188.56
Date: 20231204 13:30:30 US/Eastern, Open: 188.56, High: 188.57, Low: 188.54, Close: 188.55
Date: 20231204 13:30:45 US/Eastern, Open: 188.54, High: 188.54, Low: 188.54, Close: 188.54
 

Receiving Historical BarsCopy Location
EWrapper.historicalData (
reqId: int. Request identifier used to track data.

bar: Bar. The OHLC historical data Bar. The time zone of the bar is the time zone chosen on the TWS login screen. Smallest bar size is 1 second.
)

The historical data will be delivered via the EWrapper.historicalData method in the form of candlesticks. The time zone of returned bars is the time zone chosen in TWS on the login screen.

Python
Java
C++
C#
VB.NET
def historicalData(self, reqId:int, bar: BarData):
  print("HistoricalData. ReqId:", reqId, "BarData.", bar)
 

Default Return Format
The text on the right is the default formatting for returning data.

The datetime value here was modified to return UTC datetime formatting.

Note: The datetime value indicates the beginning of the request range rather than the end. The last bar on the right would then indicate data that took place between 20241111-16:53:15 to 20241111-16:53:20.

Date: 20241111-16:53:00, Open: 222.97, High: 222.97, Low: 222.96, Close: 222.97, Volume: 300, WAP: 222.965, BarCount: 2
Date: 20241111-16:53:05, Open: 222.97, High: 223.01, Low: 222.96, Close: 223.01, Volume: 5378, WAP: 222.981, BarCount: 38
Date: 20241111-16:53:10, Open: 223.02, High: 223.02, Low: 222.98, Close: 222.98, Volume: 3659, WAP: 222.997, BarCount: 24
Date: 20241111-16:53:15, Open: 222.98, High: 222.98, Low: 222.96, Close: 222.97, Volume: 2585, WAP: 222.963, BarCount: 24
EWrapper.historicalSchedule (
reqId: int. Request identifier used to track data.

startDateTime: String. Returns the start date and time of the historical schedule range.

endDateTime: String. Returns the end date and time of the historical schedule range.

timeZone: String. Returns the time zone referenced by the schedule.

sessions: HistoricalSession[]. Returns the full block of historical schedule data for the duration.
)

In the case of whatToShow=”schedule”, you will need to also define the EWrapper.historicalSchedule value. This is a unique method that will only be called in the case of the unique whatToShow value to display calendar information.

Python
Java
C++
C#
VB.NET
def historicalSchedule(self, reqId: int, startDateTime: str, endDateTime: str, timeZone: str, sessions: ListOfHistoricalSessions):
  print("HistoricalSchedule. ReqId:", reqId, "Start:", startDateTime, "End:", endDateTime, "TimeZone:", timeZone)
  for session in sessions:
    print("\tSession. Start:", session.startDateTime, "End:", session.endDateTime, "Ref Date:", session.refDate)
 

EWrapper.historicalDataUpdate (
reqId: int. Request identifier used to track data.

bar: Bar. The OHLC historical data Bar. The time zone of the bar is the time zone chosen on the TWS login screen. Smallest bar size is 1 second.
)

Receives bars in real time if keepUpToDate is set as True in reqHistoricalData. Similar to realTimeBars function, except returned data is a composite of historical data and real time data that is equivalent to TWS chart functionality to keep charts up to date. Returned bars are successfully updated using real time data.

Python
Java
C++
C#
VB.NET
def historicalDataUpdate(self, reqId: int, bar: BarData):
  print("HistoricalDataUpdate. ReqId:", reqId, "BarData.", bar)
 

EWrapper.historicalDataEnd (
reqId: int. Request identifier used to track data.

start: String. Returns the starting time of the first historical data bar.

end: String. Returns the end time of the last historical data bar.
)

Marks the ending of the historical bars reception.

Python
Java
C++
C#
VB.NET
def historicalDataEnd(self, reqId: int, start: str, end: str):
  print("HistoricalDataEnd. ReqId:", reqId, "from", start, "to", end)
 

Historical Bar whatToShowCopy Location
The historical bar types listed below can be used as the whatToShow value for historical bars. These values are used to request different data such as Trades, Midpoint, Bid_Ask data and more. Some bar types support more products than others. Please note the Supported Products section for each bar type below.