{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-08-23T02:23:59.734267400Z", "start_time": "2023-08-23T02:23:59.663266200Z"}}, "source": ["from datetime import datetime, time, timedelta\n", "\n", "import pytz\n", "\n", "\n", "APP_NAME = \"BarGenEngine\"\n", "EVENT_BAR = \"eBarGen.\"\n", "EVENT_BAR_RECORD = \"eBarGenRec.\"\n", "\n", "eastern = pytz.timezone('US/Eastern')\n", "shanghai = pytz.timezone('Asia/Shanghai')"], "outputs": []}, {"cell_type": "code", "execution_count": 2, "source": ["# 打印三种时间\n", "print(\"UTC: \", datetime.utcnow())\n", "print(\"Local: \", datetime.now())\n", "print(\"Shanghai: \", datetime.now(shanghai))\n", "print(\"Eastern: \", datetime.now(eastern))"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T02:24:16.855476100Z", "start_time": "2023-08-23T02:24:16.840477200Z"}}, "id": "c7e357ef8438c49f", "outputs": []}, {"cell_type": "code", "execution_count": 4, "source": ["# 生成时间\n", "dt = datetime(2018, 1, 1, 12, 30, 15)\n", "print(dt)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T02:24:36.557652800Z", "start_time": "2023-08-23T02:24:36.527650800Z"}}, "id": "dfded3dfe4c3f66c", "outputs": []}, {"cell_type": "code", "execution_count": 6, "source": ["# 测试dt.time是否大于datetime.time(12, 30)\n", "print(dt.time())\n", "print(dt.time() > time(12, 30))"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T02:25:17.505879800Z", "start_time": "2023-08-23T02:25:17.489878700Z"}}, "id": "adeea0dfba42ef26", "outputs": []}, {"cell_type": "code", "execution_count": 1, "source": ["from enum import Enum\n", "\n", "\n", "class Color(Enum):\n", "    RED = 1\n", "    GREEN = 2\n", "    BLUE = 3\n", "    BLACK = 1\n", "Color.RED"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T06:11:33.896811300Z", "start_time": "2023-08-23T06:11:33.874812100Z"}}, "id": "4a7371bdbf163d0e", "outputs": []}, {"cell_type": "code", "execution_count": 2, "source": ["a = Color.RED\n", "a.value"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T06:12:06.839396Z", "start_time": "2023-08-23T06:12:06.820424Z"}}, "id": "3964158567528560", "outputs": []}, {"cell_type": "code", "execution_count": 3, "source": ["a.name"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T06:12:13.492423800Z", "start_time": "2023-08-23T06:12:13.474007100Z"}}, "id": "e6e1e8f11a7adc8", "outputs": []}, {"cell_type": "code", "execution_count": 4, "source": ["Color(1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T06:13:43.499915500Z", "start_time": "2023-08-23T06:13:43.489427600Z"}}, "id": "99d439600b70425c", "outputs": []}, {"cell_type": "code", "execution_count": 12, "source": ["import pytz\n", "\n", "eastern = pytz.timezone('US/Eastern')\n", "display(eastern)\n", "shanghai = pytz.timezone('Asia/Shanghai')\n", "display(shanghai)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T14:43:01.529296500Z", "start_time": "2023-08-23T14:43:01.504298600Z"}}, "id": "b7facebff333c853", "outputs": []}, {"cell_type": "code", "execution_count": 10, "source": ["from tzlocal import get_localzone,get_localzone_name\n", "display(get_localzone())\n", "display(get_localzone_name())"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T14:35:11.460782700Z", "start_time": "2023-08-23T14:35:11.425686800Z"}}, "id": "c241d85eb99d994d", "outputs": []}, {"cell_type": "code", "execution_count": 11, "source": ["# ZoneInfo\n", "from vnpy.trader.utility import ZoneInfo\n", "display(ZoneInfo(\"US/Eastern\"))\n", "display(ZoneInfo(\"Asia/Shanghai\"))"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T14:35:33.665849200Z", "start_time": "2023-08-23T14:35:33.643855500Z"}}, "id": "fdbc8273f212a28a", "outputs": []}, {"cell_type": "code", "execution_count": 2, "source": ["# 测试datetime.fromtimestamp(int(value))，其中value为str类型\n", "from datetime import datetime\n", "str1 = \"1546300800\"\n", "dt = datetime.fromtimestamp(int(str1))\n", "dt"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-24T01:43:18.865936600Z", "start_time": "2023-08-24T01:43:18.822938200Z"}}, "id": "b1b7d2da948bbbc4", "outputs": []}, {"cell_type": "code", "execution_count": 14, "source": ["# 测试dt.replace(tzinfo=LOCAL_TZ)是否会改变datetime的值，还是只是改变了tzinfo\n", "from datetime import datetime\n", "import pytz\n", "dt = datetime.now()\n", "display(dt)\n", "display(dt.tzinfo)\n", "display(dt.replace(tzinfo=pytz.timezone('US/Eastern')))\n", "display(dt)\n", "display(dt.tzinfo)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:03:57.771165500Z", "start_time": "2023-08-23T15:03:57.748169800Z"}}, "id": "75848a80408a2603", "outputs": []}, {"cell_type": "markdown", "source": ["例如，在10月的最后一个星期天早上的美国东部时区，发生以下顺序：\n", "01:00 EDT (东部夏令时) 发生\n", "\n", "1小时后，而不是凌晨2点，时钟再次返回1小时，再次发生01:00 EST(东部标准时间)\n", "在美国，夏令时从3月的第二个星期日开始，到11月的第一个星期日结束，并在当地时间凌晨2点更改时间。春天开始施行夏令时时，在当天的凌晨2点人们要把时钟拨到3点，在秋天结束夏令时时，时钟要从凌晨2点拨到1点。每年的夏令时时间总共持续34周（238天），约占全年的65%。\n", "\n", "夏令时间开始\t2023年3月12日\n", "夏令时间结束\t2023年11月5日"], "metadata": {"collapsed": false}, "id": "f6e4f41c10948961"}, {"cell_type": "code", "execution_count": 52, "source": ["from datetime import datetime, timedelta\n", "from pytz import timezone"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:23:57.519072Z", "start_time": "2023-08-23T15:23:57.488044200Z"}}, "id": "524d12b17496ce89", "outputs": []}, {"cell_type": "code", "execution_count": 53, "source": ["utc_dt = datetime(2002, 10, 27, 6, 0, 0, tzinfo=pytz.utc)\n", "eastern = timezone('US/Eastern')\n", "fmt = '%Y-%m-%d %H:%M:%S %Z%z'# Z表示时区名称，z表示时区偏移量\n", "utc_dt.strftime(fmt)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:23:57.720043700Z", "start_time": "2023-08-23T15:23:57.699046Z"}}, "id": "81ee40e25746086a", "outputs": []}, {"cell_type": "code", "execution_count": 66, "source": ["loc_dt = utc_dt.astimezone(eastern)\n", "display(loc_dt)\n", "loc_dt.strftime(fmt)"], "metadata": {"collapsed": false}, "id": "672660fd2e6807fd", "outputs": []}, {"cell_type": "code", "execution_count": 67, "source": ["before = loc_dt - <PERSON><PERSON><PERSON>(minutes=10)\n", "display(before.strftime(fmt))\n", "# '2002-10-27 00:50:00 EST-0500'# EST Eastern Standard Time\n", "eastern.normalize(before).strftime(fmt)# normalize()方法将时间调整为夏令时或标准时间\n", "# '2002-10-27 01:50:00 EDT-0400'# EDT Eastern Daylight Time"], "metadata": {"collapsed": false}, "id": "d9c7e08937b287de", "outputs": []}, {"cell_type": "code", "execution_count": 68, "source": ["after = before + <PERSON><PERSON><PERSON>(minutes=20)\n", "display(after.strftime(fmt))\n", "eastern.normalize(after).strftime(fmt)\n", "# '2002-10-27 01:10:00 EST-0500'"], "metadata": {"collapsed": false}, "id": "c7c81f67b0984bff", "outputs": []}, {"cell_type": "code", "execution_count": 69, "source": ["# 测试vnpy的ZoneInfo是否适配夏令时\n", "from vnpy.trader.utility import ZoneInfo\n", "eastern = ZoneInfo(\"US/Eastern\")\n", "utc_dt.strftime(fmt)    "], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:29:23.759942700Z", "start_time": "2023-08-23T15:29:23.746916300Z"}}, "id": "5e547076e975b57a", "outputs": []}, {"cell_type": "code", "execution_count": 70, "source": ["loc_dt = utc_dt.astimezone(eastern)\n", "display(loc_dt)\n", "loc_dt.strftime(fmt)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:29:31.735525200Z", "start_time": "2023-08-23T15:29:31.671500400Z"}}, "id": "86e57d70ff4d2916", "outputs": []}, {"cell_type": "code", "execution_count": 71, "source": ["before = loc_dt - <PERSON><PERSON><PERSON>(minutes=10)\n", "display(before.strftime(fmt))\n", "# '2002-10-27 00:50:00 EST-0500'# EST Eastern Standard Time\n", "eastern.normalize(before).strftime(fmt)# normalize()方法将时间调整为夏令时或标准时间\n", "# '2002-10-27 01:50:00 EDT-0400'# EDT Eastern Daylight Time"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:29:48.972348900Z", "start_time": "2023-08-23T15:29:48.906523Z"}}, "id": "bbe5020355d8c8f0", "outputs": []}, {"cell_type": "code", "execution_count": 72, "source": ["after = before + <PERSON><PERSON><PERSON>(minutes=20)\n", "display(after.strftime(fmt))\n", "eastern.normalize(after).strftime(fmt)\n", "# '2002-10-27 01:10:00 EST-0500'"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-23T15:30:03.996646200Z", "start_time": "2023-08-23T15:30:03.931185400Z"}}, "id": "2a61009351ce2ca7", "outputs": []}, {"cell_type": "code", "execution_count": 10, "source": ["import pytz\n", "import datetime\n", " \n", " \n", "def is_dst(zonename, bizdate):\n", "    \"\"\"\n", "    判断当前时刻 某时区 是否为夏令时\n", "    :param zonename: example Asia/Shanghai Europe/London\n", "    :param bizdate: 日期 格式: 20220907\n", "    :return: True: 夏令时 False: 冬令时\n", "    \"\"\"\n", "    tz = pytz.timezone(zonename)\n", "    bizdate_dt = datetime.datetime.strptime(bizdate, '%Y%m%d')\n", "    dt = datetime.datetime(year=bizdate_dt.year, month=bizdate_dt.month, day=bizdate_dt.day)\n", "    now = pytz.utc.localize(dt)\n", "    print(now.astimezone(tz).dst())\n", "    return now.astimezone(tz).dst() != datetime.timedelta(0)# dst()方法返回夏令时的时间差\n", "\n", "zonename = 'Europe/London'\n", "bizdate = '20220907'\n", "ret = is_dst('Europe/London', bizdate)\n", "print(f'{zonename} {bizdate} 夏令时: {ret}')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-24T01:12:29.469548Z", "start_time": "2023-08-24T01:12:29.423428700Z"}}, "id": "25590c531fd838ff", "outputs": []}, {"cell_type": "code", "execution_count": 11, "source": ["zonename = 'US/Eastern'\n", "bizdate = '20021027'\n", "ret = is_dst('US/Eastern', bizdate)\n", "print(f'{zonename} {bizdate} 夏令时: {ret}')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-24T01:12:30.024299100Z", "start_time": "2023-08-24T01:12:29.996297200Z"}}, "id": "16fc3724c523071d", "outputs": []}, {"cell_type": "code", "execution_count": 12, "source": ["zonename = 'US/Eastern'\n", "bizdate = '20021028'\n", "ret = is_dst('US/Eastern', bizdate)\n", "print(f'{zonename} {bizdate} 夏令时: {ret}')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-24T01:12:32.893837300Z", "start_time": "2023-08-24T01:12:32.867812300Z"}}, "id": "18c52c018019b81a", "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}