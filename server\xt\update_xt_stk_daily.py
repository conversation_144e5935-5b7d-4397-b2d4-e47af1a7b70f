from typing import  Callable
from multiprocessing import Process

from pandas import DataFrame
from vnpy.trader.setting import SETTINGS

SETTINGS["database.database"] = "vnpy_stk"
SETTINGS["database.port"] = 3309 # 生产数据库端口
SETTINGS["database.user"] = "root"
SETTINGS["database.password"] = "zhP@55word"
SETTINGS["datafeed.name"] = "xt"
SETTINGS["datafeed.username"] = "token"
SETTINGS["datafeed.password"] = "******"


from vnpy.trader.database import BarOverview
from vnpy.trader.datafeed import get_datafeed
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.object import BarData, HistoryRequest
from vnpy.trader.constant import Exchange, Interval
from datetime import datetime, timedelta


INTERVAL_VT2XT: dict[Interval, str] = {
    Interval.MINUTE: "1m",
    Interval.DAILY: "1d",
    Interval.TICK: "tick"
}

EXCHANGE_VT2XT: dict[Exchange, str] = {
    Exchange.SSE: "SH",
    Exchange.SZSE: "SZ",
    Exchange.BSE: "BJ",
    Exchange.SHFE: "SF",
    Exchange.CFFEX: "IF",
    Exchange.INE: "INE",
    Exchange.DCE: "DF",
    Exchange.CZCE: "ZF",
    Exchange.GFEX: "GF",
}

EXCHANGE_XT2VT = {v: k for k, v in EXCHANGE_VT2XT.items()}


def load_symbol_dict_from_txt(file_path='/home/<USER>/stock_data_update/stock_list.txt'):
    """
    | 000009.SZSE |
    | 000021.SZSE |
    | 000027.SZSE |
    | 000031.SZSE |
    | 000039.SZSE |
    | 000050.SZSE |
    | 000060.SZSE |
    """
    with open(file_path, 'r') as f:
        lines = f.readlines()
    symbols = {}
    for line in lines:
        # line = line.strip()# 不对，两边不只是有空格
        line = line.strip('\n').strip('|').strip()

        if line:
            symbol, exchange = line.rsplit('.')
            if exchange not in symbols:
                symbols[exchange] = []
            symbols[exchange].append(symbol)
    return symbols

symbols = load_symbol_dict_from_txt()
print(f'symbols:{symbols}')

# 设置下载时间段
START_TIME = datetime(2020, 1, 1, tzinfo=DB_TZ)
END_TIME = datetime(2099, 1, 1, tzinfo=DB_TZ)

# interval = Interval.HOUR
# interval = Interval.MINUTE
interval = Interval.DAILY

def update_history_data() -> None:
    """更新历史合约信息"""
    # 在子进程中加载xtquant
    from xtquant.xtdata import download_history_data

    # 初始化数据服务
    datafeed = get_datafeed()
    datafeed.init()

    # 下载历史合约信息
    download_history_data("", "historycontract")

    print("xtquant历史合约信息下载完成")

def get_real_contract(contracts):
    '''
    获取合约的真实合约代码
    :param contracts: list 合约代码列表
    :return: list
    '''
    real_contracts = []
    for xt_symbol in contracts:
        symbol, exchange = xt_symbol.split(".")
        for i in range(len(symbol)):
            if symbol[i].isdigit():
                break
        digit = symbol[i:]
        if len(digit) != 4:
            continue

        alpha = symbol[:i]
        vt_exchange = Exchange(EXCHANGE_XT2VT[exchange])
        if vt_exchange == Exchange.CZCE:
            digit = symbol[i + 1:]

        xt_symbol = f"{alpha}{digit}.{exchange}"
        real_contracts.append(xt_symbol)

    return real_contracts


def symbols2xt_symbols(symbols):
    '''
    将合约代码转换为迅投研的合约代码
    :param symbols: dict 合约代码列表
    :return: list
    '''
    xt_symbols = []
    for exchange, contracts in symbols.items():
        for contract in contracts:
            xt_symbol = f"{contract}.{EXCHANGE_VT2XT[Exchange(exchange)]}"
            xt_symbols.append(xt_symbol)
    return xt_symbols


def update_bar_data(
    sector_name: str,
    interval: Interval = Interval.DAILY
) -> None:
    """更新K线数据"""
    # 在子进程中加载xtquant
    from xtquant.xtdata import (
        get_stock_list_in_sector,
        get_instrument_detail,
        get_local_data,
        download_history_data
    )

    def get_history_df(req: HistoryRequest, output: Callable = print) -> DataFrame:
        """获取历史数据DataFrame"""
        symbol: str = req.symbol
        exchange: Exchange = req.exchange
        start: datetime = req.start
        end: datetime = req.end
        interval: Interval = req.interval

        if not interval:
            interval = Interval.TICK

        xt_interval: str = INTERVAL_VT2XT.get(interval, None)
        if not xt_interval:
            output(f"迅投研查询历史数据失败：不支持的时间周期{interval.value}")
            return DataFrame()

        # 为了查询夜盘数据
        end += timedelta(1)

        # 从服务器下载获取
        xt_symbol: str = symbol + "." + EXCHANGE_VT2XT[exchange]
        start: str = start.strftime("%Y%m%d%H%M%S")
        end: str = end.strftime("%Y%m%d%H%M%S")

        if exchange in (Exchange.SSE, Exchange.SZSE) and len(symbol) > 6:
            xt_symbol += "O"

        download_history_data(xt_symbol, xt_interval, start, end)
        data: dict = get_local_data([], [xt_symbol], xt_interval, start, end, -1, "none", False)      # 默认等比前复权，改为不复权

        df: DataFrame = data[xt_symbol]
        return df

    # 初始化数据服务
    datafeed = get_datafeed()
    datafeed.query_bar_history.__globals__["get_history_df"] = get_history_df
    datafeed.init()

    # 连接数据库
    database = get_database()

    # 获取当前时间戳
    now: datetime = datetime.now()

    # 获取本地已有数据汇总
    data: list[BarOverview] = database.get_bar_overview()

    overviews: dict[str, BarOverview] = {}
    for o in data:
        vt_symbol: str = f"{o.symbol}.{o.exchange.value}"
        overviews[vt_symbol] = o

    # 查询交易所历史合约代码
    # xt_symbols: list[str] = get_stock_list_in_sector(sector_name)
    # xt_symbols: list[str] = get_real_contract(xt_symbols)
    xt_symbols: list[str] = symbols2xt_symbols(symbols)

    # 遍历列表查询合约信息
    for xt_symbol in xt_symbols:
        # 查询合约信息
        # data: dict = get_instrument_detail(xt_symbol, True)
        #
        # if not data:
        #     print(f"迅投研查询合约信息失败：{xt_symbol}")
        #     continue
        #
        # # 获取合约到期时间
        # expiry: datetime = None
        # if data["ExpireDate"] and data["ExpireDate"] != "0":
        #     expiry = datetime.strptime(data["ExpireDate"], "%Y%m%d")

        # 拆分迅投研代码
        symbol, xt_exchange = xt_symbol.split(".")

        # 生成本地代码
        exchange: Exchange = EXCHANGE_XT2VT[xt_exchange]
        vt_symbol: str = f"{symbol}.{exchange.value}"

        # 查询数据汇总
        overview: BarOverview = overviews.get(vt_symbol, None)

        # 如果已经到期，则跳过
        # if overview and expiry and expiry < now:
        #     continue

        # 实现增量查询
        start: datetime = START_TIME
        if overview:
            start = overview.end

        # 执行数据查询和更新入库
        req: HistoryRequest = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            start=start,
            end=now,
            interval=interval
        )

        bars: list[BarData] = datafeed.query_bar_history(req)

        if bars:
            database.save_bar_data(bars)

            start_dt: datetime = bars[0].datetime
            end_dt: datetime = bars[-1].datetime
            msg: str = f"{vt_symbol}数据更新成功，{start_dt} - {end_dt}"
            print(msg)


def main():
    # 使用子进程更新历史合约信息
    process: Process = Process(target=update_history_data)
    process.start()
    process.join()      # 等待子进程执行完成

    # 更新历史数据
    # for sector in ('上期所', '大商所', '郑商所', '中金所', '广期所'):
    #     update_bar_data(f"过期{sector}")
    #     update_bar_data(sector)
    update_bar_data('')



if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()

        # # 每日定时自动更新
        # current_time = datetime.now().time()
        # start_time = time(17, 0, 0)  # 每天17:00开始更新
        #
        # while True:
        #     sleep(60)  # 每分钟检查一次
        #     if current_time == start_time:
        #         # download_data()
        #         update_data()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(e)
        import requests, traceback

        print(traceback.format_exc())
        from send_to_wechat import WeChat

        wx = WeChat()
        ip = requests.get('https://ifconfig.me').text
        wx.send_data(f"{ip}:{__file__}: An error occurred! ", touser='liaoyuan')