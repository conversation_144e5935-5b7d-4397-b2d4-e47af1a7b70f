
# 北京时间 9.27 6    = utc时间 9.26 22

# 将UTC时间2023-09-26 22:00:00 转换为上海时间
import pytz
from datetime import datetime
# utc_dt = datetime(2023, 9, 26, 22, 0, 0, tzinfo=pytz.utc)
# print(f'utc_dt: {utc_dt}')
# bj_dt = utc_dt.astimezone(pytz.timezone('Asia/Shanghai'))
# print(f'bj_dt: {bj_dt}')
# # 美中时间
# uscentral_dt = utc_dt.astimezone(pytz.timezone('US/Central'))
# print(f'uscentral_dt: {uscentral_dt}')
# # 美东时间
# useastern_dt = utc_dt.astimezone(pytz.timezone('US/Eastern'))
# print(f'useastern_dt: {useastern_dt}')
'''
,gateway_name,extra,symbol,exchange,datetime,interval,volume,turnover,open_interest,open_price,high_price,low_price,close_price
0,IB,,ES-20231215-USD-FUT,Exchange.CME,2023/9/25 22:00,Interval.MINUTE,559,0,0,4382,4382,4380.25,4380.75
'''

# 从ES-20231215-USD-FUT_2023-09-27.csv（如上）读取数据，注意datetime列的时区是UTC，转换为美中时间
# 美中时间交易时间为：00:00-16:00 到17:00-23:59，活跃时间为08:30-16:00
# 只保留datetime和volume两列，datetime列的时区是UTC，转换为美中时间
# 分析volume列：活跃时间的volume求和、全部时间的volume求和、活跃时间的volume占全部时间的volume的比例
import pandas as pd
import pytz
from datetime import datetime
import os
df = pd.read_csv('ES-20231215-USD-FUT_2023-09-27.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
df['datetime'] = df['datetime'].dt.tz_localize('UTC').dt.tz_convert('US/Central')
df = df[['datetime', 'volume']]

# 活跃时间的volume求和
df_active = df[(df['datetime'].dt.time >= datetime(2023, 9, 27, 8, 30, 0).time()) & (df['datetime'].dt.time <= datetime(2023, 9, 27, 16, 0, 0).time())]
df_active_volume_sum = df_active['volume'].sum()
print(f'2023.9.27活跃时间（美中08:30-16:00）成交量: {df_active_volume_sum}')

# 全部时间的volume求和
df_all_volume_sum = df['volume'].sum()
print(f'2023.9.27全部时间（美中00:00-16:00 到17:00-23:59）成交量: {df_all_volume_sum}')

# 活跃时间的volume占全部时间的volume的比例
df_active_volume_ratio = df_active_volume_sum / df_all_volume_sum
# print(f'2023.9.27活跃时间（美中08:30-16:00）成交量占全部时间（美中00:00-16:00 到17:00-23:59）成交量的比例: {df_active_volume_ratio}')
# 改为百分比
print(f'2023.9.27活跃时间（美中08:30-16:00）成交量占全部时间（美中00:00-16:00 到17:00-23:59）成交量的比例: {df_active_volume_ratio*100}%')