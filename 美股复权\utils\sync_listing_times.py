import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from loguru import logger
import pandas as pd
from openpyxl import Workbook, load_workbook

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from utils.database_manager import (
    dbtz_convert, db_manager, IbProduct, ContractTime,
    WindStock, YahooInfo, FutuProduct
)
from utils.excel_utils import auto_adjust_worksheet

# 动态生成调度器日志文件名，并添加一个过滤器，只处理来自本模块的日志
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)

class ListingTimesSyncer:
    """上市时间同步器"""
    
    def __init__(self):
        """初始化"""
        self.products = []  # 存储产品信息
        
    def get_latest_products(self) -> List[Dict]:
        """获取所有标记为最新的IB产品信息"""
        logger.info("正在获取最新的IB产品信息...")
        
        products = []
        query = (IbProduct
                .select(IbProduct.conid, IbProduct.isin, IbProduct.symbol)
                .where(IbProduct.is_latest == True))
        
        for product in query:
            if not product.symbol:  # 跳过没有symbol的记录
                continue
                
            # 构建Futu symbol
            futu_symbol = f"US.{product.symbol.replace(' ', '.')}"
            
            products.append({
                'conid': product.conid,
                'isin': product.isin,
                'symbol': product.symbol,
                'futu_symbol': futu_symbol
            })
        
        logger.info(f"获取到{len(products)}条最新产品记录")
        self.products = products  # 保存到实例变量
        return products

    def fetch_listing_times(self, products: List[Dict]) -> List[Dict]:
        """从各个来源获取上市时间信息"""
        logger.info("开始从各个来源获取上市时间...")
        
        # 准备ISIN和Futu symbol的集合用于批量查询
        isins = {p['isin'] for p in products if p['isin']}
        futu_symbols = {p['futu_symbol'] for p in products}
        
        # 从Wind获取上市日期
        wind_dates = {}
        if isins:
            wind_query = (WindStock
                         .select(WindStock.isin_code, WindStock.ipo_date)
                         .where(WindStock.isin_code.in_(isins)))
            for record in wind_query:
                wind_dates[record.isin_code] = record.ipo_date
        
        # 从Yahoo获取首次交易日期（fromtimestamp无入参默认本地上海时间）
        yahoo_dates = {}
        if isins:
            yahoo_query = (YahooInfo
                          .select(YahooInfo.isin, YahooInfo.first_trade_date_milliseconds)
                          .where(YahooInfo.isin.in_(isins)))
            for record in yahoo_query:
                # Yahoo数据已经是美东时间，不需要转换
                if record.first_trade_date_milliseconds:
                    yahoo_dates[record.isin] = record.first_trade_date_milliseconds
        
        # 从Futu获取上市时间
        futu_dates = {}
        if futu_symbols:
            futu_query = (FutuProduct
                         .select(FutuProduct.code, FutuProduct.list_time)
                         .where(FutuProduct.code.in_(futu_symbols)))
            for record in futu_query:
                futu_dates[record.code] = record.list_time
        
        # 合并信息
        result = []
        for product in products:
            data = {
                'conid': product['conid'],
                'symbol': product['symbol'],  # 添加symbol字段
                'wind_ipo_date': wind_dates.get(product['isin']),
                'yahoo_first_trade_date_milliseconds': yahoo_dates.get(product['isin']),
                'futu_list_time': futu_dates.get(product['futu_symbol'])
            }
            result.append(data)
        
        logger.info("已完成上市时间信息的获取")
        return result

    def export_to_excel(self) -> None:
        """导出ContractTime数据到Excel"""
        logger.info("开始导出数据到Excel...")
        
        # 查询所有ContractTime数据
        query = ContractTime.select()
        
        # 创建数据字典列表
        data = []
        symbol_map = {p['conid']: p['symbol'] for p in self.products}
        
        for record in query:
            # 将ib_head_time从UTC时区转换为美东时区
            ib_head_time_et = dbtz_convert(record.ib_head_time, 'UTC', 'America/New_York') if record.ib_head_time else None
            
            data.append({
                'conid': record.conid,
                'symbol': symbol_map.get(record.conid, ''),  # 从products中获取symbol
                'IB': ib_head_time_et,  # 使用转换后的时间
                '万德': record.wind_ipo_date,
                '雅虎': record.yahoo_first_trade_date_milliseconds,  # Yahoo数据已经是美东时间
                '富途': record.futu_list_time
            })
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        # 生成文件名（包含当前日期）
        file_name = f'listing_times_{datetime.now().strftime("%Y%m%d")}.xlsx'
        
        # 先将数据保存到Excel
        df.to_excel(file_name, sheet_name="Listing Times", index=False)
        
        # 然后打开并调整格式
        wb = load_workbook(file_name)
        ws = wb["Listing Times"]
        
        # 调整格式
        auto_adjust_worksheet(ws)
        
        # 保存文件
        wb.save(file_name)
        logger.info(f"数据已导出到文件: {file_name}")

    def sync_listing_times(self):
        """同步上市时间信息到ContractTime表"""
        try:
            # 1. 获取最新产品信息
            products = self.get_latest_products()
            if not products:
                logger.warning("没有找到需要同步的产品信息")
                return
                
            # 2. 获取上市时间信息
            listing_times = self.fetch_listing_times(products)
            
            # 3. 保存到ContractTime表
            logger.info("开始更新ContractTime表...")
            db_manager.batch_save_to_db(
                data_list=listing_times,
                model_class=ContractTime,
                primary_key="conid",
                ignore_fields=["create_time", "update_time", "ib_head_time"],  # 不覆盖ib_head_time
            )
            
            # 4. 导出到Excel
            self.export_to_excel()
            
            logger.info("上市时间同步完成")
            
        except Exception as e:
            logger.error(f"同步上市时间时发生错误: {str(e)}")
            raise

if __name__ == "__main__":
    syncer = ListingTimesSyncer()
    syncer.sync_listing_times() 