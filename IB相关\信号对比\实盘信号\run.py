import multiprocessing
import sys
from datetime import time, datetime
from logging import INFO
from time import sleep

# 获取vnpy版本
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
# 订阅行情
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, ZoneInfo
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG

# from data_recorder_fake_bar.utils.barGen_engine import BarGenEngineIb
from data_recorder_fake_bar.utils.barGen_redis_engine import BarGenEngineIb  # test lance
from data_recorder_fake_bar.utils.recorder_engine import RecorderEngine

SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True

# import vnpy
# version = vnpy.__version__
# if version < '3.0.0':  # vnpy 2.*版本
#     # 录制IB行情数据
#     from vnpy.gateway.ib import IbGateway
# else:  # vnpy 3.*版本
#     from vnpy_ib import IbGateway
from data_recorder_fake_bar.utils.ib_gateway import IbGateway

# Define the trading periods in Eastern Time (ET)
# DAY_START = time(17, 0)
# DAY_END = time(16, 0)

# 提前15分钟开启行情录制
DAY_START = time(16, 45)
DAY_END = time(16, 15)

connect_filename = 'connect_ib.json'

data_recorder_filename = 'data_recorder_setting_ib.json'


def rewrite_setting():
    # 将data_recorder_filename的每个item的value改为每个item的value的key，保存到setting目录下的barGen_setting.json文件中
    data_recorder_setting = load_json(data_recorder_filename)
    barGen_setting = {}
    for key, value in data_recorder_setting.items():
        barGen_setting[key] = list(value.keys())
    save_json('barGen_setting.json', barGen_setting)


def check_trading_period(is_us_market: bool = True):
    """
    Check if it's trading period.
    """
    trading = False
    eastern = ZoneInfo('US/Eastern')
    current_time = datetime.now(eastern).time()
    if DAY_START <= current_time or current_time <= DAY_END:
        trading = True

    return trading


def run_child():
    """
    Running in the child process.
    """
    SETTINGS["log.file"] = True

    # 创建事件引擎
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    # 添加交易接口
    main_engine.add_gateway(IbGateway)
    main_engine.write_log("接口添加成功")

    cta_engine = main_engine.add_app(CtaStrategyApp)
    main_engine.write_log("主引擎创建成功")

    # 添加数据记录引擎
    main_engine.add_engine(RecorderEngine)
    main_engine.write_log("添加数据记录引擎")

    log_engine = main_engine.get_engine("log")
    event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
    main_engine.write_log("注册日志事件监听")

    # 获取数据接口配置
    setting = load_json(connect_filename)
    # setting["TWS地址"] = "43.159.138.138"  # test lance
    # setting["客户号"] = 66  # test lance
    main_engine.write_log("数据接口配置加载成功")

    # 连接数据接口
    main_engine.connect(setting, "IB")
    main_engine.write_log("连接IB接口")

    # 添加Bar生成引擎
    main_engine.add_engine(BarGenEngineIb)
    main_engine.write_log("添加Bar生成引擎")

    sleep(10)
    main_engine.write_log("***查询资金和持仓***")
    # 查询资金 - 自动
    main_engine.write_log(main_engine.get_all_accounts())
    # 查询持仓
    main_engine.write_log(main_engine.get_all_positions())

    cta_engine.init_engine()
    main_engine.write_log("CTA策略初始化完成")# 此时会将strategy_setting中的策略add_strategy

    # 给engine载入双均线策略，实盘运行
    main_engine.write_log("***从数据库读取准备数据, 实盘运行***")
    old_strategies = list(cta_engine.strategies.keys())
    new_strategies = []
    # cta_engine.add_strategy("DoubleMa3Strategy", "DoubleMa3Strategy_IB_20230905_v1", "AAPL-USD-STK.SMART",
    #                         {"fast_window": 10, "slow_window": 20})
    # cta_engine.add_strategy("DoubleMa5Strategy", "DoubleMa5Strategy_IB_20230905_v1", "TSLA-USD-STK.SMART",
    #                         {"fast_window": 10, "slow_window": 20})
    # 改为load_json(data_recorder_filename)，从中提取标的名称，每个标的同时挂"DoubleMa3Strategy"和"DoubleMa5Strategy"两个策略
    data_recorder_setting = load_json(data_recorder_filename)
    strategies_added = []
    for bar_type, bar_setting in data_recorder_setting.items():
        for vt_symbol, setting in bar_setting.items():
            strategy_name3 = f'DoubleMa3Strategy_IB_{vt_symbol}_v1'
            strategy_name5 = f'DoubleMa5Strategy_IB_{vt_symbol}_v1'

            # 添加或编辑策略
            for strategy_name, strategy_class in [(strategy_name3, "DoubleMa3Strategy"),
                                                  (strategy_name5, "DoubleMa5Strategy")]:
                if strategy_name in cta_engine.strategies:
                    # 已有策略，更新参数
                    cta_engine.edit_strategy(strategy_name, {'fast_window': 10, 'slow_window': 20})
                else:
                    # 新策略，添加
                    cta_engine.add_strategy(
                        strategy_class,
                        strategy_name,
                        vt_symbol,
                        {"fast_window": 10, "slow_window": 20}
                    )
                    strategies_added.append(strategy_name)

    main_engine.write_log(f"成功添加/更新 {len(strategies_added)} 个策略")
    sleep(5)  # Leave enough time to complete strategy initialization

    cta_engine.init_all_strategies()
    # 记录初始化开始时间
    time1 = datetime.now()
    # 改为如果所有strategy.inited都为True则继续
    while not all([strategy.inited for strategy in cta_engine.strategies.values()]):
        sleep(1)
    time_used = datetime.now() - time1
    main_engine.write_log(f"CTA策略全部初始化，耗时：{time_used}，平均每个策略耗时：{time_used / len(cta_engine.strategies)}")

    cta_engine.start_all_strategies()
    main_engine.write_log("CTA策略全部启动")

    while True:
        sleep(10)

        trading = check_trading_period()
        if not trading:
            print("关闭子进程")
            main_engine.close()
            sys.exit(0)


def run_parent():
    """
    Running in the parent process.
    """
    print("启动CTA策略守护父进程")

    child_process = None

    while True:
        trading = check_trading_period()

        # Start child process in trading period
        if trading and child_process is None:
            print("启动子进程")
            child_process = multiprocessing.Process(target=run_child)
            child_process.start()
            print("子进程启动成功")

        # 非记录时间则退出子进程
        if not trading and child_process is not None:
            if not child_process.is_alive():
                child_process = None
                print("子进程关闭成功")

        sleep(5)


if __name__ == "__main__":
    rewrite_setting()
    run_parent()
