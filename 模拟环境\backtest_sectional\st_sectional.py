from tools.TradeAnalyse import TradeAnalyse
import time
from datetime import datetime
from backtest_sectional import backtest_sectional
import os
print(os.path.dirname(__file__))
os.chdir(os.path.dirname(__file__))

# 策略文件
from DoubleMA_simple import DoubleMA


if __name__ == '__main__':
    t0 = time.time()
    # vt_symbols = ['adj006.SZSE','000006.SZSE']
    vt_symbols = ['000012.SZSE','000009.SZSE']
    strategy_params = {
        'interval': '1m',
        'start': datetime(2023, 1, 1),
        'end': datetime(2023, 8, 15),
        'rate': 1.5e-3,
        'slippage': 0,
        'size': 100,
        'pricetick': 0.01,
        'capital': 100e4
    }
    
    for vt_symbol in vt_symbols:
        backtest_info = backtest_sectional(DoubleMA, vt_symbol, strategy_params)
        user = '胡'
        ta = TradeAnalyse()
        ta.summary(user, backtest_info)
    t1 = time.time()
    print("耗时: %.1f"%(t1-t0))
