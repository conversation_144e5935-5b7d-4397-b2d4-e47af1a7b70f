import json
import multiprocessing
import os
import sys
from datetime import time, datetime, timedelta
from logging import INFO
from time import sleep

import pytz
# 获取vnpy版本
import vnpy
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
# 订阅行情
from vnpy.trader.object import SubscribeRequest, Exchange
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, extract_vt_symbol

from data_recorder_fake_bar.utils.barGen_engine import BarGenEngineIb, BarGenEngineCtp, BarGenEngineCtpOpt
from data_recorder_fake_bar.utils.recorder_engine import RecorderEngine, RecorderEngineCtp

SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True

version = vnpy.__version__
if version < '3.0.0':  # vnpy 2.*版本
    # 录制CTP行情数据
    from vnpy.gateway.ctp import CtpGateway
    # 录制TTS行情数据
    from vnpy.gateway.tts import TtsGateway
    # 录制IB行情数据
    from vnpy.gateway.ib import IbGateway
else:  # vnpy 3.*版本
    from vnpy_ctp.gateway.ctp_gateway import CtpGateway # 使用TTS时需要注释掉
    # from vnpy_tts.gateway.tts_gateway import TtsGateway
    from vnpy_ib import IbGateway

# Chinese futures market trading period (day/night)
MORNING_START = time(8, 45)
MORNING_END = time(11, 45)
# MORNING_END = (datetime.now()+timedelta(minutes=100)).time() # test lance

AFTERNOON_START = time(12, 45)
AFTERNOON_END = time(15, 15)
# AFTERNOON_END = (datetime.now()+timedelta(minutes=1)).time() # test lance

NIGHT_START = time(20, 45)
NIGHT_END = time(2, 45)
# NIGHT_END = (datetime.now()+timedelta(minutes=1)).time() # test lance

# Define the trading periods in Eastern Time (ET) for US stock market
# US_DAY_START = time(9, 30)   # Regular trading hours start at 9:30 AM ET
# US_DAY_END = time(16, 0)    # Regular trading hours end at 4:00 PM ET

# 提前15分钟开启行情录制
US_DAY_START = time(9, 15)  # Regular trading hours start at 9:30 AM ET
US_DAY_END = time(16, 15)  # Regular trading hours end at 4:00 PM ET

is_us_market = True

if is_us_market:
    connect_filename = 'connect_ib.json'

    data_recorder_filename = 'data_recorder_setting_ib.json'
else:
    # connect_filename = 'connect_ctp.json'
    connect_filename = 'connect_ctp_nanhua.json'
    # connect_filename = 'connect_ctp盘后.json'

    # connect_filename = 'connect_tts.json'

    data_recorder_filename = 'data_recorder_setting.json'
    # data_recorder_filename = 'data_recorder_setting_ctp.json'
    # data_recorder_filename = 'data_recorder_setting_tts.json'


def rewrite_setting():
    # 将data_recorder_filename的每个item的value改为每个item的value的key，保存到setting目录下的barGen_setting.json文件中
    data_recorder_setting = load_json(data_recorder_filename)
    barGen_setting = {}
    for key, value in data_recorder_setting.items():
        barGen_setting[key] = list(value.keys())
    save_json('barGen_setting.json', barGen_setting)

def check_trading_period(is_us_market: bool = True):
    """
    Check if it's trading period.
    """
    trading = False
    if is_us_market:
        eastern = pytz.timezone('US/Eastern')
        current_time = datetime.now(eastern).time()
        if US_DAY_START <= current_time <= US_DAY_END:
            trading = True
    else:
        current_time = datetime.now().time()
        if (MORNING_START <= current_time <= MORNING_END or AFTERNOON_START <= current_time <= AFTERNOON_END or
                current_time >= NIGHT_START or current_time <= NIGHT_END):
                # current_time >= NIGHT_START and current_time <= NIGHT_END):# test lance
            trading = True

    return trading


def run_child(is_us_market: bool = True):
    """
    Running in the child process.
    """
    # 创建事件引擎
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    main_engine.write_log("主引擎创建成功")

    # 添加数据记录引擎
    if is_us_market:
        main_engine.add_engine(RecorderEngine)
    else:
        main_engine.add_engine(RecorderEngineCtp)
        # main_engine.add_engine(RecorderEngine) # test lance tts
    main_engine.write_log("添加数据记录引擎")

    # 添加交易接口
    if is_us_market:
        main_engine.add_gateway(IbGateway)
    else:
        main_engine.add_gateway(CtpGateway)
        # main_engine.add_gateway(TtsGateway)
    main_engine.write_log("接口添加成功")

    # 获取数据接口配置
    setting = load_json(connect_filename)
    main_engine.write_log("数据接口配置加载成功")

    # 连接数据接口
    if is_us_market:
        main_engine.connect(setting, "IB")
    else:
        main_engine.connect(setting, "CTP")
        # main_engine.connect(setting, "TTS")
    main_engine.write_log("数据接口连接成功")

    # 添加Bar生成引擎
    if is_us_market:
        main_engine.add_engine(BarGenEngineIb)
    else:
        # main_engine.add_engine(BarGenEngineCtp)
        main_engine.add_engine(BarGenEngineCtpOpt)
        # main_engine.add_engine(BarGenEngineIb)
    main_engine.write_log("添加Bar生成引擎")

    while True:
        sleep(10)

        trading = check_trading_period(is_us_market)
        if not trading:
            print("关闭子进程")
            main_engine.close()
            sys.exit(0)  # sys.exit(0)表示正常退出程序，sys.exit(1)表示异常退出程序


def run_parent(is_us_market: bool = True):
    """
    Running in the parent process.
    """
    print("启动数据记录守护父进程")

    child_process = None

    while True:
        trading = check_trading_period(is_us_market)

        # Start child process in trading period
        # trading = False
        if trading and child_process is None:
            print("启动子进程")
            child_process = multiprocessing.Process(target=run_child, args=(is_us_market,))
            child_process.start()
            print("子进程启动成功")

        # 非记录时间则退出子进程
        if not trading and child_process is not None:
            if not child_process.is_alive():
                child_process = None
                print("子进程关闭成功")

        sleep(5)


if __name__ == "__main__":
    rewrite_setting()
    run_parent(is_us_market)
