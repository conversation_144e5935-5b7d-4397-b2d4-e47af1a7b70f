# Define here the models for your scraped items
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/items.html

import scrapy


class IbItem(scrapy.Item):
    # define the fields for your item here like:
    # name = scrapy.Field()
    类型 = scrapy.Field()#
    区域 = scrapy.Field()#
    国家地区 = scrapy.Field()#
    交易所 = scrapy.Field()
    产品 = scrapy.Field()#
    时间 = scrapy.Field()

    IB_Symbol = scrapy.Field()
    Product_Description = scrapy.Field()
    链接地址 = scrapy.Field()
    Symbol = scrapy.Field()
    Currency = scrapy.Field()