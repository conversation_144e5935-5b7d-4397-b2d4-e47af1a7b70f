#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查IB数据源的股票前收盘价与前一交易日收盘价的差异，识别复权情况
每个美东交易日开盘时间（转为上海时区再减去12小时）运行
"""
import sys
import traceback
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Tuple

import pandas_market_calendars as mcal
from loguru import logger
from peewee import (Model, AutoField, CharField, DateTimeField, DoubleField)
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import ZoneInfo, load_json
SETTINGS.update(load_json('vt_setting_us.json'))
from vnpy_mysql.mysql_database import ReconnectMySQLDatabase, DbTickOverview, DbTickData, DbBarData, DB_TZ

# 配置loguru
logger.add("ib_adjustment_check.log", rotation="10 MB", level="TRACE", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

# 创建美东时区
US_TZ = ZoneInfo('US/Eastern')

# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, StockAdjustmentUS
common_db = db_manager.common_db

# ================== 数据库操作类 ==================
class AdjustmentChecker:
    """复权检查器类"""

    def __init__(self):
        """初始化"""
        # 确保数据库连接
        if common_db.is_closed():
            common_db.connect()

    def __del__(self):
        """析构函数，确保关闭数据库连接"""
        if not common_db.is_closed():
            common_db.close()

    def get_symbols(self) -> List[Dict]:
        """从DbTickOverview获取所有股票符号"""
        symbols_query = DbTickOverview.select(
            DbTickOverview.symbol,
            DbTickOverview.exchange
        )
        return [{"symbol": item.symbol, "exchange": item.exchange} for item in symbols_query]

    def get_exchange_pre_close(self, symbol: str, exchange: str, previous_close_time: datetime) -> Optional[float]:
        """
        从DbTickData获取交易所复权后的前收盘价

        参数:
            symbol: 股票代码
            exchange: 交易所
            previous_close_time: 上一个交易日收盘时间（上海时区，无时区信息）

        返回:
            Optional[float]: 交易所复权后的前收盘价，如果没有数据则返回None
        """
        tick_query = (DbTickData
                     .select(DbTickData.pre_close)
                     .where(
                         (DbTickData.symbol == symbol) &
                         (DbTickData.exchange == exchange) &
                         (DbTickData.datetime > previous_close_time)  # 确保是上一个交易日收盘后的数据
                     )
                     .order_by(DbTickData.datetime.desc())
                     .limit(1))

        tick_data = tick_query.first()
        return tick_data.pre_close if tick_data else None

    def get_previous_close_price(self, symbol: str, exchange: str, previous_trading_day: date) -> Optional[float]:
        """
        从DbBarData获取前一个交易日的收盘价

        参数:
            symbol: 股票代码
            exchange: 交易所
            previous_trading_day: 前一个交易日日期

        返回:
            Optional[float]: 前一个交易日的收盘价，如果没有数据则返回None
        """
        previous_trading_day_dt = datetime.combine(previous_trading_day, datetime.min.time())

        bar_query = (DbBarData
                    .select(DbBarData.close_price)
                    .where(
                        (DbBarData.symbol == symbol) &
                        (DbBarData.exchange == exchange) &
                        (DbBarData.interval == 'd') &
                        (DbBarData.datetime >= previous_trading_day_dt) &
                        (DbBarData.datetime < previous_trading_day_dt + timedelta(days=1))
                    )
                    .order_by(DbBarData.datetime.desc())
                    .limit(1))

        bar_data = bar_query.first()
        return bar_data.close_price if bar_data else None

    def record_adjustment(self, symbol: str, exchange: str, old_price: float, new_price: float) -> None:
        """
        记录复权信息到数据库

        参数:
            symbol: 股票代码
            exchange: 交易所
            old_price: 原价格（前一交易日收盘价）
            new_price: 新价格（交易所复权后的前收盘价）
        """
        # 计算价格倍数
        if old_price != 0:
            price_multi = new_price / old_price
        else:
            price_multi = 0

        # 写入复权记录表
        StockAdjustmentUS.create(
            old_symbol=symbol,
            new_symbol=symbol,
            exchange=exchange,
            old_price=old_price,
            new_price=new_price,
            price_multi=price_multi,
            ex_date=datetime.now().date()
        )

        logger.info(f"发现复权: {symbol} 前收盘价: {old_price} 复权前收: {new_price} 倍数: {price_multi}")

    def check_all_symbols(self) -> None:
        """检查所有股票的复权情况"""
        # 获取相关时间
        is_next_market_minute, previous_close_time, previous_trading_day = self.get_us_market_times()

        # 如果当前时间+12小时不是下一个美股开盘分钟，则不进行检查
        if not is_next_market_minute:
            logger.info("当前时间+12小时不是下一个美股开盘分钟，不进行检查")
            return

        # 获取所有股票符号
        symbols = self.get_symbols()

        for symbol_info in symbols:
            symbol = symbol_info["symbol"]
            exchange = symbol_info["exchange"]

            # 获取交易所复权后的前收盘价
            exchange_pre_close = self.get_exchange_pre_close(symbol, exchange, previous_close_time)
            if exchange_pre_close is None:
                continue

            # 获取前一交易日的收盘价
            previous_close_price = self.get_previous_close_price(symbol, exchange, previous_trading_day)
            if previous_close_price is None:
                continue

            # 比较两个价格，如果不同，则添加到复权表
            if abs(exchange_pre_close - previous_close_price) > 0.000001:  # 考虑浮点数精度问题
                self.record_adjustment(symbol, exchange, previous_close_price, exchange_pre_close)

    def get_us_market_times(self, n = 1) -> Tuple[bool, datetime, date]:
        """
        获取美东市场相关时间

        返回:
            Tuple[bool, datetime, date]:
                1. 当前时间+12小时是否是下一个美股开盘分钟（布尔值）
                2. 上一个交易日收盘时间（上海时区，无时区信息）
                3. 上一个交易日日期
        """

        # 获取当前上海时间和美东时间
        now_shanghai = datetime.now(DB_TZ)
        now_eastern = now_shanghai.astimezone(US_TZ)

        # 计算当前时间+12小时
        now_plus_12h = now_eastern + timedelta(hours=12)

        # 获取当天美东日期
        eastern_date = now_eastern.date()

        # 获取NYSE交易日历 - 获取足够长的日期范围以覆盖当前日期和前后几天
        nyse = mcal.get_calendar('NYSE')
        schedule = nyse.schedule(
            start_date=eastern_date - timedelta(days=30*n),  # 往前30天，确保能找到前一个交易日
            end_date=eastern_date + timedelta(days=2)      # 往后2天，不是交易日就不运行
        )

        # 将日期索引转换为日期对象列表，便于比较
        trading_dates = [d.date() for d in schedule.index]

        # 是否为下一个美股开盘分钟的标志
        is_next_market_minute = False

        # 查找下一天的信息
        next_date = eastern_date + timedelta(days=1)  # 获取下一天日期
        tomorrow_schedule = schedule[schedule.index.date == next_date]
        if not tomorrow_schedule.empty:
            # 下一天是交易日
            market_open = tomorrow_schedule.iloc[0]['market_open'].to_pydatetime()

            # 检查当前时间+12小时是否接近开盘时间（允许1分钟误差）
            time_diff = abs((now_plus_12h - market_open).total_seconds())
            logger.debug(f'下一日开盘时间: {market_open}，当前时间+12小时: {now_plus_12h}，时间差: {time_diff}秒')
            if time_diff <= 60:  # 允许1分钟的误差
                is_next_market_minute = True
        else:
            logger.debug('下一日非交易日')

        # 查找前一个交易日
        previous_trading_dates = [d for d in trading_dates if d <= eastern_date]
        if not previous_trading_dates:
            # 如果没有找到前一个交易日（极少情况）
            return self.get_us_market_times(n+1)
        else:
            # 找到前一个交易日
            previous_trading_day = max(previous_trading_dates)

            # 获取前一个交易日的收盘时间
            previous_day_schedule = schedule[schedule.index.date == previous_trading_day]
            previous_close_time_eastern = previous_day_schedule.iloc[0]['market_close'].to_pydatetime()
            logger.debug(f'前一日收盘时间: {previous_close_time_eastern}')

        # 转换为上海时区并去除时区信息
        previous_close_time_shanghai = previous_close_time_eastern.astimezone(DB_TZ).replace(tzinfo=None)

        return is_next_market_minute, previous_close_time_shanghai, previous_trading_day

# ================== 主函数 ==================
def check_adjustment() -> None:
    """检查IB数据源的股票前收盘价与前一交易日收盘价的差异，识别复权情况"""
    start_time = datetime.now()
    logger.info(f"开始运行: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        checker = AdjustmentChecker()
        checker.check_all_symbols()
        logger.info("复权检查完成")
    except Exception as e:
        logger.error(f"复权检查失败: {e}\n{traceback.format_exc()}")
        raise
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"结束运行: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"运行时长: {duration}")

if __name__ == "__main__":
    check_adjustment()
