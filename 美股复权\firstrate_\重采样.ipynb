{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-10-12T02:45:55.556400500Z", "start_time": "2023-10-12T02:43:54.546764600Z"}}, "outputs": [], "source": ["import pandas as pd \n", "column_names = [\"TimeStamp\", \"open\", \"high\", \"low\", \"close\", \"volume\"]\n", "maindf  = pd.read_csv(\"https://frd001.s3-us-east-2.amazonaws.com/AMZN_FirstRateDatacom1.zip\",                 \n", "                 names=column_names, \n", "                 parse_dates=[\"TimeStamp\"],\n", "                 index_col=[\"TimeStamp\"])\n", "# https://f004.backblazeb2.com/file/frd-apix03/individual_contracts_update_1day_yhg78fjm92.zip\n", "# TimeStamp为\"US/Eastern\"，转换为上海时区\n", "# maindf.index = maindf.index.tz_localize(\"US/Eastern\").tz_convert(\"Asia/Shanghai\")"]}, {"cell_type": "code", "execution_count": 2, "outputs": [{"data": {"text/plain": "                        open     high      low    close  volume\nTimeStamp                                                      \n2019-01-02 04:00:00  1473.01  1473.01  1465.00  1465.00    2880\n2019-01-02 04:01:00  1462.50  1462.50  1461.42  1461.42     353\n2019-01-02 04:13:00  1465.67  1467.25  1465.67  1467.00     742\n2019-01-02 04:40:00  1468.00  1468.00  1468.00  1468.00     380\n2019-01-02 04:41:00  1465.50  1465.50  1465.50  1465.50     298\n...                      ...      ...      ...      ...     ...\n2019-12-30 18:58:00  1846.40  1846.40  1846.40  1846.40     200\n2019-12-30 19:14:00  1846.50  1846.50  1846.50  1846.50     220\n2019-12-30 19:35:00  1846.50  1846.50  1846.50  1846.50     306\n2019-12-30 19:54:00  1846.06  1846.06  1846.06  1846.06     107\n2019-12-30 19:56:00  1846.06  1846.06  1846.06  1846.06     154\n\n[122489 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>open</th>\n      <th>high</th>\n      <th>low</th>\n      <th>close</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>TimeStamp</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2019-01-02 04:00:00</th>\n      <td>1473.01</td>\n      <td>1473.01</td>\n      <td>1465.00</td>\n      <td>1465.00</td>\n      <td>2880</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 04:01:00</th>\n      <td>1462.50</td>\n      <td>1462.50</td>\n      <td>1461.42</td>\n      <td>1461.42</td>\n      <td>353</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 04:13:00</th>\n      <td>1465.67</td>\n      <td>1467.25</td>\n      <td>1465.67</td>\n      <td>1467.00</td>\n      <td>742</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 04:40:00</th>\n      <td>1468.00</td>\n      <td>1468.00</td>\n      <td>1468.00</td>\n      <td>1468.00</td>\n      <td>380</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 04:41:00</th>\n      <td>1465.50</td>\n      <td>1465.50</td>\n      <td>1465.50</td>\n      <td>1465.50</td>\n      <td>298</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 18:58:00</th>\n      <td>1846.40</td>\n      <td>1846.40</td>\n      <td>1846.40</td>\n      <td>1846.40</td>\n      <td>200</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 19:14:00</th>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>220</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 19:35:00</th>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>1846.50</td>\n      <td>306</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 19:54:00</th>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>107</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 19:56:00</th>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>1846.06</td>\n      <td>154</td>\n    </tr>\n  </tbody>\n</table>\n<p>122489 rows × 5 columns</p>\n</div>"}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["maindf"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-12T02:45:55.635978200Z", "start_time": "2023-10-12T02:45:55.549434600Z"}}, "id": "3098b5259ed631ca"}, {"cell_type": "code", "execution_count": 4, "outputs": [{"data": {"text/plain": "                          open      close       high       low   volume\nTimeStamp                                                              \n2019-01-02 04:00:00  1473.0100  1470.2000  1473.2000  1461.420     8568\n2019-01-02 06:00:00  1471.0000  1470.0100  1481.0000  1469.210    28937\n2019-01-02 08:00:00  1475.0000  1488.5771  1491.0000  1460.930  1055060\n2019-01-02 10:00:00  1487.3300  1513.3500  1531.0800  1479.315  2295166\n2019-01-02 12:00:00  1513.1501  1549.1805  1553.3600  1511.920  1880805\n...                        ...        ...        ...       ...      ...\n2019-12-30 10:00:00  1853.4800  1843.5800  1858.8916  1840.620  1181514\n2019-12-30 12:00:00  1843.3400  1849.3350  1856.0100  1843.290   578177\n2019-12-30 14:00:00  1849.2800  1847.1800  1849.5000  1843.150   651489\n2019-12-30 16:00:00  1846.8900  1845.5194  1846.8900  1845.000    34537\n2019-12-30 18:00:00  1845.6000  1846.0600  1846.5000  1845.600     1179\n\n[4352 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>open</th>\n      <th>close</th>\n      <th>high</th>\n      <th>low</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>TimeStamp</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2019-01-02 04:00:00</th>\n      <td>1473.0100</td>\n      <td>1470.2000</td>\n      <td>1473.2000</td>\n      <td>1461.420</td>\n      <td>8568</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 06:00:00</th>\n      <td>1471.0000</td>\n      <td>1470.0100</td>\n      <td>1481.0000</td>\n      <td>1469.210</td>\n      <td>28937</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 08:00:00</th>\n      <td>1475.0000</td>\n      <td>1488.5771</td>\n      <td>1491.0000</td>\n      <td>1460.930</td>\n      <td>1055060</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 10:00:00</th>\n      <td>1487.3300</td>\n      <td>1513.3500</td>\n      <td>1531.0800</td>\n      <td>1479.315</td>\n      <td>2295166</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 12:00:00</th>\n      <td>1513.1501</td>\n      <td>1549.1805</td>\n      <td>1553.3600</td>\n      <td>1511.920</td>\n      <td>1880805</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 10:00:00</th>\n      <td>1853.4800</td>\n      <td>1843.5800</td>\n      <td>1858.8916</td>\n      <td>1840.620</td>\n      <td>1181514</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 12:00:00</th>\n      <td>1843.3400</td>\n      <td>1849.3350</td>\n      <td>1856.0100</td>\n      <td>1843.290</td>\n      <td>578177</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 14:00:00</th>\n      <td>1849.2800</td>\n      <td>1847.1800</td>\n      <td>1849.5000</td>\n      <td>1843.150</td>\n      <td>651489</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 16:00:00</th>\n      <td>1846.8900</td>\n      <td>1845.5194</td>\n      <td>1846.8900</td>\n      <td>1845.000</td>\n      <td>34537</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 18:00:00</th>\n      <td>1845.6000</td>\n      <td>1846.0600</td>\n      <td>1846.5000</td>\n      <td>1845.600</td>\n      <td>1179</td>\n    </tr>\n  </tbody>\n</table>\n<p>4352 rows × 5 columns</p>\n</div>"}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sampled_df =  maindf.resample(\"2H\").agg({'open': 'first', 'close': 'last', 'high' : 'max', 'low' : 'min', 'volume': 'sum'}) \n", "# 注意resample默认规则是左闭右开。\n", "sampled_df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-12T02:45:55.699808Z", "start_time": "2023-10-12T02:45:55.580401Z"}}, "id": "67211a9fd8b04175"}, {"cell_type": "code", "execution_count": 5, "outputs": [{"data": {"text/plain": "                          open      close       high       low   volume\nTimeStamp                                                              \n2019-01-02 04:00:00  1473.0100  1470.2000  1473.2000  1461.420     8568\n2019-01-02 06:00:00  1471.0000  1470.0100  1481.0000  1469.210    28937\n2019-01-02 08:00:00  1475.0000  1488.5771  1491.0000  1460.930  1055060\n2019-01-02 10:00:00  1487.3300  1513.3500  1531.0800  1479.315  2295166\n2019-01-02 12:00:00  1513.1501  1549.1805  1553.3600  1511.920  1880805\n...                        ...        ...        ...       ...      ...\n2019-12-30 10:00:00  1853.4800  1843.5800  1858.8916  1840.620  1181514\n2019-12-30 12:00:00  1843.3400  1849.3350  1856.0100  1843.290   578177\n2019-12-30 14:00:00  1849.2800  1847.1800  1849.5000  1843.150   651489\n2019-12-30 16:00:00  1846.8900  1845.5194  1846.8900  1845.000    34537\n2019-12-30 18:00:00  1845.6000  1846.0600  1846.5000  1845.600     1179\n\n[1944 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>open</th>\n      <th>close</th>\n      <th>high</th>\n      <th>low</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>TimeStamp</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2019-01-02 04:00:00</th>\n      <td>1473.0100</td>\n      <td>1470.2000</td>\n      <td>1473.2000</td>\n      <td>1461.420</td>\n      <td>8568</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 06:00:00</th>\n      <td>1471.0000</td>\n      <td>1470.0100</td>\n      <td>1481.0000</td>\n      <td>1469.210</td>\n      <td>28937</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 08:00:00</th>\n      <td>1475.0000</td>\n      <td>1488.5771</td>\n      <td>1491.0000</td>\n      <td>1460.930</td>\n      <td>1055060</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 10:00:00</th>\n      <td>1487.3300</td>\n      <td>1513.3500</td>\n      <td>1531.0800</td>\n      <td>1479.315</td>\n      <td>2295166</td>\n    </tr>\n    <tr>\n      <th>2019-01-02 12:00:00</th>\n      <td>1513.1501</td>\n      <td>1549.1805</td>\n      <td>1553.3600</td>\n      <td>1511.920</td>\n      <td>1880805</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 10:00:00</th>\n      <td>1853.4800</td>\n      <td>1843.5800</td>\n      <td>1858.8916</td>\n      <td>1840.620</td>\n      <td>1181514</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 12:00:00</th>\n      <td>1843.3400</td>\n      <td>1849.3350</td>\n      <td>1856.0100</td>\n      <td>1843.290</td>\n      <td>578177</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 14:00:00</th>\n      <td>1849.2800</td>\n      <td>1847.1800</td>\n      <td>1849.5000</td>\n      <td>1843.150</td>\n      <td>651489</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 16:00:00</th>\n      <td>1846.8900</td>\n      <td>1845.5194</td>\n      <td>1846.8900</td>\n      <td>1845.000</td>\n      <td>34537</td>\n    </tr>\n    <tr>\n      <th>2019-12-30 18:00:00</th>\n      <td>1845.6000</td>\n      <td>1846.0600</td>\n      <td>1846.5000</td>\n      <td>1845.600</td>\n      <td>1179</td>\n    </tr>\n  </tbody>\n</table>\n<p>1944 rows × 5 columns</p>\n</div>"}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["sampled_df = sampled_df[sampled_df.open > 0]\n", "sampled_df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-12T02:45:55.701808100Z", "start_time": "2023-10-12T02:45:55.611014400Z"}}, "id": "134fe614a447925c"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}