# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html
import pandas as pd
# useful for handling different item types with a single interface
from itemadapter import ItemAdapter


class IbPipeline:
    def __init__(self):
        self.data = []  # 用于存储所有item数据

    def process_item(self, item, spider):
        类型 = item['类型']#
        区域 = item['区域']#
        国家地区 = item['国家地区']#
        交易所 = item['交易所']
        产品 = item['产品']#
        时间 = item['时间']

        IB_Symbol = item['IB_Symbol']
        Product_Description = item['Product_Description']
        链接地址 = item['链接地址']
        Symbol = item['Symbol']
        Currency = item['Currency']

        # 将item数据存储到列表中
        self.data.append({
            '类型': 类型,#
            '区域': 区域,#
            '国家地区': 国家地区,#
            '交易所': 交易所,
            '产品': 产品,#
            '时间': 时间,

            'IB_Symbol': IB_Symbol,
            'Product_Description': Product_Description,
            '链接地址': 链接地址,
            'Symbol': Symbol,
            'Currency': Currency,
        })
        # print(f'process_item: {item}')
        # print(f'process_item: IB_Symbol={IB_Symbol}')
        return item

    def close_spider(self, spider):
        # 在Spider关闭时，将数据转换为DataFrame并保存为Excel
        if self.data:
            # 去除所有列都重复的行
            df = pd.DataFrame(self.data)
            # 打印去重前的行数
            print(f'去重前的行数: {df.shape[0]}')
            df = df.drop_duplicates()
            # 打印去重后的行数
            print(f'去重后的行数: {df.shape[0]}')
            # df = df.set_index(['交易所', '时间', 'IB_Symbol']).sort_index()#
            df = df.set_index(['类型', '区域', '国家地区', '交易所', '产品', '时间', 'IB_Symbol']).sort_index()


            # 保存DataFrame为Excel文件
            df.to_excel('IB.xlsx', encoding='utf-8-sig')
