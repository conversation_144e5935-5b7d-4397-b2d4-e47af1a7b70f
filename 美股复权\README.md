# 美股合约式复权工具

## 1. 工具概述

`firstrate_preprocess.py` 是一个处理美股数据的合约式复权工具，可将不同数据源的美股数据按除权日期转换为新合约，支持实盘环境和历史数据处理。

### 1.1 功能特点

- 支持多种数据来源（FirstRate本地数据和IBKR实时数据）
- 数据筛选与清洗自动化
- 时间对齐与规范化处理
- 按除权日期生成新合约的前复权处理
- 高效的并行处理多标的能力
- 完善的断点续传机制，便于长时间运行和处理大量数据

### 1.2 系统组件

完整的美股合约式复权系统包含三个主要组件：

1. **复权因子检查工具**：`check_ib_adjustment.py`
2. **实盘数据库维护工具**：`stock_adj_us.py`
3. **历史数据复权处理工具**：`firstrate_preprocess.py`

## 2. 安装与配置

### 2.1 前置依赖

在使用本工具前，确保：
1. 已安装Python 3.7+和相关依赖库（vnpy等）
2. 数据库环境已正确配置（MySQL）
3. 数据源文件已准备好（FirstRate或IBKR数据）

### 2.2 数据库配置

工具使用多个数据库配置：

1. **原始数据库配置**
   - 用于读取原始数据
   - 通过`raw_settings`配置：
   ```python
   raw_settings = copy(SETTINGS)
   raw_settings['database.database'] = 'vnpy_stk_us_ib'
   raw_mysql_database = create_mysql_database(raw_settings)
   ```

2. **写入数据库配置**
   - 用于保存处理后的数据
   - 通过vnpy默认配置文件`.vntrader/vt_setting.json`配置

3. **复权因子数据库配置**
   - 来源：`vt_setting_remote.json`
   - 通过`DatabaseManager`加载
   - 包含`FutuRehab`、`FirstrateRehab`等复权因子表

### 2.3 匹配文件准备

在运行`firstrate_preprocess.py`之前，必须先获取IB和数据源的匹配关系：

```bash
python get_latest_conid.py -db
```

## 3. 使用方法

### 3.1 命令格式

```bash
python firstrate_preprocess.py [选项]
```

### 3.2 常用参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--data-path` | `-p` | `full_1min` | 数据路径，可选`full_1min`或`month_1min` |
| `--data-dir` | `-d` | `S:\firstrate\stock` | FirstRate数据目录（仅在不使用-db时需要） |
| `--start-date` | `-s` | `2024-03-01` | 开始日期，只处理该日期之后的数据 |
| `--start-conid` | `-c` | 无 | 从指定的conid开始处理，用于断点恢复 |
| `--n` | `-n` | 无 | 处理第1到n个conid（不包含第n个） |
| `--conid-list` | `-l` | 无 | 要处理的conid列表，用逗号分隔 |
| `--ignore-overview` | `-i` | `False` | 忽略数据库中的overview信息，强制全量覆盖 |
| `--db-threads` | `-dt` | `10` | 数据库保存线程数，可根据服务器性能调整 |
| `--process-threads` | `-pt` | `10` | 标的处理线程数，可根据CPU核心数调整 |
| `--use-database` | `-db` | `False` | 使用数据库模式加载数据（优先级最高） |

### 3.3 运行模式

1. **数据库模式**（推荐）
   - 使用`-db`参数
   - 优先级最高，一旦指定则忽略`-d`参数
   - 适用于处理IBKR增量数据

2. **本地文件模式**
   - 使用`-d`参数指定FirstRate数据目录
   - 仅在未指定`-db`参数时生效
   - 适用于处理存量的FirstRate大型本地数据文件

### 3.4 典型用例

1. **测试处理少量数据**
   ```bash
   # 使用数据库模式
   python firstrate_preprocess.py -n 20 -db
   
   # 使用本地文件模式
   python firstrate_preprocess.py -n 20 -d "/path/to/data"
   ```

   使用`-n`参数时会：
   - 输出后续处理的命令，格式为：`-c next_conid`
   - （程序中断时也会）生成SQL文件用于清理相关标的数据
     - SQL文件命名格式：`cleanup_to_n.sql`或`cleanup_to_interrupted.sql`
     - 包含清理DbBarData和DbBarOverview表的SQL语句
     - 同时包含重新处理这些标的的Python命令

2. **断点续传**
   ```bash
   # 使用输出的conid继续处理
   python firstrate_preprocess.py -c 571221112 -db
   ```

3. **处理特定标的列表**
   ```bash
   python firstrate_preprocess.py -l "95514904,548309229,162225735" -db
   ```

4. **后台运行**
   ```bash
   nohup python firstrate_preprocess.py -db > rehab.log 2>&1 &
   ```

5. **增量处理**
   - 工具支持当日重复运行，只处理新增数据
   - 自动检查overview记录的数据范围
   - 只处理overview中没有的新数据，跳过已处理部分

## 4. 运行流程

### 4.1 实盘运行流程

实盘环境下的完整处理流程：

1. **复权因子检测**
   - `check_ib_adjustment.py`定时运行
   - 比较交易所推送的前收盘价与前一交易日收盘价
   - 识别复权事件并记录到`StockAdjustmentUS`表

2. **数据库实时维护**
   - `stock_adj_us.py`根据复权记录更新数据
   - 为实盘订阅和交易的标的创建新合约
   - 执行合约式复权处理

3. **增量数据处理**
   - `firstrate_preprocess.py`运行，处理新增数据
   - 自动检查数据库已有记录，跳过已处理的数据
   - 只处理未复权的数据

### 4.2 历史数据处理流程

处理大量历史数据的标准流程：

1. **准备阶段**
   - 获取IB和数据源的匹配关系
   - 配置数据库连接参数
   - 确定需要处理的数据范围

2. **测试阶段**
   - 先处理少量数据测试（20个标的）
   - 确认处理结果符合预期

3. **全量处理**
   - 使用断点续传功能处理大量数据
   - 可选择后台运行并记录日志
   - 处理完成后验证数据

## 5. 数据源和复权因子

### 5.1 数据来源

工具支持两种主要数据来源：

1. **FirstRate本地数据**
   - 适用于处理历史数据
   - 需要指定本地数据目录

2. **IBKR实时数据**
   - 适用于实盘环境
   - 直接从数据库中读取

### 5.2 复权因子来源

工具使用多个数据源的复权因子，按优先级排序：

1. **实盘复权因子**
   - 来源：`StockAdjustmentUS`表
   - 优势：最早获取，实时性最好
   - 使用场景：实盘订阅和交易的标的

2. **富途复权因子**
   - 来源：`FutuRehab`表
   - 包含完整的除权除息信息
   - 用于历史数据的复权处理

3. **FirstRate复权因子**
   - 来源：`FirstrateRehab`表
   - 作为补充数据源
   - 用于验证和补充富途数据

> **⚠️ 重要提示：** 在处理非交易标的数据前，必须确保`FutuRehab`表已包含原始数据库（如`vnpy_stk_us_ib`）中每个标的最新日期的复权因子。如果对应日期的复权因子缺失（`FirstrateRehab`更新比`FutuRehab`晚1日），程序以该标的该日无复权入库。`FutuRehab`表只在有复权事件时才会有对应日期的记录。

### 5.3 复权因子处理逻辑

处理复权因子的具体策略：

1. **检查复权因子存在性**
   - 先检查富途复权因子
   - 如果富途没有，检查FirstRate是否有对应时间范围的复权数据

2. **缺失处理策略**
   - 如果所有来源都没有复权因子：使用原始数据，不进行复权处理
   - 如果FirstRate有复权数据但富途没有：标记为复权因子缺失，跳过处理

## 6. 疑难解答

### 6.1 常见问题

1. **数据库锁错误**
   - 程序会自动重试
   - 使用指数退避算法等待

2. **复权因子缺失**
   - 记录到日志中，可稍后重试
   - 确认富途数据源是否更新

3. **程序中断**
   - 支持处理SIGTERM和SIGINT信号
   - 会优雅关闭并保存进度

### 6.2 故障排除

遇到问题时的建议步骤：

1. 检查日志文件中的错误信息
2. 确认数据库连接正常
3. 验证数据源文件是否完整
4. 检查复权因子是否缺失
