import time
from datetime import datetime
from typing import List, Dict, Optional
import typer
import pandas as pd
import requests

import shutil
from pathlib import Path
from peewee import chunked, fn
import numpy as np
from loguru import logger

from vnpy.trader.utility import load_json
# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager,  IbProduct , PolygonRehab
from utils.wecom_alert import WecomAlertManager

import traceback
import pandas_market_calendars as mcal
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

app = typer.Typer()

class polygonDataManager:
    def __init__(self):
        """初始化"""
        self.config = load_json("connect_polygon.json")
        self.data_path = Path("data")
        self.rehab_records = []  # 用于批量存储复权数据
        self.symbol_conid_map = self._load_ib_products()

    def _init_nyse_calendar(self) -> list:
        """初始化NYSE交易日历（2020年至今）"""
        try:
            # 获取NYSE交易日历
            nyse = mcal.get_calendar('NYSE')

            # 获取2020年1月1日至今的所有交易日
            start_date = '2025-08-15'
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')

            # 获取交易日历
            schedule = nyse.schedule(start_date=start_date, end_date=end_date)

            # 提取交易日期并排序（已经是按时间排序的）
            trading_days = sorted(schedule.index.date)

            logger.info(f"已加载NYSE交易日历，从{start_date}到{end_date}，共{len(trading_days)}个交易日")
            return trading_days

        except Exception as e:
            logger.error(f"初始化NYSE交易日历失败: {str(e)}")
            return []


    def _load_ib_products(self) -> Dict[str, int]:
        """加载IB产品信息，建立symbol到conid的映射"""
        symbol_conid_map = {}
        try:
            query = IbProduct.select(IbProduct.symbol, IbProduct.conid).where(
                IbProduct.is_latest == True
            ).order_by(IbProduct.created_time.asc())
            for record in query:
                if record.symbol and record.conid:
                    symbol_conid_map[record.symbol] = record.conid
            logger.info(f"已加载{len(symbol_conid_map)}个IB产品映射关系")
        except Exception as e:
            logger.error(f"加载IB产品信息时出错: {str(e)}\n{traceback.format_exc()}")
        return symbol_conid_map

    def _get_ib_conid(self, tipranks_symbol: str) -> int:
        """根据polygon的symbol获取IB的conid"""
        try:
            # 处理TipRanks的symbol格式
            # 先尝试：把.换成空格去搜索
            # 如果搜不到，再把原先.后面的字母前加上PR
            # 比如 ACP.A 先尝试 'ACP A'，搜不到再尝试 'ACP PRA'
            if '.' in tipranks_symbol:
                parts = tipranks_symbol.split('.')
                if len(parts) == 2:
                    base_symbol = parts[0]
                    suffix = parts[1]

                    # 方法1：直接替换点号为空格
                    ib_symbol_1 = f"{base_symbol} {suffix}"
                    conid = self.symbol_conid_map.get(ib_symbol_1)

                    if conid:
                        logger.debug(f"找到conid: {tipranks_symbol} -> {ib_symbol_1} -> {conid}")
                        return conid

                    # 方法2：在空格后的字母前加上'PR'
                    ib_symbol_2 = f"{base_symbol} PR{suffix}"
                    conid = self.symbol_conid_map.get(ib_symbol_2)

                    if conid:
                        logger.debug(f"找到conid: {tipranks_symbol} -> {ib_symbol_2} -> {conid}")
                        return conid

                    # 两种方法都没找到
                    logger.debug(f"未找到conid: {tipranks_symbol} -> 尝试了 {ib_symbol_1} 和 {ib_symbol_2}")
                    return None
                else:
                    return None
            else:
                # 没有点号，直接搜索
                conid = self.symbol_conid_map.get(tipranks_symbol)
                if conid:
                    logger.debug(f"找到conid: {tipranks_symbol} -> {conid}")
                return conid

        except Exception as e:
            logger.warning(f"获取conid失败: {tipranks_symbol}, 错误: {e}")
            return None

    def download_and_extract(self,trade_date:str,type) -> None:
        """获取API中数据"""
        if type == 'dividends':
            try:
                logger.info("尝试通过API获取历史分红数据...")
                response = requests.get(f'{self.config["stock_dividends"]}',params={'ex_dividend_date':trade_date,'order':'asc','sort':"ex_dividend_date",'apiKey':self.config['apiKey']})
                response.raise_for_status()
                if response.status_code == 200:
                    data = response.json()
                    return data
            except Exception as e:
                logger.error(f"获取历史分红数据时发生错误: {e}")
                return None

        else:
            try:
                logger.info("尝试通过API获取历史拆分数据...")
                response = requests.get(f'{self.config["stock_split"]}',
                                        params={'execution_date': trade_date, 'order': 'asc', 'limit': '1000',
                                                'sort':'execution_date',
                                                'apiKey': self.config['apiKey']})
                response.raise_for_status()
                if response.status_code == 200:
                    data = response.json()
                    return data
            except Exception as e:
                logger.error(f"获取历史拆分数据时发生错误: {e}")
                return None

    def save_to_database(self, db_records: List[Dict]) -> bool:
        """保存数据到数据库"""
        try:
            if not db_records:
                logger.info("没有数据需要保存")
                return True

            # 检查表是否存在，如果不存在则创建
            if not PolygonRehab.table_exists():
                logger.info("数据库表不存在，将创建新表")
                PolygonRehab.create_table()

            logger.info(f"开始保存 {len(db_records)} 条记录到数据库...")

            # 使用批量插入方式保存数据
            try:
                with db_manager.common_db.atomic():
                    for batch in chunked(db_records, 50):
                        PolygonRehab.insert_many(batch).on_conflict_replace().execute()

                logger.info("数据保存成功")
            except Exception as e:
                logger.error(f"批量插入数据失败: {e}")
                raise
            return True

        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def update_rehab_data(self,date : datetime) -> None:
        """更新复权数据"""

        if date:
            trade_date = date.strftime('%Y-%m-%d')
        else:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        dividend_datas = self.download_and_extract(trade_date,'dividends')['results']
        logger.info(f"成功获取到 {len(dividend_datas)} 条股票分红记录")
        split_datas = self.download_and_extract( trade_date,'split')['results']
        logger.info(f"成功获取到 {len(split_datas)} 条股票拆分记录")

        records = []
        dividend_records = []
        split_records = []

        for dividend_data in dividend_datas:

            symbol = dividend_data['ticker']
            ex_div_date = dividend_data['ex_dividend_date']
            dividend =  float(dividend_data['cash_amount'])
            records.append({'symbol':symbol,'ex_div_date':ex_div_date,'dividend':dividend,'split_from':None,'split_to':None,'split_ratio': None})
            dividend_records.append({'symbol':symbol,'ex_div_date':ex_div_date,'dividend':dividend,'split_from':None,'split_to':None,'split_ratio': None})
        for split_data in split_datas:

            symbol =split_data['ticker']
            ex_div_date = split_data['execution_date']
            split_from = float(split_data['split_from'])
            split_to = float(split_data['split_to'])
            split_ratio = float(split_from/split_to)
            records.append({'symbol': symbol, 'ex_div_date': ex_div_date, 'dividend': None, 'split_from': split_from,
                            'split_to': split_to, 'split_ratio': split_ratio})
            split_records.append({'symbol': symbol, 'ex_div_date': ex_div_date, 'dividend': None, 'split_from': split_from,
                            'split_to': split_to, 'split_ratio': split_ratio})

        logger.info("合并重复记录...")

        merged_records = self.merge_duplicate_records(records)

        self.save_to_database(db_records=merged_records)
        message = self.format_alert_message(split_records,dividend_records,trade_date)
        self.send_alert(message)

    def format_alert_message(self, today_splits: List[Dict], today_dividends: List[Dict] = None,trade_date:str = None) -> str:
        """格式化警报消息 - Markdown表格形式"""

        if not today_splits and not today_dividends:
            return ""

        # 统计数量
        forward_splits = [s for s in today_splits if s.get('split_ratio') and s.get('split_ratio') < 1]
        reverse_splits = [s for s in today_splits if s.get('split_ratio') and s.get('split_ratio') > 1]
        dividend_count = len(today_dividends) if today_dividends else 0

        # 构建markdown消息
        message_lines = [
            f"# {trade_date} polygon",
            "",
            f"今日拆股 {len(forward_splits)} 只，合股 {len(reverse_splits)} 只，分红 {dividend_count} 只",
            "",
            "| Symbol | Conid | Split_From | Split_To | Split Ratio | Dividend |",
            "|---|---|---|---|---|---|"
        ]

        # 合并所有记录并排序
        all_records = []

        # 添加拆分记录
        for split in today_splits:
            symbol = split.get('symbol', '')
            conid = self._get_ib_conid(symbol)
            conid_str = str(conid) if conid else ''

            all_records.append({
                'symbol': symbol,
                'conid': conid_str,
                'split_from': split.get('split_from', ''),
                'split_to':  split.get('split_to', ''),
                'split_ratio': split.get('split_ratio', ''),
                'dividend': split.get('dividend', ''),
                'ex_div_date': split.get('ex_div_date', ''),
                'type': 'split'
            })

        # 添加分红记录（如果还没有包含在拆分记录中）
        if today_dividends:
            for dividend in today_dividends:
                ticker = dividend.get('symbol', '')
                dividend_date = dividend.get('ex_div_date', '')

                # 检查是否已经存在于拆分记录中
                existing_record = any(
                    s.get('symbol') == ticker and s.get('ex_div_date') == dividend_date
                    for s in today_splits
                )

                if not existing_record:
                    conid = self._get_ib_conid(ticker)
                    conid_str = str(conid) if conid else ''

                    all_records.append({
                             'symbol': ticker,
                                'conid': conid_str,
                                'split_from': dividend.get('split_from', ''),
                                'split_to':  dividend.get('split_to', ''),
                                'split_ratio': dividend.get('split_ratio', ''),
                                'dividend': dividend.get('dividend', ''),
                                'ex_div_date': dividend.get('ex_div_date', ''),
                                'type': 'dividend'
                    })

        # 排序：先按类型分组（拆股、合股、分红），再按symbol字母顺序排序
        def sort_key(record):
            split_ratio = record.get('split_ratio')
            symbol = record.get('symbol', '')

            # 确定记录类型和优先级
            if split_ratio is not None:
                if split_ratio < 1:
                    # 拆股：优先级1，按symbol排序
                    return (1, symbol)
                else:
                    # 合股：优先级2，按symbol排序
                    return (2, symbol)
            else:
                # 分红：优先级3，按symbol排序
                return (3, symbol)

        # 应用排序
        all_records.sort(key=sort_key)


        # 生成表格行
        for record in all_records:
            ticker = record.get('symbol', '')
            conid = record.get('conid', '')
            split_from = record.get('split_from', '')
            split_to = record.get('split_to', '')
            split_ratio = record.get('split_ratio', '')
            dividend = record.get('dividend', '')

            # 格式化数据，处理None值
            ticker_str = ticker if ticker else ''
            conid_str = conid if conid else ''
            split_from_str =  split_from if  split_from else ''
            split_to_str = split_to if split_to else ''
            split_ratio_str = str(split_ratio) if split_ratio is not None else ''
            dividend_str = str(dividend) if dividend is not None else ''

            message_lines.append(
                f"| {ticker_str} | {conid_str} | { split_from_str } | { split_to_str } | {split_ratio_str} | {dividend_str} |")

        return "\n".join(message_lines)

    def send_alert(self, message: str) -> bool:
        """发送企业微信警报"""
        if not message:
            logger.info("没有今日生效的股票拆分或分红，不发送通知")
            return True

        try:
            logger.info("发送企业微信通知...")
            logger.info(f"通知内容:\n{message}")

            manager = WecomAlertManager(use_markdown_v2=True,key= '379668d7-5d0f-42af-9a17-a2858426837c')
            manager.start()
            manager.add_message(message)
            manager.stop()

            logger.info("企业微信通知发送成功")
            return True

        except Exception as e:
            logger.error(f"发送企业微信通知失败: {e}")
            logger.error(traceback.format_exc())
            return False

    def merge_duplicate_records(self, all_records):
        """合并具有相同(symbol, ex_div_date)的记录"""
        logger.info("开始合并重复记录...")

        # 使用字典来合并记录，key为(symbol, ex_div_date)
        merged_dict = {}

        for record in all_records:
            symbol = record.get('symbol', '')
            ex_div_date = record.get('ex_div_date', '')

            if not symbol or not ex_div_date:
                logger.warning(f"跳过无效记录: {record}")
                continue

            key = (symbol, ex_div_date)

            if key in merged_dict:
                # 如果已经有记录，添加到待合并列表
                if isinstance(merged_dict[key], list):
                    merged_dict[key].append(record)
                else:
                    # 将现有记录转换为列表
                    merged_dict[key] = [merged_dict[key], record]
            else:
                # 新记录
                merged_dict[key] = record

        # 合并所有记录
        final_merged_records = []
        for key, records in merged_dict.items():
            if isinstance(records, list):
                # 多条记录需要合并
                merged_record = self.merge_multiple_records(records)
                final_merged_records.append(merged_record)
                logger.debug(f"合并了 {len(records)} 条记录: {key[0]} {key[1]}")
            else:
                # 单条记录直接添加
                final_merged_records.append(records)

        logger.info(f"合并完成: 原始 {len(all_records)} 条记录 -> 合并后 {len(final_merged_records)} 条记录")
        return final_merged_records

    def merge_multiple_records(self, records):
        """合并多条记录"""
        if not records:
            return None

        if len(records) == 1:
            return records[0].copy()

        logger.debug(f"开始合并 {len(records)} 条记录")

        # 初始化合并后的记录
        merged = records[0].copy()
        symbol = merged.get('symbol', '')
        ex_div_date = merged.get('ex_div_date', '')

        # 统计各种类型的记录
        split_records = [r for r in records if r.get('split_ratio') is not None]
        dividend_records = [r for r in records if r.get('dividend') is not None]

        logger.debug(
            f"记录 {symbol} {ex_div_date}: {len(split_records)} 条拆分记录, {len(dividend_records)} 条分红记录")

        # 处理split_ratio
        if len(split_records) > 1:
            # 如果有多条拆分记录，记录警告并保留第一个
            logger.warning(f"发现 {len(split_records)} 条重复的split_ratio: {symbol} {ex_div_date}")
            merged['split_ratio'] = split_records[0].get('split_ratio')
        elif len(split_records) == 1:
            merged['split_ratio'] = split_records[0].get('split_ratio')
        else:
            merged['split_ratio'] = None

        # 处理dividend - 累加所有分红
        if dividend_records:
            total_dividend = 0.0
            dividend_details = []

            for record in dividend_records:
                dividend = record.get('dividend')
                if isinstance(dividend, (int, float)):
                    total_dividend += dividend
                    dividend_details.append(dividend)
                else:
                    logger.warning(f"跳过无效的dividend值: {dividend}")

            if total_dividend > 0:
                merged['dividend'] = total_dividend
                logger.info(f"合并dividend: {symbol} {ex_div_date} -> 累加 {dividend_details} = {total_dividend}")
            else:
                merged['dividend'] = None
        else:
            merged['dividend'] = None

        # 处理description（公司名称）
        descriptions = [r.get('description', '') for r in records if r.get('description')]
        if descriptions:
            # 使用第一个非空描述，如果都不同则记录警告
            first_desc = descriptions[0]
            if len(set(descriptions)) > 1:
                logger.warning(f"描述不同，保留第一个: {descriptions}")
            merged['description'] = first_desc

        return merged


@app.command()
def main(
        date: Optional[datetime] = typer.Option(
            None,  # 默认值设为None
            "--date",
            "-d",  # 建议用-d避免与-u(通常表示UTC)冲突
            help="指定日期（格式: YYYY-MM-DD），默认为当天"
        ),
) -> None:
    start_time = datetime.now()

    logger.info(f"开始运行: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    data_path = Path("data")
    if data_path.exists():
        shutil.rmtree(data_path)
    data_path.mkdir(exist_ok=True)
    manager = polygonDataManager()
    try:

        # 更新复权因子
        logger.info("正在更新复权因子...")
        manager.update_rehab_data(date)
    except Exception as e:
        logger.error(f"数据更新失败: {e},错误信息: \n{traceback.format_exc()}")
        raise
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"结束运行: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"运行时长: {duration}")

if __name__ == "__main__":
    app()