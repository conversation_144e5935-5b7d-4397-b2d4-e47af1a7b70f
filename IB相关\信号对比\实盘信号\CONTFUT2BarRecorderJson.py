import multiprocessing
import sys
from time import sleep
from datetime import datetime, time
from logging import INFO
import pandas as pd
import numpy as np

from vnpy.event import EventEngine
from vnpy.trader.setting import SETTINGS
from vnpy.trader.engine import MainEngine

from data_recorder_fake_bar.utils.ib_gateway import IbGateway
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG

# 订阅行情
from vnpy.trader.object import SubscribeRequest, Exchange
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json

# 数据库入库
# import clickhouse_driver
import pymysql
# import calendar

SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
# print(SETTINGS)

IB_setting = {
    "TWS地址": "localhost",
    "TWS端口": 4002,
    "客户号": 12,
    "交易账户": ""
}


# Chinese futures market trading period (day/night)
DAY_START = time(8, 45)
DAY_END = time(18, 0)

NIGHT_START = time(20, 45)
NIGHT_END = time(2, 45)


def check_trading_period():
    """"""
    current_time = datetime.now().time()

    trading = False
    if (
        (current_time >= DAY_START and current_time <= DAY_END)
        or (current_time >= NIGHT_START)
        or (current_time <= NIGHT_END)
    ):
        trading = True

    # return trading
    return True


def run_child():
    """
    Running in the child process.
    """
    SETTINGS["log.file"] = True

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    main_engine.add_gateway(IbGateway)

    # cta_engine = main_engine.add_app(CtaStrategyApp)
    main_engine.write_log("主引擎创建成功")

    log_engine = main_engine.get_engine("log")
    event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
    main_engine.write_log("注册日志事件监听")

    sleep(10)
    main_engine.connect(IB_setting, "IB")
    main_engine.write_log("连接IB接口")
    
    from vnpy.trader.utility import extract_vt_symbol
    vn_symbol_fut = load_json("vn_symbol_fut.json")
    # vn_symbol_fut = load_json("vn_symbol.json")

    for vt_symbol in vn_symbol_fut:
        print("合约：", vt_symbol)
        symbol, exchange = extract_vt_symbol(vt_symbol)
        req = SubscribeRequest(symbol=symbol, exchange=exchange)
        main_engine.subscribe(req, "IB")
    
    sleep(20)
    print("#"*100)
    # 获取信息并保存到数据库
    contract_dict = main_engine.get_gateway('IB').api.contract_dict
    print(contract_dict)
    save_json("contract_dict.json", contract_dict)
    # print(f'contracts_details: {main_engine.get_gateway("IB").api.contracts_details}')
    # 1. 将CONTFUT替换为FUT；2. 修改格式保存到'data_recorder_setting_ib_fut.json'
    # {
    #     "tick": {},
    #     "bar": {
    #         "ES-20240315-USD-FUT.CME": {
    #             "symbol": "ES-20240315-USD-FUT.CME",
    #             "exchange": "CME",
    #             "gateway_name": "IB"
    #         },
    #         "NQ-20240315-USD-FUT.CME": {
    #             "symbol": "NQ-20240315-USD-FUT.CME",
    #             "exchange": "CME",
    #             "gateway_name": "IB"
    #         }
    #     }
    # }
    data_recorder_setting_ib_fut = {'tick': {}, 'bar': {}}
    for symbol, contract_list in contract_dict.items():
        for contract in contract_list:
            contract = contract.replace("-CONTFUT.", "-FUT.")
            # 将contract以-拆分，每段再以‘ ’拆分，取第一段，再以-合并回来
            # for i, seg in enumerate(contract.split("-")):# MCDOWELL-
            #     seg = seg.split(" ")[0]
            #     if i == 0:
            #         contract = seg
            #     else:
            #         contract += "-" + seg
            data_recorder_setting_ib_fut["bar"][contract] = {
                "symbol": contract,
                "exchange": contract.rsplit(".", 1)[1],
                "gateway_name": "IB"
            }
    save_json("data_recorder_setting_ib_fut.json", data_recorder_setting_ib_fut)

    main_engine.close()
    sys.exit(0)# 退出子进程

def run_parent():
    """
    Running in the parent process.
    """
    print("启动CTA策略守护父进程")

    child_process = None

    trading = check_trading_period()

    # Start child process in trading period
    if trading and child_process is None:
        print("启动子进程")
        child_process = multiprocessing.Process(target=run_child)
        child_process.start()
        print("子进程启动成功")

if __name__ == "__main__":
    # create_table()
    run_parent()
