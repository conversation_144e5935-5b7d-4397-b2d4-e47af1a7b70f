from datetime import datetime
from typing import Dict
import time

from peewee import (
    DoubleField,
    Model,
    Char<PERSON><PERSON>,
    DateTimeField,
    BooleanField,
    BigIntegerField,
    IntegerField
)
from vnpy.trader.constant import Exchange, Interval

# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
# 获取数据库实例
from utils.database_manager import db_manager, FutuRehab

class DbBarData(Model):
    """K线数据表映射对象"""
    symbol: str = CharField()
    exchange: str = CharField()
    datetime: datetime = DateTimeField()
    interval: str = CharField()

    volume: float = DoubleField()
    turnover: float = DoubleField()
    open_interest: float = DoubleField()
    open_price: float = DoubleField()
    high_price: float = DoubleField()
    low_price: float = DoubleField()
    close_price: float = DoubleField()

    class Meta:
        database = db_manager.history_db
        table_name = 'dbbardata'
        indexes: tuple = ((("symbol", "exchange", "interval", "datetime"), True),)

class DbBarOverview(Model):
    """K线汇总数据表映射对象"""

    symbol: str = CharField()
    exchange: str = CharField()
    interval: str = CharField()
    count: int = IntegerField()
    start: datetime = DateTimeField()
    end: datetime = DateTimeField()

    class Meta:
        database = db_manager.history_db
        table_name = 'dbbaroverview'
        indexes: tuple = ((("symbol", "exchange", "interval"), True),)

class IbProduct(Model):
    """IB产品信息表"""
    type = CharField(max_length=63)  # BOND, STK等
    symbol = CharField()  # BRK, BRKHEC等
    exchange_id = CharField(max_length=63)  # NYSE, TRADEWEB, IBDESK等
    local_symbol = CharField()  # BRK1.46510/23/3191918972等
    description = CharField()  # BRK 1.465 10/23/31等
    conid = BigIntegerField()  # 64位整数,范围约±9.2E18
    under_conid = BigIntegerField(null=True)  # 同上
    isin = CharField(null=True)  # CA08465W1005等,可能为null
    cusip = CharField(null=True)  # 08465W100等,可能为null
    currency = CharField(max_length=63)  # USD, JPY, EUR等
    country = CharField(max_length=63)  # US等
    is_prime_exch = BooleanField(null=True)  # T或null
    is_new_product = CharField(max_length=1)  # F或T,只有一个字符
    assoc_entity_id = CharField(max_length=63, null=True)  # e1434383等,可能为null
    created_time = DateTimeField(default=datetime.now)  # 创建时间

    class Meta:
        database = db_manager.common_db
        table_name = 'ibproduct'
        indexes = (
            # 联合唯一索引
            (('conid', 'symbol', 'local_symbol'), True),
        )


def get_ib_symbol_map() -> Dict[str, str]:
    """获取富途代码到IB conid的映射"""
    # 从IB产品表获取映射关系
    mapping = {}
    for product in IbProduct.select(IbProduct.symbol, IbProduct.conid):
        futu_code = f"US.{product.symbol}"
        mapping[str(product.conid)] = futu_code  # 改为conid -> futu_code
    return mapping


def update_prev_close():
    """更新前一日收盘价和交易所前收盘价"""
    start_time = time.time()
    
    # 获取代码映射
    symbol_map = get_ib_symbol_map()
    
    # 先获取有日线数据的标的
    bar_symbols = (DbBarOverview
                  .select(DbBarOverview.symbol)
                  .where(
                      (DbBarOverview.interval == Interval.DAILY.value) &
                      (DbBarOverview.exchange == Exchange.SMART.value)
                  )
                  .tuples())
    
    bar_symbols = [symbol[0] for symbol in bar_symbols]
    
    if not bar_symbols:
        print("没有找到任何日线数据")
        return
        
    # 获取这些标的中需要更新的复权因子记录
    rehab_records = (FutuRehab
                    .select()
                    .where(
                        (FutuRehab.code << [symbol_map[s] for s in bar_symbols 
                                          if s in symbol_map]) &
                        ((FutuRehab.prev_close.is_null()) |
                         (FutuRehab.exchange_prev_close.is_null())) &
                        (FutuRehab.ex_div_date >= datetime(2023, 1, 1))
                    ))
    
    total = rehab_records.count()
    print(f"找到{total}条需要更新的记录")
    
    updated = 0
    last_progress_time = time.time()
    
    for idx, record in enumerate(rehab_records, 1):        
        # 从futu代码反查IB代码
        ib_symbol = None
        for conid, futu_code in symbol_map.items():
            if futu_code == record.code:
                ib_symbol = conid
                break
                
        if not ib_symbol:
            continue
            
        # 获取前一日收盘价
        prev_bar = (DbBarData
                   .select()
                   .where(
                       (DbBarData.symbol == ib_symbol) &
                       (DbBarData.exchange == Exchange.SMART.value) &
                       (DbBarData.interval == Interval.DAILY.value) &
                       (DbBarData.datetime < record.ex_div_date)
                   )
                   .order_by(DbBarData.datetime.desc())
                   .first())
                   
        if not prev_bar:
            continue
            
        # 更新前一日收盘价
        record.prev_close = prev_bar.close_price
        
        # 计算交易所前收盘价
        record.exchange_prev_close = (
            record.prev_close - 
            (record.per_cash_div or 0) - 
            (record.special_dividend or 0)
        ) * (record.split_ratio or 1)
        
        # 保存更新
        record.save()
        updated += 1
        
        if idx % 100 == 0:
            current_time = time.time()
            elapsed = current_time - last_progress_time
            speed = 100 / elapsed
            print(f"进度: {idx}/{total}, 最近100条平均速度: {speed:.2f}条/秒")
            last_progress_time = current_time
    
    total_time = time.time() - start_time
    avg_speed = updated / total_time if total_time > 0 else 0
    print(f"共更新了{updated}条记录")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均速度: {avg_speed:.2f}条/秒")


if __name__ == "__main__":
    update_prev_close()
