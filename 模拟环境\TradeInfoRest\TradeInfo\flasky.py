from app import db, create_app
from app.logConf import logger

# # 测试用
# app = create_app('default')
# 部署用
app = create_app('production')


@app.cli.command()# 用法：flask deploy或者python flasky.py deploy
def deploy():
    """Run deployment tasks."""
    # 初始化数据库
    db.create_all()
    logger.info('数据库初始化成功')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        logger.info('数据库初始化成功')
    # app.run(debug=True)
    # app.run()  # 千万不要在生产服务器中启用调试模式。客户端通过调试器能请求执行远程代码，因此可能导致生产服务器遭到攻击。
    # 服务端配置，host设为0.0.0.0
    app.run(host='0.0.0.0')
