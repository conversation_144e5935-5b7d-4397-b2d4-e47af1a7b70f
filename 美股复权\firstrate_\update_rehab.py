from datetime import datetime
from typing import List, Dict
import typer
import pandas as pd
import requests
import zipfile
import io
import shutil
from pathlib import Path
from peewee import chunked, fn
import numpy as np
from loguru import logger

from vnpy.trader.utility import load_json
# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, FirstrateStock, FirstrateRehab
from utils.mixin import NA_VALUES
import traceback

# 创建CLI应用
app = typer.Typer()

class FirstrateDataManager:
    """FirstRate数据管理器"""
    def __init__(self):
        """初始化"""
        self.config = load_json("connect_firstrate.json")
        self.data_path = Path("data")
        self.rehab_records = []  # 用于批量存储复权数据
        
            
    def get_product_key(self, product_data: dict) -> tuple:
        """获取产品数据的唯一键"""
        return (
            product_data["ticker"],
            product_data["company_name"],
            product_data["country"],
            product_data["state"],
            product_data["exchange"],
            product_data["sector"],
            product_data["industry"],
            product_data["ipo_date"]
        )
            
    def download_and_extract(self, url: str, folder: str) -> None:
        """下载并解压数据文件"""
        # 下载文件
        response = requests.get(url)
        response.raise_for_status()
        
        # 解压文件
        with zipfile.ZipFile(io.BytesIO(response.content)) as zip_ref:
            zip_ref.extractall(self.data_path / folder)
            
    def update_stock_info(self) -> None:
        """更新股票信息"""
        # 下载并解压公司信息文件
        self.download_and_extract(self.config["company_profiles"], "")
            
        # 读取整个CSV文件并找到标题行
        csv_path = self.data_path / "company_profiles.csv"
        temp_path = self.data_path / "company_profiles_fixed.csv"
        
        # 查找标题行
        header_line_index = -1
        with open(csv_path, 'r', encoding='utf-8', errors='ignore') as f:
            for i, line in enumerate(f):
                if line.startswith('Ticker,Company Name'):
                    header_line_index = i
                    break
        
        if header_line_index == -1:
            logger.error("未找到标题行，无法处理CSV文件")
            return
        
        # 如果标题行不在第一行，则创建新的CSV文件，将标题行移到第一行
        if header_line_index > 0:
            logger.info(f"标题行位于第{header_line_index+1}行，正在修复CSV文件")
            with open(csv_path, 'r', encoding='utf-8', errors='ignore') as f_in, open(temp_path, 'w', encoding='utf-8') as f_out:
                # 读取所有行
                lines = f_in.readlines()
                # 将标题行写在第一行
                f_out.write(lines[header_line_index])
                # 写入标题行之前的数据
                for i in range(header_line_index):
                    f_out.write(lines[i])
                # 写入标题行之后的数据
                for i in range(header_line_index + 1, len(lines)):
                    f_out.write(lines[i])
            
            # 使用修复后的文件
            csv_file_to_use = temp_path
        else:
            # 标题行已经在第一行，直接使用原文件
            csv_file_to_use = csv_path
        
        # 读取CSV文件
        df = pd.read_csv(
            csv_file_to_use,
            na_values=NA_VALUES,
            keep_default_na=False,  # 不使用默认的NA值列表
            parse_dates=['Ipo Date'],  # 直接在读取时解析日期列
            date_parser=lambda x: pd.to_datetime(x, errors='coerce'),
            encoding='utf-8'  # 指定编码为UTF-8
        )
        
        # 添加创建时间
        df['created_time'] = datetime.now()
        
        # 重命名列以匹配数据库字段
        df = df.rename(columns={
            'Ticker': 'ticker',
            'Company Name': 'company_name',
            'Country': 'country',
            'State': 'state',
            'Exchange': 'exchange',
            'Sector': 'sector',
            'Industry': 'industry',
            'Ipo Date': 'ipo_date'
        })
        
        # 按ipo_date和ticker排序
        df = df.sort_values(['ipo_date', 'ticker'])
        
        # 获取现有记录的键集合
        existing_keys = set()
        for record in FirstrateStock.select():
            record_dict = {
                "ticker": record.ticker,
                "company_name": record.company_name,
                "country": record.country,
                "state": record.state,
                "exchange": record.exchange,
                "sector": record.sector,
                "industry": record.industry,
                "ipo_date": record.ipo_date if record.ipo_date else None,
            }
            # 将NA值转换为None而不是过滤掉
            record_dict = {k: None if v in NA_VALUES else v for k, v in record_dict.items()}
            key = self.get_product_key(record_dict)
            existing_keys.add(key)

        # 处理新数据
        rows_to_save = []
        for _, row in df.iterrows():
            # 转换为字典并将NA值转换为None
            product_data = {k: None if v in NA_VALUES else v for k, v in row.items()}
            key = self.get_product_key(product_data)
            if key not in existing_keys:
                rows_to_save.append(product_data)
                
        # 保存非重复数据
        if rows_to_save:
            with db_manager.common_db.atomic():
                for batch in chunked(rows_to_save, 100):
                    FirstrateStock.insert_many(batch).on_conflict_replace().execute()
            logger.info(f"更新了 {len(rows_to_save)} 条股票信息")
        else:
            logger.info("没有发现新记录或更新")
        
    def process_dividend_file(self, file_path: Path) -> List[Dict]:
        """处理单个股息文件"""
        symbol = file_path.stem.replace("_divs", "")
        records = []
        
        # 读取股息数据
        df = pd.read_csv(file_path, header=None, names=['ex_div_date', 'dividend'])
        df['symbol'] = symbol
        df['ex_div_date'] = pd.to_datetime(df['ex_div_date'])
        
        # 转换为记录
        for _, row in df.iterrows():
            if pd.notna(row['dividend']):
                records.append({
                    'symbol': symbol,
                    'ex_div_date': row['ex_div_date'],
                    'dividend': float(row['dividend']),
                    'split_ratio': None
                })
            
        return records
        
    def process_split_file(self, file_path: Path) -> List[Dict]:
        """处理单个拆股文件"""
        symbol = file_path.stem
        records = []
        
        # 读取拆股数据
        df = pd.read_csv(file_path, header=None, names=['ex_div_date', 'split_ratio'])
        df['symbol'] = symbol
        df['ex_div_date'] = pd.to_datetime(df['ex_div_date'])
        
        # 转换为记录
        for _, row in df.iterrows():
            if pd.notna(row['split_ratio']):
                records.append({
                    'symbol': symbol,
                    'ex_div_date': row['ex_div_date'],
                    'split_ratio': float(row['split_ratio'])
                })
            
        return records
        
    def update_rehab_data(self, force_update: bool = False) -> None:
        """更新复权因子数据"""
        # 下载并解压股息数据
        self.download_and_extract(self.config["stock_dividends"], "stock_dividends")
            
        # 下载并解压拆股数据
        self.download_and_extract(self.config["stock_splits"], "stock_splits")
            
        # 获取所有需要处理的标的代码
        symbols = set()
        dividends_path = self.data_path / "stock_dividends"
        splits_path = self.data_path / "stock_splits"
        
        # 收集股息文件中的标的
        for file_path in dividends_path.glob("*_divs.txt"):
            symbol = file_path.stem.replace("_divs", "")
            symbols.add(symbol)
            
        # 收集拆股文件中的标的
        for file_path in splits_path.glob("*.txt"):
            if file_path.name != "_splits_readme.txt":  # 跳过说明文件
                symbol = file_path.stem
                symbols.add(symbol)
                
        # 如果是强制更新，清空对应标的的数据
        if force_update and symbols:
            FirstrateRehab.delete().where(FirstrateRehab.symbol.in_(symbols)).execute()
            
        # 获取现有记录的最新日期
        symbol_latest_dates = {}
        if not force_update:
            query = (FirstrateRehab
                    .select(FirstrateRehab.symbol, fn.MAX(FirstrateRehab.ex_div_date).alias('latest_date'))
                    .group_by(FirstrateRehab.symbol))
            for record in query:
                symbol_latest_dates[record.symbol] = record.latest_date
            
        # 处理股息数据
        dividend_records = {}  # 用字典存储，键为(symbol, date)
        for file_path in dividends_path.glob("*_divs.txt"):
            for record in self.process_dividend_file(file_path):
                symbol = record['symbol']
                ex_div_date = record['ex_div_date']
                
                # 只处理新的记录
                if not force_update:
                    latest_date = symbol_latest_dates.get(symbol)
                    if latest_date and ex_div_date <= latest_date:
                        continue
                        
                key = (symbol, ex_div_date)
                if key not in dividend_records:
                    dividend_records[key] = record
                    
        # 处理拆股数据
        for file_path in splits_path.glob("*.txt"):
            if file_path.name != "_splits_readme.txt":  # 跳过说明文件
                for record in self.process_split_file(file_path):
                    symbol = record['symbol']
                    ex_div_date = record['ex_div_date']
                    
                    # 只处理新的记录
                    if not force_update:
                        latest_date = symbol_latest_dates.get(symbol)
                        if latest_date and ex_div_date <= latest_date:
                            continue
                            
                    key = (symbol, ex_div_date)
                    if key in dividend_records:
                        # 合并同一天的股息和拆股数据
                        dividend_records[key].update(record)
                    else:
                        dividend_records[key] = record
        
        # 转换为列表
        self.rehab_records = list(dividend_records.values())
                
        # 批量保存数据
        if self.rehab_records:
            with db_manager.common_db.atomic():
                for batch in chunked(self.rehab_records, 100):
                    FirstrateRehab.insert_many(batch).on_conflict_replace().execute()
                    
            logger.info(f"更新了 {len(self.rehab_records)} 条复权因子数据")
            self.rehab_records = []

@app.command()
def main(
    update: bool = typer.Option(False, "--update", "-u", help="是否强制更新"),
) -> None:
    """FirstRate数据更新工具"""
    start_time = datetime.now()
    logger.info(f"开始运行: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 清空data文件夹
    data_path = Path("data")
    if data_path.exists():
        shutil.rmtree(data_path)
    data_path.mkdir(exist_ok=True)
    
    # 创建数据表
    db_manager.common_db.create_tables([FirstrateStock, FirstrateRehab], safe=True)
    
    # 创建数据管理器
    manager = FirstrateDataManager()
    
    try:
        # 更新股票信息
        logger.info("正在更新股票信息...")
        manager.update_stock_info()
        
        # 更新复权因子
        logger.info("正在更新复权因子...")
        manager.update_rehab_data(update)
                
    except Exception as e:
        logger.error(f"数据更新失败: {e},错误信息: \n{traceback.format_exc()}")
        raise
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"结束运行: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"运行时长: {duration}")

if __name__ == "__main__":
    # 配置loguru
    logger.add("logs/firstrate_rehab_update.log", rotation="10 MB", level="TRACE", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
    app() 