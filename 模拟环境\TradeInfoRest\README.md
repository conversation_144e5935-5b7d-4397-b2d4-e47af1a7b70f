# TradeInfoRest

## Linux环境下的安装和运行（生产环境）

### 安装步骤

#### 1. 安装依赖（[requirements.txt](TradeInfo%2Frequirements.txt)）

```bash
pip install -r requirements.txt
```

#### 2. 配置数据库

##### 2.1. 建库

###### 2.1.1. 若无数据库，安装([setup_database.sh](TradeInfo%2Fsetup_database.sh))
根据账号密码修改[setup_database.sh](TradeInfo%2Fsetup_database.sh)，然后运行脚本
```bash
./setup_database.sh
```

###### 2.1.2. 若有数据库，跳过

##### 2.2. 建表

```bash
python flasky.py deploy
```

#### 3. 配置flask项目数据库连接
根据账号密码修改[config.py](TradeInfo%2Fconfig.py)中的数据库账号密码

#### 4. 安装gunicon
使用gunicon解决flask的并发问题
```bash
pip install gunicorn
```

### 脚本运行

#### 1. 开始运行：使用gunicorn运行flask
```bash
gunicorn -w 3 -b 0.0.0.0:5000 flasky:app -D
```

注：

-w参数：指定worker数量，一般为cpu核心数*2+1

-b参数：指定绑定的ip和端口
cpu核心数获取方法
```python
import multiprocessing
multiprocessing.cpu_count()
```

#### 2. 停止运行：使用kill命令停止gunicorn
```bash
kill -9 `ps -ef | grep gunicorn | grep -v grep | awk '{print $2}'`
```
或
```bash
ps -ef | grep gunicorn | awk '{print $2}' | xargs kill
```

### 使用指南

#### 运行策略文件（[dxjc.py](%B2%DF%C2%D4%D1%F9%C0%FD%2Fdxjc.py)）
```bash
python dxjc.py
```

## Linux环境下的安装和运行（测试环境）

### 安装步骤

#### 1. 安装依赖（[requirements.txt](TradeInfo%2Frequirements.txt)）

```bash
pip install -r requirements.txt
```

#### 2. 配置flask项目数据库连接
根据账号密码修改[config.py](TradeInfo%2Fconfig.py)中的数据库账号密码

#### 3. 配置flask变量
```bash
export FLASK_APP=flasky.py
```

#### 4.  根据开发/生产环境配置flask变量
修改[flasky.py](TradeInfo%2Fflasky.py)第4-7行的注释，根据开发/生产环境选择配置

### 脚本运行

#### 运行flask
```bash
flask run
```

### 使用指南

#### 运行策略文件（[dxjc.py](%B2%DF%C2%D4%D1%F9%C0%FD%2Fdxjc.py)）
```bash
python dxjc.py
```

## Windows环境下的安装和运行

### 安装步骤

#### 安装依赖（[requirements.txt](requirements.txt)）

```bash
pip install -r requirements.txt
```

### 脚本运行

#### 使用脚本（[start.bat](TradeInfo%2Fstart.bat)）
直接双击运行[start.bat](TradeInfo%2Fstart.bat)即可
若通过cmd运行
```bash
start.bat
```

### 使用指南

#### 运行策略文件（[dxjc.py](%B2%DF%C2%D4%D1%F9%C0%FD%2Fdxjc.py)）
```bash
python dxjc.py
```