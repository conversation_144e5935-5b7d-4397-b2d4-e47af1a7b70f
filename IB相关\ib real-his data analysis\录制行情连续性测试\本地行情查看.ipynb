{"cells": [{"cell_type": "code", "id": "d3a6d39100c2545f", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-05-28T05:05:25.930058Z", "start_time": "2024-05-28T05:05:25.520024Z"}}, "source": ["from sqlalchemy import create_engine\n", "from urllib.parse import quote_plus as urlquote\n", "userName = 'root'\n", "password = 'p0o9i8u7'\n", "dbHost = 'localhost'\n", "# dbHost = '************'\n", "dbPort = 3306\n", "dbName = 'vnpyib'\n", "DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'\n", "engine = create_engine(\n", "    DB_CONNECT,\n", "    max_overflow=50,  # 超过连接池大小外最多创建的连接\n", "    pool_size=50,  # 连接池大小\n", "    pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错\n", "    pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）\n", "    encoding='utf-8',\n", "    echo=False\n", ")"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T05:05:37.143109Z", "start_time": "2024-05-28T05:05:37.073407Z"}}, "cell_type": "code", "source": ["import shelve\n", "from vnpy.trader.object import ContractData\n", "\n", "with shelve.open(r'E:\\git\\IntervalTools\\IB相关\\信号对比\\实盘信号\\.vntrader\\ib_contract_data.db') as f:\n", "    # contracts = f.get(\"contracts\", {})\n", "    ib_contracts = f.get(\"ib_contracts\", {})\n", "    # contracts_details = f.get(\"contracts_details\", {})\n", "# print(len(contracts.keys()))\n", "# print(contracts.keys())\n", "# print(contracts.values())\n", "# 打印contracts的一个value\n", "# print(contracts['ES-20231215-USD-FUT.CME'])\n", "\n", "print(len(ib_contracts.keys()))\n", "print(ib_contracts.keys())\n", "# print(ib_contracts.values())\n", "# # 打印ib_contracts的第一个value\n", "# print(ib_contracts[list(ib_contracts.keys())[0]])\n", "symbol0 = list(ib_contracts.keys())[0]\n", "# print(ib_contracts[symbol0])\n", "\n", "\n", "# print(len(contracts_details.keys()))\n", "# print(contracts_details.keys())\n", "# # print(contracts_details.values())\n", "# # print(contracts_details[contracts_details.keys()[0]])# TypeError: 'dict_keys' object is not subscriptable\n", "# key0 = list(contracts_details.keys())[0]\n", "# print(contracts_details[key0])\n", "# print(contracts_details[key0].timeZoneId)\n", "\n", "# 含有ES的合约\n", "# key_ES = [key for key in contracts_details.keys() if 'ES-' in key]\n", "# print(key_ES)\n", "# print(contracts_details[key_ES[1]].timeZoneId)"], "id": "c565cd4f87ff25c", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100\n", "dict_keys(['551601561.CME', '700735038.SMART', '365598061.SMART', '620730920.CME', '673392035.SMART', '526906304.SMART', '36285627.SMART', '316490710.SMART', '274144952.SMART', '324078607.COMEX', '29622921.SMART', '168812158.SMART', '476026060.SMART', '558869874.SMART', '578561452.SMART', '531539945.SEHK', '14016494.SMART', '620730945.CME', '280801120.SMART', '447545380.SMART', '578561429.SMART', '425203222.SMART', '3655833.SMART', '692196414.SMART', '443224436.SMART', '364036140.SMART', '36337428.SMART', '692025016.SMART', '474219659.SMART', '517641765.SMART', '11468575.SMART', '43261373.SMART', '71364351.SMART', '563839543.EUREX', '513880596.SMART', '4815747.SMART', '101898880.SMART', '195014116.SMART', '291995344.SMART', '460492620.SMART', '517867107.SMART', '332794741.SMART', '436977848.SMART', '338978199.SMART', '375868349.SMART', '112131223.SMART', '579848128.SMART', '505002183.SMART', '257311941.SEHKSZSE', '272110.SMART', '639094583.SMART', '98542021.SMART', '76615083.SMART', '254457731.SMART', '583974170.SMART', '393897513.SMART', '444857009.SMART', '195426111.SMART', '909083.SMART', '114929760.SMART', '382633646.SMART', '560066464.SMART', '387597494.SMART', '35111040.SMART', '257313803.SEHKSZSE', '257310782.SEHKSZSE', '13905785.SMART', '494162724.SMART', '665380967.SMART', '542125742.SMART', '40673933.SMART', '525768800.SMART', '679628463.SEHKNTL', '195486659.TWSE', '39234893.SMART', '188625650.SMART', '506568272.SEHK', '76792991.SMART', '38768927.TWSE', '198950253.SMART', '2585769.SMART', '481863646.SMART', '88385302.SMART', '692166900.SMART', '4816415.SMART', '13857203.SMART', '635944944.SMART', '13905675.SMART', '673308395.SEHKSZSE', '619193152.SEHKSTAR', '131083965.SMART', '179002152.SMART', '909271.SMART', '292830677.SMART', '481691285.SMART', '13905884.SMART', '602261424.SMART', '202225016.SMART', '39130003.TWSE', '681320253.HKFE'])\n"]}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T05:11:33.474810Z", "start_time": "2024-05-28T05:11:31.096563Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "\n", "# sql_query = \"select t.* from dbbardata t where instr(symbol, '324078607')\"\n", "# sql_query = \"select t.* from dbbardata t\"\n", "\n", "sql_query = f\"select t.* from dbbardata t where symbol = '{symbol0.rsplit('.', 1)[0]}'\"\n", "df = pd.read_sql_query(sql_query, engine)\n", "df"], "id": "initial_id", "outputs": [{"data": {"text/plain": ["       id     symbol exchange            datetime interval  volume  turnover  \\\n", "0    6469  551601561      CME 2024-05-28 10:25:00       1m    24.0       0.0   \n", "1    6479  551601561      CME 2024-05-28 10:26:00       1m    22.0       0.0   \n", "2    6493  551601561      CME 2024-05-28 10:27:00       1m    25.0       0.0   \n", "3    6494  551601561      CME 2024-05-28 10:28:00       1m     3.0       0.0   \n", "4    6520  551601561      CME 2024-05-28 10:29:00       1m    31.0       0.0   \n", "..    ...        ...      ...                 ...      ...     ...       ...   \n", "161  8085  551601561      CME 2024-05-28 13:06:00       1m    65.0       0.0   \n", "162  8097  551601561      CME 2024-05-28 13:07:00       1m     5.0       0.0   \n", "163  8112  551601561      CME 2024-05-28 13:08:00       1m    27.0       0.0   \n", "164  8123  551601561      CME 2024-05-28 13:09:00       1m    32.0       0.0   \n", "165  8138  551601561      CME 2024-05-28 13:10:00       1m    25.0       0.0   \n", "\n", "     open_interest  open_price  high_price  low_price  close_price  \n", "0        2109535.0     5327.75     5328.00    5327.75      5327.75  \n", "1        2109535.0     5327.50     5327.75    5327.50      5327.50  \n", "2        2109535.0     5327.75     5327.75    5327.50      5327.50  \n", "3        2109535.0     5327.50     5327.50    5327.50      5327.50  \n", "4        2109535.0     5327.50     5327.50    5327.00      5327.00  \n", "..             ...         ...         ...        ...          ...  \n", "161      2109535.0     5327.75     5328.25    5327.75      5328.25  \n", "162      2109535.0     5328.25     5328.25    5328.25      5328.25  \n", "163      2109535.0     5328.25     5328.50    5328.25      5328.25  \n", "164      2109535.0     5328.25     5328.25    5328.00      5328.00  \n", "165      2109535.0     5328.25     5328.25    5328.25      5328.25  \n", "\n", "[166 rows x 12 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>datetime</th>\n", "      <th>interval</th>\n", "      <th>volume</th>\n", "      <th>turnover</th>\n", "      <th>open_interest</th>\n", "      <th>open_price</th>\n", "      <th>high_price</th>\n", "      <th>low_price</th>\n", "      <th>close_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6469</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>1m</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.75</td>\n", "      <td>5328.00</td>\n", "      <td>5327.75</td>\n", "      <td>5327.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6479</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:26:00</td>\n", "      <td>1m</td>\n", "      <td>22.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.50</td>\n", "      <td>5327.75</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6493</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:27:00</td>\n", "      <td>1m</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.75</td>\n", "      <td>5327.75</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6494</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:28:00</td>\n", "      <td>1m</td>\n", "      <td>3.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6520</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:29:00</td>\n", "      <td>1m</td>\n", "      <td>31.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.50</td>\n", "      <td>5327.50</td>\n", "      <td>5327.00</td>\n", "      <td>5327.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>8085</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 13:06:00</td>\n", "      <td>1m</td>\n", "      <td>65.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.75</td>\n", "      <td>5328.25</td>\n", "      <td>5327.75</td>\n", "      <td>5328.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>8097</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 13:07:00</td>\n", "      <td>1m</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>8112</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 13:08:00</td>\n", "      <td>1m</td>\n", "      <td>27.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5328.25</td>\n", "      <td>5328.50</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>8123</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 13:09:00</td>\n", "      <td>1m</td>\n", "      <td>32.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "      <td>5328.00</td>\n", "      <td>5328.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>8138</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 13:10:00</td>\n", "      <td>1m</td>\n", "      <td>25.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "      <td>5328.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>166 rows × 12 columns</p>\n", "</div>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T05:12:02.637404Z", "start_time": "2024-05-28T05:12:02.620405Z"}}, "cell_type": "code", "source": ["# 理论上datetime列的时间上一个和下一个id之间的时间差应该是1分钟，如果不是1分钟，说明数据有问题，查看一下\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "df['diff'] = df['datetime'].diff().dt.total_seconds()\n", "df[df['diff'] != 60]"], "id": "cf19874599038a81", "outputs": [{"data": {"text/plain": ["     id     symbol exchange            datetime interval  volume  turnover  \\\n", "0  6469  551601561      CME 2024-05-28 10:25:00       1m    24.0       0.0   \n", "\n", "   open_interest  open_price  high_price  low_price  close_price  diff  \n", "0      2109535.0     5327.75      5328.0    5327.75      5327.75   NaN  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>datetime</th>\n", "      <th>interval</th>\n", "      <th>volume</th>\n", "      <th>turnover</th>\n", "      <th>open_interest</th>\n", "      <th>open_price</th>\n", "      <th>high_price</th>\n", "      <th>low_price</th>\n", "      <th>close_price</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6469</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>1m</td>\n", "      <td>24.0</td>\n", "      <td>0.0</td>\n", "      <td>2109535.0</td>\n", "      <td>5327.75</td>\n", "      <td>5328.0</td>\n", "      <td>5327.75</td>\n", "      <td>5327.75</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-27T07:50:04.041884Z", "start_time": "2024-05-27T07:50:04.027546Z"}}, "cell_type": "code", "source": ["# 清空mysql的'dbbardata'和'dbbaroverview'表\n", "sql_query = \"delete from d<PERSON><PERSON><PERSON>\"\n", "engine.execute(sql_query)\n", "sql_query = \"delete from dbbaroverview\"\n", "engine.execute(sql_query)\n", "sql_query = \"select t.* from dbbardata t\"\n", "df = pd.read_sql_query(sql_query, engine)\n", "df"], "id": "b0ea0b20cf33e580", "outputs": [{"data": {"text/plain": ["Empty DataFrame\n", "Columns: [id, symbol, exchange, datetime, interval, volume, turnover, open_interest, open_price, high_price, low_price, close_price]\n", "Index: []"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>datetime</th>\n", "      <th>interval</th>\n", "      <th>volume</th>\n", "      <th>turnover</th>\n", "      <th>open_interest</th>\n", "      <th>open_price</th>\n", "      <th>high_price</th>\n", "      <th>low_price</th>\n", "      <th>close_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "execution_count": 115}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-27T06:10:41.091496Z", "start_time": "2024-05-27T06:10:41.073494Z"}}, "cell_type": "code", "source": ["import redis\n", "\n", "host = 'localhost'\n", "port = 6380\n", "passwd = 'p0o9i8u7'\n", "r = redis.Redis(host=host, port=port, password=passwd, decode_responses=True)"], "id": "2f066584afa3fb10", "outputs": [], "execution_count": 45}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-27T09:01:03.475007Z", "start_time": "2024-05-27T09:01:03.456007Z"}}, "cell_type": "code", "source": "r.keys()", "id": "d2a6bdbd59a9bb57", "outputs": [{"data": {"text/plain": ["['29622921.SMART',\n", " '551601561.CME',\n", " '324078607.COMEX',\n", " '447545380.SMART',\n", " '681320253.HKFE',\n", " '620730945.CME',\n", " '620730920.CME',\n", " '563839543.EUREX']"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "execution_count": 142}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-27T08:20:21.376758Z", "start_time": "2024-05-27T08:20:21.321261Z"}}, "cell_type": "code", "source": ["# 删除所有keys\n", "<PERSON><PERSON>()\n", "r.keys()"], "id": "f37513bdf28b750d", "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "execution_count": 122}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}