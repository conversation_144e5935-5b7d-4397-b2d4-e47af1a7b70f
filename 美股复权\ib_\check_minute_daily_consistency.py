#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IB分钟线与日线数据一致性检查脚本
检查2025.8.15 04:00:00前最后一根分钟线与当日日线收盘价的差异
应用复权因子进行修正，输出仍有显著差异的股票供人工核查
"""

import os
import sys
import traceback
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Tuple
import pandas as pd
import numpy as np
import typer
from loguru import logger

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

from vnpy.trader.utility import load_json, ZoneInfo
from vnpy.trader.setting import SETTINGS
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.database import DB_TZ

# 加载数据库配置
from utils.mysql_database import create_mysql_database
from utils.database_manager import db_manager, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tip<PERSON>s<PERSON><PERSON><PERSON>, dbtz_convert
from firstrate_.update_conid import get_conid_mapping

# 时区定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时间

# 配置日志
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


# 目标检查时间点（上海时间，无时区信息）
TARGET_TIME = datetime(2025, 8, 15, 4, 0, 0)

class ConsistencyChecker:
    """分钟线与日线数据一致性检查器"""
    
    def __init__(self):
        """初始化数据库连接"""
        # 初始化分钟线数据库连接
        minute_settings = SETTINGS.copy()
        minute_settings['database.database'] = 'vnpy_stk_us_ib_m'
        self.minute_db, self.MinuteBarData, _, self.MinuteBarOverview, _ = create_mysql_database(minute_settings)

        # 初始化日线数据库连接
        daily_settings = SETTINGS.copy()
        daily_settings['database.database'] = 'vnpy_stk_us_ib'
        self.daily_db, self.DailyBarData, _, _, _ = create_mysql_database(daily_settings)
        
        # 复权因子数据库已通过db_manager连接
        self.common_db = db_manager.common_db

        # 结果存储
        self.results = []

        # 数据缓存
        self.conid_symbol_map = {}  # conid -> symbol映射
        self.conid_stable_map = {}  # conid -> stable_id映射
        self.stable_latest_map = {}  # stable_id -> latest_conid_info映射
        self.futu_factors_cache = {}  # futu_code -> factors_df映射
        self.tipranks_factors_cache = {}  # symbol -> factors_df映射

        # 初始化数据缓存
        self._load_data_cache()

    def _load_data_cache(self) -> None:
        """加载数据缓存以提高查询性能"""
        logger.info("开始加载数据缓存...")

        try:
            # 1. 加载所有IbProduct数据到缓存
            logger.info("加载IbProduct数据...")
            for product in IbProduct.select(IbProduct.conid, IbProduct.symbol, IbProduct.stable_id, IbProduct.is_latest):
                conid_str = str(product.conid)

                # 建立conid -> symbol映射
                if product.symbol:
                    self.conid_symbol_map[conid_str] = product.symbol

                # 建立conid -> stable_id映射
                if product.stable_id:
                    self.conid_stable_map[conid_str] = product.stable_id

                # 建立stable_id -> latest_conid_info映射
                if product.is_latest and product.stable_id:
                    self.stable_latest_map[product.stable_id] = {
                        'conid': str(product.conid),
                        'symbol': product.symbol or ""
                    }

            logger.info(f"已加载 {len(self.conid_symbol_map)} 个conid->symbol映射")
            logger.info(f"已加载 {len(self.conid_stable_map)} 个conid->stable_id映射")
            logger.info(f"已加载 {len(self.stable_latest_map)} 个stable_id->latest映射")

            # 2. 获取dbbaroverview的最小start时间
            logger.info("查询dbbaroverview的最小start时间...")
            min_start_query = (self.MinuteBarOverview
                             .select(self.MinuteBarOverview.start)
                             .where(
                                 (self.MinuteBarOverview.exchange == 'SMART') &
                                 (self.MinuteBarOverview.interval == '1m')
                             )
                             .order_by(self.MinuteBarOverview.start.asc())
                             .limit(1))

            min_start_record = min_start_query.first()
            if min_start_record:
                min_start_date = min_start_record.start.date()
                logger.info(f"dbbaroverview最小start时间: {min_start_date}")
            else:
                # 如果没有数据，使用默认日期
                min_start_date = TARGET_TIME
                logger.warning(f"未找到dbbaroverview数据，使用默认日期: {min_start_date}")

            # 3. 只加载最小start时间以来且share_factor不为1的FutuRehab数据到缓存
            logger.info(f"加载 {min_start_date} 以来且share_factor不为1的FutuRehab数据...")
            futu_factors = {}
            factor_query = (FutuRehab
                          .select()
                          .where(
                              (FutuRehab.ex_div_date >= min_start_date) &
                              (FutuRehab.share_factor != 1.0) &
                              (FutuRehab.share_factor.is_null(False))
                          ))

            for factor in factor_query:
                code = factor.code
                if code not in futu_factors:
                    futu_factors[code] = []

                futu_factors[code].append({
                    'ex_div_date': factor.ex_div_date.date() if hasattr(factor.ex_div_date, 'date') else factor.ex_div_date,
                    'share_factor': factor.share_factor if factor.share_factor else 1.0,
                    'div_factor': factor.div_factor if factor.div_factor else 1.0,
                    'combined_factor': factor.combined_factor if factor.combined_factor else 1.0
                })

            # 转换为DataFrame并缓存
            for code, factors_list in futu_factors.items():
                if factors_list:
                    df = pd.DataFrame(factors_list)
                    df.set_index('ex_div_date', inplace=True)
                    df.sort_index(inplace=True)
                    self.futu_factors_cache[code] = df

            logger.info(f"已加载 {len(self.futu_factors_cache)} 个股票的FutuRehab复权因子数据（{min_start_date}以来）")

            # 4. 加载最小start时间以来且split_ratio不为null的TipranksRehab数据到缓存
            logger.info(f"加载 {min_start_date} 以来且split_ratio不为null的TipranksRehab数据...")
            tipranks_factors = {}
            tipranks_query = (TipranksRehab
                            .select()
                            .where(
                                (TipranksRehab.ex_div_date >= min_start_date) &
                                (TipranksRehab.split_ratio.is_null(False))
                            ))

            for factor in tipranks_query:
                symbol = factor.symbol
                if symbol not in tipranks_factors:
                    tipranks_factors[symbol] = []

                tipranks_factors[symbol].append({
                    'ex_div_date': factor.ex_div_date.date() if hasattr(factor.ex_div_date, 'date') else factor.ex_div_date,
                    'split_ratio': factor.split_ratio,
                })

            # 转换为DataFrame并缓存
            for symbol, factors_list in tipranks_factors.items():
                if factors_list:
                    df = pd.DataFrame(factors_list)
                    df.set_index('ex_div_date', inplace=True)
                    df.sort_index(inplace=True)
                    self.tipranks_factors_cache[symbol] = df

            logger.info(f"已加载 {len(self.tipranks_factors_cache)} 个股票的TipranksRehab复权因子数据（{min_start_date}以来）")
            logger.info("数据缓存加载完成")

        except Exception as e:
            logger.error(f"加载数据缓存失败: {e}")
            logger.error(traceback.format_exc())

    def get_stock_list(self) -> List[str]:
        """获取所有股票代码列表"""
        try:
            # 从分钟线数据库的overview表获取所有股票代码
            query = (self.MinuteBarOverview
                    .select(self.MinuteBarOverview.symbol)
                    .where(
                        (self.MinuteBarOverview.exchange == 'SMART') &
                        (self.MinuteBarOverview.interval == '1m') &
                        (self.MinuteBarOverview.start < TARGET_TIME)  # 确保有目标时间前的数据
                    ))

            symbols = [item.symbol for item in query]
            logger.info(f"获取到 {len(symbols)} 只股票（从overview表）")
            return symbols

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_last_minute_bar(self, symbol: str) -> Optional[Tuple[datetime, float]]:
        """获取指定股票在目标时间前的最后一根分钟线"""
        try:
            query = (self.MinuteBarData
                    .select(self.MinuteBarData.datetime, self.MinuteBarData.close_price)
                    .where(
                        (self.MinuteBarData.symbol == symbol) &
                        (self.MinuteBarData.exchange == 'SMART') &
                        (self.MinuteBarData.interval == '1m') &
                        (self.MinuteBarData.datetime < TARGET_TIME)
                    )
                    .order_by(self.MinuteBarData.datetime.desc())
                    .limit(1))
            
            result = query.first()
            if result:
                return result.datetime, result.close_price
            return None
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 分钟线数据失败: {e}")
            return None
    
    def get_daily_close(self, symbol: str, et_date: date) -> Optional[float]:
        """获取指定股票指定美东日期的日线收盘价

        Args:
            symbol: 股票代码
            et_date: 美东日期
        """
        try:
            # 构造美东当日0点，然后转回DB_TZ
            et_start = datetime.combine(et_date, datetime.min.time()).replace(tzinfo=ET_TZ)

            # 转换为DB_TZ并移除时区信息，作为精确的查询时间
            query_time = et_start.astimezone(DB_TZ).replace(tzinfo=None)

            query = (self.DailyBarData
                    .select(self.DailyBarData.close_price)
                    .where(
                        (self.DailyBarData.symbol == symbol) &
                        (self.DailyBarData.exchange == 'SMART') &
                        (self.DailyBarData.interval == 'd') &
                        (self.DailyBarData.datetime == query_time)
                    )
                    .limit(1))
            
            result = query.first()
            if result:
                return result.close_price
            return None
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 日线数据失败: {e}")
            return None
    
    def get_adjustment_factors(self, conid: str, et_date: date) -> pd.DataFrame:
        """获取指定股票从指定美东日期开始的复权因子

        Args:
            conid: 股票代码（IB的conid）
            et_date: 美东日期
        """
        try:
            # 1. 从缓存中查询conid对应的stable_id
            stable_id = self.conid_stable_map.get(conid)
            if not stable_id:
                logger.debug(f"未找到conid {conid} 的stable_id")
                return pd.DataFrame()

            # 2. 从缓存中查询最新的conid和symbol
            latest_info = self.stable_latest_map.get(stable_id)
            if not latest_info or not latest_info['symbol']:
                logger.debug(f"未找到stable_id {stable_id} 的最新conid和symbol")
                return pd.DataFrame()

            # 3. 获取最新的conid和symbol信息
            latest_conid = latest_info['conid']
            ib_symbol = latest_info['symbol']

            # 只有当conid发生变更时才记录
            if conid != latest_conid:
                logger.info(f"conid变更: {conid} -> {latest_conid} (symbol: {ib_symbol})")

            # 4. 将最新的ib_symbol转换为futu的code格式：'US.{ib_symbol.replace(' ','.')}'
            futu_code = f"US.{ib_symbol.replace(' ', '.')}"

            # 5. 从缓存中获取复权因子数据
            if futu_code not in self.futu_factors_cache:
                return pd.DataFrame()

            factors_df = self.futu_factors_cache[futu_code]

            # 6. 筛选指定日期之后的复权因子
            et_datetime = pd.Timestamp(et_date)
            future_factors = factors_df[factors_df.index > et_datetime]

            return future_factors

        except Exception as e:
            logger.error(f"获取股票 {conid} 复权因子失败: {e}")
            return pd.DataFrame()

    def get_tipranks_factors(self, symbol: str, et_date: date) -> pd.DataFrame:
        """从tipranks缓存中获取复权因子

        Args:
            symbol: 股票代码（ib_symbol）
            et_date: 美东日期

        Returns:
            pd.DataFrame: 复权因子数据
        """
        try:
            # 从缓存中获取tipranks复权因子数据
            if symbol not in self.tipranks_factors_cache:
                return pd.DataFrame()

            factors_df = self.tipranks_factors_cache[symbol]

            # 筛选指定日期之后的复权因子
            future_factors = factors_df[factors_df.index > et_date]

            return future_factors

        except Exception as e:
            logger.error(f"获取股票 {symbol} tipranks复权因子失败: {e}")
            return pd.DataFrame()
    
    def apply_reverse_adjustment(self, conid: str, minute_price: float, daily_price: float,
                               factors_df: pd.DataFrame, et_date: date) -> Tuple[float, str]:
        """对价格应用逆复权处理，每应用一个因子都判断是否会使差距降低

        Args:
            conid: 股票代码（IB的conid）
            minute_price: 分钟线价格
            daily_price: 日线价格
            factors_df: 复权因子DataFrame
            et_date: 美东日期

        Returns:
            Tuple[float, str]: (调整后价格, 应用的复权因子值每个前加/)
        """
        if factors_df.empty:
            return minute_price, ""

        try:
            # 找到价格日期之后的所有复权因子（按时间顺序）
            # 筛选指定日期之后的复权因子
            future_factors = factors_df[factors_df.index > et_date]

            if future_factors.empty:
                return minute_price, ""

            # 计算初始差距
            current_price = minute_price
            current_diff = abs(daily_price - current_price)
            applied_factors = []

            # 按时间顺序逐个应用股数变动因子的逆处理
            for ex_date, row in future_factors.iterrows():
                share_factor = row['share_factor']
                if share_factor != 1.0 and share_factor > 0:
                    # 计算应用此因子后的价格
                    test_price = current_price / share_factor
                    test_diff = abs(daily_price - test_price)

                    # 判断是否会使差距降低
                    if test_diff < current_diff:
                        # 差距降低，应用此因子
                        current_price = test_price
                        current_diff = test_diff
                        applied_factors.append(str(share_factor))
                        symbol = self.conid_symbol_map.get(conid, conid)
                        logger.debug(f"conid {conid}({symbol}) 应用逆复权因子 {share_factor} 于 {ex_date}, "
                                   f"价格: {minute_price} -> {current_price}, "
                                   f"差距: {current_diff:.4f}")
                    else:
                        # 差距不降低，停止应用后续因子
                        symbol = self.conid_symbol_map.get(conid, conid)
                        logger.info(f"conid {conid}({symbol}) 逆复权因子 {share_factor} 于 {ex_date} 不会降低差距，停止处理 - "
                                  f"日线价格: {daily_price:.4f}, 分钟线价格: {current_price:.4f} -> {test_price:.4f}, "
                                  f"差距: {current_diff:.4f} -> {test_diff:.4f}")
                        break
                elif share_factor <= 0:
                    logger.warning(f"发现异常的复权因子 {share_factor}，跳过处理")

            # 返回调整后价格和应用的因子值（每个数字前加/，便于SQL中直接使用）
            applied_factors_str = "/" + "/".join(applied_factors) if applied_factors else ""
            return current_price, applied_factors_str

        except Exception as e:
            logger.error(f"应用逆复权失败: {e}")
            return minute_price, ""
    
    def check_stock_consistency(self, symbol: str) -> Dict:
        """检查单只股票的数据一致性"""
        try:
            # 获取symbol对应的ib_symbol
            ib_symbol = self.conid_symbol_map.get(symbol, "")

            # 1. 获取最后一根分钟线
            minute_result = self.get_last_minute_bar(symbol)
            if not minute_result:
                logger.debug(f"股票 {symbol} 无分钟线数据，跳过")
                return {
                    'conid': symbol,
                    'symbol': ib_symbol,
                    'skip_reason': '无分钟线数据',
                    'minute_time': None,
                    'minute_price': None,
                    'daily_price': None,
                    'adjusted_minute_price': None,
                    'initial_diff_ratio': None,
                    'final_diff_ratio': None,
                    'price_diff': None,
                    'threshold': None,
                    'needs_review': False,
                    'factors_applied': "",
                    'total_factors': 0,
                    'share_factor_details': "",
                    'tipranks_factor_details': ""
                }
            
            minute_time, minute_price = minute_result

            # 统一时区转换：分钟线时间转换为美东日期
            minute_time_dbtz = minute_time.replace(tzinfo=DB_TZ)
            minute_time_et = minute_time_dbtz.astimezone(ET_TZ)
            et_date = minute_time_et.date()

            # 2. 获取当日日线收盘价
            daily_price = self.get_daily_close(symbol, et_date)
            if daily_price is None:
                logger.debug(f"股票 {symbol} 无当日日线数据，跳过")
                return {
                    'conid': symbol,
                    'symbol': ib_symbol,
                    'skip_reason': '无当日日线数据',
                    'minute_time': minute_time,
                    'minute_price': minute_price,
                    'daily_price': None,
                    'adjusted_minute_price': None,
                    'initial_diff_ratio': None,
                    'final_diff_ratio': None,
                    'price_diff': None,
                    'threshold': None,
                    'needs_review': True,
                    'factors_applied': "",
                    'total_factors': 0,
                    'share_factor_details': "",
                    'tipranks_factor_details': ""
                }

            # 3. 计算初始差异
            initial_diff_ratio = max(daily_price / minute_price, minute_price / daily_price) - 1

            # 4. 查询复权因子
            factors_df = self.get_adjustment_factors(symbol, et_date)

            # 5. 生成share_factor_details
            share_factor_details = ""
            if not factors_df.empty:
                details = []
                for ex_date, row in factors_df.iterrows():
                    share_factor = row['share_factor']
                    if share_factor != 1.0:
                        details.append(f"{ex_date.strftime('%Y-%m-%d')}:{share_factor}")
                share_factor_details = ";".join(details)

            # 6. 查询tipranks复权因子
            tipranks_symbol = ib_symbol.replace(' ', '.')
            tipranks_factors_df = self.get_tipranks_factors(tipranks_symbol, et_date)
            tipranks_factor_details = ""
            if not tipranks_factors_df.empty:
                tipranks_details = []
                for ex_date, row in tipranks_factors_df.iterrows():
                    split_ratio = row['split_ratio']
                    if pd.notna(split_ratio):
                        tipranks_details.append(f"{ex_date.strftime('%Y-%m-%d')}:{split_ratio}")
                tipranks_factor_details = ";".join(tipranks_details)

            # 7. 应用复权因子
            adjusted_minute_price, applied_factors_str = self.apply_reverse_adjustment(symbol, minute_price, daily_price, factors_df, et_date)

            # 8. 计算复权后差异
            final_diff_ratio = max(daily_price / adjusted_minute_price, adjusted_minute_price / daily_price) - 1

            # 9. 判断是否需要人工核查
            if daily_price <= 1:
                threshold = max(0.0005, daily_price * 0.001)  # 价格<=1时使用更小的阈值
            else:
                threshold = max(0.05, daily_price * 0.001)  # 价格>1时使用原阈值
            price_diff = abs(daily_price - adjusted_minute_price)

            result = {
                'conid': symbol,
                'symbol': ib_symbol,
                'skip_reason': "",
                'minute_time': minute_time,
                'minute_price': minute_price,
                'daily_price': daily_price,
                'adjusted_minute_price': adjusted_minute_price,
                'initial_diff_ratio': initial_diff_ratio,
                'final_diff_ratio': final_diff_ratio,
                'price_diff': price_diff,
                'threshold': threshold,
                'needs_review': price_diff > threshold,
                'factors_applied': applied_factors_str,  # 应用的复权因子值字符串（每个前加/）
                'total_factors': len(factors_df),  # 总复权因子数量
                'share_factor_details': share_factor_details,  # 不为1的share_factor的ex_div_date和factor
                'tipranks_factor_details': tipranks_factor_details  # tipranks的split_ratio不为null的ex_div_date和factor
            }
            
            # if result['needs_review']:
            #     logger.warning(f"股票 {symbol} 需要人工核查: 差异 {price_diff:.4f} > 阈值 {threshold:.4f}")
            
            return result

        except Exception as e:
            logger.error(f"检查股票 {symbol} 一致性失败: {e}")
            ib_symbol = self.conid_symbol_map.get(symbol, "")
            return {
                'conid': symbol,
                'symbol': ib_symbol,
                'skip_reason': f'处理失败: {str(e)}',
                'minute_time': None,
                'minute_price': None,
                'daily_price': None,
                'adjusted_minute_price': None,
                'initial_diff_ratio': None,
                'final_diff_ratio': None,
                'price_diff': None,
                'threshold': None,
                'needs_review': False,
                'factors_applied': "",
                'total_factors': 0,
                'share_factor_details': "",
                'tipranks_factor_details': ""
            }

    def run_consistency_check(self, conid_list: Optional[List[str]] = None) -> None:
        """运行完整的一致性检查

        Args:
            conid_list: 指定的conid列表，None表示检查所有股票
        """
        logger.info("开始IB分钟线与日线数据一致性检查")
        logger.info(f"目标检查时间: {TARGET_TIME}")

        # 获取股票列表
        if conid_list:
            symbols = conid_list
            logger.info(f"指定检查 {len(symbols)} 个conid: {symbols}")
        else:
            symbols = self.get_stock_list()
            if not symbols:
                logger.error("未获取到股票列表，退出检查")
                return

        # 统计信息
        total_stocks = len(symbols)
        processed_stocks = 0
        skipped_stocks = 0
        review_needed_stocks = 0

        logger.info(f"开始检查 {total_stocks} 只股票...")

        # 逐个检查股票
        for i, symbol in enumerate(symbols, 1):
            try:
                result = self.check_stock_consistency(symbol)

                processed_stocks += 1
                self.results.append(result)

                if result['skip_reason']:
                    skipped_stocks += 1
                    logger.debug(f"[{i}/{total_stocks}] 跳过股票 {symbol}: {result['skip_reason']}")
                elif result['needs_review']:
                    review_needed_stocks += 1

            except Exception as e:
                logger.error(f"处理股票 {symbol} 时发生错误: {e}")
                # 即使出错也要添加结果记录
                ib_symbol = self.conid_symbol_map.get(symbol, "")
                error_result = {
                    'conid': symbol,
                    'symbol': ib_symbol,
                    'skip_reason': f'处理异常: {str(e)}',
                    'minute_time': None,
                    'minute_price': None,
                    'daily_price': None,
                    'adjusted_minute_price': None,
                    'initial_diff_ratio': None,
                    'final_diff_ratio': None,
                    'price_diff': None,
                    'threshold': None,
                    'needs_review': False,
                    'factors_applied': "",
                    'total_factors': 0,
                    'share_factor_details': "",
                    'tipranks_factor_details': ""
                }
                self.results.append(error_result)
                processed_stocks += 1
                skipped_stocks += 1

        # 输出统计结果
        logger.info("=" * 60)
        logger.info("检查完成统计:")
        logger.info(f"总股票数: {total_stocks}")
        logger.info(f"成功处理: {processed_stocks}")
        logger.info(f"跳过股票: {skipped_stocks}")
        logger.info(f"需要核查: {review_needed_stocks}")
        logger.info("=" * 60)

        # 保存结果到文件
        self.save_results()

        # 生成SQL修复语句
        self.generate_sql_fix_file()

    def save_results(self) -> None:
        """保存检查结果到文件"""
        if not self.results:
            logger.warning("无结果数据，跳过文件保存")
            return

        try:
            # 转换为DataFrame
            df = pd.DataFrame(self.results)

            # 按final_diff_ratio倒序排列（None值排在最后）
            df['final_diff_ratio_sort'] = df['final_diff_ratio'].fillna(-1)  # 将None替换为-1用于排序
            df_sorted = df.sort_values('final_diff_ratio_sort', ascending=False)
            df_sorted = df_sorted.drop('final_diff_ratio_sort', axis=1)  # 删除辅助排序列

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存所有结果到单个CSV文件
            results_file = f"consistency_check_{timestamp}.csv"
            df_sorted.to_csv(results_file, index=False, encoding='utf-8-sig')
            logger.info(f"检查结果已保存到: {results_file}")

            # 统计需要核查的结果
            review_df = df_sorted[df_sorted['needs_review'] == True]
            if not review_df.empty:
                logger.info("\n前10个最大差异股票:")
                logger.info("-" * 80)
                for _, row in review_df.head(10).iterrows():
                    if pd.notna(row['final_diff_ratio']):
                        logger.info(f"conid {row['conid']}({row['symbol']}): 差异比例 {row['final_diff_ratio']:.4f} "
                                  f"({row['final_diff_ratio']*100:.2f}%), "
                                  f"分钟线 {row['adjusted_minute_price']:.4f} vs "
                                  f"日线 {row['daily_price']:.4f}")
                    else:
                        logger.info(f"conid {row['conid']}({row['symbol']}): {row['skip_reason']}")
            else:
                logger.info("所有股票数据一致性良好，无需人工核查")

        except Exception as e:
            logger.error(f"保存结果文件失败: {e}")
            logger.error(traceback.format_exc())

    def generate_sql_fix_file(self) -> None:
        """生成SQL修复语句文件"""
        if not self.results:
            logger.warning("无结果数据，跳过SQL文件生成")
            return

        try:
            # 筛选出需要应用复权因子的股票
            fix_needed = [r for r in self.results if r['factors_applied']]

            if not fix_needed:
                logger.info("无需要应用复权因子的股票，跳过SQL文件生成")
                return

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sql_file = f"consistency_fix_{timestamp}.sql"

            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write("-- IB分钟线数据复权修复SQL语句\n")
                f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"-- 目标检查时间: {TARGET_TIME}\n")
                f.write(f"-- 需要修复的股票数量: {len(fix_needed)}\n\n")

                for result in fix_needed:
                    symbol = result['symbol']
                    factors_str = result['factors_applied']

                    f.write(f"-- 股票: {symbol}\n")
                    f.write(f"-- 应用的复权因子: {factors_str}\n")
                    f.write(f"-- 修复前差异: {result['initial_diff_ratio']:.4f}\n")
                    f.write(f"-- 修复后差异: {result['final_diff_ratio']:.4f}\n")

                    # 直接使用每个数字前加/的字符串构造SQL表达式
                    # 例如: "/2.0/1.5" -> "1.0/2.0/1.5"
                    cumulative_factor_expr = f"1.0{factors_str}"

                    # 生成UPDATE语句修复分钟线数据
                    f.write(f"UPDATE vnpy_stk_us_ib_m.dbbardata\n")
                    f.write(f"SET \n")
                    f.write(f"    open_price = open_price {cumulative_factor_expr},\n")
                    f.write(f"    high_price = high_price {cumulative_factor_expr},\n")
                    f.write(f"    low_price = low_price {cumulative_factor_expr},\n")
                    f.write(f"    close_price = close_price {cumulative_factor_expr},\n")
                    f.write(f"    volume = volume {cumulative_factor_expr.replace('/', '*')}\n")
                    f.write(f"WHERE symbol = '{symbol}' \n")
                    f.write(f"  AND exchange = 'SMART'\n")
                    f.write(f"  AND `interval` = '1m'\n")
                    f.write(f"  AND datetime < '{TARGET_TIME.strftime('%Y-%m-%d %H:%M:%S')}';\n\n")

                # 添加overview重置语句
                f.write("-- 重置分钟线overview\n")
                f.write("INSERT INTO vnpy_stk_us_ib_m.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)\n")
                f.write("SELECT\n")
                f.write("    symbol,\n")
                f.write("    exchange,\n")
                f.write("    `interval`,\n")
                f.write("    COUNT(id) AS `count`,\n")
                f.write("    MIN(datetime) AS `start`,\n")
                f.write("    MAX(datetime) AS `end`\n")
                f.write("FROM vnpy_stk_us_ib_m.dbbardata\n")
                f.write("WHERE symbol IN (")
                symbols = [f"'{r['symbol']}'" for r in fix_needed]
                f.write(", ".join(symbols))
                f.write(")\nGROUP BY symbol, exchange, `interval`\n")
                f.write("ON DUPLICATE KEY UPDATE\n")
                f.write("    `count` = VALUES(`count`),\n")
                f.write("    `start` = VALUES(`start`),\n")
                f.write("    `end` = VALUES(`end`);\n")

            logger.info(f"SQL修复文件已生成: {sql_file}")
            logger.info(f"需要修复的股票数量: {len(fix_needed)}")

        except Exception as e:
            logger.error(f"生成SQL修复文件失败: {e}")
            logger.error(traceback.format_exc())


app = typer.Typer()

@app.command()
def main(
    conid_list: Optional[str] = typer.Option(
        None,
        "--conid-list",
        "-c",
        help="要检查的conid列表，用逗号分隔，例如：95514904,548309229,162225735"
    )
):
    """IB分钟线与日线数据一致性检查"""

    start_time = datetime.now()
    logger.info(f"程序开始运行: {start_time}")

    # 解析conid列表
    conids = None
    if conid_list:
        conids = [conid.strip() for conid in conid_list.split(',') if conid.strip()]
        logger.info(f"指定检查 {len(conids)} 个conid")

    try:
        checker = ConsistencyChecker()
        checker.run_consistency_check(conid_list=conids)

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        logger.error(traceback.format_exc())

    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"程序结束运行: {end_time}")
        logger.info(f"总运行时长: {duration}")


if __name__ == "__main__":
    app()
