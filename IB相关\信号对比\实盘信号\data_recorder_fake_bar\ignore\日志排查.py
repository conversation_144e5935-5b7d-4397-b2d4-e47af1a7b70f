# 提取当前目录下所有.log文件中含有“NZD-20240617-USD-FUT”的行，将结果写入到result.txt文件中。
from glob import glob
import os


def extract_log(log_dir, keyword, exclude_keyword_list, output_file):
    with (open(output_file, 'w', encoding='utf-8') as f):
        for log_file in glob(os.path.join(log_dir, '*.log')):
            with open(log_file, encoding='utf-8') as f1:
                for line in f1:
                    if keyword in line and all(exclude_keyword not in line for exclude_keyword in exclude_keyword_list):
                        f.write(line)


extract_log('../../.vntrader/log', 'NZD-20240617-USD-FUT', ['signalbar', 'HHI.HK-20240429-HKD-FUT.HKFE'], 'result.txt')
