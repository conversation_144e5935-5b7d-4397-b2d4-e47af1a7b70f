
# 读取文本文件并跳过前两行元数据
import pandas as pd

# 读取文本文件并跳过前两行元数据
with open(r"E:\迅雷下载\usa.txt", 'r') as file:
    lines = file.readlines()[2:]

# 处理数据，确保每一行的数据列数一致，并删除多余的空字段
data = []
for line in lines:
    split_line = line.strip().split('|')
    # 如果最后一列是空字符串，删除它
    if split_line[-1] == '':
        split_line = split_line[:-1]
    # 只取前8列，确保与列名对应
    data.append(split_line[:8])

columns = ['SYM', 'CUR', 'NAME', 'CON', 'ISIN', 'REBATERATE', 'FEERATE', 'AVAILABLE']
df = pd.DataFrame(data, columns=columns)

# 将REBATERATE和FEERATE列转换为数值型
df['REBATERATE'] = pd.to_numeric(df['REBATERATE'], errors='coerce')
df['FEERATE'] = pd.to_numeric(df['FEERATE'], errors='coerce')

# 筛选条件：REBATERATE + FEERATE < 0
# filtered_df = df[df['REBATERATE'] + df['FEERATE'] < 0]

# 将结果保存为Excel文件
df.to_excel('filtered_usa.xlsx', index=False)

print("筛选完成并保存为 filtered_usa.xlsx")