import json
import os

from flask import jsonify, request

from . import api
from .errors import bad_request
from .. import db
from ..logConf import logger
from ..models import TradeInfo


# @api.route('/trade/<int:id>', methods=['GET'])
# def trade(id):
#     trade = TradeInfo.query.get_or_404(id)
#     logger.info(f'Trade {trade} has been queried.')
#     return jsonify(trade.to_json()), 200
#
#
# @api.route('/trades/', methods=['GET'])
# def trades():
#     all_posts = TradeInfo.query.all()
#     logger.info(f'All trades have been queried.')
#     return jsonify({'trades': [p.to_json() for p in all_posts]}), 200


@api.route('/update/', methods=['POST'])
def update_trades_info():
    if not request.json:
        return bad_request('Bad request. JSON data expected.')

    data = request.get_json()

    # 去重
    current_dir = os.path.dirname(os.path.abspath(__file__))
    strategy_names_file = os.path.join(current_dir, '../../strategy_names.json')
    with open(strategy_names_file, 'r') as f:
        strategy_names = json.load(f)['strategy_names']
    # 处理空成交记录：
    try:
        # strategy_name = list(list(data.values())[0].values())[0]["strategy_name"]
        # 改为从list(data.values())的每个元素中尝试取出strategy_name，直到成功为止
        for i in list(data.values()):
            try:
                strategy_name = list(i.values())[0]["strategy_name"]
                break
            except:
                continue
        logger.info(f'strategy_name: {strategy_name}')
    except Exception as e:
        logger.info(f'成交记录为空，数据库成交记录未更新')
        return jsonify({'message': '成交记录为空，数据库成交记录未更新'}), 403

    if strategy_name in strategy_names:
        # 删除数据库中的strategy_name为strategy_name的所有数据
        TradeInfo.query.filter_by(strategy_name=strategy_name).delete()
        db.session.commit()
        logger.info(f'{strategy_name} old trades info has been deleted.')

        # 插入
        trades_to_insert = [TradeInfo.from_json(trade_info) for trades_info in data.values() for trade_info in
                            trades_info.values()]

        db.session.add_all(trades_to_insert)
        db.session.commit()
        logger.info(f'{strategy_name} new trades info has been inserted.')
        return jsonify({'message': 'Trades info has been updated.'}), 200

    else:
        logger.info(f'Strategy_name {strategy_name} is not in the strategy_names.json file.')
        # 返回禁止访问
        return jsonify({'message': 'Strategy_name is not in the strategy_names.json file.'}), 403


# 增加删除成交记录的接口
@api.route('/delete/', methods=['POST'])
def delete_trades_info():
    if not request.json:
        return bad_request('Bad request. JSON data expected.')

    data = request.get_json()
    strategy_name = data.get('strategy_name')

    # 去重
    current_dir = os.path.dirname(os.path.abspath(__file__))
    strategy_names_file = os.path.join(current_dir, '../../strategy_names.json')
    with open(strategy_names_file, 'r') as f:
        strategy_names = json.load(f)['strategy_names']

    if strategy_name and strategy_name in strategy_names:
        # 删除数据库中的strategy_name为strategy_name的所有数据
        TradeInfo.query.filter_by(strategy_name=strategy_name).delete()
        db.session.commit()
        logger.info(f'{strategy_name} old trades info has been deleted.')
        return jsonify({'message': 'Trades info has been deleted.'}), 200
    else:
        logger.info(f'Strategy_name {strategy_name} is not in the strategy_names.json file.')
        # 返回禁止访问
        return jsonify({'message': 'Strategy_name is not in the strategy_names.json file.'}), 403