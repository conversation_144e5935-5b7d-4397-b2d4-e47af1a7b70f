from datetime import datetime
from typing import Union
from warnings import simplefilter

import requests
# 不限制输出列数
from pandas import set_option

set_option('display.max_columns', None)
import numpy as np
import talib
from vnpy.trader.constant import Interval, Offset, Direction, Exchange
from vnpy.trader.utility import extract_vt_symbol
# 如果版本大于3.0.0
import vnpy
version = vnpy.__version__
if version >= '3.0.0':
    from vnpy_spreadtrading import SpreadData, LegData
    from vnpy_spreadtrading.backtesting import BacktestingEngine as SpreadBacktestingEngine
    from vnpy_portfoliostrategy import StrategyTemplate, StrategyEngine
    from vnpy_portfoliostrategy.utility import PortfolioBarGenerator
    from vnpy_portfoliostrategy.backtesting import BacktestingEngine as PortfolioBacktestingEngine
    from vnpy_ctastrategy import (CtaTemplate, TickData, BarData, TradeData, OrderData, BarGenerator, ArrayManager, StopOrder, TargetPosTemplate)
    from vnpy_ctastrategy.backtesting import BacktestingEngine
else:
    from vnpy.app.spread_trading import SpreadData, LegData
    from vnpy.app.spread_trading.backtesting import BacktestingEngine as SpreadBacktestingEngine
    from vnpy.app.portfolio_strategy import StrategyTemplate, StrategyEngine
    from vnpy.app.portfolio_strategy.utility import PortfolioBarGenerator
    from vnpy.app.portfolio_strategy.backtesting import BacktestingEngine as PortfolioBacktestingEngine
    from vnpy.app.cta_strategy import (CtaTemplate, TickData, BarData, TradeData, OrderData, BarGenerator, ArrayManager, StopOrder, TargetPosTemplate)
    from vnpy.app.cta_strategy.backtesting import BacktestingEngine

simplefilter("ignore")


class NewArrayManager(ArrayManager):
    def dxjc(self, num: int = 8,
             array: bool = False) -> Union[float, np.ndarray]:
        """
        N := IF(BARSCOUNT(C) >= 8, 8, BARSCOUNT(C));
        AMOV := VOL * (C + L + H) / 3;
        MN := SUM(AMOV, N) / SUM(VOL, N);

        NOTEXT1: IF(C >= MN, MN, DRAWNULL), LINETHICK3, COLORYELLOW;
        NOTEXT2: IF(C < MN, MN, DRAWNULL), LINETHICK3, COLORGRAY;
        """
        n = num if len(self.close) >= num else len(self.close)
        amov = self.volume * (self.close + self.low + self.high) / 3
        mn = talib.SUM(amov, n) / talib.SUM(self.volume, n)
        flag = self.close >= mn
        if array:
            return flag
        return flag[-1]


class DXJC(CtaTemplate):
    author = "廖"
    strategy_name = "liaoyuan_DXJC"
    X_interval = 1  # 30  # 基于30分钟的K线
    size = 100  # 100

    num = 6

    atr_window = 14  # 用于仓位控制
    risk_level = 300  # 用于仓位控制
    trading_size = 10  # 用于仓位控制

    entry_price = 0
    last_pos = 0

    parameters = ["X_interval", "size",
                  "num",  # dxjc参数
                  "atr_window", "risk_level",
                  ]
    variables = ["trading_size", "last_pos"
                 ]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.bg = BarGenerator(
            self.on_bar,
            window=self.X_interval,
            on_window_bar=self.on_XInterval_bar,
            interval=Interval.MINUTE)  # 1m 1h d
        self.am = NewArrayManager(size=self.size)

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_bar(10)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData):
        self.bg.update_bar(bar)
        if self.pos > 0:
            gain = (bar.close_price - self.entry_price) * self.pos * 10
            # print(f"{self.vt_symbol} {bar.datetime} | close:{bar.close_price} | entry: {self.entry_price} |pos:{self.pos}| gain:{gain}")
            if gain < -20000:
                self.sell(bar.close_price - 10, abs(self.pos))
                # print(f"{self.vt_symbol} zhisun gain:{gain} close:{round(bar.close_price,1)} entry:{round(self.entry_price,1)} pos: {self.pos}")
        elif self.pos < 0:
            gain = (self.entry_price - bar.close_price) * self.pos * 10
            # print(f"{self.vt_symbol} {bar.datetime} | close:{bar.close_price} | entry: {self.entry_price} |pos:{self.pos}| gain:{gain}")
            if gain < -20000:
                self.cover(bar.close_price + 10, abs(self.pos))
                # print(f"{self.vt_symbol} zhisun gain:{gain} close:{round(bar.close_price,1)} entry:{round(self.entry_price,1)} pos: {self.pos}")

    def on_XInterval_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.cancel_all()
        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return

        # 仓位控制
        ATR = am.atr(self.atr_window)
        if ATR:
            self.trading_size = int(self.risk_level / ATR)

        # 计算dxjc
        dxjc = am.dxjc(num=self.num, array=True)
        # 上穿
        up_cross = (dxjc[-1] and not dxjc[-2] and not dxjc[-3])
        # 下穿
        down_cross = (not dxjc[-1] and dxjc[-2] and dxjc[-3])

        if not self.pos:
            if down_cross:
                self.buy(bar.close_price * 1.1, self.trading_size)
            elif up_cross:
                self.short(bar.close_price * 0.9, self.trading_size)

        # 指标反向止盈止损
        elif self.pos > 0:
            if up_cross:
                self.sell(bar.close_price * 0.9, abs(self.pos))
                self.short(bar.close_price * 0.9, self.trading_size)
        elif self.pos < 0:
            if down_cross:
                self.cover(bar.close_price * 1.1, abs(self.pos))
                self.buy(bar.close_price * 1.1, self.trading_size)

        self.put_event()

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        if self.pos == 0:
            gain = (trade.price - self.entry_price) * self.last_pos * 10
            # print(f"End!:{self.vt_symbol} {trade.datetime} | close:{trade.price} | entry: {self.entry_price} |pos:{self.last_pos}| gain:{gain}")
            # print("=========================================================")
            self.entry_price = 0
        elif self.pos > 0 and self.last_pos == 0:
            self.entry_price = trade.price
        self.last_pos = self.pos
        # print(f"{self.pos}  {self.entry_price}")
        self.put_event()  # 发出状态更新事件

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass


if __name__ == '__main__':
    # # 用法1
    # all_symbol = ["FG888.CZCE",
    #               "y888.DCE",
    #               "p888.DCE", "SR888.CZCE"
    #               ]
    # trades_dict = {}
    # for elem in all_symbol:
    #     engine = BacktestingEngine()
    #     engine.set_parameters(
    #         vt_symbol=elem,
    #         interval="1m",
    #         start=datetime(2022, 10, 1),
    #         end=datetime(2023, 1, 10),
    #         rate=0,
    #         slippage=0,
    #         size=10,
    #         pricetick=1,
    #         capital=1_000_000,
    #     )
    #     engine.add_strategy(DXJC, {})
    #
    #     engine.load_data()
    #     engine.run_backtesting()
    #     df = engine.calculate_result()
    #     engine.calculate_statistics()
    #     trades_dict[elem] = engine.trades
    #     # engine.show_chart()
    #
    # from utils.trade_info import send_trades_info
    # send_trades_info(trades_dict, DXJC)

    # 用法2
    from utils.trade_info import get_888trades_dict, send_trades_info
    trades_dict = get_888trades_dict(DXJC,['FG','y','p','SR'])
    send_trades_info(trades_dict, DXJC)