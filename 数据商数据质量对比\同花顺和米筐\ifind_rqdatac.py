from iFinDPy import *
from datetime import datetime
import pandas as pd
import time as _time
import json
from threading import Thread,Lock,Semaphore
import requests
import rqdatac

rqdatac.init( 'license', 'hKzEyfcbN4O4B22wGXKfOZnOkVIyQ4fnW7VSUepZ5shkCx3Wpfkb63nMWozKudSUfMCiSx6cuWYasEyqaIVQ7a91WnYFIhxSw39GKxvHmhnlIjaSjBNncRY0Y3ZH3wWYiYbjK25Gxl9FuVkH6sA5VmnbMBmJoQHeT_seHEFYVPw=LVXNtX-oQgO2T9QDKkPx1hhlyjgrkYETwszLKzPA3ItRHWcp4crJu9dlykAOaJv4AtQuPy-THTFzBP4DfcFtIWm-W5vGQNyMMu3lD8cc1u_kxXFfihqajhijKdIi8nJvVrOexx1XVI6Vv-FdzrL0IVNY9e9GCcZ9lavQanQ4BNw=' )

start_time1 = input('请输入开始日期（如2023-05-17）：')
start_time2 = start_time1 + ' 09:00:00'
end_time2 = start_time1 + ' 15:00:00'

sem = Semaphore(5)  # 此变量用于控制最大并发数
dllock = Lock()  #此变量用来控制实时行情推送中落数据到本地的锁

# 登录函数

thsLogin = THS_iFinDLogin("zh35637","7bfb84")
print(thsLogin)
if thsLogin != 0:
    print('登录失败')
else:
    print('登录成功')                


excel_path = r'C:\Users\<USER>\Desktop\ifind米筐数据对比\股票池.xlsx'
order_book_ids = pd.read_excel(excel_path, sheet_name='Sheet1')
l = len(order_book_ids)
for i in range(l):
        a = order_book_ids['代码'][i]
        b = int(a[0])
        if b == 0 or b == 3:
                rq_book_ids = a[0:6] + '.XSHE'
                ifind_book_ids = a[0:6] + '.SZ'
                

        elif b == 6:
                rq_book_ids = a[0:6] + '.XSHG'
                ifind_book_ids = a[0:6] + '.SH'
                
        
        
        data1 = rqdatac.get_price(order_book_ids=rq_book_ids
                 , start_date=start_time1, end_date=start_time1,fields=['open','close','low','high','volume'],frequency='1m',time_slice=('09:00', '15:00'),adjust_type='pre',expect_df=False)
        file_name1 = str(rq_book_ids) + '_' + 'rq' + '.csv'

        data1 = data1[1:]
        
        data1.to_csv(file_name1)

        

        data2=THS_HF(ifind_book_ids,'open;close;low;high;volume','CPS:forward,baseDate:1900-01-01,Fill:Omit,Interval:1',start_time2,end_time2)
        
        file_name2 = str(ifind_book_ids) + '_' + 'ifind' + '.csv'
        data3 = data2.data
        data3 = data3[2:]
        
        data3=data3[data3.columns[2:7]]
        data3.columns=['open','close','low','high','volume']
        
        data3.index=data1.index
        data3.to_csv(file_name2)
        
        h = data3 - data1
        result = h.loc[~(h==0).all(axis=1)]
        print(file_name2)
        print(result)
        