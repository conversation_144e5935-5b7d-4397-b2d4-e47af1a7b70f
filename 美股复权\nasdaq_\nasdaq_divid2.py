import requests
import logging
from datetime import datetime, timedelta
import time
import argparse
from typing import List, Dict, Optional
import json
import pymysql
from pymysql import MySQLError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dividend_updater.log"),
        logging.StreamHandler()
    ]
)


class DividendDatabase:
    def __init__(self, config_path: str = "vt_setting.json"):
        self.config = self.load_config(config_path)
        self.init_db()

    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            raise

    def get_db_connection(self):
        """获取数据库连接"""
        try:
            conn = pymysql.connect(
                host=self.config.get("database.host", "localhost"),
                port=self.config.get("database.port", 3306),
                user=self.config.get("database.user", "root"),
                password=self.config.get("database.password", ""),
                database=self.config.get("database.database", "world"),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return conn
        except MySQLError as e:
            logging.error(f"数据库连接失败: {e}")
            raise

    def init_db(self):
        """初始化数据库表结构"""
        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建分红数据表
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS dividends (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            symbol VARCHAR(20) NOT NULL,
                            company_name TEXT,
                            ex_dividend_date DATE,
                            payment_date DATE,
                            record_date DATE,
                            dividend_rate DECIMAL(10, 4),
                            annual_dividend DECIMAL(10, 4),
                            announcement_date DATE,
                            as_of_date DATE,
                            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY unique_dividend (symbol, ex_dividend_date)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ''')

                    # 创建索引
                    cursor.execute('CREATE INDEX idx_symbol ON dividends (symbol)')
                    cursor.execute('CREATE INDEX idx_ex_dividend_date ON dividends (ex_dividend_date)')
                conn.commit()
                logging.info("MySQL数据库初始化完成")
        except MySQLError as e:
            logging.error(f"数据库初始化失败: {e}")
            raise

    def fetch_dividend_data(self, date_str: str) -> Optional[Dict]:
        """从NASDAQ API获取分红数据"""
        url = f"https://api.nasdaq.com/api/calendar/dividends?date={date_str}"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://www.nasdaq.com",
            "Referer": "https://www.nasdaq.com/",
        }

        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"获取数据失败: {e}")
            return None

    def parse_dividend_data(self, raw_data: Dict, target_date: str) -> List[Dict]:
        """解析API返回的分红数据"""
        dividends = []

        try:
            calendar = raw_data.get("data", {}).get("calendar", {})
            rows = calendar.get("rows", [])
            as_of = calendar.get("asOf", "")

            for row in rows:
                # 清洗和转换数据
                dividend = {
                    "symbol": row.get("symbol", "").strip(),
                    "company_name": row.get("companyName", "").strip(),
                    "ex_dividend_date": self.parse_date(row.get("dividend_Ex_Date", "")),
                    "payment_date": self.parse_date(row.get("payment_Date", "")),
                    "record_date": self.parse_date(row.get("record_Date", "")),
                    "dividend_rate": float(row.get("dividend_Rate", 0)) if row.get("dividend_Rate") else 0,
                    "annual_dividend": float(row.get("indicated_Annual_Dividend", 0)) if row.get(
                        "indicated_Annual_Dividend") else 0,
                    "announcement_date": self.parse_date(row.get("announcement_Date", "")),
                    "as_of_date": target_date,
                    "raw_as_of": as_of
                }
                dividends.append(dividend)

            logging.info(f"解析了 {len(dividends)} 条分红记录")
        except Exception as e:
            logging.error(f"解析数据时出错: {e}")

        return dividends

    def parse_date(self, date_str: str) -> Optional[str]:
        """将各种日期格式转换为YYYY-MM-DD格式"""
        if not date_str or date_str.strip() == "":
            return None

        try:
            # 处理多种可能日期格式
            for fmt in ("%m/%d/%Y", "%Y-%m-%d", "%d-%m-%Y", "%m-%d-%Y"):
                try:
                    dt = datetime.strptime(date_str.strip(), fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue
            logging.warning(f"无法解析日期: {date_str}")
            return None
        except Exception as e:
            logging.error(f"日期解析错误: {date_str} - {e}")
            return None

    def save_dividends(self, dividends: List[Dict]):
        """将分红数据保存到数据库"""
        if not dividends:
            logging.warning("没有分红数据需要保存")
            return

        inserted_count = 0
        updated_count = 0

        try:
            with self.get_db_connection() as conn:
                with conn.cursor() as cursor:

                    for dividend in dividends:
                        try:
                            # 检查记录是否已存在
                            cursor.execute(
                                "SELECT id FROM dividends WHERE symbol = %s AND ex_dividend_date = %s",
                                (dividend["symbol"], dividend["ex_dividend_date"])
                            )

                            if cursor.fetchone():
                                # 更新现有记录
                                cursor.execute('''
                                    UPDATE dividends SET
                                        company_name = %s,
                                        payment_date = %s,
                                        record_date = %s,
                                        dividend_rate = %s,
                                        annual_dividend = %s,
                                        announcement_date = %s,
                                        as_of_date = %s,
                                        last_updated = CURRENT_TIMESTAMP
                                    WHERE symbol = %s AND ex_dividend_date = %s
                                ''', (
                                    dividend["company_name"],
                                    dividend["payment_date"],
                                    dividend["record_date"],
                                    dividend["dividend_rate"],
                                    dividend["annual_dividend"],
                                    dividend["announcement_date"],
                                    dividend["as_of_date"],
                                    dividend["symbol"],
                                    dividend["ex_dividend_date"]
                                ))
                                updated_count += 1
                            else:
                                # 插入新记录
                                cursor.execute('''
                                    INSERT INTO dividends 
                                    (symbol, company_name, ex_dividend_date, payment_date, record_date, 
                                     dividend_rate, annual_dividend, announcement_date, as_of_date)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                ''', (
                                    dividend["symbol"],
                                    dividend["company_name"],
                                    dividend["ex_dividend_date"],
                                    dividend["payment_date"],
                                    dividend["record_date"],
                                    dividend["dividend_rate"],
                                    dividend["annual_dividend"],
                                    dividend["announcement_date"],
                                    dividend["as_of_date"]
                                ))
                                inserted_count += 1
                        except MySQLError as e:
                            logging.error(f"数据库操作失败: {e}")

                    conn.commit()

                logging.info(f"数据保存完成: 新增 {inserted_count} 条, 更新 {updated_count} 条")
        except MySQLError as e:
            logging.error(f"数据库连接失败: {e}")

    def update_for_date(self, target_date: str):
        """更新指定日期的分红数据"""
        logging.info(f"开始更新 {target_date} 的分红数据")

        # 获取数据
        raw_data = self.fetch_dividend_data(target_date)
        if not raw_data:
            logging.error(f"未能获取 {target_date} 的数据")
            return False

        # 解析数据
        dividends = self.parse_dividend_data(raw_data, target_date)

        # 保存数据
        self.save_dividends(dividends)

        logging.info(f"{target_date} 的数据更新完成")
        return True


def main():
    parser = argparse.ArgumentParser(description="美股NASDAQ分红数据库更新工具")
    parser.add_argument("--date", help="要更新的日期(YYYY-MM-DD格式)，默认为今天")
    parser.add_argument("--config", help="配置文件路径，默认为vt_setting.json")
    parser.add_argument("--range", type=int, help="更新多天的数据，从指定日期往前推的天数")

    args = parser.parse_args()

    # 确定配置文件路径
    config_path = args.config if args.config else "vt_setting.json"

    try:
        db = DividendDatabase(config_path)
    except Exception as e:
        logging.error(f"初始化数据库失败: {e}")
        return

    # 确定目标日期
    if args.date:
        target_date = args.date
    else:
        target_date = datetime.now().strftime("%Y-%m-%d")

    # 单日更新或多日更新
    if args.range:
        start_date = datetime.strptime(target_date, "%Y-%m-%d")
        for i in range(args.range):
            current_date = start_date - timedelta(days=i)
            date_str = current_date.strftime("%Y-%m-%d")
            success = db.update_for_date(date_str)
            if not success:
                logging.error(f"更新 {date_str} 失败，跳过后续日期")
                break
            # 避免请求过于频繁
            time.sleep(1)
    else:
        db.update_for_date(target_date)


if __name__ == "__main__":
    main()