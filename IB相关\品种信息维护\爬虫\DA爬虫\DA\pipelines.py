# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html
# useful for handling different item types with a single interface
import re

import pandas as pd


class DaPipeline:
    def __init__(self):
        self.data = []  # 用于存储所有item数据

    def process_item(self, item, spider):
        类型 = item['类型']
        交易所 = item['交易所']
        合约名称 = item['合约名称']
        交易所代码 = item['交易所代码']
        DA代码 = item['DA代码']
        香港交易 = item['香港交易']
        最低波幅 = item['最低波幅']
        合约大小 = item['合约大小']
        合约月份 = item['合约月份']
        涨跌停 = item['涨跌停']
        首次通知日 = item['首次通知日']
        最后交易日 = item['最后交易日']
        交割方式 = item['交割方式']

        # 将item数据存储到列表中
        self.data.append(
            {'类型': 类型, '交易所': 交易所, '合约名称': 合约名称, '交易所代码': 交易所代码, 'DA代码': DA代码,
             '香港交易': 香港交易, '最低波幅': 最低波幅, '合约大小': 合约大小, '合约月份': 合约月份, '涨跌停': 涨跌停,
             '首次通知日': 首次通知日, '最后交易日': 最后交易日, '交割方式': 交割方式})

        return item

    def close_spider(self, spider):
        # 在Spider关闭时，将数据转换为DataFrame并保存为Excel
        if self.data:
            df = pd.DataFrame(self.data)
            # 将交易所列转为大写字母
            df['交易所'] = df['交易所'].str.upper()

            # 使用apply函数将交易所列以"_-"分割为两列
            def split_exchange(x):
                parts = re.split('_|-', x)
                if len(parts) == 2:
                    return parts
                else:
                    return [x, x]

            df[['交易所1', '交易所2']] = df['交易所'].apply(split_exchange).tolist()

            # 删除原交易所列
            df = df.drop('交易所', axis=1)

            df = df.set_index(['交易所1', '交易所2', '类型', '香港交易', '合约名称'])
            # 以交易所1、交易所2对数据进行排序
            df = df.sort_index(level=['交易所1', '交易所2'])

            # 保存DataFrame为Excel文件
            df.to_excel('DA.xlsx', encoding='utf-8-sig')