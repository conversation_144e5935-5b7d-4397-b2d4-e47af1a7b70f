from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, login_required, logout_user, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SubmitField, <PERSON>oleanField
from wtforms.validators import DataRequired, Length, ValidationError
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import re
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Pat<PERSON>Fill
from config import Config
from models import db, User, MainContractManagement
from symbol_info import all_symbol_pres

app = Flask(__name__)
app.config.from_object(Config)
db.init_app(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Log In')

class ContractForm(FlaskForm):
    main_symbol = StringField('Main Symbol', validators=[DataRequired(), Length(max=20)])
    specific_symbol = StringField('Specific Symbol', validators=[DataRequired(), Length(max=20)])
    submit = SubmitField('Update Contract')

    def validate_main_symbol(self, field):
        if not validate_main_symbol(field.data):
            raise ValidationError('Invalid main symbol format. It should be in the format of {symbol}88.{exchange}')

    def validate_specific_symbol(self, field):
        if not validate_specific_symbol(field.data):
            raise ValidationError('Invalid specific symbol format. It should be in the format of {symbol}{3 or 4 digits}.{exchange}')

    def validate(self, extra_validators=None):
        if not super().validate(extra_validators):
            return False
        
        main_symbol = self.main_symbol.data
        specific_symbol = self.specific_symbol.data
        
        if not compare_symbols(main_symbol, specific_symbol):
            self.specific_symbol.errors.append('Main symbol and specific symbol must only differ in their numeric parts.')
            return False
        
        return True

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(max=64)])
    password = PasswordField('Password', validators=[DataRequired()])
    is_admin = BooleanField('Admin')
    submit = SubmitField('Add User')

@app.route('/')
@login_required
def index():
    search = request.args.get('search', '')
    query = MainContractManagement.query
    if search:
        query = query.filter(
            (MainContractManagement.main_symbol.like(f'%{search}%')) |
            (MainContractManagement.specific_symbol.like(f'%{search}%'))
        )
    contracts = query.order_by(MainContractManagement.update_time.desc()).all()
    return render_template('index.html', contracts=contracts)

@app.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            return redirect(url_for('index'))
        flash('Invalid username or password')
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/update_contract', methods=['GET', 'POST'])
@login_required
def update_contract():
    form = ContractForm()
    if form.validate_on_submit():
        contract = MainContractManagement.query.filter_by(main_symbol=form.main_symbol.data).first()
        if contract:
            contract.specific_symbol = form.specific_symbol.data
            contract.update_time = datetime.now()
            # 只在创建新记录时设置 createUser
        else:
            new_contract = MainContractManagement(
                main_symbol=form.main_symbol.data,
                specific_symbol=form.specific_symbol.data,
                createUser=current_user.username  # 设置创建用户
            )
            db.session.add(new_contract)
        try:
            db.session.commit()
            flash('Contract updated successfully', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Database error: {str(e)}', 'error')
    
    if form.errors:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('update_contract.html', form=form)

@app.route('/users')
@login_required
def users():
    if not current_user.is_admin:
        flash('You do not have permission to access this page.', 'error')
        return redirect(url_for('index'))
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/add_user', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.is_admin:
        flash('You do not have permission to access this page.', 'error')
        return redirect(url_for('index'))
    form = UserForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, is_admin=form.is_admin.data)
        user.set_password(form.password.data)
        db.session.add(user)
        try:
            db.session.commit()
            flash('User added successfully', 'success')
            return redirect(url_for('users'))
        except Exception as e:
            db.session.rollback()
            flash(f'Database error: {str(e)}', 'error')
    return render_template('add_user.html', form=form)

@app.route('/delete_user/<int:user_id>', methods=['POST'])
@login_required
def delete_user(user_id):
    if not current_user.is_admin:
        flash('You do not have permission to perform this action.', 'error')
        return redirect(url_for('index'))
    user = User.query.get_or_404(user_id)
    if user.username == 'root':
        flash('Cannot delete root user.', 'error')
        return redirect(url_for('users'))
    db.session.delete(user)
    db.session.commit()
    flash('User deleted successfully', 'success')
    return redirect(url_for('users'))

@app.route('/update_specific_symbol', methods=['POST'])
@login_required
def update_specific_symbol():
    contract_id = request.form.get('pk')
    new_specific_symbol = request.form.get('value')
    
    contract = MainContractManagement.query.get(contract_id)
    if contract:
        if validate_specific_symbol(new_specific_symbol) and compare_symbols(contract.main_symbol, new_specific_symbol):
            contract.specific_symbol = new_specific_symbol
            contract.update_time = datetime.now()
            contract.createUser = current_user.username
            db.session.commit()
            return jsonify({'status': 'success'})
        else:
            return jsonify({'status': 'error', 'msg': 'Invalid specific symbol format or does not match main symbol.'})
    return jsonify({'status': 'error', 'msg': 'Contract not found.'})

def validate_main_symbol(main_symbol):
    pattern = r'^([A-Za-z]+)88\.([A-Z]+)$'
    match = re.match(pattern, main_symbol)
    if not match:
        return False
    symbol, exchange = match.groups()
    return any(symbol in symbols for symbols in all_symbol_pres.values()) and exchange in all_symbol_pres

def validate_specific_symbol(specific_symbol):
    pattern = r'^([A-Za-z]+)(\d{3,4})\.([A-Z]+)$'
    match = re.match(pattern, specific_symbol)
    if not match:
        return False
    symbol, number, exchange = match.groups()
    if exchange == 'CZCE' and len(number) != 3:
        return False
    if exchange != 'CZCE' and len(number) != 4:
        return False
    return any(symbol in symbols for symbols in all_symbol_pres.values()) and exchange in all_symbol_pres

def compare_symbols(main_symbol, specific_symbol):
    main_pattern = r'^([A-Za-z]+)88\.([A-Z]+)$'
    specific_pattern = r'^([A-Za-z]+)(\d{3,4})\.([A-Z]+)$'
    
    main_match = re.match(main_pattern, main_symbol)
    specific_match = re.match(specific_pattern, specific_symbol)
    
    if not main_match or not specific_match:
        return False
    
    main_symbol_part, main_exchange = main_match.groups()
    specific_symbol_part, specific_number, specific_exchange = specific_match.groups()
    
    return (main_symbol_part == specific_symbol_part) and (main_exchange == specific_exchange)

def load_excel_data():
    try:
        # 创建一个默认的工作簿和样式
        wb = Workbook()
        default_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        wb.active.sheet_properties.tabColor = 'FFFFFF'

        df = pd.read_excel("主力合约管理数据.xlsx", engine='openpyxl')
        for _, row in df.iterrows():
            main_symbol = row["888合约"]
            specific_symbol = row["具体合约"]
            
            contract = MainContractManagement.query.filter_by(main_symbol=main_symbol).first()
            if not contract:
                new_contract = MainContractManagement(
                    main_symbol=main_symbol,
                    specific_symbol=specific_symbol,
                    createUser="system"  # 使用 "system" 作为创建用户
                )
                db.session.add(new_contract)
        
        db.session.commit()
        print("Excel data loaded successfully")
    except Exception as e:
        print(f"Error loading Excel data: {str(e)}")
        db.session.rollback()

@app.route('/api/contracts', methods=['GET'])
def get_contracts():
    contracts = MainContractManagement.query.all()
    return jsonify([{
        'id': c.id,
        'main_symbol': c.main_symbol,
        'specific_symbol': c.specific_symbol,
        'update_time': c.update_time.isoformat(),
        'createUser': c.createUser
    } for c in contracts])

def init_db():
    with app.app_context():
        db.create_all()
        # Add root user if not exists
        if not User.query.filter_by(username='xxjs@zh').first():
            root_user = User(username='xxjs@zh', is_admin=True)
            root_user.set_password('******')
            db.session.add(root_user)
        
        # Add zh user if not exists
        if not User.query.filter_by(username='zh').first():
            zh_user = User(username='zh', is_admin=False)
            zh_user.set_password('zhP@55word')
            db.session.add(zh_user)
        
        db.session.commit()
        
        # Load Excel data
        load_excel_data()

if __name__ == '__main__':
    init_db()
    app.run(debug=True)
