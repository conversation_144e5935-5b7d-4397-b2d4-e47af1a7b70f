# # 获取指定数据服务实例
# from vnpy_rqdata.rqdata_datafeed import RqdataDatafeed
# from vnpy_mysql.mysql_database import MysqlDatabase
# datafeed = RqdataDatafeed()
# database = MysqlDatabase()
from vnpy.trader.setting import SETTINGS

SETTINGS["database.database"] = "vnpy_stk"
SETTINGS["database.port"] = 3309 # 生产数据库端口
SETTINGS["database.user"] = "root"
SETTINGS["database.password"] = "zhP@55word"
SETTINGS["datafeed.username"] = "license"
SETTINGS["datafeed.password"] = "hKzEyfcbN4O4B22wGXKfOZnOkVIyQ4fnW7VSUepZ5shkCx3Wpfkb63nMWozKudSUfMCiSx6cuWYasEyqaIVQ7a91WnYFIhxSw39GKxvHmhnlIjaSjBNncRY0Y3ZH3wWYiYbjK25Gxl9FuVkH6sA5VmnbMBmJoQHeT_seHEFYVPw=LVXNtX-oQgO2T9QDKkPx1hhlyjgrkYETwszLKzPA3ItRHWcp4crJu9dlykAOaJv4AtQuPy-THTFzBP4DfcFtIWm-W5vGQNyMMu3lD8cc1u_kxXFfihqajhijKdIi8nJvVrOexx1XVI6Vv-FdzrL0IVNY9e9GCcZ9lavQanQ4BNw="

from vnpy_rqdata.rqdata_datafeed import to_rq_symbol, INTERVAL_VT2RQ

from vnpy.trader.database import get_database, DB_TZ

database = get_database()
# from vnpy.trader.datafeed import get_datafeed
from vnpy_rqdata.rqdata_datafeed import RqdataDatafeed
from datetime import datetime
from typing import Optional, Callable

from vnpy.trader.constant import Interval
from vnpy.trader.object import BarData, HistoryRequest


class MyRqdataDatafeed(RqdataDatafeed):

    def _query_bar_history(self, req: HistoryRequest, output: Callable = print) -> Optional[list[BarData]]:
        """查询K线数据"""
        if not self.inited:
            n: bool = self.init(output)
            if not n:
                return []

        symbol: str = req.symbol
        exchange: Exchange = req.exchange
        interval: Interval = req.interval
        start: datetime = req.start
        end: datetime = req.end

        # 股票期权不添加交易所后缀
        if exchange in [Exchange.SSE, Exchange.SZSE] and symbol in self.symbols:
            rq_symbol: str = symbol
        else:
            rq_symbol: str = to_rq_symbol(symbol, exchange, self.symbols)

        # 检查查询的代码在范围内
        if rq_symbol not in self.symbols:
            output(f"RQData查询K线数据失败：不支持的合约代码{req.vt_symbol}")
            return []

        rq_interval: str = INTERVAL_VT2RQ.get(interval)
        if not rq_interval:
            output(f"RQData查询K线数据失败：不支持的时间周期{req.interval.value}")
            return []

        # 为了将米筐时间戳（K线结束时点）转换为VeighNa时间戳（K线开始时点）
        adjustment: timedelta = INTERVAL_ADJUSTMENT_MAP[interval]

        # 只对衍生品合约才查询持仓量数据
        fields: list = ["open", "high", "low", "close", "volume", "total_turnover"]
        if not symbol.isdigit():
            fields.append("open_interest")

        df: DataFrame = get_price(rq_symbol, frequency=rq_interval, fields=fields, start_date=start,
                                  end_date=get_next_trading_date(end),  # 为了查询夜盘数据
                                  adjust_type="none")

        data: list[BarData] = []

        if df is not None:
            # 填充NaN为0
            df.fillna(0, inplace=True)

            for row in df.itertuples():
                dt: datetime = row.Index[1].to_pydatetime() - adjustment
                dt: datetime = dt.replace(tzinfo=CHINA_TZ)

                if dt >= end:
                    break

                bar: BarData = BarData(symbol=symbol, exchange=exchange, interval=interval, datetime=dt,
                                       open_price=round_to(row.open, 0.000001), high_price=round_to(row.high, 0.000001),
                                       low_price=round_to(row.low, 0.000001), close_price=round_to(row.close, 0.000001),
                                       volume=row.volume, turnover=row.total_turnover,
                                       open_interest=getattr(row, "open_interest", 0), gateway_name="RQ")

                data.append(bar)

        return data


# datafeed = get_datafeed()
datafeed = MyRqdataDatafeed()

# 设置合约品种
# symbols = {
#     # "SHFE": ["AG", "AL", "AU", "BU", "CU", "FU", "HC", "LU", "NI", "NR", "PB", "RB", "RU", "SC", "SN", "SP", "SS", "WR",
#     #          "ZN"],
#     # "DCE": ["A","B","BB","C","CS","EG","EB","FB","I","J","JD","JM","L","LH","M","P","PG","PP","RR","V","Y"],
#     # "CZCE": ["AP","CF","CJ","CY","FG","JR","LR","MA","OI","PF","PM","RI","RM","RS","SA","SF","SM","SR","TA","UR","WH","ZC"],
#     # "CFFEX": ["IC", "IF", "IH", "T", "TF", "TS"],
#     "SSE": ["600941", "601186", "600233", "601878", "601666", "603087", "603737", "600320", "600808", "605090",
#             "600975", "603013", "603367", "603328", "600400", "600897", "603360", "603098", "603275", "603666",
#             "603669", "603190", "600706", "600892", "605169", "605288", "603326", "603280", "600241", "600243"],
#     "SZSE": ["002352", "000157", "000513", "002850", "000830", "002698", "001323", "002506", "000521", "001301",
#              "002755", "000980", "002204", "002038", "000917", "000755", "003000", "002990", "002344", "002918",
#              "002845", "002520", "000713", "000151", "001217", "002940", "002412", "002976", "002213", "003036",
#              "002357", "002529", "000953"]}

def load_symbol_dict_from_txt(file_path='stock_list.txt'):
    """
    | 000009.SZSE |
    | 000021.SZSE |
    | 000027.SZSE |
    | 000031.SZSE |
    | 000039.SZSE |
    | 000050.SZSE |
    | 000060.SZSE |
    """
    with open(file_path, 'r') as f:
        lines = f.readlines()
    symbols = {}
    for line in lines:
        # line = line.strip()# 不对，两边不只是有空格
        line = line.strip('\n').strip('|').strip()

        if line:
            symbol, exchange = line.rsplit('.')
            if exchange not in symbols:
                symbols[exchange] = []
            symbols[exchange].append(symbol)
    return symbols

symbols = load_symbol_dict_from_txt()
print(f'symbols:{symbols}')

# 设置下载时间段
start_date = datetime(2020, 1, 1, tzinfo=DB_TZ)
end_date = datetime(2099, 1, 1, tzinfo=DB_TZ)

# interval = Interval.HOUR
# interval = Interval.MINUTE
interval = Interval.DAILY

from datetime import datetime, timedelta

from pandas import DataFrame
from rqdatac.services.get_price import get_price
from rqdatac.services.calendar import get_next_trading_date

from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData, HistoryRequest
from vnpy.trader.utility import round_to

from vnpy_rqdata.rqdata_datafeed import INTERVAL_ADJUSTMENT_MAP, CHINA_TZ


# 批量下载
def query_save_data(req):
    data = datafeed.query_bar_history(req)
    if data:
        database.save_bar_data(data)
        print(f"{req.symbol}历史数据下载完成")


def download_data(symbol_type="88"):
    bar_overview = database.get_bar_overview()
    overview_symbols = [(d.symbol, d.exchange.value, d.interval) for d in bar_overview]

    symbols_to_download = []
    for exchange, symbols_list in symbols.items():
        for symbol in symbols_list:
            if (symbol + symbol_type, exchange, interval) not in overview_symbols:
                symbols_to_download.append((symbol + symbol_type, Exchange(exchange), interval))
    print(f'symbols_to_download:{symbols_to_download}')

    for symbol, exchange, _ in symbols_to_download:
        req = HistoryRequest(
            symbol=symbol,
            exchange=exchange,
            start=start_date,
            interval=interval,
            end=end_date,
        )
        query_save_data(req)


# download_data()

# 增量更新
def update_data():
    end = datetime.now().astimezone(DB_TZ)
    data = database.get_bar_overview()
    for d in data:
        symbol = d.symbol
        exchange = d.exchange
        start = d.end

        req = HistoryRequest(symbol=symbol, exchange=exchange, start=start, interval=interval, end=end, )
        query_save_data(req=req)


def main():
    download_data(symbol_type="")
    update_data()


if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()

        # # 每日定时自动更新
        # current_time = datetime.now().time()
        # start_time = time(17, 0, 0)  # 每天17:00开始更新
        #
        # while True:
        #     sleep(60)  # 每分钟检查一次
        #     if current_time == start_time:
        #         # download_data()
        #         update_data()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(e)
        from send_to_wechat import WeChat

        wx = WeChat()
        import requests
        ip = requests.get('https://ifconfig.me').text
        wx.send_data(f"{ip}:{__file__}: An error occurred! ", touser='liaoyuan')