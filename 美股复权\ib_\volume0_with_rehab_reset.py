#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os
from datetime import datetime
from typing import Dict, Optional, List, Tuple
import traceback
from loguru import logger
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据库相关模块
from utils.database_manager import db_manager, IbProduct, FutuRehab, FirstrateRehab
from utils.mysql_database import create_mysql_database
from vnpy.trader.utility import load_json
from vnpy.trader.constant import Exchange, Interval

# 配置日志
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
os.makedirs("logs", exist_ok=True)
logger.add(f"logs/volume_rehab_fixer.{timestamp}.log", 
          format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
          level="INFO",
          rotation="1 MB")

class VolumeRehabFixer:
    """Volume复权事件修复器"""
    
    def __init__(self):
        """初始化"""
        self.conid_symbol_map = {}  # conid到symbol的映射
        self.us_db_manager = None
        self.DbBarData = None
        self.DbBarOverview = None
        self.us_stock_settings = load_json("vt_setting.json")
        self._init_databases()
        self._load_symbol_mappings()
    
    def _init_databases(self):
        """初始化数据库连接"""
        try:
            # 初始化US股票数据库
            self.us_db_manager, self.DbBarData, _, self.DbBarOverview, _ = create_mysql_database(self.us_stock_settings)
            logger.info("US股票数据库初始化成功")
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {str(e)}\n{traceback.format_exc()}")
            self.us_db_manager = None
            self.DbBarData = None
            self.DbBarOverview = None
    
    def _load_symbol_mappings(self):
        """加载conid到symbol的映射关系"""
        try:
            query = IbProduct.select(IbProduct.symbol, IbProduct.conid, IbProduct.is_latest)
            
            for record in query:
                if record.symbol and record.conid and record.is_latest:
                    self.conid_symbol_map[str(record.conid)] = record.symbol
            
            logger.info(f"已加载{len(self.conid_symbol_map)}个conid到symbol的映射关系")
            
        except Exception as e:
            logger.error(f"加载symbol映射失败: {str(e)}\n{traceback.format_exc()}")
            self.conid_symbol_map = {}
    
    def _get_symbol(self, conid: str) -> Optional[str]:
        """获取conid对应的symbol"""
        return self.conid_symbol_map.get(str(conid))
    
    def _get_rehab_info(self, symbol: str, ex_div_date: datetime) -> Dict:
        """
        从FutuRehab和FirstrateRehab表中获取分红拆股信息
        
        复权事件判断条件：
        - FutuRehab: split_ratio或per_share_div_ratio不为null且>0
        - FirstrateRehab: split_ratio不为null且>0
        
        Args:
            symbol: 股票symbol (如AAPL)
            ex_div_date: 除权日期
            
        Returns:
            包含分红拆股信息的字典，包括if_rehab字段标识数据来源
        """
        try:
            # 处理symbol格式
            futu_code = f'US.{symbol.replace(" ", ".")}'
            if ' ' in symbol:
                fr_symbol = symbol.split(' ')[-1]
                fr_symbol = symbol.split(' ')[0]+'.'+fr_symbol[-1]
            else:
                fr_symbol = symbol
            
            # 首先尝试FutuRehab表
            futu_record = FutuRehab.get_or_none(
                (FutuRehab.code == futu_code) & 
                (FutuRehab.ex_div_date == ex_div_date.date())
            )
            
            if futu_record:
                # 检查FutuRehab是否有有效的复权事件：split_ratio或per_share_div_ratio不为null且>0
                has_split = futu_record.split_ratio is not None and futu_record.split_ratio > 0
                has_div_ratio = futu_record.per_share_div_ratio is not None and futu_record.per_share_div_ratio > 0
                
                if has_split or has_div_ratio:
                    return {
                        'split_ratio': futu_record.split_ratio,
                        'per_share_div_ratio': futu_record.per_share_div_ratio,
                        'per_cash_div': futu_record.per_cash_div,
                        'special_div': futu_record.special_dividend,
                        'if_rehab': 'futu',
                        'has_rehab': True
                    }
            
            # 如果FutuRehab没找到或没有有效复权事件，尝试FirstrateRehab表
            fr_record = FirstrateRehab.get_or_none(
                (FirstrateRehab.symbol == fr_symbol) & 
                (FirstrateRehab.ex_div_date == ex_div_date.date())
            )
            
            if fr_record:
                # 检查FirstrateRehab是否有有效的复权事件：split_ratio不为null且>0
                has_split = fr_record.split_ratio is not None and fr_record.split_ratio > 0
                
                if has_split:
                    return {
                        'split_ratio': fr_record.split_ratio,
                        'per_share_div_ratio': None,  # FirstrateRehab没有这个字段
                        'per_cash_div': fr_record.dividend,  # 使用dividend字段填充per_cash_div
                        'special_div': None,  # FirstrateRehab没有这个字段
                        'if_rehab': 'fr',
                        'has_rehab': True
                    }
            
            # 两个表都没找到
            return {
                'split_ratio': None,
                'per_share_div_ratio': None,
                'per_cash_div': None,
                'special_div': None,
                'if_rehab': 'no_rehab',
                'has_rehab': False
            }
                
        except Exception as e:
            logger.error(f"查询复权信息失败 {symbol} {ex_div_date}: {str(e)}")
            return {
                'split_ratio': None,
                'per_share_div_ratio': None,
                'per_cash_div': None,
                'special_div': None,
                'if_rehab': 'no_rehab',
                'has_rehab': False
            }
    
    def _get_all_conids(self) -> List[str]:
        """从DbBarOverview获取所有日线数据的conid列表"""
        try:
            if not self.DbBarOverview:
                logger.error("DbBarOverview未初始化")
                return []
            
            query = (self.DbBarOverview.select(self.DbBarOverview.symbol)
                    .where(
                        (self.DbBarOverview.exchange == Exchange.SMART.value) &
                        (self.DbBarOverview.interval == Interval.DAILY.value)
                    )
                    .distinct())
            
            conids = [record.symbol for record in query]
            logger.info(f"从DbBarOverview获取到{len(conids)}个conid")
            return conids
            
        except Exception as e:
            logger.error(f"获取conid列表失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def _find_zero_volume_with_rehab(self, conid: str) -> List[Dict]:
        """
        查找指定conid中volume为0且有复权事件的K线，并且当天open和前一根bar的close价格比例大于1%
        
        Args:
            conid: 要处理的conid
            
        Returns:
            符合条件的K线列表
        """
        try:
            # 获取该conid的所有日线数据，按日期排序
            query = (self.DbBarData.select()
                    .where(
                        (self.DbBarData.symbol == conid) &
                        (self.DbBarData.exchange == Exchange.SMART.value) &
                        (self.DbBarData.interval == Interval.DAILY.value) &
                        (self.DbBarData.volume == 0)  # 只查找volume为0的
                    )
                    .order_by(self.DbBarData.datetime))
            
            bars = list(query)
            if not bars:
                return []
            
            symbol = self._get_symbol(conid)
            if not symbol:
                return []
            
            results = []
            
            for bar in bars:
                # 检查当日是否有复权事件
                rehab_info = self._get_rehab_info(symbol, bar.datetime)
                
                if rehab_info['has_rehab']:
                    # 获取前一根bar
                    prev_bar = self._get_prev_bar(conid, bar.datetime)
                    
                    # 检查是否有前一根bar，并且价格比例大于1%
                    price_ratio_check = False
                    prev_close = None
                    
                    if prev_bar and prev_bar.close_price and prev_bar.close_price > 0:
                        prev_close = prev_bar.close_price
                        price_ratio = abs(bar.open_price - prev_close) / prev_close
                        if price_ratio > 0.01:  # 大于1%
                            price_ratio_check = True
                    
                    # 只有当价格比例检查通过时才添加到结果中
                    if price_ratio_check:
                        result = {
                            'symbol': symbol,
                            'conid': conid,
                            'datetime': bar.datetime,
                            'interval': bar.interval,
                            'volume': bar.volume,
                            'open_price': bar.open_price,
                            'high_price': bar.high_price,
                            'low_price': bar.low_price,
                            'close_price': bar.close_price,
                            'prev_close': prev_close,
                            'price_ratio': abs(bar.open_price - prev_close) / prev_close,
                            'split_ratio': rehab_info['split_ratio'],
                            'per_share_div_ratio': rehab_info['per_share_div_ratio'],
                            'per_cash_div': rehab_info['per_cash_div'],
                            'special_div': rehab_info['special_div'],
                            'if_rehab': rehab_info['if_rehab']
                        }
                        
                        results.append(result)
                        
                        logger.info(f"发现volume=0且有复权事件且价格变动>1%: {symbol} "
                                  f"({bar.datetime.strftime('%Y-%m-%d')}) "
                                  f"open: {bar.open_price:.2f}, prev_close: {prev_close:.2f}, "
                                  f"比例: {result['price_ratio']:.2%}, "
                                  f"复权来源: {rehab_info['if_rehab']}")
            
            return results
            
        except Exception as e:
            logger.error(f"处理conid {conid} 失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def _get_prev_bar(self, conid: str, target_date: datetime) -> Optional[object]:
        """
        获取指定日期的前一根bar（不考虑volume是否为0）
        
        Args:
            conid: 合约ID
            target_date: 目标日期
            
        Returns:
            前一根bar对象，如果没找到返回None
        """
        try:
            query = (self.DbBarData.select()
                    .where(
                        (self.DbBarData.symbol == conid) &
                        (self.DbBarData.exchange == Exchange.SMART.value) &
                        (self.DbBarData.interval == Interval.DAILY.value) &
                        (self.DbBarData.datetime < target_date)
                    )
                    .order_by(self.DbBarData.datetime.desc())
                    .limit(1))
            
            prev_bar = query.first()
            return prev_bar
            
        except Exception as e:
            logger.error(f"获取前一根bar失败 {conid} {target_date}: {str(e)}")
            return None

    def _get_prev_valid_close(self, conid: str, target_date: datetime) -> Optional[float]:
        """
        获取指定日期之前最近的一个volume不为0的收盘价
        
        Args:
            conid: 合约ID
            target_date: 目标日期
            
        Returns:
            前一个有效的收盘价，如果没找到返回None
        """
        try:
            query = (self.DbBarData.select()
                    .where(
                        (self.DbBarData.symbol == conid) &
                        (self.DbBarData.exchange == Exchange.SMART.value) &
                        (self.DbBarData.interval == Interval.DAILY.value) &
                        (self.DbBarData.datetime < target_date) &
                        (self.DbBarData.volume > 0)  # volume大于0
                    )
                    .order_by(self.DbBarData.datetime.desc())
                    .limit(1))
            
            prev_bar = query.first()
            if prev_bar:
                return prev_bar.close_price
            
            return None
            
        except Exception as e:
            logger.error(f"获取前一有效收盘价失败 {conid} {target_date}: {str(e)}")
            return None
    
    def _fix_zero_volume_bar(self, bar_info: Dict) -> bool:
        """
        修复volume为0且有复权事件的K线数据
        
        Args:
            bar_info: K线信息字典
            
        Returns:
            修复是否成功
        """
        try:
            conid = bar_info['conid']
            target_date = bar_info['datetime']
            
            # 获取前一个有效的收盘价
            prev_close = self._get_prev_valid_close(conid, target_date)
            
            if prev_close is None:
                logger.warning(f"未找到{bar_info['symbol']}在{target_date.strftime('%Y-%m-%d')}之前的有效收盘价")
                return False
            
            # 更新K线数据，将OHLC都设置为前一个有效收盘价
            update_query = (self.DbBarData.update(
                open_price=prev_close,
                high_price=prev_close,
                low_price=prev_close,
                close_price=prev_close
            ).where(
                (self.DbBarData.symbol == conid) &
                (self.DbBarData.exchange == Exchange.SMART.value) &
                (self.DbBarData.interval == Interval.DAILY.value) &
                (self.DbBarData.datetime == target_date)
            ))
            
            rows_updated = update_query.execute()
            
            if rows_updated > 0:
                logger.info(f"成功修复 {bar_info['symbol']} "
                          f"({target_date.strftime('%Y-%m-%d')}) "
                          f"OHLC设置为: {prev_close:.2f}")
                return True
            else:
                logger.warning(f"修复失败，未找到对应的K线记录")
                return False
                
        except Exception as e:
            logger.error(f"修复K线失败: {str(e)}\n{traceback.format_exc()}")
            return False
    
    def process_all_conids(self, replace: bool = False) -> List[Dict]:
        """
        处理所有conid，查找或修复volume为0且有复权事件且价格变动>1%的K线
        
        条件：
        1. volume为0
        2. 当天有复权事件
        3. 当天open价格与前一根bar的close价格比例大于1%
        
        Args:
            replace: False输出查找结果，True进行修复操作
            
        Returns:
            处理结果列表
        """
        try:
            if not self.us_db_manager or not self.DbBarData or not self.DbBarOverview:
                logger.error("数据库未初始化")
                return []
            
            # 获取所有conid
            conids = self._get_all_conids()
            if not conids:
                logger.error("未获取到任何conid")
                return []
            
            all_results = []
            processed_count = 0
            fixed_count = 0
            
            logger.info(f"开始处理{len(conids)}个conid...")
            logger.info(f"模式: {'修复' if replace else '查找'}")
            
            # 处理每个conid
            for conid in conids:
                processed_count += 1
                
                if processed_count % 100 == 0:
                    logger.info(f"已处理 {processed_count}/{len(conids)} 个conid...")
                
                # 查找volume为0且有复权事件的K线
                zero_volume_bars = self._find_zero_volume_with_rehab(conid)
                
                if zero_volume_bars:
                    all_results.extend(zero_volume_bars)
                    
                    if replace:
                        # 修复模式：对每个找到的K线进行修复
                        for bar_info in zero_volume_bars:
                            if self._fix_zero_volume_bar(bar_info):
                                fixed_count += 1
            
            logger.info(f"处理完成，共处理{len(conids)}个conid")
            logger.info(f"发现{len(all_results)}个volume=0且有复权事件且价格变动>1%的K线")
            
            if replace:
                logger.info(f"成功修复{fixed_count}个K线")
            
            return all_results
            
        except Exception as e:
            logger.error(f"处理失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def export_to_csv(self, results: List[Dict], filename: str = None) -> str:
        """
        将结果导出到CSV文件
        
        Args:
            results: 结果列表
            filename: 输出文件名，如果为None则自动生成
            
        Returns:
            输出文件名
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.us_stock_settings['database.database']}_zero_volume_rehab_{timestamp}.csv"
        
        try:
            if not results:
                logger.warning("没有数据需要导出")
                return filename
            
            df = pd.DataFrame(results)
            
            # 选择需要的列并排序
            columns = ['symbol', 'conid', 'datetime', 'interval', 'volume',
                      'open_price', 'high_price', 'low_price', 'close_price',
                      'prev_close', 'price_ratio',
                      'split_ratio', 'per_share_div_ratio', 'per_cash_div', 'special_div', 'if_rehab']
            
            df = df[columns].sort_values(['symbol', 'datetime'], ascending=[True, True])
            
            df.to_csv(filename, index=False)
            logger.info(f"结果已导出到: {filename}")
            
            return filename
            
        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}\n{traceback.format_exc()}")
            return filename
    
    def generate_summary_report(self, results: List[Dict], replace: bool):
        """生成汇总报告"""
        if not results:
            logger.warning("没有找到volume=0且有复权事件且价格变动>1%的数据")
            return
        
        logger.info("=" * 60)
        logger.info(f"Volume=0复权事件价格变动>1%{'修复' if replace else '查找'}报告")
        logger.info("=" * 60)
        
        df = pd.DataFrame(results)
        
        logger.info(f"发现记录总数: {len(results)}")
        logger.info(f"涉及股票数: {df['symbol'].nunique()}")
        
        # 按复权来源统计
        rehab_stats = df['if_rehab'].value_counts()
        logger.info(f"\n按复权数据源统计:")
        for source, count in rehab_stats.items():
            logger.info(f"  {source}: {count}个")
        
        # 按年份统计
        df['year'] = pd.to_datetime(df['datetime']).dt.year
        yearly_stats = df.groupby('year').size()
        
        logger.info(f"\n按年份统计:")
        for year, count in yearly_stats.items():
            logger.info(f"  {year}: {count}个")
        
        # 前10个股票
        top_symbols = df['symbol'].value_counts().head(10)
        logger.info(f"\n涉及最多的前10个股票:")
        for symbol, count in top_symbols.items():
            logger.info(f"  {symbol}: {count}个")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Volume=0复权事件修复工具')
    parser.add_argument('--replace', action='store_true',
                       help='是否进行修复操作（默认为False，只查找不修复）')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出CSV文件名 (默认: 自动生成)')
    
    args = parser.parse_args()
    
    logger.info(f"开始Volume=0复权事件{'修复' if args.replace else '查找'}...")
    
    try:
        # 创建修复器
        fixer = VolumeRehabFixer()
        
        # 处理所有conid
        results = fixer.process_all_conids(replace=args.replace)
        
        # 导出结果
        if results:
            output_file = fixer.export_to_csv(results, args.output)
        
        # 生成汇总报告
        fixer.generate_summary_report(results, args.replace)
        
        logger.info("处理完成")
        if results:
            logger.info(f"结果文件: {output_file}")
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}\n{traceback.format_exc()}")
    
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main() 