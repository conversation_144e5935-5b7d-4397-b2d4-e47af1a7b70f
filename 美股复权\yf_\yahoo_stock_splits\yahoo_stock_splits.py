from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import logging
from datetime import datetime, timedelta
import numpy as np
from peewee import chunked
import json
import requests
from loguru import logger

# 导入数据库配置和企业微信消息功能
from utils.database_manager import db_manager, InvestingRehabOriginal, InvestingRehabMerged
from utils.wecom_alert import *

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_driver():
    """设置并返回 Chrome WebDriver"""
    chrome_options = Options()
    # 暂时禁用无头模式以便观察加载过程
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument(
        "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

    # 禁用图像加载以加快页面加载速度
    chrome_options.add_experimental_option("prefs", {
        "profile.default_content_setting_values.images": 2,
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0
    })

    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        logger.error(f"初始化WebDriver失败: {e}")
        return None


def wait_for_page_complete(driver, timeout=30):
    """等待页面完全加载"""
    logger.info("等待页面完全加载...")
    try:
        # 等待页面加载完成 - 检查是否有数据或"No results"提示
        WebDriverWait(driver, timeout).until(
            lambda d: d.find_elements(By.CSS_SELECTOR, "div.table-container table") or
                      d.find_elements(By.CSS_SELECTOR, "p.primary.yf-1p1o0uf.centerText")
        )
        logger.info("页面加载完成")
        return True
    except TimeoutException:
        logger.error("页面加载超时")
        return False


def check_no_results(driver):
    """检查页面是否显示无结果的提示"""
    try:
        no_results_element = driver.find_element(By.CSS_SELECTOR, "p.primary.yf-1p1o0uf.centerText")
        if "We couldn't find any results" in no_results_element.text:
            logger.info("页面显示无拆合股数据")
            return True
    except NoSuchElementException:
        pass  # 没有找到无结果提示，说明可能有数据
    return False


def process_ratio_data(df):
    """处理Ratio列数据，判断是合股还是拆股"""
    # 初始化新列
    df['join_base'] = None
    df['join_ert'] = None
    df['split_base'] = None
    df['split_ert'] = None
    df['split_ratio'] = None

    for idx, row in df.iterrows():
        ratio = row['Ratio']
        if ratio and isinstance(ratio, str) and '-' in ratio:
            try:
                # 分割字符串并去除空格
                parts = ratio.split('-')
                left = float(parts[0].strip())
                right = float(parts[1].strip())

                # 判断是合股还是拆股
                if left > right:
                    # 合股情况
                    df.at[idx, 'join_base'] = left
                    df.at[idx, 'join_ert'] = right
                    df.at[idx, 'split_ratio'] = left / right
                elif left < right:
                    # 拆股情况
                    df.at[idx, 'split_base'] = left
                    df.at[idx, 'split_ert'] = right
                    df.at[idx, 'split_ratio'] = left / right
                else:
                    # 相等情况（理论上不会发生）
                    df.at[idx, 'split_base'] = left
                    df.at[idx, 'split_ert'] = right
                    df.at[idx, 'split_ratio'] = 1.0
            except ValueError:
                # 处理无法转换为数字的情况
                logger.warning(f"无法解析Ratio值: {ratio} (行 {idx + 1})")
                continue

    return df


def get_stock_splits_data_for_date(driver, target_date):
    """获取特定日期的股票分割数据"""
    # 计算日期范围
    from_date = (target_date - timedelta(days=3)).strftime("%Y-%m-%d")
    to_date = (target_date + timedelta(days=3)).strftime("%Y-%m-%d")
    day_str = target_date.strftime("%Y-%m-%d")

    # 构建URL
    url = f"https://finance.yahoo.com/calendar/splits?from={from_date}&to={to_date}&day={day_str}"
    logger.info(f"正在访问页面: {url}")

    try:
        driver.get(url)

        # 等待页面完全加载
        if not wait_for_page_complete(driver):
            return []

        # 检查是否有"No results"提示
        if check_no_results(driver):
            logger.info(f"{target_date.strftime('%Y-%m-%d')} 无拆合股数据")
            return []

        return extract_data_from_page(driver, target_date)
    except Exception as e:
        logger.error(f"获取{target_date}数据时出错: {e}")
        return []


def extract_data_from_page(driver, target_date):
    """从当前页面提取数据"""
    data = []
    page_num = 1
    target_date_str = target_date.strftime("%Y-%m-%d")

    while True:
        logger.info(f"正在处理第 {page_num} 页数据...")

        try:
            # 找到所有数据行
            rows = driver.find_elements(By.CSS_SELECTOR, "tr[data-testid='data-table-v2-row']")
            logger.info(f"找到 {len(rows)} 行数据")

            for i, row in enumerate(rows):
                try:
                    row_data = {}
                    row_data['Date'] = target_date_str

                    # 提取Symbol
                    try:
                        symbol_elem = row.find_element(By.CSS_SELECTOR, "[data-testid-cell='ticker']")
                        row_data['Symbol'] = symbol_elem.text.strip()
                    except NoSuchElementException:
                        row_data['Symbol'] = "N/A"

                    # 提取Company
                    try:
                        company_elem = row.find_element(By.CSS_SELECTOR, "[data-testid-cell='companyshortname']")
                        row_data['Company'] = company_elem.text.strip()
                    except NoSuchElementException:
                        row_data['Company'] = "N/A"

                    # 提取Payable On
                    try:
                        date_elem = row.find_element(By.CSS_SELECTOR, "[data-testid-cell='startdatetime']")
                        row_data['Payable On'] = date_elem.text.strip()
                    except NoSuchElementException:
                        row_data['Payable On'] = "N/A"

                    # 提取Optionable
                    try:
                        optionable_elem = row.find_element(By.CSS_SELECTOR, "[data-testid-cell='optionable']")
                        row_data['Optionable?'] = optionable_elem.text.strip()
                    except NoSuchElementException:
                        row_data['Optionable?'] = "N/A"

                    # 提取Ratio
                    try:
                        ratio_elem = row.find_element(By.CSS_SELECTOR, "[data-testid-cell='share_worth']")
                        row_data['Ratio'] = ratio_elem.text.strip()
                    except NoSuchElementException:
                        row_data['Ratio'] = "N/A"

                    data.append(row_data)

                    # 每处理5行打印一次进度
                    if (i + 1) % 5 == 0:
                        logger.info(f"已处理 {i + 1} 行数据")

                except Exception as e:
                    logger.warning(f"处理第 {i + 1} 行时出错: {e}")
                    continue

            logger.info(f"第 {page_num} 页完成，共提取 {len(rows)} 条记录")

        except Exception as e:
            logger.error(f"提取数据时出错: {e}")
            break

        # 尝试点击下一页
        try:
            # 使用更精确的选择器定位下一页按钮
            next_button = driver.find_element(
                By.CSS_SELECTOR,
                "button[data-testid='next-page-button']"
            )

            # 检查按钮是否可点击
            if next_button.get_attribute("disabled"):
                logger.info("已到达最后一页")
                break

            # 使用JavaScript点击按钮，避免元素被遮挡
            driver.execute_script("arguments[0].click();", next_button)
            logger.info("点击下一页按钮")

            # 等待页面加载
            time.sleep(2)  # 短暂等待
            if not wait_for_page_complete(driver):
                logger.error("下一页加载失败")
                break

            page_num += 1

        except NoSuchElementException:
            logger.info("未找到下一页按钮，可能是最后一页")
            break
        except Exception as e:
            logger.error(f"点击下一页时出错: {e}")
            break

    return data


def get_stock_splits_data(start_date, end_date):
    """获取指定日期范围内的股票分割数据"""
    driver = setup_driver()
    if not driver:
        return None

    try:
        all_data = []

        # 将字符串日期转换为datetime对象
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        # 计算日期范围
        delta = end - start
        date_list = [start + timedelta(days=i) for i in range(delta.days + 1)]

        logger.info(f"开始获取从 {start_date} 到 {end_date} 的数据，共 {len(date_list)} 天")

        for i, current_date in enumerate(date_list):
            logger.info(f"正在处理第 {i + 1}/{len(date_list)} 天: {current_date.strftime('%Y-%m-%d')}")

            # 获取单日数据
            daily_data = get_stock_splits_data_for_date(driver, current_date)
            all_data.extend(daily_data)

            logger.info(f"已获取 {current_date.strftime('%Y-%m-%d')} 的数据，共 {len(daily_data)} 条记录")

            # 添加延迟，避免请求过于频繁
            time.sleep(1)

        # 创建DataFrame
        df = pd.DataFrame(all_data)

        # 处理Ratio列数据
        if not df.empty:
            df = process_ratio_data(df)
            df.to_csv("yahoo_stock_splits.csv", index=False)
            logger.info(f"所有数据已保存到 yahoo_stock_splits.csv，共 {len(df)} 条记录")
            return df
        else:
            logger.warning("未提取到任何数据")
            return None

    except Exception as e:
        logger.error(f"访问页面时出错: {e}")
        return None

    finally:
        if driver:
            driver.quit()


def save_to_database(df):
    """将数据保存到数据库"""
    try:
        # 转换数据格式以匹配数据库模型
        db_records = []
        for _, row in df.iterrows():
            # 确定是拆股还是合股
            if pd.notna(row.get('split_ratio')):
                if row.get('split_ratio', 1) > 1:
                    # 拆股
                    split_base = row.get('split_base')
                    split_ert = row.get('split_ert')
                    join_base = None
                    join_ert = None
                else:
                    # 合股
                    split_base = None
                    split_ert = None
                    join_base = row.get('join_base')
                    join_ert = row.get('join_ert')
            else:
                split_base = None
                split_ert = None
                join_base = None
                join_ert = None

            # 创建记录
            record = {
                'symbol': row.get('Symbol', ''),
                'description': row.get('Company', ''),
                'ex_div_date': datetime.strptime(row.get('Date'), "%Y-%m-%d"),
                'split_base': split_base,
                'split_ert': split_ert,
                'join_base': join_base,
                'join_ert': join_ert,
                'split_ratio': row.get('split_ratio'),
                'action_count': 1  # 默认每次行动计数为1
            }
            db_records.append(record)

        # 使用数据库管理器保存数据
        if db_records:
            # 保存到原始表
            db_manager.batch_save_to_db(db_records, InvestingRehabOriginal, replace=True)
            logger.info(f"成功将 {len(db_records)} 条记录保存到 InvestingRehabOriginal 表")

            # 保存到合并表
            db_manager.batch_save_to_db(db_records, InvestingRehabMerged, replace=True)
            logger.info(f"成功将 {len(db_records)} 条记录保存到 InvestingRehabMerged 表")

        return True

    except Exception as e:
        logger.error(f"保存数据到数据库时出错: {e}")
        return False


def get_today_summary_markdown(df):
    """生成今日数据的Markdown格式摘要"""
    today = datetime.now().strftime("%Y-%m-%d")

    # 筛选今日数据
    today_data = df[df['Date'] == today]

    if today_data.empty:
        return f"# {today} 股票拆合股数据\n\n今日无拆合股数据"

    # 创建Markdown表格
    markdown = f"# {today} 股票拆合股数据\n\n"
    markdown += "| 代码 | 公司 | 拆合股比例 | 类型 |\n"
    markdown += "|------|------|------------|------|\n"

    for _, row in today_data.iterrows():
        symbol = row.get('Symbol', 'N/A')
        company = row.get('Company', 'N/A')
        ratio = row.get('Ratio', 'N/A')

        # 判断是拆股还是合股
        action_type = "未知"
        if pd.notna(row.get('split_ratio')):
            if row.get('split_ratio', 1) > 1:
                action_type = "合股"
            else:
                action_type = "拆股"

        markdown += f"| {symbol} | {company} | {ratio} | {action_type} |\n"

    return markdown


# 调用函数
if __name__ == "__main__":
    # 设置日期范围
    start_date = "2025-08-25"
    end_date = "2025-08-31"

    # 企业微信群webhook URL
    wecom_webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY_HERE"

    # 获取数据
    stock_splits_data = get_stock_splits_data(start_date, end_date)

    if stock_splits_data is not None:
        print(stock_splits_data.head(10))
        print(f"总共获取了 {len(stock_splits_data)} 条记录")

        # 保存数据到数据库
        save_to_database(stock_splits_data)

        # 生成今日摘要并发送到企业微信
        markdown_message = get_today_summary_markdown(stock_splits_data)

        # 发送企业微信消息
        success, msg = report_we_alert(
            text="",
            key="ee0b6801-f2c5-4811-ba1f-227b543b3459",  # 使用您提供的默认key
            msgtype="markdown_v2",
            markdown_v2_content=markdown_message
        )

        if success:
            logger.info("企业微信消息发送成功")
        else:
            logger.error(f"企业微信消息发送失败: {msg}")
    else:
        print("未能获取数据")
        # 发送错误消息到企业微信
        error_message = f"# {datetime.now().strftime('%Y-%m-%d')} 股票拆合股数据获取失败\n\n未能从Yahoo Finance获取{start_date}到{end_date}的数据"

        success, msg = report_we_alert(
            text="",
            key="ee0b6801-f2c5-4811-ba1f-227b543b3459",
            msgtype="markdown_v2",
            markdown_v2_content=error_message
        )

        if success:
            logger.info("错误消息发送成功")
        else:
            logger.error(f"错误消息发送失败: {msg}")