-- VWAV 799151668 2025-07-15前frt没数据 ib明显不对，没有复权因子，nasdaq有数据 看起来正常 
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-07-15 12:00:00';

-- DAIC 793105127 和nasdaq比少了2025-06-20bar其他前后一致，frt没数据，可以用nasdaq补，但是对应分钟线无法操作，维持不变 

-- ATCH 751482788 2024-02-09当天volume是0ohlc有明显偏差,前后和NASDAQ一致，当天nasdaqvolume是178，frt无数据, 可删除当天，
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '751482788' AND datetime = '2024-02-09 13:00:00';

-- IVF 800320224 ib中间缺一段，frt不缺，这一段frt数据质量和nasdaq一致；前后ib和frt一致，用frt填充 2025-03-17 至2024-10-22 ** fixdb无需处理
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '800320224' AND datetime < '2025-03-18 12:00:00' AND datetime >= '2024-10-22 12:00:00';
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '800320224' AND datetime < '2025-03-18 12:00:00' AND datetime >= '2024-10-22 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- GE 498843743 2024-04-02用frt覆盖一天数据，前后ib,frt对的上，当天和nasdaq差不多，有差异是因为分红处理 ** fixdb2无需处理
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '498843743' AND datetime >= '2024-04-02 12:00:00' AND datetime < '2024-04-03 12:00:00';
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '498843743' AND datetime >= '2024-04-02 12:00:00' AND datetime < '2024-04-03 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- SYTA 751043413 2023-08-09用frt覆盖一天数据，frt和nasdaq差不多，有差异是因为分红处理 ** fixdb2无需处理
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '751482788' AND datetime >= '2023-08-09 12:00:00' AND datetime < '2023-08-10 12:00:00';
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '751043413' AND datetime >= '2023-08-09 12:00:00' AND datetime < '2023-08-10 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- CEP 722303929 2025-04-23 12:00:00 frt和ib差不多，nasdaq open是12.52 和frtopen前后一样，可换可不换 *** 无需处理

-- UMAC 698227338 2024-12-02 13:00:00 frt和ib差不多，ib 22.61, nasdaq open是22.17 和frtopen一样 ，若替换跳开为18%，现为20.7% *** 无需处理

-- KAVL 679966975 2024-06-25 12:00:00 frt和ib差不多，ib 1.46, nasdaq frt open是1.44 和frtopen一样，若替换跳开为19%，现为20.7% *** 无需处理

-- 重置overview
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
            SELECT
                symbol,
                exchange,
                `interval`,
                COUNT(id) AS `count`,
                MIN(datetime) AS `start`,
                MAX(datetime) AS `end`
            FROM
                vnpy_stk_us_ib_d_2206_250814__.dbbardata
            GROUP BY
                symbol, exchange, `interval`
            ON DUPLICATE KEY UPDATE
                `count` = VALUES(`count`),
                `start` = VALUES(`start`),
                `end` = VALUES(`end`);