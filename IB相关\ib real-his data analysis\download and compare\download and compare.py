from datetime import datetime, timedelta, time
from urllib.parse import quote as urlquote

# 录制数据库配置
username = 'root'
password = 'p0o9i8u7'
dbHost = '**************'
dbPort = 3306
dbName = 'vnpyzhtest'
DATABASE_URI = f'mysql+pymysql://{username}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}'

# 使用pandas和sql语句，从数据库中读取数据保存到csv文件中
import pandas as pd
from sqlalchemy import create_engine
engine = create_engine(DATABASE_URI)
# 前一天下午9点到今天上午6点的数据
today = datetime.now().date()
yesterday = today - timedelta(days=1)

yesterday_9pm_str = datetime.combine(yesterday, time(21, 30)).strftime('%Y-%m-%d %H:%M:%S')
today_4am_str = datetime.combine(today, time(4, 0)).strftime('%Y-%m-%d %H:%M:%S')

sql = f"select * from dbbardata where datetime >= '{yesterday_9pm_str}' and datetime <= '{today_4am_str}'"
df = pd.read_sql(sql, engine)
# df.to_csv(f'{today}.csv', index=False)
from IPython.display import display
display(df)
# print(f'{today}.csv文件已经保存到当前目录下。')


# 历史数据库配置
dbName = 'vnpyibhis'
DATABASE_URI_HIS = f'mysql+pymysql://{username}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}'
engine_his = create_engine(DATABASE_URI_HIS)

df_his = pd.read_sql(sql, engine_his)
# df_his.to_csv(f'{today}_his.csv', index=False)
display(df_his)
# print(f'{today}_his.csv文件已经保存到当前目录下。')


# 比较df与df_his的数据（删除索引列、interval、id、exchange列，以symbol、datetime为索引），输出：df中有，df_his中没有的数据、df_his中有，df中没有的数据；df中df_his中都有的数据则作差，输出不为0的差值
# 删除索引列、id列、interval列
df = df.drop(['id', 'interval', 'exchange'], axis=1)
df_his = df_his.drop(['id', 'interval', 'exchange'], axis=1)
# 以symbol、datetime为二层索引
df = df.set_index(['symbol', 'datetime']).sort_index()
df_his = df_his.set_index(['symbol', 'datetime']).sort_index()
# df_his中有，df中没有的数据
df_his_diff_df = df_his[~df_his.index.isin(df.index)]
# 打印df_his中有，df中没有的数据的个数
print(f'df_his中有，df中没有的数据的个数：{len(df_his_diff_df)}')
# 打印df中有，df_his中没有的数据
df_diff_df_his = df[~df.index.isin(df_his.index)]
# 打印df中有，df_his中没有的数据的个数
print(f'df中有，df_his中没有的数据的个数：{len(df_diff_df_his)}')
# df中df_his中都有的数据的索引
df_diff_same_index = df.index.intersection(df_his.index)
# 不为0的差值
df_diff_same = df.loc[df_diff_same_index] - df_his.loc[df_diff_same_index]
# 以datetime、symbol为二层索引
df_diff_same = df_diff_same.reset_index().set_index(['symbol', 'datetime']).sort_index()
# 打印不为0的差值
display(df_diff_same)
# 保存三个结果到同一个excel文件的不同sheet中，注意为空的情况
with pd.ExcelWriter(f'{today}_diff.xlsx') as writer:
    # 如果df不为空，则保存到excel中
    if len(df) > 0:
        df.to_excel(writer, sheet_name='df_real')
    # 如果df_his不为空，则保存到excel中
    if len(df_his) > 0:
        df_his.to_excel(writer, sheet_name='df_his')
    # 如果df_diff_df_his不为空，则保存到excel中
    if len(df_diff_df_his) > 0:
        df_diff_df_his.to_excel(writer, sheet_name='df_diff_df_his')
    # 如果df_his_diff_df不为空，则保存到excel中
    if len(df_his_diff_df) > 0:
        df_his_diff_df.to_excel(writer, sheet_name='df_his_diff_df')
    # 如果df_diff_same不为空，则保存到excel中
    if len(df_diff_same) > 0:
        df_diff_same.to_excel(writer, sheet_name='df_diff_same')
print(f'{today}_diff.xlsx文件已经保存到当前目录下。')
