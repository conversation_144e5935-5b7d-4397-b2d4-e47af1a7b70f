from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
import talib
import numpy as np
import pandas as pd

class DoubleMA(CtaTemplate):
    
    author = "test"
    strategy_name = "DoubleMA"
    
    
    ma1_length = 12
    ma2_length = 26
    multi = 2        # 止损参数 atr的倍数
    fixed_size = 1   # 下单手数

    ma1 = []
    ma2 = []
    signal = 0

    parameters = [
        "ma1_length",
        "ma2_length",
        "multi",
        "fixed_size"
    ]
    variables = [
        "ma1",
        "ma2",
        "signal"
    ]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager()
        self.flatten_long = 0
        self.flatten_short = 0
        self.stoplock = 0        # 止损后是否不再开同方向的信号单
        self.signal_price = 0    # 记录信号价格
        self.lastdate = ''       # 记录上一根bar的日期
        self.open_date = ''      # 记录开仓日期，股票开仓平仓不能在同一交易日
        self.signal_adj_facctor = 1  # 有信号的时候记录需要对信号价的调整，用于计算止损价格

        # ex_factor部分
        ex_factor_file_path = 'ex_factor.json'
        data = pd.read_json(ex_factor_file_path)
        data['ex_date'] = pd.to_datetime(data['ex_date'], unit='ms').dt.strftime("%Y-%m-%d")       # 这里用str速度比较快
        data['announcement_date'] = pd.to_datetime(data['announcement_date'], unit='ms').dt.strftime("%Y-%m-%d")
        data = data[data['order_book_id'].str.contains(vt_symbol.split('.')[0])]
        self.ex_factor = data[['ex_date','order_book_id','ex_factor','announcement_date']]
        self.ex_date_list = data['ex_date'].to_list()

        self.bg15 = BarGenerator(self.on_bar, 15, self.on_15min_bar)
        self.am15 = ArrayManager()
        self.first_bar = True

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_bar(10)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)

    def record_signal_info(self, bar, mode):
        # 进场记录
        if mode == 'enter':
            self.open_date =  pd.to_datetime(bar.datetime.strftime("%Y-%m-%d"))
            self.signal_price = bar.close_price
        if mode == 'exit':
            self.signal_adj_facctor = 1
            self.flatten_long = 0
            self.open_date = ''
            self.signal_price = 0

    def cal_signal(self, bar):
        """
        信号部分
        """
        self.flatten_long = 0
        self.flatten_short = 0
        ma1 = self.am15.sma(self.ma1_length)
        ma2 = self.am15.sma(self.ma2_length)
        self.ma1.append(ma1)
        self.ma2.append(ma2)
        if len(self.ma1)>=2:
            if self.ma1[-1]>self.ma2[-1] and self.ma1[-2]<self.ma2[-2]:
                self.signal = 1
            elif self.ma1[-1]<self.ma2[-1]:
                self.signal = 0
                self.flatten_long = 1
                # print('flatten')

    def exe_logical(self, bar):
        """
        下单逻辑
        """
        if self.pos == 0:
            if self.signal > 0 and self.stoplock <= 0:
                # self.buy(round(bar.close_price*1.1,2), self.fixed_size)
                self.buy(bar.close_price+1, self.fixed_size)
                self.record_signal_info(bar, mode='enter')
                self.write_log(f"signal record! open signal! time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction:{self.signal}, price:{bar.close_price}, volume:{self.fixed_size}")
            elif self.signal < 0 and self.stoplock >= 0 :
                self.short(round(bar.close_price*0.9,2), self.fixed_size)
                self.record_signal_info(bar, mode='enter')
                # print(f'空头开仓: {str(self.open_date)[:10]}')
        elif self.pos > 0:
            if self.flatten_long and self.open_date != pd.to_datetime(bar.datetime.strftime("%Y-%m-%d")) and self.open_date != '':   # 股票的开仓日期和平仓日期必须不同(T+1)
                self.sell(bar.close_price-1, abs(self.pos))
                self.record_signal_info(bar, mode='exit')
                self.write_log(f"signal record! flatten signal! time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction:{self.signal}, price:{bar.close_price}, volume:{self.fixed_size}")
        elif self.pos < 0:
            if self.flatten_short:
                self.cover(bar.close_price, abs(self.pos))
                self.record_signal_info(bar, mode='exit')

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.cancel_all()
        self.bg15.update_bar(bar)

        # 更新数据
        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return
        self.put_event()

    def on_15min_bar(self, bar: BarData):
        self.cancel_all()
        # 更新数据
        am15 = self.am15
        am15.update_bar(bar)
        if not am15.inited:
            return
        if self.first_bar:
            print("The first bar", bar.datetime, bar.open_price)
            self.first_bar = False
        self.cal_signal(bar)
        self.exe_logical(bar)
        self.put_event()

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
