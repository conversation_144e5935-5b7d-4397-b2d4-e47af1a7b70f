""""""
import sys
import traceback
from threading import Thread
from queue import Queue, Empty
from typing import Callable, Dict, Optional
from datetime import datetime,time
from vnpy.event import Event, EventEngine
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.object import (
    SubscribeRequest,
    TickData,
    BarData,
    ContractData
)
import redis
from vnpy.trader.event import EVENT_TICK, EVENT_CONTRACT, EVENT_TIMER
from vnpy.trader.utility import load_json, save_json 
from vnpy.trader.object import BarData, TickData, Interval
from vnpy.trader.constant import (
    Exchange
)
from vnpy.trader.setting import SETTINGS
APP_NAME = "BarGenEngine"
EVENT_BAR = "eBarGen."
EVENT_BAR_RECORD = "eBarGenRec."
from vnpy.trader.database import DB_TZ
from .symbol_info import get_closing_minutes, extract_symbol_pre


# 定时重载 setting.json
class BarGenEngine(BaseEngine):
    """"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.queue = Queue()
        self.thread = Thread(target=self.run)
        self.active = False

        self.bar_recordings = []
        # self.bar_generators = {}
        
        self.bars: Dict[str, BarData] = {}
        self.last_ticks: Dict[str, TickData] = {}
        #self.last_dt: datetime = None
        self.last_dts: Dict[str, datetime] = {}

        self.tick_time: Dict[str, str] = {} # cache tick time for debug info
        self.timer_count = 0
        self.timer_interval = 10
        # create connection
        self.r = redis.Redis(
            host=SETTINGS["redis.host"],
            port=SETTINGS["redis.port"], 
            password=SETTINGS["redis.password"],
            decode_responses=True
        )
        self.register_event()
        # self.start()  # need test vigar 1216 !
        self.put_event()

        # 记录每个合约的收盘时间
        # 格式: {vt_symbol: set(closing_time)}
        self.closing_times: Dict[str, set[datetime.time]] = {}

    def run(self):
        """"""
        while self.active:
            try:
                pass
            except Exception:
                msg = f"error 触发异常已停止\n{traceback.format_exc()}"
                self.write_log(msg)

    def close(self):
        """"""
        self.active = False
        if self.thread.is_alive():
            self.thread.join()

    def start(self):
        """"""
        self.active = True
        self.thread.start()

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            contract = self.main_engine.get_contract(vt_symbol)
            if not contract:
                self.write_log(f"找不到合约：{vt_symbol}")
                return
            self.write_log(f"prepare to send subscribe req：{vt_symbol}")
            self.subscribe(contract)
        except Exception:
            msg = f"error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"添加K线记录成功：{vt_symbol}")

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)

    def update_redis(self, tick: TickData):
        """更新行情数据到Redis"""
        data = {
            "price": tick.last_price,
            "limit_up": tick.limit_up,
            "limit_down": tick.limit_down,
            "volume": tick.volume,
            "open_interest": tick.open_interest,
            "datetime": tick.datetime.strftime('%Y%m%d%H%M%S.%f')
        }
        vt_symbol = f"{tick.symbol}.{tick.exchange.value}"
        self.r.hmset(vt_symbol, data)

    def update_tick(self, tick: TickData):
        if not tick.last_price:
            return
        
        self.update_redis(tick)
        
        # reach time, send all bars
        last_dt = self.last_dts.get(tick.vt_symbol, None)
        
        if not last_dt or last_dt and last_dt.minute != tick.datetime.minute:
            bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
            if bar:
                bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                bar.datetime = bar.datetime.replace(tzinfo=DB_TZ)
                self.record_bar(bar)
                self.bars[tick.vt_symbol] = None
                
        bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
        if not bar:
            bar = BarData(
                symbol=tick.symbol,
                exchange=tick.exchange,
                interval=Interval.MINUTE,
                datetime=tick.datetime,
                gateway_name=tick.gateway_name,
                open_price=tick.last_price,
                high_price=tick.last_price,
                low_price=tick.last_price,
                close_price=tick.last_price,
                open_interest=tick.open_interest
            )
            self.bars[bar.vt_symbol] = bar
        else:
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.open_interest = tick.open_interest
            bar.datetime = tick.datetime

        last_tick: Optional[TickData] = self.last_ticks.get(tick.vt_symbol, None)
        if last_tick:
            bar.volume += max(tick.volume - last_tick.volume, 0)
            bar.turnover += max(tick.turnover - last_tick.turnover, 0)

        self.last_ticks[tick.vt_symbol] = tick
        self.last_dts[tick.vt_symbol] = tick.datetime
            
    def init_closing_times(self, vt_symbol: str) -> None:
        """初始化合约的收盘时间"""
        symbol_pre, _, _ = extract_symbol_pre(vt_symbol)
        closing_minutes = get_closing_minutes(symbol_pre)
        
        if closing_minutes:
            self.closing_times[vt_symbol] = set(closing_minutes)
            self.write_log(f"初始化{vt_symbol}的收盘时间: {[t.strftime('%H:%M') for t in closing_minutes]}")

    def process_timer_event(self, event: Event):
        """"""
        self.timer_count += 1
        if self.timer_count < self.timer_interval:
            return
        
        current = datetime.now()
        current_time = current.time()
            
        # 检查是否有需要收盘合bar的合约
        for vt_symbol in self.bars:
            bar: Optional[BarData] = self.bars.get(vt_symbol, None)
            if not bar:
                continue

            # 如果合约未初始化收盘时间则初始化
            if vt_symbol not in self.closing_times:
                self.init_closing_times(vt_symbol)
                continue
                
            # 检查每个收盘时间点
            for closing_time in self.closing_times[vt_symbol]:
                # 检查是否到收盘时间
                if (current_time.hour == closing_time.hour and 
                    current_time.minute == closing_time.minute and 
                    current.second >= 10):
                    
                    if bar:  # 如果存在未处理的bar才进行处理
                        bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                        bar.datetime = bar.datetime.replace(tzinfo=DB_TZ)
                        self.record_bar(bar)
                        self.bars[vt_symbol] = None
                        self.write_log(f"收盘时间{closing_time.strftime('%H:%M')}强制合成bar: {vt_symbol}")
        
        tick_time_str = self.tick_time_info()
        self.write_log(tick_time_str)
        self.timer_count = 0
        
    def process_tick_event(self, event: Event):
        """"""
        # update cache
        tick = event.data
        time_str = tick.datetime.strftime('%Y%m%d%H%M%S')
        self.tick_time[tick.symbol] = time_str
        # self.write_log(f"@@@redis_engine: process_tick_event: {tick}")
        self.update_tick(tick)
        
    def tick_time_info(self):
        ret = "{"
        for k,v in self.tick_time.items():
            info = f"{k} {v}\n"
            ret = ret + info
        ret = ret+"}"
        return ret

    def process_contract_event(self, event: Event):
        """"""
        contract = event.data
        vt_symbol = contract.vt_symbol

        if vt_symbol in self.bar_recordings:
            self.subscribe(contract)

    def write_log(self, msg: str):
        """"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}] {msg}"
        self.main_engine.write_log(formatted_msg)

    def put_event(self):
        """"""
        pass

    # 处理bar,供其它应用处理
    def record_bar(self, bar: BarData):
        """"""
        try:
            time_str = datetime.now().strftime("%Y-%m-%d-%H%M%S")
            self.write_log(f" ======1======record bar memory: {time_str}: {bar.vt_symbol} {bar.datetime} "
                          f"o:{bar.open_price} h:{bar.high_price} l:{bar.low_price} c:{bar.close_price}")
            self.write_log(f" ======2========put to event_engine: {bar}")
            event = Event(EVENT_BAR, bar)
            event2 = Event(EVENT_BAR_RECORD, bar)
            self.event_engine.put(event)
            self.event_engine.put(event2)
        except Exception:
            msg = f"error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)

    def subscribe(self, contract: ContractData):
        """"""
        req = SubscribeRequest(
            symbol=contract.symbol,
            exchange=contract.exchange
        )
        self.write_log(f"send subscribe req {contract.symbol}")
        self.main_engine.subscribe(req, contract.gateway_name)
