{"cells": [{"cell_type": "code", "execution_count": 14, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-08-29T01:26:00.572142200Z", "start_time": "2023-08-29T01:25:59.246145700Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_his的shape：(7800, 7)\n", "df_real的shape：(7787, 7)\n"]}], "source": ["import pandas as pd\n", "# 读取2023-08-29_diff.xlsx文件的df_his和df_real两个sheet，以symbol、exchange、datetime为索引\n", "df_his = pd.read_excel('2023-08-29_diff.xlsx', sheet_name='df_his', index_col=[0, 1, 2])\n", "df_real = pd.read_excel('2023-08-29_diff.xlsx', sheet_name='df_real', index_col=[0, 1, 2])\n", "print(f'df_his的shape：{df_his.shape}')\n", "print(f'df_real的shape：{df_real.shape}')"]}, {"cell_type": "code", "execution_count": 13, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_real中有，df_his中没有的数据的个数：0\n", "df_his中有，df_real中没有的数据的个数：13\n"]}, {"data": {"text/plain": "                                           volume  turnover  open_interest  \\\ndatetime            exchange symbol                                          \n2023-08-28 21:30:00 SMART    NU-USD-STK       102         0              0   \n                             NIO-USD-STK     -731         0              0   \n                             PBR-USD-STK      -93         0              0   \n                             VALE-USD-STK     133         0              0   \n                             BABA-USD-STK     383         0              0   \n...                                           ...       ...            ...   \n2023-08-29 03:59:00 SMART    UBER-USD-STK    9166         0              0   \n                             AMC-USD-STK     2153         0              0   \n                             NIO-USD-STK     -989         0              0   \n                             SNAP-USD-STK     523         0              0   \n                             WFC-USD-STK     1081         0              0   \n\n                                           open_price  high_price  low_price  \\\ndatetime            exchange symbol                                            \n2023-08-28 21:30:00 SMART    NU-USD-STK          0.02       -0.01       0.01   \n                             NIO-USD-STK         0.00       -0.01       0.00   \n                             PBR-USD-STK         0.01        0.00       0.01   \n                             VALE-USD-STK       -0.01        0.00       0.00   \n                             BABA-USD-STK        0.00       -0.01       0.05   \n...                                               ...         ...        ...   \n2023-08-29 03:59:00 SMART    UBER-USD-STK       -0.01        0.00       0.00   \n                             AMC-USD-STK        -0.02        0.00       0.00   \n                             NIO-USD-STK         0.00        0.00       0.00   \n                             SNAP-USD-STK        0.00        0.00       0.00   \n                             WFC-USD-STK         0.01        0.00       0.00   \n\n                                           close_price  \ndatetime            exchange symbol                     \n2023-08-28 21:30:00 SMART    NU-USD-STK           0.00  \n                             NIO-USD-STK          0.01  \n                             PBR-USD-STK          0.00  \n                             VALE-USD-STK         0.00  \n                             BABA-USD-STK         0.01  \n...                                                ...  \n2023-08-29 03:59:00 SMART    UBER-USD-STK         0.00  \n                             AMC-USD-STK          0.00  \n                             NIO-USD-STK          0.00  \n                             SNAP-USD-STK        -0.01  \n                             WFC-USD-STK          0.00  \n\n[7787 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th></th>\n      <th>volume</th>\n      <th>turnover</th>\n      <th>open_interest</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n    </tr>\n    <tr>\n      <th>datetime</th>\n      <th>exchange</th>\n      <th>symbol</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">2023-08-28 21:30:00</th>\n      <th rowspan=\"5\" valign=\"top\">SMART</th>\n      <th>NU-USD-STK</th>\n      <td>102</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.02</td>\n      <td>-0.01</td>\n      <td>0.01</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>NIO-USD-STK</th>\n      <td>-731</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.00</td>\n      <td>-0.01</td>\n      <td>0.00</td>\n      <td>0.01</td>\n    </tr>\n    <tr>\n      <th>PBR-USD-STK</th>\n      <td>-93</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.01</td>\n      <td>0.00</td>\n      <td>0.01</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>VALE-USD-STK</th>\n      <td>133</td>\n      <td>0</td>\n      <td>0</td>\n      <td>-0.01</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>BABA-USD-STK</th>\n      <td>383</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.00</td>\n      <td>-0.01</td>\n      <td>0.05</td>\n      <td>0.01</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">2023-08-29 03:59:00</th>\n      <th rowspan=\"5\" valign=\"top\">SMART</th>\n      <th>UBER-USD-STK</th>\n      <td>9166</td>\n      <td>0</td>\n      <td>0</td>\n      <td>-0.01</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>AMC-USD-STK</th>\n      <td>2153</td>\n      <td>0</td>\n      <td>0</td>\n      <td>-0.02</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>NIO-USD-STK</th>\n      <td>-989</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n    </tr>\n    <tr>\n      <th>SNAP-USD-STK</th>\n      <td>523</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>-0.01</td>\n    </tr>\n    <tr>\n      <th>WFC-USD-STK</th>\n      <td>1081</td>\n      <td>0</td>\n      <td>0</td>\n      <td>0.01</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n    </tr>\n  </tbody>\n</table>\n<p>7787 rows × 7 columns</p>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2023-08-29_diff2.xlsx文件已经保存到当前目录下。\n"]}], "source": ["from datetime import datetime\n", "# 比较df_his和df_real的数据，输出：df_real中有，df_his中没有的数据、df_his中有，df_real中没有的数据；df_real中df_his中都有的数据则作差，输出不为0的差值\n", "df_real_diff_df_his = df_real[~df_real.index.isin(df_his.index)]\n", "# 打印df_real中有，df_his中没有的数据的个数\n", "print(f'df_real中有，df_his中没有的数据的个数：{len(df_real_diff_df_his)}')\n", "\n", "df_his_diff_df_real = df_his[~df_his.index.isin(df_real.index)]\n", "# 打印df_his中有，df_real中没有的数据的个数\n", "print(f'df_his中有，df_real中没有的数据的个数：{len(df_his_diff_df_real)}')\n", "\n", "# df_real中df_his中都有的数据的索引\n", "df_real_diff_same_index = df_real.index.intersection(df_his.index)\n", "# 不为0的差值\n", "df_real_diff_same = df_real.loc[df_real_diff_same_index] - df_his.loc[df_real_diff_same_index]\n", "# 以datetime、exchange、symbol为三层索引    \n", "df_real_diff_same = df_real_diff_same.reset_index().set_index(['datetime', 'exchange', 'symbol'])\n", "# 打印不为0的差值\n", "display(df_real_diff_same)\n", "\n", "today = datetime.now().date()\n", "# 保存三个结果到同一个excel文件的不同sheet中，注意为空的情况\n", "with pd.ExcelWriter(f'{today}_diff2.xlsx') as writer:\n", "    # 如果df_real不为空，则保存到excel中\n", "    if len(df_real) > 0:\n", "        df_real.to_excel(writer, sheet_name='df_real')\n", "    # 如果df_his不为空，则保存到excel中\n", "    if len(df_his) > 0:\n", "        df_his.to_excel(writer, sheet_name='df_his')\n", "    # 如果df_his_diff_df_real不为空，则保存到excel中\n", "    if len(df_his_diff_df_real) > 0:\n", "        df_his_diff_df_real.to_excel(writer, sheet_name='df_his_diff_df_real')\n", "    # 如果df_real_diff_df_his不为空，则保存到excel中\n", "    if len(df_real_diff_df_his) > 0:\n", "        df_real_diff_df_his.to_excel(writer, sheet_name='df_real_diff_df_his')\n", "    # 如果df_real_diff_same不为空，则保存到excel中\n", "    if len(df_real_diff_same) > 0:\n", "        df_real_diff_same.to_excel(writer, sheet_name='df_real_diff_same')\n", "print(f'{today}_diff2.xlsx文件已经保存到当前目录下。')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-29T01:24:12.582727200Z", "start_time": "2023-08-29T01:24:10.045722300Z"}}, "id": "f044ae8981e6d78c"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}