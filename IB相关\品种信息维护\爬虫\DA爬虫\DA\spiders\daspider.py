import scrapy
from DA.items import DaItem

class DaspiderSpider(scrapy.Spider):
    name = 'daspider'
    start_urls = ['https://www.directaccess.com.hk/callcenter/futures_contract.html?&lang=GD']

    # To store in CSV format
    custom_settings = {
        'FEEDS': {'data.csv': {'format': 'csv', 'encoding': 'utf-8-sig', 'overwrite': True}}# overwrite=True覆盖原文件
        # 'data/book_data.json': {'format': 'json', 'overwrite': True},
        # 'data/book_data.jsonl': {'format': 'jsonlines', 'overwrite': True},
        # 'data/book_data.xml': {'format': 'xml', 'overwrite': True}
    }

    def parse(self, response):
        # # 使用css选择器提取数据
        # div_list = response.css('div.shoufeineirong')
        # for div in div_list:
        #     item = DaItem()
        #     # 提取头部 (金属及贵金属期货 Metal Futures)
        #     item['类型'] = div.css('h2::text').get()
        #     print(item['类型'])
        #     # 提取表格
        #     table = div.css('div.shoufeibiaoge table tbody tr')
        #     for tr in table:
        #         # 提取表格数据
        #         item['交易所'] = tr.css('td:nth-child(1)::text').get()
        #         item['合约名称'] = tr.css('td:nth-child(2)::text').get()
        #         # 如果合约名称为空，则跳过
        #         if item['合约名称'] is None:
        #             continue
        #         item['交易所代码'] = tr.css('td:nth-child(3)::text').get()
        #         item['DA代码'] = tr.css('td:nth-child(4)::text').get()
        #         item['香港交易'] = tr.css('td:nth-child(5)::text').get()
        #         item['最低波幅'] = tr.css('td:nth-child(6)::text').get()
        #         item['合约大小'] = tr.css('td:nth-child(7)::text').get()
        #         item['合约月份'] = tr.css('td:nth-child(8)::text').get()
        #         item['涨跌停'] = tr.css('td:nth-child(9)::text').get()
        #         item['首次通知日'] = tr.css('td:nth-child(10)::text').get()
        #         item['最后交易日'] = tr.css('td:nth-child(11)::text').get()
        #         item['交割方式'] = tr.css('td:nth-child(12)::text').get()
        #         yield item

        # 使用XPath提取数据
        div_list = response.xpath('//div[contains(@class, "shoufeineirong")]')
        for div in div_list:
            item = DaItem()
            # 提取头部 (金属及贵金属期货 Metal Futures)
            item['类型'] = div.xpath('h2/text()').get()
            print(item['类型'])
            # 提取表格
            table = div.xpath('div[@class="shoufeibiaoge"]//table/tbody/tr')
            for tr in table:

                # 提取表格数据
                item['交易所'] = tr.xpath('td[1]/text()').get()
                item['合约名称'] = tr.xpath('td[2]/text()').get()
                # 如果合约名称为空，则跳过
                if item['合约名称'] is None:
                    continue
                item['交易所代码'] = tr.xpath('td[3]/text()').get()
                item['DA代码'] = tr.xpath('td[4]/text()').get()
                # item['香港交易'] = tr.xpath('td[5]/text()').get()
                # '香港交易' 可能有多个text
                item['香港交易'] = '\n'.join([x.strip() for x in tr.xpath('td[5]//text()').getall() if x.strip() != ''])

                item['最低波幅'] = tr.xpath('td[6]/text()').get()
                item['合约大小'] = tr.xpath('td[7]/text()').get()
                item['合约月份'] = tr.xpath('td[8]/text()').get()
                item['涨跌停'] = tr.xpath('td[9]/text()').get()
                item['首次通知日'] = tr.xpath('td[10]/text()').get()
                item['最后交易日'] = tr.xpath('td[11]/text()').get()
                item['交割方式'] = tr.xpath('td[12]/text()').get()
                yield item

        # 总结
        # CSS 替换为 XPath
        # element::text 替换为 element/text()
        # element::attr(attribute) 替换为 element/@attribute
        # element1 element2 替换为 element1/element2
        # element:nth-child(n) 替换为 element[n]
        # element:first-child 替换为 element[1]
        # element:last-child 替换为 element[last()]