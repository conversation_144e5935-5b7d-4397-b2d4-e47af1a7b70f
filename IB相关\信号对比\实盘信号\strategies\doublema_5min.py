from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
import time
import os, math


class DoubleMa5Strategy(CtaTemplate):
    author = "用Python的交易员"
    strategy_name = "DoubleMa5Strategy"

    fast_window = 10
    slow_window = 20

    fast_ma0 = 0.0
    fast_ma1 = 0.0

    slow_ma0 = 0.0
    slow_ma1 = 0.0

    parameters = ["fast_window", "slow_window"]
    variables = ["fast_ma0", "fast_ma1", "slow_ma0", "slow_ma1"]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager()
        self.bg_xmin = BarGenerator(self.on_bar, 5, self.on_xmin_bar)
        self.am_xmin = ArrayManager()

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_bar(1, use_database=True)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")
        self.put_event()

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

        self.put_event()

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        self.bg.update_tick(tick)

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.cancel_all()
        self.bg_xmin.update_bar(bar)
        am = self.am
        am.update_bar(bar)
        if not am.inited:
            return
        self.put_event()

    def process_price(self, price, direct, tick_size=0.01):
        tick_size = max(tick_size, 0.01)
        if direct == 1:
            a = math.ceil(price/tick_size) * tick_size
        else:
            a = math.floor(price/tick_size) * tick_size
        return a

    def on_xmin_bar(self, bar):
        self.cancel_all()
        # 更新数据
        am_xmin = self.am_xmin
        am_xmin.update_bar(bar)

        fast_ma = am_xmin.sma(self.fast_window, array=True)
        self.fast_ma0 = fast_ma[-1]
        self.fast_ma1 = fast_ma[-2]

        slow_ma = am_xmin.sma(self.slow_window, array=True)
        self.slow_ma0 = slow_ma[-1]
        self.slow_ma1 = slow_ma[-2]

        cross_over = self.fast_ma0 > self.slow_ma0 and self.fast_ma1 < self.slow_ma1
        cross_below = self.fast_ma0 < self.slow_ma0 and self.fast_ma1 > self.slow_ma1
        self.write_log(f"signalbar record!! strategy_name: {self.strategy_name}, contract: {self.vt_symbol}, time: {bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, close: {bar.close_price}, fast_ma: {fast_ma[-1]}, slow_ma: {slow_ma[-1]}, cross_over: {cross_over}, cross_below: {cross_below}")


        if cross_over:
            if self.pos == 0:
                self.buy(self.process_price(bar.close_price,1), 10)
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: buy, offset: open, price:{bar.close_price}, volume: 1")
            elif self.pos < 0:
                self.cover(self.process_price(bar.close_price,1), 10)
                self.buy(self.process_price(bar.close_price,1), 10)
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: buy, offset: flatten, price:{bar.close_price}, volume: 1")
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: buy, offset: open, price:{bar.close_price}, volume: 1")

        elif cross_below:
            if self.pos == 0:
                self.short(self.process_price(bar.close_price,-1), 10)
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: sell, offset: open, price:{bar.close_price}, volume: 1")
            elif self.pos > 0:
                self.sell(self.process_price(bar.close_price,-1), 10)
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: sell, offset: flatten, price:{bar.close_price}, volume: 1")
                self.short(self.process_price(bar.close_price,-1), 10)
                self.write_log(f"signal record!! strategy_name: {self.strategy_name}, contract:{self.vt_symbol}, time:{bar.datetime.strftime('%Y-%m-%d %H:%M:%S')}, direction: sell, offset: open, price:{bar.close_price}, volume: 1")

        self.put_event()

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        # 打印order的id
        self.write_log(f'order record!! contract: {self.vt_symbol}, order_id: {order.orderid}, direction: {order.direction}, offset: {order.offset}, price: {order.price}, volume: {order.volume}, status: {order.status}, traded: {order.traded}')

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        # 打印trade的信息
        self.write_log(f'trade record!! contract: {self.vt_symbol}, trade_id: {trade.tradeid}, direction: {trade.direction}, offset: {trade.offset}, price: {trade.price}, volume: {trade.volume}')

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
