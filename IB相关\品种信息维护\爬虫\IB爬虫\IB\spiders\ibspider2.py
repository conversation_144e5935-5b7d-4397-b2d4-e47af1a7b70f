import scrapy
from IB.items import IbItem
from urllib.parse import urljoin
from copy import copy

class I<PERSON>pider2Spider(scrapy.Spider):
    name = "ibspider2"
    allowed_domains = ["www.interactivebrokers.com.hk"]
    start_urls = ["https://www.interactivebrokers.com.hk/cn/index.php?f=5430"]

    # To store in CSV format
    # custom_settings = {
    #     'FEEDS': {'data.csv': {'format': 'csv', 'encoding': 'utf-8-sig', 'overwrite': True}}# overwrite=True覆盖原文件
    #     # 'data/book_data.json': {'format': 'json', 'overwrite': True},
    #     # 'data/book_data.jsonl': {'format': 'jsonlines', 'overwrite': True},
    #     # 'data/book_data.xml': {'format': 'xml', 'overwrite': True}
    # }

    def parse(self, response):
        # 遍历//*[@id="toptabs"]/ul/li/a的href的产品，返回各个市场的界面，给parse_market处理
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=stk
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=fut
        for href in response.xpath('//*[@id="toptabs"]/ul/li/a/@href'):
            url = urljoin(response.url, href.get())
            # 如果url含有fut
            # if 'fut' in url:
            # if 'stk' in url:
            yield scrapy.http.Request(url=url, callback=self.parse_market)

    def parse_market(self, response):
        print(f'parse_market: url = {response.url}')
        # 再遍历//div[2]/ul/li/a/span的href的市场，返回各个大洲/地域的界面，给parse_area处理
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=stk
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=europe_stk
        for href in response.xpath('//section[2]/div/div[2]/ul/li/a/@href'):
            base_url = 'https://' + self.allowed_domains[0]
            url = urljoin(base_url, href.get())
            yield scrapy.http.Request(url=url, callback=self.parse_area, dont_filter=True)

    def parse_area(self, response):
        print(f'parse_area: url = {response.url}')
        meta_item = {}
        # //div/h2 为 “期权 - 北美”，分割取“期权”
        meta_item['类型'] = response.xpath('//*[@id="product-listings"]/div/div/div/h2/text()').get().split(' - ')[0]
        print(f'parse_market: item[类型] = {meta_item["类型"]}')
        meta_item['区域'] = response.xpath('//*[@id="product-listings"]/div/div/div/h2/text()').get().split(' - ')[1]
        print(f'parse_market: item[区域] = {meta_item["区域"]}')
        # 再遍历//div[1]/table的table
        exchange_table = response.xpath('//div[1]/table')
        for tr in exchange_table.xpath('./tbody/tr'):
            item = copy(meta_item)
            # 检查当前行中的td数量
            td_count = len(tr.xpath('./td'))
            # 如果td数量大于等于4，说明这一行包含足够的信息
            # 再遍历//table/tbody/tr/td[1]/img的src的国家地区，td_count>=4的行才有国家地区
            if td_count == 4:
                国家地区 = tr.xpath('.//td[1]/img/@src').get()# src="/images/2015/template/flags/USA.png"
                item['国家地区'] = 国家地区.split('/')[-1].split('.')[0]
                # print(f'parse_area 4: item[国家地区] = {item["国家地区"]}')
                item['交易所'] = tr.xpath('.//td[2]/a/text()').get()
                # print(f'parse_area 4: item[交易所] = {item["交易所"]}')
                item['产品'] = tr.xpath('.//td[3]/text()').get().strip()
                # print(f'parse_area 4: item[产品] = {item["产品"]}')
                # item['时间'] = '\n'.join(tr.xpath('.//td[4]/text()').getall())
                # print(f'parse_area 4: item[时间] = {item["时间"]}')
                item['时间'] = '\n'.join([x.strip() for x in tr.xpath('.//td[4]//text()').getall() if x.strip() != ''])
                # 如果有子元素li，则提取子元素li的text，以\n分隔添加到item['时间']中
                for li in tr.xpath('.//td[4]/ul/li'):
                    item['时间'] += '\n' + li.xpath('./text()').get().strip()
                # 再遍历//table/tbody/tr/td/a的href的交易所，返回parse_page处理
                for href in tr.xpath('.//td/a/@href'):
                    url = urljoin(response.url, href.get())
                    yield scrapy.http.Request(url=url, callback=self.parse_page, meta={'item': item})
            # 如果td数量等于3，说明这一行不包含国家地区，获取上方最近的tr的国家地区
            elif td_count == 3:
                # 获取上方最近的有4个td的tr的国家地区
                国家地区 = tr.xpath('./preceding-sibling::tr[td[4]][last()]/td[1]/img/@src').get()
                item['国家地区'] = 国家地区.split('/')[-1].split('.')[0]
                # print(f'parse_area 3: item[国家地区] = {item["国家地区"]}')
                item['交易所'] = tr.xpath('.//td[1]/a/text()').get()
                # print(f'parse_area 3: item[交易所] = {item["交易所"]}')
                item['产品'] = tr.xpath('.//td[2]/text()').get().strip()
                # print(f'parse_area 3: item[产品] = {item["产品"]}')
                # item['时间'] = '\n'.join(tr.xpath('.//td[3]/text()').getall())
                # print(f'parse_area 3: item[时间] = {item["时间"]}')
                item['时间'] = '\n'.join([x.strip() for x in tr.xpath('.//td[3]//text()').getall() if x.strip() != ''])
                # 如果有子元素li，则提取子元素li的text，以\n分隔添加到item['时间']中
                for li in tr.xpath('.//td[3]/ul/li'):
                    item['时间'] += '\n' + li.xpath('./text()').get().strip()

                # 再遍历//table/tbody/tr/td/a的href的交易所，返回parse_page处理
                for href in tr.xpath('.//td/a/@href'):
                    url = urljoin(response.url, href.get())
                    yield scrapy.http.Request(url=url, callback=self.parse_page, meta={'item': item})

    def parse_page(self, response):
        print(f'parse_page: url = {response.url}')
        item = response.meta['item']
        # response.url为https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=nyse&showcategories=STK
        # 遍历各页的链接，返回parse_detail处理
        # https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=nyse&showcategories=STK&p=&ptab=&cc=&limit=100&page=107
        # 最小页数为1，最大页数为//div/div/div/div/div/ul/li[last()-1]/a/text()
        max_page = response.xpath('//*[@id="exchange-products"]//li[last()-1]/a/text()').get()
        if max_page:
            url_list = [response.url + f'&p=&ptab=&cc=&limit=100&page={i}' for i in range(1, int(max_page)+1)]
        else:
            url_list = [response.url]
        for url in url_list:
            yield scrapy.http.Request(url=url, callback=self.parse_detail, meta={'item': item}, dont_filter=True)

    def parse_detail(self, response):
        print(f'parse_detail: url = {response.url}')
        item_dict = response.meta['item']

        table = response.xpath('//*[@id="exchange-products"]//table/tbody/tr')
        for tr in table:
            item_dict = copy(item_dict)
            item = IbItem()
            item['类型'] = item_dict['类型']
            item['区域'] = item_dict['区域']
            item['国家地区'] = item_dict['国家地区']
            item['交易所'] = item_dict['交易所']
            item['产品'] = item_dict['产品']
            item['时间'] = item_dict['时间']

            # # Exchange - National Stock Exchange of India (NSE)
            # 交易所 = response.xpath('//*[@id="exchange-info"]/div/div/div/h2/text()').get()
            # item['交易所'] = 交易所.replace('Exchange - ', '')
            # item['时间'] = response.xpath('//*[@id="exchange-info"]//table/tbody/tr/td[1]/text()').get()

            item['IB_Symbol'] = tr.xpath('td[1]/text()').get()
            # print(f'parse_detail: item[IB_Symbol] = {item["IB_Symbol"]}')
            item['Product_Description'] = tr.xpath('td[2]/a/text()').get()
            # "javascript:NewWindow('https://contract.ibkr.info/index.php?action=Details&site=GEN&conid=644107700 "
            #          "','Details','600','600','custom','front');"
            链接地址 = tr.xpath('td[2]/a/@href').get()
            # 提取链接地址中的url
            链接地址 = 链接地址.split("'")[1] if 链接地址 else None
            item['链接地址'] = 链接地址
            item['Symbol'] = tr.xpath('td[3]/text()').get()
            item['Currency'] = tr.xpath('td[4]/text()').get()
            yield item