from vnpy_ctastrategy.backtesting import BacktestingEngine
from datetime import datetime
import pandas as pd
import os

from doublema_5min import DoubleMa5Strategy

def process_trades(trades):
    data = []
    for trades_num in trades.keys():
        data.append([trades[trades_num].symbol, trades[trades_num].datetime, trades[trades_num].direction, trades[trades_num].offset, trades[trades_num].price, trades[trades_num].volume])
    df = pd.DataFrame(data, columns = ['symbol', 'datetime','direction', 'offset','price','volume'])
    return df

def save_trades(trades, slippage, size):
    trades_df = process_trades(trades)
    trades_df['datetime'] = pd.to_datetime(trades_df['datetime'])+pd.Timedelta('59T')
    trades_df['datetime'] = trades_df['datetime'].dt.tz_localize(None)
    trades_df['enter_time'] = trades_df['datetime']
    trades_df['exit_time'] = trades_df['datetime'].shift(-1)
    trades_df['enter_price'] = trades_df['price']
    trades_df['exit_price'] = trades_df['price'].shift(-1)
    trades_df['direction'] = (trades_df['direction'].astype(str)=='Direction.LONG')*1 + (trades_df['direction'].astype(str)=='Direction.SHORT')*-1
    trades_df['pnl'] = ((trades_df['exit_price']-trades_df['enter_price'])*trades_df['direction']*size - slippage)*trades_df.volume
    td_df = trades_df[trades_df.offset.astype(str)=='Offset.OPEN']
    return td_df[['symbol','enter_time','exit_time','direction','volume','enter_price','exit_price','pnl']]

def run(vt_symbol):
    print(vt_symbol)
    slippage = 0
    size = 100
    engine = BacktestingEngine()
    engine.set_parameters(
        vt_symbol=vt_symbol,
        interval=data_interval,
        start=datetime(2023, 8, 24),
        # end=datetime(2023, 9, 19),
        end=datetime.now().date(),
        rate=0.3/10000,
        slippage=1,
        size=100,
        pricetick=0.01,
        capital=1_000_000,
    )
    engine.add_strategy(DoubleMa5Strategy, {})

    engine.load_data()
    engine.run_backtesting()
    # print(engine.trades)
    df = engine.calculate_result()
    stats = engine.calculate_statistics()


if __name__ == '__main__':
    data_interval = '1m'
    strategy = 'DoubleMa5Strategy'
    # vt_symbols = ['AAPL-USD-STK.SMART', 'TSLA-USD-STK.SMART']
    vt_symbols = ['ES-20230915-USD-FUT.CME', 'NQ-20230915-USD-FUT.CME']

    for vt_symbol in vt_symbols:
        run(vt_symbol)

