#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os
from datetime import datetime
from typing import Dict, Optional, List, Tuple
import traceback
from loguru import logger
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据库相关模块
from utils.database_manager import db_manager, IbProduct, FutuRehab, FirstrateRehab
from utils.mysql_database import create_mysql_database
from vnpy.trader.utility import load_json
from vnpy.trader.constant import Exchange, Interval

# 配置日志
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
logger.add(f"logs/gap_checker_optimized.{timestamp}.log", 
          format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
          level="INFO",
          rotation="1 MB")

class GapCheckerOptimized:
    """优化版跳开检查器 - 分批处理避免内存错误"""
    
    def __init__(self):
        """初始化"""
        self.conid_symbol_map = {}  # conid到symbol的映射
        self.us_db_manager = None
        self.DbBarData = None
        self.DbBarOverview = None
        self.us_stock_settings = load_json("vt_setting.json")
        self._init_databases()
        self._load_symbol_mappings()
    
    def _init_databases(self):
        """初始化数据库连接"""
        try:
            # 初始化US股票数据库
            self.us_db_manager, self.DbBarData, _, self.DbBarOverview, _ = create_mysql_database(self.us_stock_settings)
            logger.info("US股票数据库初始化成功")
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {str(e)}\n{traceback.format_exc()}")
            self.us_db_manager = None
            self.DbBarData = None
            self.DbBarOverview = None
    
    def _load_symbol_mappings(self):
        """加载conid到symbol的映射关系"""
        try:
            query = IbProduct.select(IbProduct.symbol, IbProduct.conid).where(
                IbProduct.is_latest == True
            ).order_by(IbProduct.created_time.asc())
            
            for record in query:
                if record.symbol and record.conid:
                    self.conid_symbol_map[str(record.conid)] = record.symbol
            
            logger.info(f"已加载{len(self.conid_symbol_map)}个conid到symbol的映射关系")
            
        except Exception as e:
            logger.error(f"加载symbol映射失败: {str(e)}\n{traceback.format_exc()}")
            self.conid_symbol_map = {}
    
    def _get_symbol(self, conid: str) -> Optional[str]:
        """获取conid对应的symbol"""
        return self.conid_symbol_map.get(str(conid))
    
    def _get_rehab_info(self, symbol: str, ex_div_date: datetime) -> Dict:
        """
        从FutuRehab和FirstrateRehab表中获取分红拆股信息
        
        Args:
            symbol: 股票symbol (如AAPL)
            ex_div_date: 除权日期
            
        Returns:
            包含分红拆股信息的字典，包括if_rehab字段标识数据来源
        """
        try:
            # 首先尝试FutuRehab表
            futu_code = f"US.{symbol}"
            futu_record = FutuRehab.get_or_none(
                (FutuRehab.code == futu_code) & 
                (FutuRehab.ex_div_date == ex_div_date.date())
            )
            
            if futu_record:
                return {
                    'split_ratio': futu_record.split_ratio,
                    'per_share_div_ratio': futu_record.per_share_div_ratio,
                    'per_cash_div': futu_record.per_cash_div,
                    'special_div': futu_record.special_dividend,
                    'if_rehab': 'futu'
                }
            
            # 如果FutuRehab没找到，尝试FirstrateRehab表
            fr_record = FirstrateRehab.get_or_none(
                (FirstrateRehab.symbol == symbol) & 
                (FirstrateRehab.ex_div_date == ex_div_date.date())
            )
            
            if fr_record:
                return {
                    'split_ratio': fr_record.split_ratio,
                    'per_share_div_ratio': None,  # FirstrateRehab没有这个字段
                    'per_cash_div': fr_record.dividend,  # 使用dividend字段填充per_cash_div
                    'special_div': None,  # FirstrateRehab没有这个字段
                    'if_rehab': 'fr'
                }
            
            # 两个表都没找到
            return {
                'split_ratio': None,
                'per_share_div_ratio': None,
                'per_cash_div': None,
                'special_div': None,
                'if_rehab': 'no_rehab'
            }
                
        except Exception as e:
            logger.error(f"查询复权信息失败 {symbol} {ex_div_date}: {str(e)}")
            return {
                'split_ratio': None,
                'per_share_div_ratio': None,
                'per_cash_div': None,
                'special_div': None,
                'if_rehab': 'no_rehab'
            }
    
    def calculate_gap_rate(self, current_open: float, prev_close: float) -> Optional[float]:
        """
        计算跳开比率
        
        Args:
            current_open: 当前bar的开盘价
            prev_close: 前一bar的收盘价
            
        Returns:
            跳开比率 (current_open/prev_close - 1)
        """
        if prev_close and current_open and prev_close > 0:
            return (current_open / prev_close) - 1
        return None
    
    def _get_all_conids(self) -> List[str]:
        """从DbBarOverview获取所有日线数据的conid列表"""
        try:
            if not self.DbBarOverview:
                logger.error("DbBarOverview未初始化")
                return []
            
            query = (self.DbBarOverview.select(self.DbBarOverview.symbol)
                    .where(
                        (self.DbBarOverview.exchange == Exchange.SMART.value) &
                        (self.DbBarOverview.interval == Interval.DAILY.value)
                    )
                    .distinct())
            
            conids = [record.symbol for record in query]
            logger.info(f"从DbBarOverview获取到{len(conids)}个conid")
            return conids
            
        except Exception as e:
            logger.error(f"获取conid列表失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def _get_filtered_conids(self) -> set:
        """获取筛选后的conid集合"""
        try:
            # 检查CSV文件是否存在
            csv_file = 'scanner_unique_stk_us_all.csv'
            if not os.path.exists(csv_file):
                logger.warning(f"CSV文件{csv_file}不存在，将处理所有conid")
                return set()  # 返回空集合，表示不进行筛选
            
            # 从IbProduct获取数据并转换为DataFrame
            ib_query = IbProduct.select(IbProduct.symbol, IbProduct.conid, IbProduct.is_latest)
            
            ib_data = []
            for record in ib_query:
                ib_data.append({
                    'symbol': record.symbol,
                    'conid': str(record.conid),  # 转换为字符串
                    'is_latest': record.is_latest
                })
            
            if not ib_data:
                logger.warning("未从IbProduct获取到任何数据")
                return set()
            
            ib_product_df = pd.DataFrame(ib_data)
            
            # 读取CSV文件
            csv_df = pd.read_csv(csv_file)
            csv_df['conId'] = csv_df['conId'].astype(str)
            
            # 找到共同的conid
            common_conids = set(ib_product_df['conid']).intersection(set(csv_df['conId']))
            
            # 获取共同conid对应的symbol
            symbol_list = ib_product_df[ib_product_df['conid'].isin(common_conids)]['symbol'].tolist()
            
            # 合并CSV中的symbol和通过conid找到的symbol
            csv_symbols = csv_df['symbol'].tolist() + symbol_list
            
            # 找到共同的symbol
            common_symbols = set(csv_symbols).intersection(set(ib_product_df['symbol']))
            
            # 获取最终的conid集合
            result_conids = set(ib_product_df[
                (ib_product_df['symbol'].isin(common_symbols)) & 
                (ib_product_df['is_latest'] == True)
            ]['conid'].tolist())
            
            return result_conids
            
        except Exception as e:
            logger.error(f"获取筛选conid失败: {str(e)}\n{traceback.format_exc()}")
            return set()  # 返回空集合，表示不进行筛选
    
    def _process_single_conid(self, conid: str, threshold: float) -> List[Dict]:
        """
        处理单个conid的跳开检查
        
        Args:
            conid: 要处理的conid
            threshold: 跳开阈值
            
        Returns:
            该conid的跳开结果列表
        """
        try:
            # 获取该conid的所有日线数据，按日期排序
            query = (self.DbBarData.select()
                    .where(
                        (self.DbBarData.symbol == conid) &
                        (self.DbBarData.exchange == Exchange.SMART.value) &
                        (self.DbBarData.interval == Interval.DAILY.value)
                    )
                    .order_by(self.DbBarData.datetime))
            
            bars = list(query)
            if len(bars) < 2:
                return []
            
            results = []
            
            # 逐对比较相邻的bar
            for i in range(1, len(bars)):
                prev_bar = bars[i-1]
                current_bar = bars[i]
                
                # 计算跳开比率
                gap_rate = self.calculate_gap_rate(current_bar.open_price, prev_bar.close_price)
                
                if gap_rate is not None and abs(gap_rate) > threshold:
                    # 获取对应的symbol
                    symbol = self._get_symbol(conid)
                    
                    # 获取当前交易日的复权信息
                    rehab_info = {}
                    if symbol:
                        rehab_info = self._get_rehab_info(symbol, current_bar.datetime)
                    else:
                        rehab_info = {
                            'split_ratio': None,
                            'per_share_div_ratio': None,
                            'per_cash_div': None,
                            'special_div': None,
                            'if_rehab': 'no_rehab'
                        }
                    
                    # 获取前一交易日的复权信息
                    prev_rehab_info = {}
                    if symbol:
                        prev_rehab_info = self._get_rehab_info(symbol, prev_bar.datetime)
                    else:
                        prev_rehab_info = {
                            'split_ratio': None,
                            'per_share_div_ratio': None,
                            'per_cash_div': None,
                            'special_div': None,
                            'if_rehab': 'no_rehab'
                        }
                    
                    result = {
                        'symbol': symbol or 'UNKNOWN',
                        'conid': conid,
                        'datetime': current_bar.datetime,
                        'prev_datetime': prev_bar.datetime,
                        'interval': current_bar.interval,
                        'volume': current_bar.volume,
                        'prev_volume': prev_bar.volume,
                        'open_price': current_bar.open_price,
                        'prev_close': prev_bar.close_price,
                        'gap_rate': gap_rate,
                        'abs_gap_rate': abs(gap_rate),
                        'split_ratio': rehab_info['split_ratio'],
                        'per_share_div_ratio': rehab_info['per_share_div_ratio'],
                        'per_cash_div': rehab_info['per_cash_div'],
                        'special_div': rehab_info['special_div'],
                        'if_rehab': rehab_info['if_rehab'],
                        'prev_split_ratio': prev_rehab_info['split_ratio'],
                        'prev_per_share_div_ratio': prev_rehab_info['per_share_div_ratio'],
                        'prev_per_cash_div': prev_rehab_info['per_cash_div'],
                        'prev_special_div': prev_rehab_info['special_div'],
                        'prev_if_rehab': prev_rehab_info['if_rehab']
                    }
                    
                    results.append(result)
                    
                    # 增强日志信息
                    rehab_msg = ""
                    current_rehab_msg = ""
                    prev_rehab_msg = ""
                    
                    if rehab_info['if_rehab'] != 'no_rehab':
                        rehab_values = [f"{k}={v}" for k, v in rehab_info.items() if v is not None and k != 'if_rehab']
                        current_rehab_msg = f"当前({rehab_info['if_rehab']}): {', '.join(rehab_values)}"
                    
                    if prev_rehab_info['if_rehab'] != 'no_rehab':
                        prev_rehab_values = [f"{k}={v}" for k, v in prev_rehab_info.items() if v is not None and k != 'if_rehab']
                        prev_rehab_msg = f"前一日({prev_rehab_info['if_rehab']}): {', '.join(prev_rehab_values)}"
                    
                    if current_rehab_msg or prev_rehab_msg:
                        rehab_parts = [msg for msg in [current_rehab_msg, prev_rehab_msg] if msg]
                        rehab_msg = f" [复权信息: {'; '.join(rehab_parts)}]"
                    
                    logger.info(f"发现大跳开: {symbol or conid} "
                              f"({current_bar.datetime.strftime('%Y-%m-%d')}) "
                              f"跳开: {gap_rate:.2%} "
                              f"({prev_bar.close_price:.2f} -> {current_bar.open_price:.2f})"
                              f"{rehab_msg}")
            
            return results
            
        except Exception as e:
            logger.error(f"处理conid {conid} 失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def find_gaps(self, threshold: float = 0.35) -> List[Dict]:
        """
        查找数据库中所有大于阈值的跳开（优化版本，分批处理）
        
        Args:
            threshold: 跳开阈值，默认35%
            
        Returns:
            包含跳开信息的字典列表
        """
        try:
            if not self.us_db_manager or not self.DbBarData or not self.DbBarOverview:
                logger.error("数据库未初始化")
                return []
            
            logger.info(f"开始查找跳开大于{threshold:.1%}的数据...")
            
            # 获取所有conid
            conids = self._get_all_conids()
            if not conids:
                logger.error("未获取到任何conid")
                return []
            
            all_results = []
            processed_count = 0
            
            # 获取筛选后的conid集合
            result_conids = self._get_filtered_conids()
            conids = [conid for conid in conids if conid in result_conids]
            logger.info(f"开始分批处理{len(conids)}个conid...")
            
            # 分批处理每个conid
            for conid in conids:

                processed_count += 1
                
                if processed_count % 100 == 0:
                    logger.info(f"已处理 {processed_count}/{len(conids)} 个conid...")
                
                # 处理单个conid
                conid_results = self._process_single_conid(conid, threshold)
                all_results.extend(conid_results)
            
            logger.info(f"数据处理完成，共处理{len(conids)}个conid，发现{len(all_results)}个大跳开")
            return all_results
            
        except Exception as e:
            logger.error(f"查找跳开失败: {str(e)}\n{traceback.format_exc()}")
            return []
    
    def export_to_csv(self, results: List[Dict], filename: str = None) -> str:
        """
        将结果导出到CSV文件
        
        Args:
            results: 跳开结果列表
            filename: 输出文件名，如果为None则自动生成
            
        Returns:
            输出文件名
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.us_stock_settings['database.database']}_gap_analysis.csv"
        
        try:
            if not results:
                logger.warning("没有数据需要导出")
                return filename
            
            df = pd.DataFrame(results)
            
            # 选择需要的列并排序
            columns = ['symbol', 'conid', 'datetime', 'prev_datetime', 'interval', 'volume', 'prev_volume',
                      'open_price', 'prev_close', 'gap_rate', 'abs_gap_rate', 
                      'split_ratio', 'per_share_div_ratio', 'per_cash_div', 'special_div', 'if_rehab',
                      'prev_split_ratio', 'prev_per_share_div_ratio', 'prev_per_cash_div', 'prev_special_div', 'prev_if_rehab']
            
            # 先按symbol排序（A到Z），再按datetime排序（小到大）
            df = df[columns].sort_values(['symbol', 'datetime'], ascending=[True, True])
            
            df.to_csv(filename, index=False)
            logger.info(f"结果已导出到: {filename}")
            
            return filename
            
        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}\n{traceback.format_exc()}")
            return filename
    
    def generate_summary_report(self, results: List[Dict], threshold: float):
        """生成汇总报告"""
        if not results:
            logger.warning("没有跳开数据")
            return
        
        logger.info("=" * 60)
        logger.info("跳开分析汇总报告 (优化版)")
        logger.info("=" * 60)
        
        df = pd.DataFrame(results)
        
        logger.info(f"分析阈值: {threshold:.1%}")
        logger.info(f"发现跳开总数: {len(results)}")
        logger.info(f"涉及股票数: {df['symbol'].nunique()}")
        
        # 跳开幅度统计
        logger.info(f"\n跳开幅度统计:")
        logger.info(f"  平均: {df['abs_gap_rate'].mean():.2%}")
        logger.info(f"  中位数: {df['abs_gap_rate'].median():.2%}")
        logger.info(f"  最大: {df['abs_gap_rate'].max():.2%}")
        logger.info(f"  最小: {df['abs_gap_rate'].min():.2%}")
        
        # 前10大跳开
        top_gaps = df.nlargest(10, 'abs_gap_rate')
        logger.info(f"\n前10大跳开:")
        for idx, row in top_gaps.iterrows():
            logger.info(f"  {row['symbol']}: {row['gap_rate']:.2%} "
                      f"({row['datetime'].strftime('%Y-%m-%d')}) "
                      f"({row['prev_close']:.2f} -> {row['open_price']:.2f})")
        
        # 有复权信息的跳开统计
        rehab_data = df[df['if_rehab'] != 'no_rehab']
        if len(rehab_data) > 0:
            logger.info(f"\n有复权信息的跳开: {len(rehab_data)}个 ({len(rehab_data)/len(df):.1%})")
            
            # 按数据源统计
            futu_count = len(rehab_data[rehab_data['if_rehab'] == 'futu'])
            fr_count = len(rehab_data[rehab_data['if_rehab'] == 'fr'])
            
            logger.info(f"  来源FutuRehab: {futu_count}个")
            logger.info(f"  来源FirstrateRehab: {fr_count}个")
            
            # 分类统计
            split_count = len(rehab_data[rehab_data['split_ratio'].notna()])
            div_count = len(rehab_data[rehab_data['per_cash_div'].notna()])
            special_count = len(rehab_data[rehab_data['special_div'].notna()])
            
            logger.info(f"  拆股相关: {split_count}个")
            logger.info(f"  派息相关: {div_count}个") 
            logger.info(f"  特别股息: {special_count}个")
        
        # 按年份统计
        df['year'] = pd.to_datetime(df['datetime']).dt.year
        yearly_stats = df.groupby('year').agg({
            'abs_gap_rate': ['count', 'mean', 'max']
        }).round(4)
        
        logger.info(f"\n按年份统计:")
        for year, stats in yearly_stats.iterrows():
            count = stats[('abs_gap_rate', 'count')]
            mean = stats[('abs_gap_rate', 'mean')]
            max_gap = stats[('abs_gap_rate', 'max')]
            logger.info(f"  {year}: {count}次跳开, 平均{mean:.2%}, 最大{max_gap:.2%}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='美股跳开检查工具（优化版）')
    parser.add_argument('--threshold', '-t', type=float, default=0.35,
                       help='跳开阈值 (默认: 0.35 即 35%%)')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出CSV文件名 (默认: 自动生成)')
    
    args = parser.parse_args()
    
    logger.info(f"开始跳开检查（优化版），阈值: {args.threshold:.1%}")
    
    try:
        # 创建检查器
        checker = GapCheckerOptimized()
        
        # 查找跳开
        results = checker.find_gaps(threshold=args.threshold)
        
        # 导出结果
        output_file = checker.export_to_csv(results, args.output)
        
        # 生成汇总报告
        checker.generate_summary_report(results, args.threshold)
        
        logger.info("跳开检查完成")
        logger.info(f"结果文件: {output_file}")
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}\n{traceback.format_exc()}")
    
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main() 