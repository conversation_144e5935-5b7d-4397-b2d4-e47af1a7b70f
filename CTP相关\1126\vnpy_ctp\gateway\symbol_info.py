import re
from decimal import Decimal
from vnpy.trader.constant import Exchange
from vnpy.trader.utility import extract_vt_symbol
import json
from importlib.resources import files
from datetime import datetime, time


import json
all_sizes = {'a': 10, 'ag': 15, 'al': 5, 'ao': 20, 'AP': 10, 'au': 1000, 'b': 10, 'bb': 500, 'bc': 5, 'br': 5, 'bu': 10,
             'c': 10, 'CF': 5, 'CJ': 5, 'cs': 10, 'cu': 5, 'CY': 5, 'eb': 5, 'ec': 50, 'eg': 10, 'fb': 10, 'FG': 20,
             'fu': 10, 'hc': 10, 'i': 100, 'j': 100, 'jd': 10, 'jm': 60, 'JR': 20, 'l': 5, 'lc': 1, 'lg': 90, 'lh': 16,
             'LR': 20, 'lu': 10, 'm': 10, 'MA': 10, 'ni': 1, 'nr': 10, 'OI': 10, 'p': 10, 'pb': 5, 'PF': 5, 'pg': 20,
             'PK': 5, 'PM': 50, 'pp': 5, 'PR': 15, 'ps': 3, 'PX': 5, 'rb': 10, 'RI': 20, 'RM': 10, 'rr': 10, 'RS': 10,
             'ru': 10, 'SA': 20, 'sc': 1000, 'sctas': 1000, 'SF': 5, 'SH': 30, 'si': 5, 'SM': 5, 'sn': 1, 'sp': 10,
             'SR': 10, 'ss': 5, 'TA': 5, 'UR': 20, 'v': 5, 'WH': 20, 'wr': 10, 'y': 10, 'ZC': 100, 'zn': 5, 'IC': 200,
             'IF': 300, 'IH': 300, 'IM': 200, 'T': 10000, 'TF': 10000, 'TS': 20000, 'TL': 10000}

all_priceticks = {'a': 1.0, 'ag': 1.0, 'al': 5.0, 'ao': 1.0, 'AP': 1.0, 'au': 0.02, 'b': 1.0, 'bb': 0.05, 'bc': 10.0,
                  'br': 5.0, 'bu': 1.0, 'c': 1.0, 'CF': 5.0, 'CJ': 5.0, 'cs': 1.0, 'cu': 10.0, 'CY': 5.0, 'eb': 1.0,
                  'ec': 0.1, 'eg': 1.0, 'fb': 0.5, 'FG': 1.0, 'fu': 1.0, 'hc': 1.0, 'i': 0.5, 'j': 0.5, 'jd': 1.0,
                  'jm': 0.5, 'JR': 1.0, 'l': 1.0, 'lc': 20.0, 'lg': 0.5, 'lh': 5.0, 'LR': 1.0, 'lu': 1.0, 'm': 1.0,
                  'MA': 1.0, 'ni': 10.0, 'nr': 5.0, 'OI': 1.0, 'p': 2.0, 'pb': 5.0, 'PF': 2.0, 'pg': 1.0, 'PK': 2.0,
                  'PM': 1.0, 'pp': 1.0, 'PR': 2.0, 'ps': 5.0, 'PX': 2.0, 'rb': 1.0, 'RI': 1.0, 'RM': 1.0, 'rr': 1.0,
                  'RS': 1.0, 'ru': 5.0, 'SA': 1.0, 'sc': 0.1, 'sctas': 0.1, 'SF': 2.0, 'SH': 1.0, 'si': 5.0, 'SM': 2.0,
                  'sn': 10.0, 'sp': 2.0, 'SR': 1.0, 'ss': 5.0, 'TA': 2.0, 'UR': 1.0, 'v': 1.0, 'WH': 1.0, 'wr': 1.0,
                  'y': 2.0, 'ZC': 0.2, 'zn': 5.0, 'IC': 0.2, 'IF': 0.2, 'IH': 0.2, 'IM': 0.2, 'T': 0.005, 'TF': 0.005,
                  'TS': 0.002, 'TL': 0.01}

all_symbol_pres = {
    'DCE': ['a', 'b', 'bb', 'c', 'cs', 'eb', 'eg', 'fb', 'i', 'j', 'jd', 'jm', 'l', 'lg', 'lh', 'm', 'p', 'pg', 'pp',
            'rr', 'v', 'y'],
    'SHFE': ['ag', 'al', 'ao', 'au', 'br', 'bu', 'cu', 'fu', 'hc', 'ni', 'pb', 'rb', 'ru', 'sn', 'sp', 'ss', 'wr',
             'zn'],
    'CZCE': ['AP', 'CF', 'CJ', 'CY', 'FG', 'JR', 'LR', 'MA', 'OI', 'PF', 'PK', 'PM', 'PR', 'PX', 'RI', 'RM', 'RS', 'SA',
             'SF', 'SH', 'SM', 'SR', 'TA', 'UR', 'WH', 'ZC'], 'INE': ['bc', 'ec', 'lu', 'nr', 'sc', 'sctas'],
    'GFEX': ['lc', 'si', 'ps'], 'CFFEX': ['IC', 'IF', 'IH', 'IM', 'T', 'TF', 'TS', 'TL']}

all_symbols = [symbol for exchange in all_symbol_pres.values() for symbol in exchange]

dbsymbols = ['a', 'ag', 'al', 'AP', 'au', 'b', 'bb', 'bc', 'bu', 'c', 'CF', 'CJ', 'cs', 'cu', 'CY', 'eb', 'eg', 'fb',
             'FG', 'fu', 'hc', 'i', 'IC', 'IF', 'IH', 'IM', 'j', 'jd', 'jm', 'JR', 'l', 'lc', 'lh', 'LR', 'lu', 'm',
             'MA', 'ni', 'nr', 'OI', 'p', 'pb', 'PF', 'pg', 'PK', 'PM', 'pp', 'rb', 'RI', 'RM', 'rr', 'RS', 'ru', 'SA',
             'sc', 'SF', 'si', 'SM', 'sn', 'sp', 'SR', 'ss', 'T', 'TA', 'TF', 'TL', 'TS', 'UR', 'v', 'WH', 'wr', 'y',
             'ZC', 'zn', 'ao', 'br', 'ec', 'PX', 'SH', 'PR', 'lg', 'ps']

trading_hours = {
    0: ['AP', 'CJ', 'JR', 'LR', 'PK', 'PM', 'RI', 'RS', 'SF', 'SM', 'UR', 'WH', 'bb', 'ec', 'fb', 'jd', 'lc', 'lh',
        'si', 'wr', 'lg', 'ps'],  # 0：白盘品种
    1: ['CF', 'CY', 'FG', 'MA', 'OI', 'PF', 'PX', 'RM', 'SA', 'SH', 'SR', 'TA', 'ZC', 'a', 'b', 'br', 'bu', 'c', 'cs',
        'eb', 'eg', 'fu', 'hc', 'i', 'j', 'jm', 'l', 'lu', 'm', 'nr', 'p', 'pg', 'pp', 'rb', 'rr', 'ru', 'sp', 'v',
        'y', 'PR'],  # 1：夜盘到23点品种
    2: ['al', 'ao', 'bc', 'cu', 'ni', 'pb', 'sn', 'sn', 'ss', 'zn'],  # 2：夜盘到凌晨1点品种
    3: ['ag', 'au', 'sc'],  # 3：夜盘到凌晨2点30分品种
    4: ['T', 'TF', 'TL', 'TS'],  # 4：9:30-11:30,13:00-15:15
    5: ['IC', 'IF', 'IH', 'IM'],  # 5：9:30-11:30,13:00-15:00
    6: ['sctas'],  # 6：每周一至周五的开市集合竞价阶段, 21:00 - 02:30（+1）,9:00 - 10:15
}

def extract_symbol_num(symbol):
    for i, char in enumerate(symbol):
        if char.isdigit():
            return symbol[:i], symbol[i:]
    return symbol, ""

# 修改extract_vt_symbol函数，使用正则表达式提取合约代码、合约数字、合约交易所
def extract_symbol_pre(vt_symbol):
    '''
    :param vt_symbol: 'rb2010.SHFE'
    :return: rb，2010，SHFE
    '''
    symbol, exchange = extract_vt_symbol(vt_symbol)
    symbol_pre, symbol_num = extract_symbol_num(symbol)
    return symbol_pre, symbol_num, Exchange(exchange)

# trading_hours反向映射
trading_hours_reverse = {symbol.upper(): k for k, v in trading_hours.items() for symbol in v}

# 大写symbol向vt_symbol写法转换，例如 AG->ag
upper2specific_dict = {symbol.upper(): symbol for k, v in trading_hours.items() for symbol in v}

# 给定symbol（例如AP, rb）, 返回是在交易时间中(True,False)【只根据交易时间和周六日过滤，不考虑假日TDAYS情况】
def is_trading(symbol, symbol_datetime):
    ret = False
    time_str = symbol_datetime.strftime("%H%M%S")
    time_weekday = symbol_datetime.weekday()

    exchange = all_symbol_pres_rev.get(symbol, None)

    # 根据品种过滤非交易时间的tick
    if exchange in ('CFFEX',) and time_weekday in (0, 1, 2, 3, 4):
        # 如果tick时间在9点半到11点半，13点到15点之间
        if "093000" <= time_str < "113000" or "130000" <= time_str < "150000":
            ret = True
        elif "150000" <= time_str < "151500":
            if symbol in trading_hours[4]:
                ret = True
    elif exchange in ('SHFE', 'CZCE', 'DCE', 'INE', 'GFEX'):
        if "090000" <= time_str < "101500" or "103000" <= time_str < "113000" or "133000" <= time_str < "150000":
            if time_weekday in (0, 1, 2, 3, 4):
                if symbol in trading_hours[0] + trading_hours[1] + trading_hours[2] + trading_hours[3]:
                    ret = True
        elif "210000" <= time_str < "230000":
            if time_weekday in (0, 1, 2, 3, 4):
                if symbol in trading_hours[1] + trading_hours[2] + trading_hours[3]:
                    ret = True
        elif "230000" <= time_str <= "235959":
            if time_weekday in (0, 1, 2, 3, 4):
                if symbol in trading_hours[2] + trading_hours[3]:
                    ret = True
        elif "000000" <= time_str < "010000":
            if time_weekday in (1, 2, 3, 4, 5):
                if symbol in trading_hours[2] + trading_hours[3]:
                    ret = True
        elif "010000" <= time_str < "023000":
            if time_weekday in (1, 2, 3, 4, 5):
                if symbol in trading_hours[3]:
                    ret = True
    return ret


tradetime_dict = {
    0:"9:00-10:15,10:30-11:30,13:30-15:00",
    1:"9:00-10:15,10:30-11:30,13:30-15:00,21:00-23:00",
    2:"9:00-10:15,10:30-11:30,13:30-15:00,21:00-next day 01:00",
    3:"9:00-10:15,10:30-11:30,13:30-15:00,21:00-next day 02:30",
    4:"9:30-11:30,13:00-15:15",
    5:"9:30-11:30,13:00-15:00"
}

symbol_exchange_dict = {
'ZN': 'SHFE',
'NI': 'SHFE',
'SN': 'SHFE',
'AU': 'SHFE',
'AG': 'SHFE',
'SC': 'INE',
'LC': 'GFEX',
'SI': 'GFEX',
'EC': 'INE',
'LH': 'DCE',
'J': 'DCE',
'L': 'DCE',
'M': 'DCE','V': 'DCE','PP': 'DCE','P': 'DCE','OI': 'CZCE','MA': 'CZCE','RM': 'CZCE','C': 'DCE',
'SR': 'CZCE','CF': 'CZCE','HC': 'SHFE','I': 'DCE','JM': 'DCE','RB': 'SHFE','JD': 'DCE','FG': 'CZCE','SA': 'CZCE','TA': 'CZCE','Y': 'DCE','CU': 'SHFE','A': 'DCE','CS': 'DCE','FU': 'SHFE','SF': 'CZCE','SM': 'CZCE','BU': 'SHFE','EG': 'DCE','UR': 'CZCE','PF': 'CZCE','SS': 'SHFE','PG': 'DCE','PB': 'SHFE','AP': 'CZCE','EB': 'DCE','PK': 'CZCE','AL': 'SHFE',
'NR': 'INE',
'RU': 'SHFE','CJ': 'CZCE','SP': 'SHFE','B': 'DCE','IF': 'CFFEX','IM': 'CFFEX','IH': 'CFFEX','IC': 'CFFEX'
}

symbol_min_order = {'PM': 10, 'ZC': 4}
contract_min_order = {'UR2401': 4, 'UR401': 4, 'UR2402': 4, 'UR402': 4, 'UR2403': 4, 'UR403': 4, 'UR2404': 4,
                      'UR404': 4, 'UR2405': 4, 'UR405': 4, 'SA2401': 4, 'SA401': 4, 'SA2402': 4, 'SA402': 4,
                      'SA2403': 4, 'SA403': 4, 'SA2404': 4, 'SA404': 4, 'SA2405': 4, 'SA405': 4, 'SA2406': 4,
                      'SA406': 4, 'SA2407': 4, 'SA407': 4, 'SA2408': 4, 'SA408': 4}

all_symbol_pres_rev = {symbol: k for k, v in all_symbol_pres.items() for symbol in v}

# 修改extract_vt_symbol函数，使用正则表达式提取合约代码、合约数字、合约交易所
def extract_symbol(symbol, with_exchange=True):
    '''
    :param symbol: 'rb2010'
    :return: rb，2010, SHFE or rb, 2010
    '''
    for i, s in enumerate(symbol):
        if s.isdigit():
            pre = symbol[:i]
            num = symbol[i:]
            if with_exchange:
                return pre, num, Exchange(all_symbol_pres_rev.get(pre, Exchange.LOCAL))
            else:
                return pre, num

# 合约交易指令每次最小开仓下单量，如：RM405 -> 1，UR2401 -> 4，UR401 -> 4
def get_min_order(vt_symbol):
    '''
    :param vt_symbol: 'rb2010.SHFE'
    :return: minimum order quantity, int
    '''
    symbol, exchange = extract_vt_symbol(vt_symbol)
    symbol_pre = extract_symbol(symbol, with_exchange=False)[0]
    print(f"debug get_min_order {symbol} {exchange} {symbol_pre}")
    return symbol_min_order.get(symbol_pre, symbol_min_order.get(symbol_pre.lower(), contract_min_order.get(symbol, 1)))

# 取出bian中各种bi的价格精度、volume精度、最小下单手数
def get_bian_info(vt_symbol):
    # 加载JSON信息
    filename = files('vnpy.usertools').joinpath('exchangeInfo.json')
    with open(filename, 'r') as file:
        data = json.load(file)
    
    symbol, exchange = extract_vt_symbol(vt_symbol)
    
    # 遍历symbols寻找匹配的pair
    for symbol_info in data['symbols']:
        if symbol == symbol_info['pair']:
            # 找到匹配的pair，提取所需信息
            price_precision = symbol_info['pricePrecision']
            quantity_precision = symbol_info['quantityPrecision']
            # 在filters中查找LOT_SIZE过滤器以获取minQty
            min_qty = None
            for filter in symbol_info['filters']:
                if filter['filterType'] == 'LOT_SIZE':
                    min_qty = filter['minQty']
                    break  # 找到后退出循环
            
            if min_qty is not None:
                return (price_precision, quantity_precision, min_qty)
            else:
                print("Error: LOT_SIZE filter not found.")
                return (0, 0, 0)
    # 如果没有找到匹配的pair
    print("Error: Symbol not found.")
    return (0, 0, 0)

# 取出bian中各种bi的价格精度、volume精度、最小下单手数
def get_xiaoshuwei(vt_symbol):
    symbol, exchange = extract_vt_symbol(vt_symbol)
    if exchange in [Exchange.SSE , Exchange.SZSE, Exchange.BSE]: 
        return 2
    elif exchange in [Exchange.DCE, Exchange.SHFE, Exchange.CZCE, Exchange.INE, Exchange.CFFEX, Exchange.GFEX]:
        return 2
    else:
        (price_precision, quantity_precision, min_qty)= get_bian_info(vt_symbol)
        return price_precision

def get_pricetick(vt_symbol):
    price_tick = 0.01
    symbol, exchange = extract_vt_symbol(vt_symbol)
    if exchange in [Exchange.SSE , Exchange.SZSE]: # 缺少北交所
        price_tick = 0.01
    elif exchange in [Exchange.DCE, Exchange.SHFE, Exchange.CZCE, Exchange.INE, Exchange.CFFEX, Exchange.GFEX]:
        simple_name = ''.join([char for char in symbol if not char.isdigit()])  # Remove all numbers
        # Step 2: 尝试用 simple_name 作为键从 all_priceticks 中获取尺寸
        if simple_name in all_priceticks:
            price_tick = all_priceticks[simple_name]
        else:
            print(f"error1 警告：{vt_symbol} '{simple_name}' 在 all_sizes 中未找到，使用默认值 0.1")  # 打印日志
    elif exchange in [Exchange.BINANCE]:
        xiaoshuwei = get_xiaoshuwei(vt_symbol)
        price_tick = 1/(pow(10, xiaoshuwei))
    else:
        print(f"error3 警告：binance{vt_symbol} exchange 没找到,price_tick使用默认值 0.1")  # 打印日志
    return price_tick

def get_closing_minutes(symbol: str) -> list[datetime.time]:
    """获取品种的收盘时间点列表"""
    trading_type = trading_hours_reverse.get(symbol.upper())
    if trading_type is None:
        return []
        
    closing_minutes = []
    periods = tradetime_dict[trading_type].split(',')
    
    for period in periods:
        end_time_str = period.split('-')[1].strip()
        if "next day" in end_time_str:
            end_time_str = end_time_str.replace("next day ", "")
        end_time = datetime.strptime(end_time_str, "%H:%M").time()
        closing_minutes.append(end_time)
            
    return closing_minutes

if __name__ == "__main__":
    print(get_closing_minutes("rb"))