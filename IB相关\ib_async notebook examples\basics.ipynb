{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Basics\n", "\n", "Let's first take a look at what's inside the ``ib_async`` package:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-05T01:09:20.715296Z", "start_time": "2025-03-05T01:09:20.508283Z"}}, "source": ["import ib_async\n", "print(ib_async.__all__)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Event', 'util', 'Client', 'Bag', 'Bond', 'CFD', 'ComboLeg', 'Commodity', 'ContFuture', 'Contract', 'ContractDescription', 'ContractDetails', 'Crypto', 'DeltaNeutralContract', 'Forex', 'Future', 'FuturesOption', 'Index', 'MutualFund', 'Option', 'ScanData', 'Stock', 'TagValue', 'Warrant', 'FlexError', 'FlexReport', 'IB', 'IBC', 'Watchdog', 'AccountValue', 'BarData', 'BarDataList', 'CommissionReport', 'ConnectionStats', 'DOMLevel', 'DepthMktDataDescription', 'Dividends', 'Execution', 'ExecutionFilter', 'FamilyCode', 'Fill', 'FundamentalRatios', 'HistogramData', 'HistoricalNews', 'HistoricalTick', 'HistoricalTickBidAsk', 'HistoricalTickLast', 'HistoricalSchedule', 'HistoricalSession', 'MktDepthData', 'NewsArticle', 'NewsBulletin', 'NewsProvider', 'NewsTick', 'OptionChain', 'OptionComputation', 'PnL', 'PnLSingle', 'PortfolioItem', 'Position', 'PriceIncrement', 'RealTimeBar', 'RealTimeBarList', 'ScanDataList', 'ScannerSubscription', 'SmartComponent', 'SoftDollarTier', 'TickAttrib', 'TickAttribBidAsk', 'TickAttribLast', 'TickByTickAllLast', 'WshEventData', 'TickByTickBidAsk', 'TickByTickMidPoint', 'TickData', 'TradeLogEntry', 'BracketOrder', 'ExecutionCondition', 'LimitOrder', 'MarginCondition', 'MarketOrder', 'Order', 'OrderComboLeg', 'OrderCondition', 'OrderState', 'OrderStatus', 'PercentChangeCondition', 'PriceCondition', 'StopLimitOrder', 'StopOrder', 'TimeCondition', 'Trade', 'VolumeCondition', 'Ticker', '__version__', '__version_info__', 'RequestError', 'Wrapper', 'StartupFetch', 'StartupFetchALL', 'StartupFetchNONE']\n"]}], "execution_count": 1}, {"cell_type": "markdown", "metadata": {}, "source": ["### Importing\n", "The following two lines are used at the top of all notebooks. The first line imports everything and the second\n", "starts an event loop to keep the notebook live updated:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-05T01:09:20.838287Z", "start_time": "2025-03-05T01:09:20.820758Z"}}, "source": ["from ib_async import *\n", "util.startLoop()"], "outputs": [], "execution_count": 2}, {"cell_type": "markdown", "metadata": {}, "source": ["*Note that startLoop() only works in notebooks, not in regular Python programs.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connecting\n", "The main player of the whole package is the \"IB\" class. Let's create an IB instance and connect to a running TWS/IBG application:"]}, {"cell_type": "code", "metadata": {"scrolled": true, "ExecuteTime": {"end_time": "2025-03-05T01:13:44.690800Z", "start_time": "2025-03-05T01:13:44.071539Z"}}, "source": ["ib = IB()\n", "# ib.connect('127.0.0.1', 7497, clientId=10)\n", "ib.connect('************', 4003, clientId=10)"], "outputs": [{"data": {"text/plain": ["<IB connected to **************:4002 clientId=12>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"cell_type": "markdown", "metadata": {}, "source": ["If the connection failed, then verify that the application has the API port enabled and double-check the hostname and port. For IB Gateway the default port is 4002. Make sure the clientId is not already in use.\n", "\n", "If the connection succeeded, then ib will be synchronized with TWS/IBG. The \"current state\" is now available via methods such as ib.positions(), ib.trades(), ib.openTrades(), ib.accountValues() or ib.tickers(). Let's list the current positions:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-05T01:09:27.423912100Z", "start_time": "2025-03-04T06:52:56.365209Z"}}, "source": ["ib.positions()"], "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"cell_type": "markdown", "metadata": {}, "source": ["Or filter the account values to get the liquidation value:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:52:56.765095Z", "start_time": "2025-03-04T06:52:56.750916Z"}}, "source": ["[v for v in ib.accountValues() if v.tag == 'NetLiquidationByCurrency' and v.currency == 'BASE']"], "outputs": [{"data": {"text/plain": ["[AccountValue(account='DUE117262', tag='NetLiquidationByCurrency', value='1012809.01', currency='BASE', modelCode='')]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "execution_count": 18}, {"cell_type": "markdown", "metadata": {}, "source": ["The \"current state\" will automatically be kept in sync with TWS/IBG. So an order fill will be added as soon as it is reported, or account values will be updated as soon as they change in TWS."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Contracts\n", "\n", "Contracts can be specified in different ways:\n", "* The ibapi way, by creating an empty Contract object and setting its attributes one by one;\n", "* By using Contract and giving the attributes as keyword argument;\n", "* By using the specialized Stock, Option, Future, Forex, Index, CFD, Commodity,\n", "  Bond, FuturesOption, MutualFund or Warrant contracts.\n", "\n", "Some examples:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:52:57.419170Z", "start_time": "2025-03-04T06:52:57.407637Z"}}, "source": ["Contract(conId=270639)\n", "Stock('AMD', 'SMART', 'USD')\n", "Stock('INTC', 'SMART', 'USD', primaryExchange='NASDAQ')\n", "Forex('EURUSD')\n", "CFD('IBUS30')\n", "Future('ES', '********', 'GLOBEX')\n", "Option('SPY', '********', 240, 'C', 'SMART')\n", "Bond(secIdType='ISIN', secId='US03076KAA60');"], "outputs": [], "execution_count": 19}, {"cell_type": "markdown", "metadata": {}, "source": ["### Sending a request\n", "\n", "The IB class has nearly all request methods that the IB API offers. The methods that return a result will block until finished and then return the result. Take for example reqContractDetails:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:52:58.396271Z", "start_time": "2025-03-04T06:52:58.003931Z"}}, "source": ["contract = Stock('TSLA', 'SMART', 'USD')\n", "ib.reqContractDetails(contract)"], "outputs": [{"data": {"text/plain": ["[ContractDetails(contract=Contract(secType='STK', conId=76792991, symbol='TSLA', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='TSLA', tradingClass='NMS'), marketName='NMS', minTick=0.01, orderTypes='ACTIVETIM,AD,ADDONT,ADJUST,ALERT,ALGO,ALLOC,AON,AVGCOST,BASKET,BENCHPX,CASHQTY,COND,CONDORDER,DARKONLY,DARKPOLL,DAY,DEACT,DEACTDIS,DEACTEOD,DIS,DUR,GAT,GTC,GTD,GTT,HID,IBKRATS,ICE,IMB,IOC,LIT,LMT,LOC,MIDPX,MIT,MKT,MOC,MTL,NGCOMB,NODARK,NONALGO,OCA,OPG,OPGREROUT,PEGBENCH,PEGMID,POSTATS,POSTONLY,PRE<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,REL,REL2MID,<PERSON><PERSON><PERSON><PERSON><PERSON>,R<PERSON>,<PERSON><PERSON>,<PERSON>AL<PERSON>,SCALEODD,SCALERST,<PERSON>IZECH<PERSON>,SMARTSTG,SNAPMID,SNAPMKT,SNAPREL,STP,STPLMT,SWEEP,TRAIL,TRAILLIT,TRAILLMT,TRAILMIT,WHATIF', validExchanges='SMART,AMEX,NYSE,CBOE,PHLX,ISE,CHX,ARCA,NASDAQ,DRCTEDGE,BEX,BATS,EDGEA,BYX,IEX,EDGX,FOXRIVER,PEARL,NYSENAT,LTSE,MEMX,IBEOS,OVERNIGHT,TPLUS0,PSX', priceMagnifier=1, underConId=0, longName='TESLA INC', contractMonth='', industry='Consumer, Cyclical', category='Auto Manufacturers', subcategory='Auto-Cars/Light Trucks', timeZoneId='US/Eastern', tradingHours='20250304:0400-20250304:2000;20250305:0400-20250305:2000;20250306:0400-20250306:2000;20250307:0400-20250307:2000', liquidHours='20250304:0930-20250304:1600;20250305:0930-20250305:1600;20250306:0930-20250306:1600;20250307:0930-20250307:1600', evRule='', evMultiplier=0, mdSizeMultiplier=1, aggGroup=1, underSymbol='', underSecType='', marketRuleIds='26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26', secIdList=[TagValue(tag='ISIN', value='US88160R1014')], realExpirationDate='', lastTradeTime='', stockType='COMMON', minSize=0.0001, sizeIncrement=0.0001, suggestedSizeIncrement=100.0, cusip='', ratings='', descAppend='', bondType='', couponType='', callable=False, putable=False, coupon=0, convertible=False, maturity='', issueDate='', nextOptionDate='', nextOptionType='', nextOptionPartial=False, notes='')]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"cell_type": "markdown", "metadata": {}, "source": ["### Current state vs request\n", "\n", "Doing a request involves network traffic going up and down and can take considerable time. The current state on the other hand is always immediately available. So it is preferable to use the current state methods over requests. For example, use ``ib.openOrders()`` in preference over ``ib.reqOpenOrders()``, or ``ib.positions()`` over ``ib.reqPositions()``, etc:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:53:00.011514Z", "start_time": "2025-03-04T06:53:00.000996Z"}}, "source": ["%time l = ib.positions()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 0 ns\n", "Wall time: 0 ns\n"]}], "execution_count": 21}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:53:00.756344Z", "start_time": "2025-03-04T06:53:00.715701Z"}}, "source": ["%time l = ib.reqPositions()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: total: 0 ns\n", "Wall time: 33.3 ms\n"]}], "execution_count": 22}, {"cell_type": "markdown", "metadata": {}, "source": ["### Logging\n", "\n", "The following will put log messages of INFO and higher level under the current active cell:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:53:02.430695Z", "start_time": "2025-03-04T06:53:02.419119Z"}}, "source": ["util.logToConsole()"], "outputs": [], "execution_count": 23}, {"cell_type": "markdown", "metadata": {}, "source": ["To see all debug messages (including network traffic):"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:53:03.055927Z", "start_time": "2025-03-04T06:53:03.039717Z"}}, "source": ["import logging\n", "util.logToConsole(logging.DEBUG)"], "outputs": [], "execution_count": 24}, {"cell_type": "markdown", "metadata": {}, "source": ["### Disconnecting\n", "\n", "The following will disconnect ``ib`` and clear all its state:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T06:53:03.987260Z", "start_time": "2025-03-04T06:53:03.974200Z"}}, "source": ["ib.disconnect()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-04 14:53:03,976 ib_async.ib INFO Disconnecting from **************:4002, 173 B sent in 10 messages, 19.3 kB received in 389 messages, session time 11.9 s.\n", "2025-03-04 14:53:03,977 ib_async.client INFO Disconnecting\n"]}], "execution_count": 25}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}