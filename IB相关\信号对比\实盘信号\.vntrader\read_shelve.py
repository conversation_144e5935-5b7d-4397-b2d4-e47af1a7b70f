import shelve

with shelve.open('ib_contract_data.db') as f:
    contracts = f.get("contracts", {})
    ib_contracts = f.get("ib_contracts", {})
    tradinghours_info = f.get("tradinghours_info", {})

print(len(contracts)) # 2
print(contracts) # {'265598.SMART': ContractData(gateway_name='IB', extra=None, symbol='265598', exchange=<Exchange.SMART: 'SMART'>, name='APPLE INC', product=<Product.EQUITY: '股票'>, size=1.0, pricetick=0.01, min_volume=Decimal('0.0001'), stop_supported=True, net_position=True, history_data=True, option_strike=0, option_underlying='', option_type=None, option_listed=None, option_expiry=None, option_portfolio='', option_index=''), '12087792.IDEALPRO': ContractData(gateway_name='IB', extra=None, symbol='12087792', exchange=<Exchange.IDEALPRO: 'IDEALPRO'>, name='European Monetary Union Euro', product=<Product.FOREX: '外汇'>, size=1.0, pricetick=5e-05, min_volume=Decimal('0.01'), stop_supported=True, net_position=True, history_data=True, option_strike=0, option_underlying='', option_type=None, option_listed=None, option_expiry=None, option_portfolio='', option_index='')}

print(len(ib_contracts)) # 2
print(ib_contracts) # {'265598.SMART': 1687676645440: 265598,AAPL,STK,,,0,,1.0,SMART,NASDAQ,USD,AAPL,NMS,False,,,,combo:, '12087792.IDEALPRO': 1687676645344: 12087792,EUR,CASH,,,0,,1.0,IDEALPRO,,USD,EUR.USD,EUR.USD,False,,,,combo:}

print(len(tradinghours_info)) # 2
print(tradinghours_info) # {'12087792.IDEALPRO': {'tradingPeriod': [(datetime.datetime(2025, 7, 21, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 22, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 22, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 23, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 23, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 24, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 24, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 25, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 27, 17, 15, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 28, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')))], 'timeZoneId': 'US/Eastern', 'closingStates': {datetime.datetime(2025, 7, 22, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 23, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 24, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 25, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 28, 17, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False}}, '265598.SMART': {'tradingPeriod': [(datetime.datetime(2025, 7, 22, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 22, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 23, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 23, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 24, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 24, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern'))), (datetime.datetime(2025, 7, 25, 9, 30, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')), datetime.datetime(2025, 7, 25, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')))], 'timeZoneId': 'US/Eastern', 'closingStates': {datetime.datetime(2025, 7, 22, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 23, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 24, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False, datetime.datetime(2025, 7, 25, 16, 0, tzinfo=zoneinfo.ZoneInfo(key='US/Eastern')): False}}}
