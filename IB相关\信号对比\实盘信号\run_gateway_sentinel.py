#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IB Gateway 监控脚本

监控3个IB Gateway的连接状态，每5分钟进行一次健康检查
如果检查失败，发送企业微信告警通知

使用方法:
python run_gateway_sentinel.py
"""

import asyncio
import json
import os
import signal
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from zoneinfo import ZoneInfo

from basic_datadownload import IBDataDownloader, OverallDownloadResult
from wecom_alert import WecomAlertManager
from vnpy.trader.constant import Interval

# 时区设置
US_Eastern = ZoneInfo('US/Eastern')  # IB TWS使用美国东部时间

# 配置参数
GATEWAY_PORTS = [4002, 4012, 4022]  # 三个Gateway端口
CLIENT_ID = 502  # 统一客户号
CHECK_INTERVAL = 300  # 5分钟检查间隔（秒）
TEST_SYMBOL = "265598.SMART"  # 测试用的vt_symbol
TEST_START_DATE = datetime(2025, 1, 2, tzinfo=US_Eastern)
TEST_END_DATE = datetime(2025, 1, 3, tzinfo=US_Eastern)

import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


class GatewaySentinel:
    """Gateway监控器"""
    
    def __init__(self):
        self.is_running = False
        self.wecom_manager = WecomAlertManager(key="d775d95e-1abd-4693-a040-dd011ff32a30")
        self.gateway_status: Dict[int, bool] = {port: True for port in GATEWAY_PORTS}
        self.last_alert_time: Dict[int, Optional[datetime]] = {port: None for port in GATEWAY_PORTS}
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，准备停止监控...")
        self.stop()
        
    async def check_gateway_health(self, port: int) -> bool:
        """检查单个Gateway的健康状态"""
        try:
            # 创建临时配置文件
            temp_config = {
                "TWS地址": "**************",
                "TWS端口": port,
                "客户号": CLIENT_ID,
                "交易账户": ""
            }
            # 创建下载器实例
            downloader = IBDataDownloader()

            # 修改下载器的配置
            downloader.host = temp_config["TWS地址"]
            downloader.port = temp_config["TWS端口"]
            downloader.client_id = temp_config["客户号"]

            # 执行健康检查下载，添加超时保护
            result = await asyncio.wait_for(
                downloader.download(
                    vt_symbols=[TEST_SYMBOL],
                    start_date=TEST_START_DATE,
                    end_date=TEST_END_DATE,
                    interval=Interval.DAILY
                ),
                timeout=15  # 30秒超时
            )

            return result.success
                    
        except Exception as e:
            logger.error(f"Gateway {port} 健康检查异常: {str(e)}")
            return False
    
    async def check_all_gateways(self):
        """检查所有Gateway的健康状态"""
        logger.info("开始检查所有Gateway健康状态...")
        
        # 并发检查所有Gateway
        tasks = []
        for port in GATEWAY_PORTS:
            task = asyncio.create_task(self.check_gateway_health(port))
            tasks.append((port, task))
        
        # 等待所有检查完成
        for port, task in tasks:
            try:
                is_healthy = await task
                previous_status = self.gateway_status[port]
                self.gateway_status[port] = is_healthy
                
                if is_healthy:
                    if not previous_status:
                        # Gateway恢复正常
                        message = f"✅ Gateway {port} 已恢复正常"
                        logger.info(message)
                        self.wecom_manager.add_message(message)
                        self.last_alert_time[port] = None
                    else:
                        logger.info(f"Gateway {port} 状态正常")
                else:
                    if previous_status:
                        # Gateway刚刚失败
                        message = f"❌ Gateway {port} 健康检查失败"
                        logger.error(message)
                        self.wecom_manager.add_message(message)
                        self.last_alert_time[port] = datetime.now()
                    else:
                        # Gateway持续失败，检查是否需要重复告警
                        now = datetime.now()
                        last_alert = self.last_alert_time[port]
                        if last_alert and (now - last_alert).total_seconds() >= 1800:  # 半小时重复告警
                            message = f"⚠️ Gateway {port} 持续失败超过半小时"
                            logger.warning(message)
                            self.wecom_manager.add_message(message)
                            self.last_alert_time[port] = now
                        else:
                            logger.warning(f"Gateway {port} 持续失败中...")
                            
            except Exception as e:
                logger.error(f"检查Gateway {port} 时发生异常: {str(e)}")
                self.gateway_status[port] = False
    
    def get_status_summary(self) -> str:
        """获取状态摘要"""
        healthy_count = sum(1 for status in self.gateway_status.values() if status)
        total_count = len(self.gateway_status)
        
        status_details = []
        for port, status in self.gateway_status.items():
            status_icon = "✅" if status else "❌"
            status_details.append(f"Gateway {port}: {status_icon}")
        
        return f"Gateway状态摘要 ({healthy_count}/{total_count} 正常)\n" + "\n".join(status_details)
    
    async def monitoring_loop(self):
        """监控主循环"""
        logger.info("Gateway监控已启动")
        
        # 发送启动通知
        start_message = f"🚀 Gateway监控已启动\n监控端口: {', '.join(map(str, GATEWAY_PORTS))}\n检查间隔: {CHECK_INTERVAL}秒"
        self.wecom_manager.add_message(start_message)
        
        while self.is_running:
            try:
                # 执行健康检查
                await self.check_all_gateways()
                
                # 每小时发送一次状态摘要
                current_time = datetime.now()
                if current_time.minute == 0 and current_time.second < CHECK_INTERVAL:
                    summary = self.get_status_summary()
                    self.wecom_manager.add_message(f"📊 {summary}")
                
                # 等待下次检查
                await asyncio.sleep(CHECK_INTERVAL)
                
            except Exception as e:
                logger.error(f"监控循环异常: {str(e)}")
                error_message = f"⚠️ 监控程序异常: {str(e)}"
                self.wecom_manager.add_message(error_message)
                await asyncio.sleep(60)  # 异常时等待1分钟再继续
    
    def start(self):
        """启动监控"""
        if self.is_running:
            logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        self.wecom_manager.start()
        
        try:
            # 运行监控循环
            asyncio.run(self.monitoring_loop())
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        except Exception as e:
            logger.error(f"监控程序异常退出: {str(e)}")
        finally:
            self.stop()
    
    def stop(self):
        """停止监控"""
        if not self.is_running:
            return
        
        logger.info("正在停止Gateway监控...")
        self.is_running = False
        
        # 发送停止通知
        stop_message = "🛑 Gateway监控已停止"
        self.wecom_manager.add_message(stop_message)
        
        # 等待消息发送完成后停止
        self.wecom_manager.stop()
        
        logger.info("Gateway监控已停止")

def main():
    """主函数"""
    # 创建并启动监控器
    sentinel = GatewaySentinel()
    
    try:
        sentinel.start()
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()