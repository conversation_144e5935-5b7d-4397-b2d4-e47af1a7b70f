import pandas as pd
from datetime import datetime
import pytz

# 创建测试数据
def create_test_data():
    # 创建美东时区对象
    et_tz = pytz.timezone("America/New_York")
    
    # 创建第一个DataFrame（模拟original_df）
    dates1 = [
        datetime(2024, 1, 1, 9, 30).astimezone(et_tz),
        datetime(2024, 1, 1, 9, 31).astimezone(et_tz),
        datetime(2024, 1, 1, 9, 32).astimezone(et_tz)
    ]
    original_df = pd.DataFrame({
        'open': [100.0, 101.0, 102.0],
        'high': [100.5, 101.5, 102.5],
        'low': [99.5, 100.5, 101.5],
        'close': [100.2, 101.2, 102.2],
        'volume': [1000, 1100, 1200]
    }, index=dates1)
    
    # 创建第二个DataFrame（模拟full_df）
    dates2 = [
        datetime(2024, 1, 1, 9, 31).astimezone(et_tz),  # 重复的时间戳
        datetime(2024, 1, 1, 9, 32).astimezone(et_tz),  # 重复的时间戳
        datetime(2024, 1, 1, 9, 33).astimezone(et_tz)   # 新的时间戳
    ]
    full_df = pd.DataFrame({
        'open': [201.0, 202.0, 203.0],
        'high': [201.5, 202.5, 203.5],
        'low': [200.5, 201.5, 202.5],
        'close': [201.2, 202.2, 203.2],
        'volume': [2000, 2100, 2200]
    }, index=dates2)
    
    return original_df, full_df

def test_concat_duplicates():
    # 获取测试数据
    original_df, full_df = create_test_data()
    
    print("原始数据 (original_df):")
    print(original_df)
    print("\n新数据 (full_df):")
    print(full_df)
    
    # 测试concat和去重
    print("\n合并后的数据 (concat):")
    result_df = pd.concat([original_df, full_df])
    print(result_df)
    
    print("\n去重后的数据 (keep='first'):")
    result_df = result_df[~result_df.index.duplicated(keep='first')]
    print(result_df)
    
    print("\n去重后的数据 (keep='last'):")
    result_df = pd.concat([original_df, full_df])
    result_df = result_df[~result_df.index.duplicated(keep='last')]
    print(result_df)
    
    # 测试drop_duplicates方法
    print("\n使用drop_duplicates方法 (keep='first'):")
    result_df = pd.concat([original_df, full_df])
    result_df = result_df.reset_index()
    result_df = result_df.drop_duplicates(subset=['index'], keep='first').set_index('index')
    print(result_df)

if __name__ == "__main__":
    test_concat_duplicates() 