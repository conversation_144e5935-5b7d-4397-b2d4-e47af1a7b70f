import multiprocessing
import sys
from datetime import time, datetime
from logging import INFO
from time import sleep

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, ZoneInfo
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG

from data_recorder_fake_bar.utils.barGen_redis_engine import BarGenEngineIb
from data_recorder_fake_bar.utils.recorder_engine import RecorderEngine
from data_recorder_fake_bar.utils.ib_gateway_sim import IbGateway

# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True

# 提前15分钟开启行情录制
DAY_START = time(16, 45)
DAY_END = time(16, 15)

# Configuration files
connect_filename = 'connect_ib.json'
data_recorder_filename = 'data_recorder_setting_ib_fut.json'
# data_recorder_filename = 'data_recorder_setting_ib_cash.json'


def rewrite_setting():
    """Convert data recorder settings to bar generator settings"""
    data_recorder_setting = load_json(data_recorder_filename)
    barGen_setting = {}
    for key, value in data_recorder_setting.items():
        barGen_setting[key] = list(value.keys())
    save_json('barGen_setting_fut.json', barGen_setting)


def check_trading_period(is_us_market: bool = True):
    """Check if current time is within trading period"""
    eastern = ZoneInfo('US/Eastern')
    current_time = datetime.now(eastern).time()
    return DAY_START <= current_time or current_time <= DAY_END


def run_child():
    """Run trading system in child process"""

    # Initialize engines
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    # Add components
    main_engine.add_gateway(IbGateway)
    main_engine.write_log("[IB FUT]接口添加成功")

    cta_engine = main_engine.add_app(CtaStrategyApp)
    main_engine.write_log("[IB FUT]主引擎创建成功")

    main_engine.add_engine(RecorderEngine)
    main_engine.write_log("[IB FUT]添加数据记录引擎")

    log_engine = main_engine.get_engine("log")
    event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
    main_engine.write_log("[IB FUT]注册日志事件监听")

    # Connect to IB
    setting = load_json(connect_filename)
    main_engine.write_log("[IB FUT]数据接口配置加载成功")
    main_engine.connect(setting, "IB")
    main_engine.write_log("[IB FUT]连接IB接口")

    # Add bar generation engine
    main_engine.add_engine(BarGenEngineIb)
    main_engine.write_log("[IB FUT]添加Bar生成引擎")

    # Query account and position
    sleep(10)
    main_engine.write_log("***查询资金和持仓***")
    main_engine.write_log(main_engine.get_all_accounts())
    main_engine.write_log(main_engine.get_all_positions())

    # Initialize CTA strategies
    cta_engine.init_engine()
    main_engine.write_log("[IB FUT]CTA引擎初始化完成")# 此时会将strategy_setting中的策略add_strategy

    # Load strategies
    main_engine.write_log("[IB FUT]***从数据库读取准备数据, 实盘运行***")
    old_strategies = list(cta_engine.strategies.keys())
    new_strategies = []

    data_recorder_setting = load_json(data_recorder_filename)
    for bar_type, bar_setting in data_recorder_setting.items():
        for vt_symbol, setting in bar_setting.items():
            strategy_symbol_name3 = f'DoubleMa3Strategy_IB_{vt_symbol}_v1'
            if strategy_symbol_name3 in old_strategies:
                cta_engine.edit_strategy(strategy_symbol_name3, {'fast_window': 10, 'slow_window': 20})
            else:
                cta_engine.add_strategy("DoubleMa3Strategy", strategy_symbol_name3, vt_symbol,
                                    {"fast_window": 10, "slow_window": 20})
                new_strategies.append(strategy_symbol_name3)

            strategy_symbol_name5 = f'DoubleMa5Strategy_IB_{vt_symbol}_v1'
            if strategy_symbol_name5 in old_strategies:
                cta_engine.edit_strategy(strategy_symbol_name5, {'fast_window': 10, 'slow_window': 20})
            else:
                cta_engine.add_strategy("DoubleMa5Strategy", strategy_symbol_name5, vt_symbol,
                                    {"fast_window": 10, "slow_window": 20})
                new_strategies.append(strategy_symbol_name5)

    sleep(5)

    # Start strategies
    cta_engine.init_all_strategies()
    time1 = datetime.now()
    while not all([strategy.inited for strategy in cta_engine.strategies.values()]):
        sleep(1)
    time_used = datetime.now() - time1
    main_engine.write_log(f"[IB FUT]CTA策略全部初始化，耗时：{time_used}，平均每个策略耗时：{time_used / len(cta_engine.strategies)}")

    cta_engine.start_all_strategies()
    main_engine.write_log("[IB FUT]CTA策略全部启动")

    # Main loop
    while True:
        sleep(10)
        if not check_trading_period():
            print("关闭子进程")
            main_engine.close()
            sys.exit(0)


def run_parent():
    """Run parent process to manage child process"""
    print("[IB FUT]启动CTA策略守护父进程")
    child_process = None

    while True:
        trading = check_trading_period()

        # Start child process in trading period
        if trading and child_process is None:
            print("[IB FUT]启动子进程")
            child_process = multiprocessing.Process(target=run_child)
            child_process.start()
            print("[IB FUT]子进程启动成功")

        # 非记录时间则退出子进程
        if not trading and child_process is not None:
            if not child_process.is_alive():
                child_process = None
                print("[IB FUT]子进程关闭成功")

        sleep(5)


if __name__ == "__main__":
    rewrite_setting()
    run_parent()
