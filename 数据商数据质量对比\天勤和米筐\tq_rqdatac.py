from datetime import datetime, date
from contextlib import closing
from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqSim
from tqsdk.tools import DataDownloader

import pandas as pd
import re
import rqdatac
rqdatac.init( 'license', 'hKzEyfcbN4O4B22wGXKfOZnOkVIyQ4fnW7VSUepZ5shkCx3Wpfkb63nMWozKudSUfMCiSx6cuWYasEyqaIVQ7a91WnYFIhxSw39GKxvHmhnlIjaSjBNncRY0Y3ZH3wWYiYbjK25Gxl9FuVkH6sA5VmnbMBmJoQHeT_seHEFYVPw=LVXNtX-oQgO2T9QDKkPx1hhlyjgrkYETwszLKzPA3ItRHWcp4crJu9dlykAOaJv4AtQuPy-THTFzBP4DfcFtIWm-W5vGQNyMMu3lD8cc1u_kxXFfihqajhijKdIi8nJvVrOexx1XVI6Vv-FdzrL0IVNY9e9GCcZ9lavQanQ4BNw=' )

api = TqApi(auth=TqAuth("18888925004", "zxc12345678"))
download_tasks = {}

start_time ='2023-05-12'
time1=date(2023, 5, 12)

excel_path = r'C:\Users\<USER>\Desktop\天勤米筐数据对比\股票池.xlsx'
order_book_ids = pd.read_excel(excel_path, sheet_name='Sheet1')
l = len(order_book_ids)

file_name_list_rqdatac=[]
file_name_list_tqsdk=[]


for i in range(l):
        a = order_book_ids['代码'][i]
        b = int(a[0])
        if b == 0 or b == 3:
                rq_book_ids = a[0:6] + '.XSHE'
                tqsdk_book_ids = 'SZSE.' + a[0:6]


        elif b == 6:
                rq_book_ids = a[0:6] + '.XSHG'
                tqsdk_book_ids = 'SSE.' + a[0:6]
        
        
        data1 = rqdatac.get_price(order_book_ids=rq_book_ids
                 , start_date=start_time, end_date=start_time,fields=['open','high','low','close','volume'],frequency='1m',time_slice=('09:00', '15:00'),adjust_type='pre',expect_df=False)
        file_name1 = str(rq_book_ids) + '_' + 'rq' + '.csv'
        file_name_list_rqdatac.append(file_name1)

        data2 = data1[0:236]
        data2.to_csv(file_name1)
        
        
        # 下载天勤分钟线数据
        file_name2 = str(tqsdk_book_ids) + '_' + 'tq' + '.csv'
        file_name_list_tqsdk.append(file_name2)
        download_tasks[tqsdk_book_ids] = DataDownloader(api, symbol_list=tqsdk_book_ids, dur_sec=1*60,
                    start_dt=time1, end_dt=time1, csv_file_name=file_name2)
        
        
        
# 使用with closing机制确保下载完成后释放对应的资源
with closing(api):
    while not all([v.is_finished() for v in download_tasks.values()]):
        api.wait_update()
        print("progress: ", { k:("%.2f%%" % v.get_progress()) for k,v in download_tasks.items() })


file_dir = r'C:\Users\<USER>\Desktop\天勤米筐数据对比'
for i in range(len(file_name_list_rqdatac)):
    data_rqdatac_1=pd.read_csv(file_dir+r'\%s'%file_name_list_rqdatac[i]) 
    data_rqdatac=data_rqdatac_1[data_rqdatac_1.columns[1:6]]
    
    data_tqsdk_1=pd.read_csv(file_dir+r'\%s'%file_name_list_tqsdk[i])
    data_tqsdk_2=data_tqsdk_1[0:236]
    
    data_tqsdk=data_tqsdk_2[data_tqsdk_2.columns[2:7]]

    data_tqsdk.columns=['open','high','low','close','volume']


    a=data_rqdatac-data_tqsdk
    a.columns=['open','high','low','close','volume']
    a['datetime']=data_rqdatac_1['datetime']

    file_name = file_name_list_rqdatac[i]+'_result'+'.csv'
    a.to_csv(file_name)
