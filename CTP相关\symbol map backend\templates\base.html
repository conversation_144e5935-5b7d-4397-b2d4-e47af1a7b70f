<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Symbol Map{% endblock %}</title>
    <!-- 使用国内可访问的 Bootstrap CSS CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- 添加 X-editable CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/x-editable/1.5.1/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <a class="navbar-brand" href="{{ url_for('index') }}">Symbol Map</a>
        <div class="navbar-nav ml-auto">
            {% if current_user.is_authenticated %}
                {% if current_user.is_admin %}
                    <a class="nav-item nav-link" href="{{ url_for('users') }}">User Management</a>
                {% endif %}
                <a class="nav-item nav-link" href="{{ url_for('logout') }}">Logout</a>
            {% else %}
                <a class="nav-item nav-link" href="{{ url_for('login') }}">Login</a>
            {% endif %}
        </div>
    </nav>
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </div>
    <!-- 使用国内可访问的 jQuery 和 Popper.js CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/popper.js/2.5.3/umd/popper.min.js"></script>
    <!-- 使用国内可访问的 Bootstrap JS CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <!-- 添加 X-editable JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/x-editable/1.5.1/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
