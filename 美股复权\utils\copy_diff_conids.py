#!/usr/bin/env python3
"""
处理在filtered_conid2.txt中但不在filtered_conid.txt中的conid
直接调用现有的copy_bardata_by_conid.py脚本
"""

import os
import sys
import subprocess
from pathlib import Path
from loguru import logger

def main():
    """处理差异conid"""
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    
    # 文件路径
    conid_file1 = "filtered_conid.txt"
    conid_file2 = "filtered_conid2.txt"
    
    # 调用修改后的copy_bardata_by_conid.py
    cmd = [
        sys.executable,
        str(script_dir / "copy_bardata_by_conid.py"),
        "--conid-file", conid_file1,
        "--conid-file2", conid_file2,
        "--source-db", "vnpy_stk_us_ib_m",
        "--target-db", "vnpy_stk_us_ib_m_",
        "--batch-size", "20",
        "--end-time", "2025-08-15 04:00:00"
    ]
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("执行成功！")
        logger.info(f"输出: {result.stdout}")
        if result.stderr:
            logger.warning(f"错误输出: {result.stderr}")
    except subprocess.CalledProcessError as e:
        logger.error(f"执行失败: {e}")
        logger.error(f"输出: {e.stdout}")
        logger.error(f"错误: {e.stderr}")
        sys.exit(1)

if __name__ == "__main__":
    main()
