{"cells": [{"cell_type": "code", "execution_count": 0, "source": ["from vnpy.trader.utility import save_json,load_json\n", "import os\n", "\n", "# 切换工作目录到当前ipynb文件所在目录\n", "os.chdir(os.path.dirname(os.path.abspath(\"__file__\")))\n", "print(f'当前工作目录:{os.getcwd()}')\n", "if not os.path.exists(\"data\"):\n", "    os.mkdir(\"data\")\n", "# 替换数据库配置\n", "dbsetting = load_json(\"vt_setting.json\")\n", "dbsetting[\"database.database\"] = \"vnpyibhis\"\n", "save_json(\"vt_setting.json\", dbsetting)"], "metadata": {"collapsed": false}, "outputs": []}, {"cell_type": "code", "execution_count": 4, "source": ["from vnpy.trader.database import get_database\n", "database_manager = get_database()\n", "dbsetting"], "metadata": {"collapsed": false}, "outputs": []}, {"cell_type": "code", "execution_count": 1, "source": ["\n", "from time import sleep\n", "\n", "import pandas as pd\n", "from vnpy_ib import IbGateway\n", "from vnpy_scripttrader import init_cli_trading\n", "\n", "# 订阅行情相关\n", "\n", "setting = load_json(\"connect_ib.json\")\n", "engine = init_cli_trading([IbGateway])  #返回Script_engine 示例，并且给main_engine注册了gateway\n", "engine.connect_gateway(setting, \"IB\")  #链接"], "metadata": {"collapsed": false}, "outputs": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:30:43.509441700Z", "start_time": "2023-08-25T03:30:33.488156700Z"}}, "source": ["# 查询资金 - 自动\n", "sleep(10)\n", "print(\"***查询资金和持仓***\")\n", "print(engine.get_all_accounts(use_df=True))"], "outputs": []}, {"cell_type": "code", "execution_count": 1, "source": ["# 使用script_engine订阅历史数据是从rqdata获取，vnpy v2.07已经提供历史数据获取，这里创建HistoryRequest来获取,\n", "# 查询如果没有endtime，默认当前。返回历史数据输出到数据库和csv文件\n", "# 关于api更多信息可以参见 https://interactivebrokers.github.io/tws-api/historical_bars.html\n", "print(\"***从IB读取历史数据, 返回历史数据输出到数据库和csv文件***\")\n", "from vnpy.trader.object import HistoryRequest\n", "from vnpy.trader.object import Interval\n", "import pytz\n", "from datetime import datetime, timedelta\n", "\n", "# 下载昨晚21：30到今天4：00的数据，IB的数据是UTC时间，所以需要转换\n", "today = datetime.now(pytz.timezone('Asia/Shanghai'))\n", "yesday = today - <PERSON><PERSON><PERSON>(days=1)\n", "start = datetime(yesday.year, yesday.month, yesday.day, 4, 0)\n", "# start = datetime(yesday.year, yesday.month, yesday.day, 3, 59).astimezone(pytz.timezone('UTC'))\n", "end = datetime(today.year, today.month, today.day, 4, 0)\n", "print(start, end)  # 注意下载的数据是左闭右开，不包含end时间点的数据\n", "\n", "\n", "# 定义函数（查询历史数据并把历史数据BarData输出到csv），便于使用函数循环下载\n", "def req_download_historydata(main_engine, vt_symbol, start, end, interval=Interval.MINUTE):\n", "    contract = main_engine.get_contract(vt_symbol)\n", "    if not contract:\n", "        print(f\"找不到合约：{vt_symbol}\")\n", "        return\n", "    historyreq = HistoryRequest(\n", "        symbol=contract.symbol,\n", "        exchange=contract.exchange,\n", "        start=start,\n", "        end=end,\n", "        interval=interval\n", "    )\n", "    bardatalist = main_engine.query_history(historyreq, contract.gateway_name)\n", "    # print(bardatalist)\n", "    # 把历史数据BarData输出到csv，路径为当前目录的data文件夹下，文件名为合约名、start的日期、end的日期\n", "    # pd.DataFrame(bardatalist).to_csv(str(historyreq.symbol) + \".csv\", index=True, header=True)\n", "    pd.DataFrame(bardatalist).to_csv('data/' + str(historyreq.symbol) + \"_\" + str(historyreq.start.date()) + \"_\" + str(\n", "        historyreq.end.date()) + \".csv\", index=True, header=True)\n", "    print(f'History data export to CSV: {vt_symbol}')\n", "    # 把历史数据BarData放入数据库\n", "    database_manager.save_bar_data(bardatalist)\n", "\n", "# 读取data_recorder_setting_ib.json文件，vt_symbol = \"PFE-USD-STK.SMART\"等\n", "data_recorder_setting = load_json(\"data_recorder_setting_ib.json\")\n", "\n", "# 订阅行情\n", "from vnpy.trader.object import SubscribeRequest\n", "from vnpy.trader.utility import extract_vt_symbol\n", "# 下载历史数据\n", "for vt_symbol in data_recorder_setting[\"bar\"]:\n", "    symbol, exchange = extract_vt_symbol(vt_symbol)\n", "    req1 = SubscribeRequest(symbol, exchange)  #创建行情订阅\n", "    engine.main_engine.subscribe(req1, \"IB\")\n", "    sleep(1)\n", "    req_download_historydata(engine.main_engine, vt_symbol, start, end)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-25T05:35:13.848158700Z", "start_time": "2023-08-25T05:35:13.751156Z"}}, "outputs": []}, {"cell_type": "code", "execution_count": null, "source": ["# 还原数据库配置\n", "dbsetting[\"database.database\"] = \"vnpyzhtest\"\n", "save_json(\"vt_setting.json\", dbsetting)\n", "dbsetting"], "metadata": {"collapsed": false}, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "1b43cb0bd93d5abbadd54afed8252f711d4681fe6223ad6b67ffaee289648f85"}}}, "nbformat": 4, "nbformat_minor": 2}