""""""
import traceback
from copy import copy
from datetime import datetime, time, timedelta
from queue import Queue
from threading import Thread
from time import sleep
from typing import Dict, Optional, Union

import pytz
from vnpy.event import Event, EventEngine
from vnpy.trader.constant import (Exchange)
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.event import EVENT_TICK, EVENT_CONTRACT, EVENT_TIMER
from vnpy.trader.object import BarData, TickData, Interval
from vnpy.trader.object import (SubscribeRequest, ContractData)
from vnpy.trader.utility import load_json, save_json, virtual, ZoneInfo, extract_vt_symbol


APP_NAME = "BarGenEngine"
EVENT_BAR = "eBarGen."
EVENT_BAR_RECORD = "eBarGenRec."

eastern = pytz.timezone('US/Eastern')
shanghai = pytz.timezone('Asia/Shanghai')

# Define the trading periods in Eastern Time (ET) for US stock market
US_TIME = [(time(9, 30), time(16, 0)), ]
FUT_DAY_TIME = [(time(9, 0), time(10, 15)), (time(10, 30), time(11, 30)), (time(13, 30), time(15, 0))]
FUT_TIME = FUT_DAY_TIME + [(time(21, 0), time(23, 0))]
FUT_NIGHT_TIME = FUT_DAY_TIME + [(time(21, 0), time(23, 0))]
FUT_NIGHT_TIME2 = FUT_DAY_TIME + [(time(21, 0), time(23, 0))]
# FUT_NIGHT_TIME2 += [(datetime.now().time(), (datetime.now()+timedelta(minutes=100)).time())]# test lance
INDEX_TIME = [(time(9, 30), time(11, 30)), (time(13, 0), time(15, 0))]

TDAYS = load_json("tdays_dict.json")
# 日期格式转换
TDAYS = {key: [datetime.strptime(x, "%Y%m%d").date() for x in value] for key, value in TDAYS.items()}

class MyDict(dict):
    __setattr__ = dict.__setitem__
    __getattr__ = dict.__getitem__

def dict_to_object(dictObj):
    if not isinstance(dictObj, dict):
        return dictObj
    inst = MyDict()
    for k, v in dictObj.items():
        inst[k] = dict_to_object(v)
    return inst

class BarGenEngine(BaseEngine):
    """"""
    setting_filename = "barGen_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.queue = Queue()
        self.thread = Thread(target=self.run)
        self.active = False

        self.bar_recordings = []
        # self.bar_generators = {}

        self.bars: Dict[str, BarData] = {}
        self.last_bars: Dict[str, BarData] = {}
        self.last_ticks: Dict[str, TickData] = {}
        # self.last_dt: datetime = None
        self.last_dts: Dict[str, datetime] = {}

        self.tick_time: Dict[str, str] = {}  # cache tick time for debug info

        self.load_setting()
        self.register_event()
        self.start()  # need test vigar 1216 !
        self.put_event()

    def load_setting(self):
        """"""
        setting = load_json(self.setting_filename)
        self.write_log(f"[barGen load setting] {setting}")
        self.bar_recordings = setting.get("bar", [])
        self.write_log(f"[barGen load setting] bar_recordings : {self.bar_recordings}")
        for elem in self.bar_recordings:
            self.write_log(f"[barGen load setting] subscribe elem {elem}")
            self.subscribe_recording(elem)

    def load_last_bars(self, filename: str = "last_bars.json"):
        """"""
        last_bars = dict_to_object(load_json(filename))
        # 解析每个vt_symbol对应的BarData的datetime
        for vt_symbol, bar in last_bars.items():
            bar.exchange = Exchange(bar.exchange)
            bar.datetime = datetime.strptime(bar.datetime, "%Y-%m-%d %H:%M:%S").astimezone(ZoneInfo('Asia/Shanghai'))
            bar.interval = Interval(bar.interval)
        return last_bars

    def save_setting(self):
        """"""
        setting = {"bar": self.bar_recordings}
        save_json(self.setting_filename, setting)

    def save_last_bars(self, last_bars: Dict[str, BarData], filename: str = "last_bars.json"):
        """"""
        # 先加载last_bars.json，再更新last_bars，再保存到last_bars.json
        last_bars_in_file = self.load_last_bars(filename)
        for vt_symbol, bar in last_bars.items():
            try:
                # 使用__dict__属性修改格式，否则not JSON serializable
                last_bars_in_file[vt_symbol] = {
                    "symbol": bar.symbol,
                    "exchange": bar.exchange.value,
                    "datetime": bar.datetime.strftime("%Y-%m-%d %H:%M:%S"),
                    "interval": bar.interval.value,
                    "volume": bar.volume,
                    "turnover": bar.turnover,
                    "open_interest": bar.open_interest,
                    "open_price": bar.open_price,
                    "high_price": bar.high_price,
                    "low_price": bar.low_price,
                    "close_price": bar.close_price,
                    "gateway_name": bar.gateway_name,
                }
            except Exception:
                self.write_log(f'[save_last_bars] error: {traceback.format_exc()}')
                self.write_log(f'[save_last_bars] error: bar: {bar}')
        save_json(filename, last_bars_in_file)

    def run(self):
        """"""
        while self.active:
            try:
                if datetime.now().minute % 30 == 0 and datetime.now().second == 0:
                    # self.load_setting()
                    pass

            #     # 使用队列来推送假tick，保证每分钟都有bar。问题：可能导致tick错位
            #     task: Any = self.queue.get(timeout=1)
            #     task_type, data = task
            #
            #     if task_type == "tick":
            #         for tick in data:
            #             self.update_tick(tick)
            #
            # except Empty:
            #     continue

            except Exception:
                msg = f"barGen run 触发异常已停止\n{traceback.format_exc()}"
                self.write_log(f"[run] barGen error: {msg}")

    def close(self):
        """"""
        self.active = False
        if self.thread.is_alive():
            self.thread.join()
        # 使用save_json保存self.last_bars
        self.save_last_bars(self.last_bars)
        print(f"[close engine] success to save last_bars.json")# log引擎可能已关闭，所以用print

    def start(self):
        """"""
        self.active = True
        self.thread.start()
        self.last_bars = self.load_last_bars("last_bars.json")
        self.write_log(f"[start engine] success to load last_bars.json")
        self.write_log(f"[start engine] last_bars: {self.last_bars}")

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            contract = self.main_engine.get_contract(vt_symbol)
            if not contract:
                self.write_log(f"[subscribe_recording] 找不到合约：{vt_symbol}")
                return
            self.write_log(f"[subscribe_recording] prepare to send subscribe req：{vt_symbol}")
            self.subscribe(contract)
        except Exception:
            msg = f"[subscribe_recording] barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"[subscribe_recording] 添加K线记录成功：{vt_symbol}")

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)

    # def update_tick(self, tick: TickData):
    #     """"""
    #     if tick.vt_symbol in self.bar_recordings:
    #         bg = self.get_bar_generator(tick.vt_symbol)
    #         bg.update_tick(copy(tick))

    def exchange_time_list(self, data: Union[TickData, BarData]):
        """"""
        if data.exchange in [Exchange.CFFEX, ]:
            return INDEX_TIME
        elif data.exchange in [Exchange.SHFE, Exchange.CZCE, Exchange.DCE, Exchange.INE]:
            # 根据品种判断是白盘还是夜盘到1点还是夜盘到2点半
            # 如果是白盘品种
            # if
            # 如果是夜盘品种
            # if
            # 如果是夜盘2品种
            # if
            return FUT_NIGHT_TIME2
        else:
            return US_TIME

    # 如果继承此类，需要重写此方法
    @virtual
    def tick_filter(self, tick: TickData):
        return False

    def future_tick_filter(self, tick: TickData):
        ret = False
        current_time = datetime.now(shanghai).time()
        # if time(11, 31) > current_time > time(11, 30) and tick.datetime.minute == 30:
        if tick.datetime.hour == 11 and tick.datetime.minute == 30:
            ret = True
        # if time(10, 16) > current_time > time(10, 15) and tick.datetime.minute == 15:
        if tick.datetime.hour == 10 and tick.datetime.minute == 15:
            if tick.exchange in [Exchange.DCE, Exchange.CZCE, Exchange.SHFE]:
                ret = True
        # if time(15, 1) > current_time > time(15, 0) and tick.datetime.minute == 0:
        if tick.datetime.hour == 15 and tick.datetime.minute == 0:
            ret = True
        return ret

    @virtual
    def bar_filter(self, bar: BarData):
        return False

    def update_tick(self, tick: TickData):
        if not tick.last_price:
            return

        # reach time, send all bars
        last_dt = self.last_dts.get(tick.vt_symbol, None)

        if last_dt and last_dt.minute != tick.datetime.minute:
            if self.tick_filter(tick):
                return # 将pass改为return，不再执行新bar的生成和记录
            else:
                bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
                if bar:
                    bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                    self.record_bar(bar)
                    # 如果bar的datetime在交易时间内，则将bar赋值给last_bar
                    if not self.bar_filter(bar):
                        self.last_bars[tick.vt_symbol] = bar
                    self.bars[tick.vt_symbol] = None

        bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
        if not bar:
            bar = BarData(symbol=tick.symbol, exchange=tick.exchange, interval=Interval.MINUTE, datetime=tick.datetime,
                          gateway_name=tick.gateway_name,
                          open_price=tick.last_price, high_price=tick.last_price,
                          low_price=tick.last_price, close_price=tick.last_price, open_interest=tick.open_interest)
            self.bars[bar.vt_symbol] = bar
        else:
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.open_interest = tick.open_interest
            bar.datetime = tick.datetime

        last_tick: Optional[TickData] = self.last_ticks.get(tick.vt_symbol, None)
        if last_tick:
            bar.volume += max(tick.volume - last_tick.volume, 0)
            bar.turnover += max(tick.turnover - last_tick.turnover, 0)

        self.last_ticks[tick.vt_symbol] = tick
        self.last_dts[tick.vt_symbol] = tick.datetime

    @virtual
    def process_timer_event(self, event: Event):
        """"""
        pass

    def process_tick_event(self, event: Event):
        """"""
        # update cache
        tick = event.data
        time_str = tick.datetime.strftime('%Y%m%d%H%M%S')
        self.tick_time[tick.symbol] = time_str
        # self.write_log(f"@@@redis_engine: process_tick_event: {tick}")
        self.update_tick(tick)

    def tick_time_info(self):
        ret = "{"
        for k, v in self.tick_time.items():
            info = f"{k} {v}\n"
            ret = ret + info
        ret = ret + "}"
        return ret

    def process_contract_event(self, event: Event):
        """"""
        contract = event.data
        vt_symbol = contract.vt_symbol

        if vt_symbol in self.bar_recordings:
            self.subscribe(contract)

    def write_log(self, msg: str):
        """"""
        self.main_engine.write_log(msg)

    def put_event(self):
        """"""
        # bar_symbols = list(self.bar_recordings)
        # bar_symbols.sort()
        pass

    # 处理bar,供其它应用处理
    def record_bar(self, bar: BarData):
        """"""
        try:
            time_str = datetime.now().strftime("%Y-%m-%d-%H%M%S")
            if self.bar_filter(bar):
                pass
            else:
                self.write_log(
                    f" ======1======record bar memory: {time_str}: {bar.vt_symbol} {bar.datetime} o:{bar.open_price} c:{bar.close_price} h:{bar.high_price} l:{bar.low_price} v:{bar.volume} oi:{bar.open_interest}")
                self.write_log(f" ======2========put to event_engine: {bar}")
                event = Event(EVENT_BAR, bar)
                event2 = Event(EVENT_BAR_RECORD, copy(bar))
                self.event_engine.put(event)
                self.event_engine.put(event2)
        except Exception:
            msg = f"[record_bar] 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)

    # def get_bar_generator(self, vt_symbol: str):
    #     """"""
    #     bg = self.bar_generators.get(vt_symbol, None)

    #     if not bg:
    #         bg = BarGenerator(self.record_bar)
    #         self.bar_generators[vt_symbol] = bg
    #     return bg

    def subscribe(self, contract: ContractData):
        """"""
        req = SubscribeRequest(symbol=contract.symbol, exchange=contract.exchange)
        self.write_log(f"[subscribe] send subscribe req {contract.symbol}")
        self.main_engine.subscribe(req, contract.gateway_name)


# Ib，使用usstock_tick_filter
class BarGenEngineIb(BarGenEngine):
    """"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine)

    def tick_filter(self, tick: TickData):
        time_list = self.exchange_time_list(tick)
        end_list = [x[1] for x in time_list]
        ret = False
        current_time = datetime.now(eastern)
        for end_time in end_list:
            # 如果当前时间是美股盘后1分钟，且tick时间是美股盘后1分钟，则跳过
            # if time(16, 1) >= current_time >= time(16, 0) == data.datetime.time():
            if current_time.time() >= end_time == tick.datetime.time() and (
                    current_time - timedelta(minutes=1)).time() <= end_time:
                ret = True
        return ret

    def bar_filter(self, bar: BarData):
        time_list = self.exchange_time_list(bar)
        ret = True
        current_time = datetime.now(eastern)
        # bar_time从bar.datetime.time()获取，转为美东时区
        bar_time = bar.datetime.astimezone(eastern).time()
        # 如果当前时间不在time_list的每个(start,end)区间内，则ret=False，record_bar
        for start_time, end_time in time_list:
            if start_time <= bar_time <= end_time and (current_time - timedelta(minutes=1)).time() <= end_time:
                ret = False
        if ret:
            self.write_log(
                f"[ib_bar_filter] {bar.vt_symbol}不在交易时间: bar.datetime: {bar.datetime} current_time.time(): {current_time.time()}")
        return ret

    def process_timer_event(self, event: Event):
        """"""
        current = datetime.now(shanghai)  # 行情推送、bar生成、bar记录都使用上海时间
        current_eastern = current.astimezone(eastern)
        current_time = current.time()
        if current_time.second == 10:
            if not self.last_bars:
                self.write_log(f"[process_timer_event] last_bars is empty")
            else:
                in_time = False
                for start_time, end_time in US_TIME:
                    if start_time < current_eastern.time() and (
                            current_eastern - timedelta(minutes=1)).time() <= end_time:
                        in_time = True
                        break

                if in_time:
                    # 不用last_ticks原因：last_ticks可能是当前分钟收到的tick，而不是上一分钟的tick
                    # 每分钟检查last_bars的分钟是否是上一分钟，如果不是put一次fake_bars（其中每个vt_symbol对应的volume和turnover改为0，datetime改为上一分钟）以触发EVENT_BAR，保证每分钟都有bar
                    try:
                        self.write_log(f'[process_timer_event] init fake_bar from self.last_bars: {self.last_bars}')
                        for vt_symbol in self.last_bars.keys():
                            last_bar = self.last_bars[vt_symbol]
                            if current_eastern.date() not in TDAYS.get(last_bar.exchange.value, []):
                                self.write_log(
                                    f"[process_timer_event] 标的{vt_symbol}当前美东日期不在TDAYS的{last_bar.exchange.value}的日期列表中，当前美东日期: {datetime.now(eastern).date()}")
                                # 跳过当前vt_symbol
                                continue

                            # 如果上一个bar的分钟不是上一分钟，则put一次fake_bars
                            if last_bar.datetime.minute != (current - timedelta(minutes=1)).minute:
                                # 如果有self.bars且当前bar的分钟是当前分钟没结束，或者没有self.bars，则将上一个bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)
                                if vt_symbol not in self.bars.keys() or vt_symbol in self.bars.keys() and self.bars[vt_symbol].datetime.minute == current.minute:
                                    try:
                                        fake_bar = BarData(symbol=last_bar.symbol, exchange=last_bar.exchange,
                                                           interval=Interval.MINUTE,
                                                           # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0).astimezone(ZoneInfo('US/Eastern')),
                                                           # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0, tzinfo=ZoneInfo('Asia/Shanghai')),
                                                           datetime=(current - timedelta(minutes=1)).replace(second=0,
                                                                                                             microsecond=0),
                                                           gateway_name=last_bar.gateway_name if last_bar.gateway_name else 'IB',
                                                           open_price=last_bar.close_price,
                                                           high_price=last_bar.close_price,
                                                           low_price=last_bar.close_price,
                                                           close_price=last_bar.close_price,
                                                           open_interest=last_bar.open_interest,
                                                           volume=0, turnover=0)
                                        self.record_bar(fake_bar)
                                        last_bar = fake_bar
                                        self.write_log(
                                            f"[process_timer_event] success to update {vt_symbol} fake_bars from last_bars, {last_bar.datetime} c:{last_bar.close_price}")
                                    except Exception:
                                        self.write_log(
                                            f'[process_timer_event] error to update {vt_symbol} fake_bars: {traceback.format_exc()}')
                                        # 查看
                                        self.write_log(f'[process_timer_event] error last_bar: {last_bar}')
                                        self.write_log(
                                            f"[process_timer_event] error vt_symbol: {vt_symbol} current_time: {current_time} bar_time: {last_bar.datetime.time()}")
                                # 如果当前bar的分钟不是当前分钟（可能是上一分钟，也可能是上上分钟，只循环处理为上一分钟的情况，并将当前bar改为None）
                                # 当前bar改为None之前，则将当前bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)，再将当前bar改为None
                                else:
                                    self.last_bars[vt_symbol] = self.bars[vt_symbol]
                                    self.last_bars[vt_symbol].datetime = (current - timedelta(minutes=1)).replace(
                                        second=0, microsecond=0)
                                    self.record_bar(self.last_bars[vt_symbol])
                                    self.bars[vt_symbol] = None
                                    self.write_log(
                                        f"[process_timer_event] success to update {vt_symbol} fake_bars from bars, {self.last_bars[vt_symbol].datetime} c:{self.last_bars[vt_symbol].close_price}")

                    except Exception:
                        self.write_log(f'[process_timer_event] error to update fake_bars: {traceback.format_exc()}')
                        self.write_log(f'[process_timer_event] last_bars: {self.last_bars}')
                self.write_log(f"[process_timer_event] last_bars keys: {self.last_bars.keys()}")

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            symbol, exchange = extract_vt_symbol(vt_symbol)
            self.write_log(f"[subscribe_recording] prepare to send subscribe req：{vt_symbol}")
            req = SubscribeRequest(symbol=symbol, exchange=exchange)
            self.write_log(f"[subscribe_recording] send subscribe req {symbol}")
            self.main_engine.subscribe(req, "IB")
        except Exception:
            msg = f"[subscribe_recording] barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"[subscribe_recording] 添加K线记录成功：{vt_symbol}")
# Ib，使用usstock_tick_filter

class BarGenEngineIbFut(BarGenEngine):
    """"""
    setting_filename = "barGen_setting_fut.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine)

    def process_timer_event(self, event: Event):
        """"""
        current = datetime.now(shanghai)# 行情推送、bar生成、bar记录都使用上海时间
        current_eastern = current.astimezone(eastern)
        current_time = current.time()
        if current_time.second == 10:
            if not self.last_bars:
                self.write_log(f"[process_timer_event] last_bars is empty")
            else:
                # in_time = False
                # for start_time, end_time in US_TIME:
                #     if start_time < current_eastern.time() and (current_eastern - timedelta(minutes=1)).time() <= end_time:
                #         in_time = True
                #         break
                in_time = True

                if in_time:
                    # 不用last_ticks原因：last_ticks可能是当前分钟收到的tick，而不是上一分钟的tick
                    # 每分钟检查last_bars的分钟是否是上一分钟，如果不是put一次fake_bars（其中每个vt_symbol对应的volume和turnover改为0，datetime改为上一分钟）以触发EVENT_BAR，保证每分钟都有bar
                    try:
                        self.write_log(f'[process_timer_event] init fake_bar from self.last_bars: {self.last_bars}')
                        for vt_symbol in self.last_bars.keys():
                            last_bar = self.last_bars[vt_symbol]
                            if current_eastern.date() not in TDAYS.get(last_bar.exchange.value, []):
                                self.write_log(f"[process_timer_event] 标的{vt_symbol}当前美东日期不在TDAYS的{last_bar.exchange.value}的日期列表中，当前美东日期: {datetime.now(eastern).date()}")
                                # 跳过当前vt_symbol
                                continue

                            # 如果上一个bar的分钟不是上一分钟，则put一次fake_bars
                            if last_bar.datetime.minute != (current - timedelta(minutes=1)).minute:
                                # 如果有self.bars且当前bar的分钟是当前分钟没结束，或者没有self.bars，则将上一个bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)
                                if vt_symbol not in self.bars.keys() or vt_symbol in self.bars.keys() and self.bars[vt_symbol].datetime.minute == current.minute:
                                    try:
                                        fake_bar = BarData(symbol=last_bar.symbol, exchange=last_bar.exchange, interval=Interval.MINUTE,
                                                      # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0).astimezone(ZoneInfo('US/Eastern')),
                                                      # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0, tzinfo=ZoneInfo('Asia/Shanghai')),
                                                      datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0),
                                                      gateway_name=last_bar.gateway_name if last_bar.gateway_name else 'IB',
                                                      open_price=last_bar.close_price,
                                                      high_price=last_bar.close_price,
                                                      low_price=last_bar.close_price, close_price=last_bar.close_price,
                                                      open_interest=last_bar.open_interest,
                                                      volume=0, turnover=0)
                                        self.record_bar(fake_bar)
                                        last_bar = fake_bar
                                        self.write_log(f"[process_timer_event] success to update {vt_symbol} fake_bars from last_bars, {last_bar.datetime} c:{last_bar.close_price}")
                                    except Exception:
                                        self.write_log(f'[process_timer_event] error to update {vt_symbol} fake_bars: {traceback.format_exc()}')
                                        # 查看
                                        self.write_log(f'[process_timer_event] error last_bar: {last_bar}')
                                        self.write_log(f"[process_timer_event] error vt_symbol: {vt_symbol} current_time: {current_time} bar_time: {last_bar.datetime.time()}")
                                # 如果当前bar的分钟不是当前分钟（可能是上一分钟，也可能是上上分钟，只循环处理为上一分钟的情况，并将当前bar改为None）
                                # 当前bar改为None之前，则将当前bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)，再将当前bar改为None
                                else:
                                    self.last_bars[vt_symbol] = self.bars[vt_symbol]
                                    self.last_bars[vt_symbol].datetime = (current - timedelta(minutes=1)).replace(second=0, microsecond=0)
                                    self.record_bar(self.last_bars[vt_symbol])
                                    self.bars[vt_symbol] = None
                                    self.write_log(f"[process_timer_event] success to update {vt_symbol} fake_bars from bars, {self.last_bars[vt_symbol].datetime} c:{self.last_bars[vt_symbol].close_price}")

                    except Exception:
                        self.write_log(f'[process_timer_event] error to update fake_bars: {traceback.format_exc()}')
                        self.write_log(f'[process_timer_event] last_bars: {self.last_bars}')
                self.write_log(f"[process_timer_event] last_bars keys: {self.last_bars.keys()}")

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            symbol, exchange = extract_vt_symbol(vt_symbol)
            self.write_log(f"[subscribe_recording] prepare to send subscribe req：{vt_symbol}")
            req = SubscribeRequest(symbol=symbol, exchange=exchange)
            self.write_log(f"[subscribe_recording] send subscribe req {symbol}")
            self.main_engine.subscribe(req, "IB")
        except Exception:
            msg = f"[subscribe_recording] barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"[subscribe_recording] 添加K线记录成功：{vt_symbol}")

# Ctp，使用future_tick_filter
class BarGenEngineCtp(BarGenEngine):
    """"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine)

    def tick_filter(self, tick: TickData):
        return self.future_tick_filter(tick)

    def process_timer_event(self, event: Event):
        """"""
        tick_time_str = self.tick_time_info()
        self.write_log(f"[process_timer_event] {tick_time_str}")


# CTP期权，使用future_tick_filter
class BarGenEngineCtpOpt(BarGenEngine):
    """"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine)

    def tick_filter(self, tick: TickData):
        return self.future_tick_filter(tick)

    def bar_filter(self, bar: BarData):
        time_list = self.exchange_time_list(bar)
        ret = True
        current_time = datetime.now(shanghai)
        # 如果当前时间不在time_list的每个(start,end)区间内，则ret=False，record_bar
        for start_time, end_time in time_list:
            if start_time <= bar.datetime.time() <= end_time and (current_time - timedelta(minutes=1)).time() <= end_time:
                ret = False
        if ret:
            self.write_log(f"[opt_bar_filter] {bar.vt_symbol}不在交易时间: bar.datetime: {bar.datetime} current_time.time(): {current_time.time()}")
        return ret
        # return False # test lance

    def process_timer_event(self, event: Event):
        """"""
        current = datetime.now(shanghai)
        current_time = current.time()
        if current_time.second == 10:
            if not self.last_bars:
                self.write_log(f"[process_timer_event] last_bars is empty")
            else:
                in_time = False
                for start_time, end_time in FUT_NIGHT_TIME2:
                    if start_time < current_time and (current - timedelta(minutes=1)).time() <= end_time:
                        in_time = True
                        break

                if in_time:
                    # 不用last_ticks原因：last_ticks可能是当前分钟收到的tick，而不是上一分钟的tick
                    # 每分钟检查last_bars的分钟是否是上一分钟，如果不是put一次fake_bars（其中每个vt_symbol对应的volume和turnover改为0，datetime改为上一分钟）以触发EVENT_BAR，保证每分钟都有bar
                    try:
                        self.write_log(f'[process_timer_event] init fake_bar from self.last_bars: {self.last_bars}')
                        for vt_symbol in self.last_bars.keys():
                            last_bar = self.last_bars[vt_symbol]
                            if last_bar.datetime.minute != (current - timedelta(minutes=1)).minute:
                                # if self.bars[vt_symbol].datetime.minute == current.minute:
                                # 如果有self.bars且当前bar的分钟是当前分钟没结束，或者没有self.bars，则将上一个bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)
                                if vt_symbol not in self.bars.keys() or vt_symbol in self.bars.keys() and self.bars[vt_symbol].datetime.minute == current.minute:
                                    try:
                                        fake_bar = BarData(symbol=last_bar.symbol, exchange=last_bar.exchange, interval=Interval.MINUTE,
                                                      # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0).astimezone(ZoneInfo('Asia/Shanghai')),
                                                      datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0),
                                                      gateway_name=last_bar.gateway_name if last_bar.gateway_name else 'CTP',
                                                      open_price=last_bar.close_price,
                                                      high_price=last_bar.close_price,
                                                      low_price=last_bar.close_price, close_price=last_bar.close_price,
                                                      open_interest=last_bar.open_interest,
                                                      volume=0, turnover=0)
                                        self.record_bar(fake_bar)
                                        last_bar = fake_bar
                                        self.write_log(f"[process_timer_event] success to update {vt_symbol} fake_bars from last_bars, {last_bar.datetime} c:{last_bar.close_price}")
                                    except Exception:
                                        self.write_log(f'[process_timer_event] error to update {vt_symbol} fake_bars: {traceback.format_exc()}')
                                        # 查看
                                        self.write_log(f'[process_timer_event] error last_bar: {last_bar}')
                                        self.write_log(f"[process_timer_event] error vt_symbol: {vt_symbol} current_time: {current_time} bar_time: {last_bar.datetime.time()}")
                                else:
                                    # 如果当前bar的分钟不是当前分钟，则将当前bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)，清空bars
                                    self.last_bars[vt_symbol] = self.bars[vt_symbol]
                                    self.last_bars[vt_symbol].datetime = (current - timedelta(minutes=1)).replace(second=0, microsecond=0)
                                    self.record_bar(self.last_bars[vt_symbol])
                                    self.bars[vt_symbol] = None
                                    # self.bars[vt_symbol].volume = 0
                                    # self.bars[vt_symbol].turnover = 0
                                    # self.last_ticks[vt_symbol] = None # test lance
                                    # self.last_dts[vt_symbol] = None # test lance
                                    self.write_log(f"[process_timer_event] success to update {vt_symbol} fake_bars from bars, {self.last_bars[vt_symbol].datetime} c:{self.last_bars[vt_symbol].close_price}")
                    except Exception:
                        self.write_log(f'[process_timer_event] error to update fake_bars: {traceback.format_exc()}')
                        self.write_log(f'[process_timer_event] last_bars: {self.last_bars}')
                self.write_log(f"[process_timer_event] last_bars keys: {self.last_bars.keys()}")