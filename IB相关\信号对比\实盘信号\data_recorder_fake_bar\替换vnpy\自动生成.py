#!/usr/bin/env python3
"""
自动解析exchange.txt文件并生成Exchange枚举代码
"""

def parse_exchange_file(file_path):
    """解析exchange.txt文件"""
    exchanges_by_region = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 跳过标题行
    for line in lines[1:]:
        line = line.strip()
        if not line:
            continue
            
        parts = line.split('|')
        if len(parts) != 3:
            continue
            
        description = parts[0].strip()
        products = parts[1].strip()
        region = parts[2].strip()
        
        # 提取交易所代码（括号前的部分）
        if '(' in description:
            exchange_code = description.split('(')[0].strip()
        else:
            exchange_code = description.strip()
            
        if region not in exchanges_by_region:
            exchanges_by_region[region] = {}
            
        if products not in exchanges_by_region[region]:
            exchanges_by_region[region][products] = []
            
        exchanges_by_region[region][products].append({
            'code': exchange_code,
            'description': description
        })
    
    return exchanges_by_region

def generate_exchange_enum(exchanges_by_region):
    """生成Exchange枚举代码"""
    lines = []
    lines.append('class Exchange(Enum):')
    lines.append('    """')
    lines.append('    Exchange.')
    lines.append('    """')
    
    # 第一遍：收集所有交易所代码，识别重复
    seen_codes = set()
    duplicates = set()  # 存储重复的交易所代码
    duplicate_infos = {}  # 存储重复交易所的详细信息
    
    sorted_regions = sorted(exchanges_by_region.keys())
    
    for region in sorted_regions:
        products_dict = exchanges_by_region[region]
        sorted_products = sorted(products_dict.keys())
        
        for products in sorted_products:
            exchanges = sorted(products_dict[products], key=lambda x: x['code'])
            
            for exchange in exchanges:
                code = exchange['code'].replace(".", "_")
                
                if code in seen_codes:
                    duplicates.add(code)
                    if code not in duplicate_infos:
                        duplicate_infos[code] = []
                    duplicate_infos[code].append({
                        'region': region,
                        'products': products,
                        'original_code': exchange['code'],
                        'description': exchange['description']
                    })
                else:
                    seen_codes.add(code)
                    # 为第一次出现的也记录信息，以备后用
                    if code not in duplicate_infos:
                        duplicate_infos[code] = []
                    duplicate_infos[code].append({
                        'region': region,
                        'products': products,
                        'original_code': exchange['code'],
                        'description': exchange['description']
                    })
    
    # 第二遍：生成代码，重复的注释掉，非重复的正常生成
    for region in sorted_regions:
        lines.append('')
        lines.append(f'    # {region}')
        
        # 按产品类型排序
        products_dict = exchanges_by_region[region]
        sorted_products = sorted(products_dict.keys())
        
        for products in sorted_products:
            lines.append(f'    # {products}')
            
            # 按交易所代码排序
            exchanges = sorted(products_dict[products], key=lambda x: x['code'])
            
            for exchange in exchanges:
                code = exchange['code'].replace(".", "_")
                original_code = exchange['code']
                desc = exchange['description']
                
                if code in duplicates:
                    # 重复的交易所，注释掉
                    lines.append(f'    # {code} = "{original_code}"         # {desc} (duplicate)')
                else:
                    # 非重复的交易所，正常生成
                    lines.append(f'    {code} = "{original_code}"         # {desc}')
    
    # 在最后添加重复的交易所的统一定义（不注释）
    if duplicates:
        lines.append('')
        lines.append('    # Unified definitions for duplicate exchange codes:')
        for code in sorted(duplicates):
            # 显示该交易所出现在哪些地区/产品中
            lines.append(f'    # {code} appears in multiple regions/products:')
            for info in duplicate_infos[code]:
                lines.append(f'    #   {info["region"]} - {info["products"]}: {info["description"]}')
            
            # 使用第一次出现的定义
            first_info = duplicate_infos[code][0]
            lines.append(f'    {code} = "{first_info["original_code"]}"         # {first_info["description"]}')
            lines.append('')
    
    return '\n'.join(lines)

def main():
    """主函数"""
    file_path = 'exchange.txt'
    
    try:
        exchanges_by_region = parse_exchange_file(file_path)
        enum_code = generate_exchange_enum(exchanges_by_region)
        
        # 输出到文件
        with open('generated_exchange_enum.py', 'w', encoding='utf-8') as f:
            f.write(enum_code)
        
        print("Exchange枚举代码已生成到 generated_exchange_enum.py")
        
        # 也打印到控制台
        print("\n生成的代码:")
        print(enum_code)
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
    except Exception as e:
        print(f"错误：{e}")

if __name__ == "__main__":
    main()