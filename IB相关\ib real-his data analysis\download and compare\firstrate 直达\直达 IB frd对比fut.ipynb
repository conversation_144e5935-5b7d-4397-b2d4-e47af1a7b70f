{"cells": [{"cell_type": "code", "execution_count": 20, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21960\\531268364.py:36: FutureWarning: The default value of regex will change from True to False in a future version.\n", "  df['symbol'] = df['symbol'].str.replace(r'-\\d{8}-', '-')\n"]}, {"data": {"text/plain": "           symbol             datetime  open_price  high_price  low_price  \\\n9360   ES-USD-FUT  2023-08-28 21:30:00     4437.75     4438.75    4434.75   \n9361   ES-USD-FUT  2023-08-28 21:31:00     4435.00     4438.50    4435.00   \n9362   ES-USD-FUT  2023-08-28 21:32:00     4437.00     4440.25    4437.00   \n9363   ES-USD-FUT  2023-08-28 21:33:00     4439.75     4443.75    4439.25   \n9364   ES-USD-FUT  2023-08-28 21:34:00     4443.25     4445.00    4443.00   \n...           ...                  ...         ...         ...        ...   \n10135  NQ-USD-FUT  2023-08-29 03:55:00    15079.00    15091.00   15073.20   \n10136  NQ-USD-FUT  2023-08-29 03:56:00    15090.00    15094.00   15083.80   \n10137  NQ-USD-FUT  2023-08-29 03:57:00    15084.80    15092.00   15083.80   \n10138  NQ-USD-FUT  2023-08-29 03:58:00    15088.50    15094.50   15087.20   \n10139  NQ-USD-FUT  2023-08-29 03:59:00    15094.20    15099.50   15085.00   \n\n       close_price   volume  \n9360       4434.75  10731.0  \n9361       4437.00   7262.0  \n9362       4439.50   6185.0  \n9363       4443.25   7937.0  \n9364       4443.25   7252.0  \n...            ...      ...  \n10135     15090.00   1470.0  \n10136     15085.00   1495.0  \n10137     15088.00    831.0  \n10138     15094.50   1372.0  \n10139     15091.50   8727.0  \n\n[780 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>9360</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:30:00</td>\n      <td>4437.75</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10731.0</td>\n    </tr>\n    <tr>\n      <th>9361</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:31:00</td>\n      <td>4435.00</td>\n      <td>4438.50</td>\n      <td>4435.00</td>\n      <td>4437.00</td>\n      <td>7262.0</td>\n    </tr>\n    <tr>\n      <th>9362</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:32:00</td>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4437.00</td>\n      <td>4439.50</td>\n      <td>6185.0</td>\n    </tr>\n    <tr>\n      <th>9363</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:33:00</td>\n      <td>4439.75</td>\n      <td>4443.75</td>\n      <td>4439.25</td>\n      <td>4443.25</td>\n      <td>7937.0</td>\n    </tr>\n    <tr>\n      <th>9364</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:34:00</td>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.25</td>\n      <td>7252.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>10135</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 03:55:00</td>\n      <td>15079.00</td>\n      <td>15091.00</td>\n      <td>15073.20</td>\n      <td>15090.00</td>\n      <td>1470.0</td>\n    </tr>\n    <tr>\n      <th>10136</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 03:56:00</td>\n      <td>15090.00</td>\n      <td>15094.00</td>\n      <td>15083.80</td>\n      <td>15085.00</td>\n      <td>1495.0</td>\n    </tr>\n    <tr>\n      <th>10137</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 03:57:00</td>\n      <td>15084.80</td>\n      <td>15092.00</td>\n      <td>15083.80</td>\n      <td>15088.00</td>\n      <td>831.0</td>\n    </tr>\n    <tr>\n      <th>10138</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 03:58:00</td>\n      <td>15088.50</td>\n      <td>15094.50</td>\n      <td>15087.20</td>\n      <td>15094.50</td>\n      <td>1372.0</td>\n    </tr>\n    <tr>\n      <th>10139</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 03:59:00</td>\n      <td>15094.20</td>\n      <td>15099.50</td>\n      <td>15085.00</td>\n      <td>15091.50</td>\n      <td>8727.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>780 rows × 7 columns</p>\n</div>"}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime, timedelta, time\n", "from urllib.parse import quote as urlquote\n", "\n", "# 录制数据库配置\n", "username = 'root'\n", "password = 'p0o9i8u7'\n", "dbHost = '**************'\n", "dbPort = 3306\n", "dbName = 'vnpyibhis'\n", "DATABASE_URI = f'mysql+pymysql://{username}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}'\n", "\n", "# 使用pandas和sql语句，从数据库中读取数据保存到csv文件中\n", "import pandas as pd\n", "from sqlalchemy import create_engine\n", "engine = create_engine(DATABASE_URI)\n", "# 前一天下午9点到今天上午6点的数据\n", "# today = datetime.now().date()\n", "today = datetime(2023, 8, 29).date()\n", "yesterday = today - <PERSON><PERSON><PERSON>(days=1)\n", "\n", "yesterday_9pm_str = datetime.combine(yesterday, time(21, 30)).strftime('%Y-%m-%d %H:%M:%S')\n", "today_4am_str = datetime.combine(today, time(4, 0)).strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "sql = f\"select * from dbbardata where datetime >= '{yesterday_9pm_str}' and datetime <= '{today_4am_str}'\"\n", "df = pd.read_sql(sql, engine)\n", "# df.to_csv(f'{today}.csv', index=False)\n", "# from IPython.display import display\n", "# display(df)\n", "# 筛选symbol为ES-20230915-USD-FUT和NQ-20230915-USD-FUT的数据\n", "df = df[df['symbol'].isin(['ES-20230915-USD-FUT', 'NQ-20230915-USD-FUT'])]\n", "# 只保留symbol、datetime、open_price、high_price、low_price、close_price、volume列\n", "df = df[['symbol', 'datetime', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']]\n", "# 将df的datetime列的格式转为2023-08-28 15:00:00\n", "df['datetime'] = pd.to_datetime(df['datetime']).dt.strftime('%Y-%m-%d %H:%M:%S')\n", "# df的symbol列由ES-20230915-USD-FUT、NQ-20230915-USD-FUT转为ES-USD-FUT、NQ-USD-FUT\n", "df['symbol'] = df['symbol'].str.replace(r'-\\d{8}-', '-')\n", "df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:41:06.386416300Z", "start_time": "2023-08-30T06:41:01.230115100Z"}}, "id": "e3e18f1dfb591a5e"}, {"cell_type": "code", "execution_count": 21, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21960\\1536812164.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21960\\1536812164.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_frd = df_frd.append(df_frd_symbol)\n"]}, {"data": {"text/plain": "                  datetime  open_price  high_price  low_price  close_price  \\\n14724  2023-08-28 21:30:00     4437.75     4438.75    4434.75      4434.75   \n14725  2023-08-28 21:31:00     4435.00     4438.50    4435.00      4437.00   \n14726  2023-08-28 21:32:00     4437.00     4440.25    4437.00      4439.50   \n14727  2023-08-28 21:33:00     4439.75     4443.75    4439.25      4443.25   \n14728  2023-08-28 21:34:00     4443.25     4445.00    4443.00      4443.00   \n...                    ...         ...         ...        ...          ...   \n15115  2023-08-29 03:56:00    15090.00    15094.00   15083.75     15085.00   \n15116  2023-08-29 03:57:00    15084.75    15092.00   15083.75     15088.00   \n15117  2023-08-29 03:58:00    15088.50    15094.50   15087.25     15094.50   \n15118  2023-08-29 03:59:00    15094.25    15099.50   15085.00     15090.25   \n15119  2023-08-29 04:00:00    15091.25    15092.25   15085.50     15090.00   \n\n       volume      symbol  \n14724   10731  ES-USD-FUT  \n14725    7262  ES-USD-FUT  \n14726    6185  ES-USD-FUT  \n14727    7937  ES-USD-FUT  \n14728    7253  ES-USD-FUT  \n...       ...         ...  \n15115    1495  NQ-USD-FUT  \n15116     831  NQ-USD-FUT  \n15117    1372  NQ-USD-FUT  \n15118    8755  NQ-USD-FUT  \n15119    2589  NQ-USD-FUT  \n\n[782 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>datetime</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n      <th>symbol</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>14724</th>\n      <td>2023-08-28 21:30:00</td>\n      <td>4437.75</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10731</td>\n      <td>ES-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>14725</th>\n      <td>2023-08-28 21:31:00</td>\n      <td>4435.00</td>\n      <td>4438.50</td>\n      <td>4435.00</td>\n      <td>4437.00</td>\n      <td>7262</td>\n      <td>ES-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>14726</th>\n      <td>2023-08-28 21:32:00</td>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4437.00</td>\n      <td>4439.50</td>\n      <td>6185</td>\n      <td>ES-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>14727</th>\n      <td>2023-08-28 21:33:00</td>\n      <td>4439.75</td>\n      <td>4443.75</td>\n      <td>4439.25</td>\n      <td>4443.25</td>\n      <td>7937</td>\n      <td>ES-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>14728</th>\n      <td>2023-08-28 21:34:00</td>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.00</td>\n      <td>7253</td>\n      <td>ES-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>15115</th>\n      <td>2023-08-29 03:56:00</td>\n      <td>15090.00</td>\n      <td>15094.00</td>\n      <td>15083.75</td>\n      <td>15085.00</td>\n      <td>1495</td>\n      <td>NQ-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>15116</th>\n      <td>2023-08-29 03:57:00</td>\n      <td>15084.75</td>\n      <td>15092.00</td>\n      <td>15083.75</td>\n      <td>15088.00</td>\n      <td>831</td>\n      <td>NQ-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>15117</th>\n      <td>2023-08-29 03:58:00</td>\n      <td>15088.50</td>\n      <td>15094.50</td>\n      <td>15087.25</td>\n      <td>15094.50</td>\n      <td>1372</td>\n      <td>NQ-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>15118</th>\n      <td>2023-08-29 03:59:00</td>\n      <td>15094.25</td>\n      <td>15099.50</td>\n      <td>15085.00</td>\n      <td>15090.25</td>\n      <td>8755</td>\n      <td>NQ-USD-FUT</td>\n    </tr>\n    <tr>\n      <th>15119</th>\n      <td>2023-08-29 04:00:00</td>\n      <td>15091.25</td>\n      <td>15092.25</td>\n      <td>15085.50</td>\n      <td>15090.00</td>\n      <td>2589</td>\n      <td>NQ-USD-FUT</td>\n    </tr>\n  </tbody>\n</table>\n<p>782 rows × 7 columns</p>\n</div>"}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import pytz\n", "# 从frd_stock_sample文件夹下的AMZN_1min_sample.csv、AAPL_1min_sample.csv、TSLA_1min_sample.csv、MSFT_1min_sample.csv文件中读取数据，AMZN等作为symbol列的值\n", "df_frd = pd.DataFrame()\n", "for symbol in ['ES', 'NQ']:\n", "    df_frd_symbol = pd.read_csv(f'frd_futures_sample/{symbol}_1min_sample.csv')\n", "    df_frd_symbol['symbol'] = f'{symbol}-USD-FUT'\n", "    df_frd = df_frd.append(df_frd_symbol)\n", "# timestamp列（格式为2023-08-28 15:00:00）设置时区为美东，然后转为上海时区，再格式化为2023-08-28 15:00:00格式\n", "df_frd['timestamp'] = pd.to_datetime(df_frd['timestamp']).dt.tz_localize('US/Eastern').dt.tz_convert('Asia/Shanghai').dt.strftime('%Y-%m-%d %H:%M:%S')\n", "# 筛选timestamp为2023-08-28 21:30:00到2023-08-29 04:00:00的数据\n", "df_frd = df_frd[(df_frd['timestamp'] >= yesterday_9pm_str) & (df_frd['timestamp'] <= today_4am_str)]\n", "# 重命名各列\n", "df_frd = df_frd.rename(columns={'timestamp': 'datetime', 'open': 'open_price', 'high': 'high_price', 'low': 'low_price', 'close': 'close_price', 'volume': 'volume'})\n", "df_frd"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:41:19.304749800Z", "start_time": "2023-08-30T06:41:18.957595200Z"}}, "id": "94a36b9b6c9d5c7d"}, {"cell_type": "code", "execution_count": 23, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21960\\1083894140.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_zhida = df_zhida.append(df_zhida_symbol)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21960\\1083894140.py:7: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df_zhida = df_zhida.append(df_zhida_symbol)\n"]}, {"data": {"text/plain": "          symbol             datetime  open_price  high_price  low_price  \\\n929   ES-USD-FUT  2023-08-28 21:30:00     4438.00     4438.75    4434.75   \n930   ES-USD-FUT  2023-08-28 21:31:00     4435.25     4438.50    4437.00   \n931   ES-USD-FUT  2023-08-28 21:32:00     4437.00     4440.25    4439.75   \n932   ES-USD-FUT  2023-08-28 21:33:00     4439.50     4443.75    4443.25   \n933   ES-USD-FUT  2023-08-28 21:34:00     4443.25     4445.00    4443.00   \n...          ...                  ...         ...         ...        ...   \n1134  NQ-USD-FUT  2023-08-29 00:55:00    15054.25    15062.00   15059.25   \n1135  NQ-USD-FUT  2023-08-29 00:56:00    15059.50    15063.25   15062.00   \n1136  NQ-USD-FUT  2023-08-29 00:57:00    15062.25    15069.75   15068.75   \n1137  NQ-USD-FUT  2023-08-29 00:58:00    15069.25    15071.50   15070.00   \n1138  NQ-USD-FUT  2023-08-29 00:59:00    15070.25    15074.75   15072.25   \n\n      close_price  volume  \n929       4434.75   10529  \n930       4435.00    7137  \n931       4437.00    6094  \n932       4439.25    7897  \n933       4443.00    7235  \n...           ...     ...  \n1134     15053.00     781  \n1135     15058.50     802  \n1136     15061.50    1338  \n1137     15066.00     825  \n1138     15069.25    1220  \n\n[420 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>929</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:30:00</td>\n      <td>4438.00</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10529</td>\n    </tr>\n    <tr>\n      <th>930</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:31:00</td>\n      <td>4435.25</td>\n      <td>4438.50</td>\n      <td>4437.00</td>\n      <td>4435.00</td>\n      <td>7137</td>\n    </tr>\n    <tr>\n      <th>931</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:32:00</td>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4439.75</td>\n      <td>4437.00</td>\n      <td>6094</td>\n    </tr>\n    <tr>\n      <th>932</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:33:00</td>\n      <td>4439.50</td>\n      <td>4443.75</td>\n      <td>4443.25</td>\n      <td>4439.25</td>\n      <td>7897</td>\n    </tr>\n    <tr>\n      <th>933</th>\n      <td>ES-USD-FUT</td>\n      <td>2023-08-28 21:34:00</td>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.00</td>\n      <td>7235</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>1134</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 00:55:00</td>\n      <td>15054.25</td>\n      <td>15062.00</td>\n      <td>15059.25</td>\n      <td>15053.00</td>\n      <td>781</td>\n    </tr>\n    <tr>\n      <th>1135</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 00:56:00</td>\n      <td>15059.50</td>\n      <td>15063.25</td>\n      <td>15062.00</td>\n      <td>15058.50</td>\n      <td>802</td>\n    </tr>\n    <tr>\n      <th>1136</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 00:57:00</td>\n      <td>15062.25</td>\n      <td>15069.75</td>\n      <td>15068.75</td>\n      <td>15061.50</td>\n      <td>1338</td>\n    </tr>\n    <tr>\n      <th>1137</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 00:58:00</td>\n      <td>15069.25</td>\n      <td>15071.50</td>\n      <td>15070.00</td>\n      <td>15066.00</td>\n      <td>825</td>\n    </tr>\n    <tr>\n      <th>1138</th>\n      <td>NQ-USD-FUT</td>\n      <td>2023-08-29 00:59:00</td>\n      <td>15070.25</td>\n      <td>15074.75</td>\n      <td>15072.25</td>\n      <td>15069.25</td>\n      <td>1220</td>\n    </tr>\n  </tbody>\n</table>\n<p>420 rows × 7 columns</p>\n</div>"}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从直达文件夹下的ES2309.txt、NQ2309.txt文件中读取数据，ES2309.txt、NQ2309.txt文件中的数据格式为：2023-08-28 21:30:00,4525.75,4525.75,4525.75,4525.75,0，没有列名，各列命名为：datetime、open_price、low_price、high_price、close_price、volume、turnover。\n", "df_zhida = pd.DataFrame()\n", "for symbol in ['ES', 'NQ']:\n", "    df_zhida_symbol = pd.read_csv(f'直达/{symbol}2309.txt', header=None)\n", "    df_zhida_symbol.columns = ['datetime', 'open_price', 'low_price', 'high_price', 'close_price', 'volume', 'turnover']\n", "    df_zhida_symbol['symbol'] = f'{symbol}-USD-FUT'\n", "    df_zhida = df_zhida.append(df_zhida_symbol)\n", "# 只保留symbol、datetime、open_price、high_price、low_price、close_price、volume列\n", "df_zhida = df_zhida[['symbol', 'datetime', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']]\n", "# 将datetime列时间减去1分钟，再转为2023-08-28 15:00:00格式\n", "df_zhida['datetime'] = pd.to_datetime(df_zhida['datetime']) - timed<PERSON>ta(minutes=1)\n", "df_zhida['datetime'] = df_zhida['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')\n", "# 筛选datetime为2023-08-28 21:30:00到2023-08-29 04:00:00的数据\n", "df_zhida = df_zhida[(df_zhida['datetime'] >= yesterday_9pm_str) & (df_zhida['datetime'] <= today_4am_str)]\n", "df_zhida"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:43:40.369502700Z", "start_time": "2023-08-30T06:43:40.293187400Z"}}, "id": "118c609e7c4f8d93"}, {"cell_type": "code", "execution_count": 24, "outputs": [{"data": {"text/plain": "                                open_price  high_price  low_price  \\\nsymbol     datetime                                                 \nES-USD-FUT 2023-08-28 21:30:00     4437.75     4438.75    4434.75   \n           2023-08-28 21:31:00     4435.00     4438.50    4435.00   \n           2023-08-28 21:32:00     4437.00     4440.25    4437.00   \n           2023-08-28 21:33:00     4439.75     4443.75    4439.25   \n           2023-08-28 21:34:00     4443.25     4445.00    4443.00   \n...                                    ...         ...        ...   \nNQ-USD-FUT 2023-08-29 03:55:00    15079.00    15091.00   15073.20   \n           2023-08-29 03:56:00    15090.00    15094.00   15083.80   \n           2023-08-29 03:57:00    15084.80    15092.00   15083.80   \n           2023-08-29 03:58:00    15088.50    15094.50   15087.20   \n           2023-08-29 03:59:00    15094.20    15099.50   15085.00   \n\n                                close_price   volume  \nsymbol     datetime                                   \nES-USD-FUT 2023-08-28 21:30:00      4434.75  10731.0  \n           2023-08-28 21:31:00      4437.00   7262.0  \n           2023-08-28 21:32:00      4439.50   6185.0  \n           2023-08-28 21:33:00      4443.25   7937.0  \n           2023-08-28 21:34:00      4443.25   7252.0  \n...                                     ...      ...  \nNQ-USD-FUT 2023-08-29 03:55:00     15090.00   1470.0  \n           2023-08-29 03:56:00     15085.00   1495.0  \n           2023-08-29 03:57:00     15088.00    831.0  \n           2023-08-29 03:58:00     15094.50   1372.0  \n           2023-08-29 03:59:00     15091.50   8727.0  \n\n[780 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">ES-USD-FUT</th>\n      <th>2023-08-28 21:30:00</th>\n      <td>4437.75</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10731.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <td>4435.00</td>\n      <td>4438.50</td>\n      <td>4435.00</td>\n      <td>4437.00</td>\n      <td>7262.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4437.00</td>\n      <td>4439.50</td>\n      <td>6185.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <td>4439.75</td>\n      <td>4443.75</td>\n      <td>4439.25</td>\n      <td>4443.25</td>\n      <td>7937.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.25</td>\n      <td>7252.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">NQ-USD-FUT</th>\n      <th>2023-08-29 03:55:00</th>\n      <td>15079.00</td>\n      <td>15091.00</td>\n      <td>15073.20</td>\n      <td>15090.00</td>\n      <td>1470.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:56:00</th>\n      <td>15090.00</td>\n      <td>15094.00</td>\n      <td>15083.80</td>\n      <td>15085.00</td>\n      <td>1495.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:57:00</th>\n      <td>15084.80</td>\n      <td>15092.00</td>\n      <td>15083.80</td>\n      <td>15088.00</td>\n      <td>831.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:58:00</th>\n      <td>15088.50</td>\n      <td>15094.50</td>\n      <td>15087.20</td>\n      <td>15094.50</td>\n      <td>1372.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:59:00</th>\n      <td>15094.20</td>\n      <td>15099.50</td>\n      <td>15085.00</td>\n      <td>15091.50</td>\n      <td>8727.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>780 rows × 5 columns</p>\n</div>"}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 以symbol、datetime为索引\n", "df = df.set_index(['symbol', 'datetime'])\n", "df_frd = df_frd.set_index(['symbol', 'datetime'])\n", "df_zhida = df_zhida.set_index(['symbol', 'datetime'])\n", "df"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:44:22.309516300Z", "start_time": "2023-08-30T06:44:22.243925Z"}}, "id": "fcfbf37d82d294ea"}, {"cell_type": "code", "execution_count": 25, "outputs": [{"data": {"text/plain": "                                open_price  high_price  low_price  \\\nsymbol     datetime                                                 \nES-USD-FUT 2023-08-28 21:30:00     4437.75     4438.75    4434.75   \n           2023-08-28 21:31:00     4435.00     4438.50    4435.00   \n           2023-08-28 21:32:00     4437.00     4440.25    4437.00   \n           2023-08-28 21:33:00     4439.75     4443.75    4439.25   \n           2023-08-28 21:34:00     4443.25     4445.00    4443.00   \n...                                    ...         ...        ...   \nNQ-USD-FUT 2023-08-29 03:56:00    15090.00    15094.00   15083.75   \n           2023-08-29 03:57:00    15084.75    15092.00   15083.75   \n           2023-08-29 03:58:00    15088.50    15094.50   15087.25   \n           2023-08-29 03:59:00    15094.25    15099.50   15085.00   \n           2023-08-29 04:00:00    15091.25    15092.25   15085.50   \n\n                                close_price  volume  \nsymbol     datetime                                  \nES-USD-FUT 2023-08-28 21:30:00      4434.75   10731  \n           2023-08-28 21:31:00      4437.00    7262  \n           2023-08-28 21:32:00      4439.50    6185  \n           2023-08-28 21:33:00      4443.25    7937  \n           2023-08-28 21:34:00      4443.00    7253  \n...                                     ...     ...  \nNQ-USD-FUT 2023-08-29 03:56:00     15085.00    1495  \n           2023-08-29 03:57:00     15088.00     831  \n           2023-08-29 03:58:00     15094.50    1372  \n           2023-08-29 03:59:00     15090.25    8755  \n           2023-08-29 04:00:00     15090.00    2589  \n\n[782 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">ES-USD-FUT</th>\n      <th>2023-08-28 21:30:00</th>\n      <td>4437.75</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10731</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <td>4435.00</td>\n      <td>4438.50</td>\n      <td>4435.00</td>\n      <td>4437.00</td>\n      <td>7262</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4437.00</td>\n      <td>4439.50</td>\n      <td>6185</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <td>4439.75</td>\n      <td>4443.75</td>\n      <td>4439.25</td>\n      <td>4443.25</td>\n      <td>7937</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.00</td>\n      <td>7253</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">NQ-USD-FUT</th>\n      <th>2023-08-29 03:56:00</th>\n      <td>15090.00</td>\n      <td>15094.00</td>\n      <td>15083.75</td>\n      <td>15085.00</td>\n      <td>1495</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:57:00</th>\n      <td>15084.75</td>\n      <td>15092.00</td>\n      <td>15083.75</td>\n      <td>15088.00</td>\n      <td>831</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:58:00</th>\n      <td>15088.50</td>\n      <td>15094.50</td>\n      <td>15087.25</td>\n      <td>15094.50</td>\n      <td>1372</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 03:59:00</th>\n      <td>15094.25</td>\n      <td>15099.50</td>\n      <td>15085.00</td>\n      <td>15090.25</td>\n      <td>8755</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 04:00:00</th>\n      <td>15091.25</td>\n      <td>15092.25</td>\n      <td>15085.50</td>\n      <td>15090.00</td>\n      <td>2589</td>\n    </tr>\n  </tbody>\n</table>\n<p>782 rows × 5 columns</p>\n</div>"}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_frd"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:44:24.511676600Z", "start_time": "2023-08-30T06:44:24.485509Z"}}, "id": "830cb8b4ae88d87"}, {"cell_type": "code", "execution_count": 26, "outputs": [{"data": {"text/plain": "                                open_price  high_price  low_price  \\\nsymbol     datetime                                                 \nES-USD-FUT 2023-08-28 21:30:00     4438.00     4438.75    4434.75   \n           2023-08-28 21:31:00     4435.25     4438.50    4437.00   \n           2023-08-28 21:32:00     4437.00     4440.25    4439.75   \n           2023-08-28 21:33:00     4439.50     4443.75    4443.25   \n           2023-08-28 21:34:00     4443.25     4445.00    4443.00   \n...                                    ...         ...        ...   \nNQ-USD-FUT 2023-08-29 00:55:00    15054.25    15062.00   15059.25   \n           2023-08-29 00:56:00    15059.50    15063.25   15062.00   \n           2023-08-29 00:57:00    15062.25    15069.75   15068.75   \n           2023-08-29 00:58:00    15069.25    15071.50   15070.00   \n           2023-08-29 00:59:00    15070.25    15074.75   15072.25   \n\n                                close_price  volume  \nsymbol     datetime                                  \nES-USD-FUT 2023-08-28 21:30:00      4434.75   10529  \n           2023-08-28 21:31:00      4435.00    7137  \n           2023-08-28 21:32:00      4437.00    6094  \n           2023-08-28 21:33:00      4439.25    7897  \n           2023-08-28 21:34:00      4443.00    7235  \n...                                     ...     ...  \nNQ-USD-FUT 2023-08-29 00:55:00     15053.00     781  \n           2023-08-29 00:56:00     15058.50     802  \n           2023-08-29 00:57:00     15061.50    1338  \n           2023-08-29 00:58:00     15066.00     825  \n           2023-08-29 00:59:00     15069.25    1220  \n\n[420 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">ES-USD-FUT</th>\n      <th>2023-08-28 21:30:00</th>\n      <td>4438.00</td>\n      <td>4438.75</td>\n      <td>4434.75</td>\n      <td>4434.75</td>\n      <td>10529</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <td>4435.25</td>\n      <td>4438.50</td>\n      <td>4437.00</td>\n      <td>4435.00</td>\n      <td>7137</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <td>4437.00</td>\n      <td>4440.25</td>\n      <td>4439.75</td>\n      <td>4437.00</td>\n      <td>6094</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <td>4439.50</td>\n      <td>4443.75</td>\n      <td>4443.25</td>\n      <td>4439.25</td>\n      <td>7897</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <td>4443.25</td>\n      <td>4445.00</td>\n      <td>4443.00</td>\n      <td>4443.00</td>\n      <td>7235</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th rowspan=\"5\" valign=\"top\">NQ-USD-FUT</th>\n      <th>2023-08-29 00:55:00</th>\n      <td>15054.25</td>\n      <td>15062.00</td>\n      <td>15059.25</td>\n      <td>15053.00</td>\n      <td>781</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:56:00</th>\n      <td>15059.50</td>\n      <td>15063.25</td>\n      <td>15062.00</td>\n      <td>15058.50</td>\n      <td>802</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:57:00</th>\n      <td>15062.25</td>\n      <td>15069.75</td>\n      <td>15068.75</td>\n      <td>15061.50</td>\n      <td>1338</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:58:00</th>\n      <td>15069.25</td>\n      <td>15071.50</td>\n      <td>15070.00</td>\n      <td>15066.00</td>\n      <td>825</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:59:00</th>\n      <td>15070.25</td>\n      <td>15074.75</td>\n      <td>15072.25</td>\n      <td>15069.25</td>\n      <td>1220</td>\n    </tr>\n  </tbody>\n</table>\n<p>420 rows × 5 columns</p>\n</div>"}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_zhida"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:44:27.997581400Z", "start_time": "2023-08-30T06:44:27.951026Z"}}, "id": "4e1ac629c2c0ef68"}, {"cell_type": "code", "execution_count": 27, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df中有，df_frd和df_zhida中均没有的数据的个数：0\n"]}, {"data": {"text/plain": "Empty DataFrame\nColumns: [open_price, high_price, low_price, close_price, volume]\nIndex: []", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["df_frd中有，df和df_zhida中均没有的数据的个数：2\n"]}, {"data": {"text/plain": "                                open_price  high_price  low_price  \\\nsymbol     datetime                                                 \nES-USD-FUT 2023-08-29 04:00:00     4441.75     4442.00     4440.5   \nNQ-USD-FUT 2023-08-29 04:00:00    15091.25    15092.25    15085.5   \n\n                                close_price  volume  \nsymbol     datetime                                  \nES-USD-FUT 2023-08-29 04:00:00      4441.25   12399  \nNQ-USD-FUT 2023-08-29 04:00:00     15090.00    2589  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>ES-USD-FUT</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>4441.75</td>\n      <td>4442.00</td>\n      <td>4440.5</td>\n      <td>4441.25</td>\n      <td>12399</td>\n    </tr>\n    <tr>\n      <th>NQ-USD-FUT</th>\n      <th>2023-08-29 04:00:00</th>\n      <td>15091.25</td>\n      <td>15092.25</td>\n      <td>15085.5</td>\n      <td>15090.00</td>\n      <td>2589</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["df_zhida中有，df和df_frd中均没有的数据的个数：0\n"]}, {"data": {"text/plain": "Empty DataFrame\nColumns: [open_price, high_price, low_price, close_price, volume]\nIndex: []", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>symbol</th>\n      <th>datetime</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n  </tbody>\n</table>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "                                open_price  high_price  low_price  \\\ndatetime            symbol                                          \n2023-08-28 21:30:00 ES-USD-FUT        0.00        0.00       0.00   \n2023-08-28 21:31:00 ES-USD-FUT        0.00        0.00       0.00   \n2023-08-28 21:32:00 ES-USD-FUT        0.00        0.00       0.00   \n2023-08-28 21:33:00 ES-USD-FUT        0.00        0.00       0.00   \n2023-08-28 21:34:00 ES-USD-FUT        0.00        0.00       0.00   \n...                                    ...         ...        ...   \n2023-08-29 00:55:00 NQ-USD-FUT       -0.05        0.00       0.00   \n2023-08-29 00:56:00 NQ-USD-FUT        0.00       -0.05       0.00   \n2023-08-29 00:57:00 NQ-USD-FUT       -0.05        0.05       0.00   \n2023-08-29 00:58:00 NQ-USD-FUT       -0.05        0.00       0.00   \n2023-08-29 00:59:00 NQ-USD-FUT        0.25        0.05      -0.05   \n\n                                close_price  volume  \ndatetime            symbol                           \n2023-08-28 21:30:00 ES-USD-FUT         0.00     0.0  \n2023-08-28 21:31:00 ES-USD-FUT         0.00     0.0  \n2023-08-28 21:32:00 ES-USD-FUT         0.00     0.0  \n2023-08-28 21:33:00 ES-USD-FUT         0.00     0.0  \n2023-08-28 21:34:00 ES-USD-FUT         0.25    -1.0  \n...                                     ...     ...  \n2023-08-29 00:55:00 NQ-USD-FUT        -0.05     0.0  \n2023-08-29 00:56:00 NQ-USD-FUT         0.00     0.0  \n2023-08-29 00:57:00 NQ-USD-FUT         0.05     0.0  \n2023-08-29 00:58:00 NQ-USD-FUT         0.05   -17.0  \n2023-08-29 00:59:00 NQ-USD-FUT        -0.05    16.0  \n\n[420 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>datetime</th>\n      <th>symbol</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2023-08-28 21:30:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.25</td>\n      <td>-1.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:55:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>-0.05</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:56:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>-0.05</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:57:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.05</td>\n      <td>0.00</td>\n      <td>0.05</td>\n      <td>0.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:58:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.05</td>\n      <td>-17.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:59:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.25</td>\n      <td>0.05</td>\n      <td>-0.05</td>\n      <td>-0.05</td>\n      <td>16.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>420 rows × 5 columns</p>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "                                open_price  high_price  low_price  \\\ndatetime            symbol                                          \n2023-08-28 21:30:00 ES-USD-FUT       -0.25        0.00       0.00   \n2023-08-28 21:31:00 ES-USD-FUT       -0.25        0.00      -2.00   \n2023-08-28 21:32:00 ES-USD-FUT        0.00        0.00      -2.75   \n2023-08-28 21:33:00 ES-USD-FUT        0.25        0.00      -4.00   \n2023-08-28 21:34:00 ES-USD-FUT        0.00        0.00       0.00   \n...                                    ...         ...        ...   \n2023-08-29 00:55:00 NQ-USD-FUT       -0.05        0.00      -6.25   \n2023-08-29 00:56:00 NQ-USD-FUT        0.00       -0.05      -3.50   \n2023-08-29 00:57:00 NQ-USD-FUT       -0.05        0.05      -7.25   \n2023-08-29 00:58:00 NQ-USD-FUT       -0.05        0.00      -4.00   \n2023-08-29 00:59:00 NQ-USD-FUT       -0.25        0.05      -3.05   \n\n                                close_price  volume  \ndatetime            symbol                           \n2023-08-28 21:30:00 ES-USD-FUT         0.00   202.0  \n2023-08-28 21:31:00 ES-USD-FUT         2.00   125.0  \n2023-08-28 21:32:00 ES-USD-FUT         2.50    91.0  \n2023-08-28 21:33:00 ES-USD-FUT         4.00    40.0  \n2023-08-28 21:34:00 ES-USD-FUT         0.25    17.0  \n...                                     ...     ...  \n2023-08-29 00:55:00 NQ-USD-FUT         6.20     3.0  \n2023-08-29 00:56:00 NQ-USD-FUT         3.50    19.0  \n2023-08-29 00:57:00 NQ-USD-FUT         7.30     3.0  \n2023-08-29 00:58:00 NQ-USD-FUT         3.80     3.0  \n2023-08-29 00:59:00 NQ-USD-FUT         2.95    17.0  \n\n[420 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>datetime</th>\n      <th>symbol</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2023-08-28 21:30:00</th>\n      <th>ES-USD-FUT</th>\n      <td>-0.25</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>202.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <th>ES-USD-FUT</th>\n      <td>-0.25</td>\n      <td>0.00</td>\n      <td>-2.00</td>\n      <td>2.00</td>\n      <td>125.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>-2.75</td>\n      <td>2.50</td>\n      <td>91.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.25</td>\n      <td>0.00</td>\n      <td>-4.00</td>\n      <td>4.00</td>\n      <td>40.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>0.25</td>\n      <td>17.0</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:55:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.00</td>\n      <td>-6.25</td>\n      <td>6.20</td>\n      <td>3.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:56:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>-0.05</td>\n      <td>-3.50</td>\n      <td>3.50</td>\n      <td>19.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:57:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.05</td>\n      <td>-7.25</td>\n      <td>7.30</td>\n      <td>3.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:58:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.05</td>\n      <td>0.00</td>\n      <td>-4.00</td>\n      <td>3.80</td>\n      <td>3.0</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:59:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.25</td>\n      <td>0.05</td>\n      <td>-3.05</td>\n      <td>2.95</td>\n      <td>17.0</td>\n    </tr>\n  </tbody>\n</table>\n<p>420 rows × 5 columns</p>\n</div>"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "                                open_price  high_price  low_price  \\\ndatetime            symbol                                          \n2023-08-28 21:30:00 ES-USD-FUT       -0.25         0.0       0.00   \n2023-08-28 21:31:00 ES-USD-FUT       -0.25         0.0      -2.00   \n2023-08-28 21:32:00 ES-USD-FUT        0.00         0.0      -2.75   \n2023-08-28 21:33:00 ES-USD-FUT        0.25         0.0      -4.00   \n2023-08-28 21:34:00 ES-USD-FUT        0.00         0.0       0.00   \n...                                    ...         ...        ...   \n2023-08-29 00:55:00 NQ-USD-FUT        0.00         0.0      -6.25   \n2023-08-29 00:56:00 NQ-USD-FUT        0.00         0.0      -3.50   \n2023-08-29 00:57:00 NQ-USD-FUT        0.00         0.0      -7.25   \n2023-08-29 00:58:00 NQ-USD-FUT        0.00         0.0      -4.00   \n2023-08-29 00:59:00 NQ-USD-FUT       -0.50         0.0      -3.00   \n\n                                close_price  volume  \ndatetime            symbol                           \n2023-08-28 21:30:00 ES-USD-FUT         0.00     202  \n2023-08-28 21:31:00 ES-USD-FUT         2.00     125  \n2023-08-28 21:32:00 ES-USD-FUT         2.50      91  \n2023-08-28 21:33:00 ES-USD-FUT         4.00      40  \n2023-08-28 21:34:00 ES-USD-FUT         0.00      18  \n...                                     ...     ...  \n2023-08-29 00:55:00 NQ-USD-FUT         6.25       3  \n2023-08-29 00:56:00 NQ-USD-FUT         3.50      19  \n2023-08-29 00:57:00 NQ-USD-FUT         7.25       3  \n2023-08-29 00:58:00 NQ-USD-FUT         3.75      20  \n2023-08-29 00:59:00 NQ-USD-FUT         3.00       1  \n\n[420 rows x 5 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th></th>\n      <th>open_price</th>\n      <th>high_price</th>\n      <th>low_price</th>\n      <th>close_price</th>\n      <th>volume</th>\n    </tr>\n    <tr>\n      <th>datetime</th>\n      <th>symbol</th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n      <th></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2023-08-28 21:30:00</th>\n      <th>ES-USD-FUT</th>\n      <td>-0.25</td>\n      <td>0.0</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>202</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:31:00</th>\n      <th>ES-USD-FUT</th>\n      <td>-0.25</td>\n      <td>0.0</td>\n      <td>-2.00</td>\n      <td>2.00</td>\n      <td>125</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:32:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>-2.75</td>\n      <td>2.50</td>\n      <td>91</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:33:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.25</td>\n      <td>0.0</td>\n      <td>-4.00</td>\n      <td>4.00</td>\n      <td>40</td>\n    </tr>\n    <tr>\n      <th>2023-08-28 21:34:00</th>\n      <th>ES-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>0.00</td>\n      <td>0.00</td>\n      <td>18</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:55:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>-6.25</td>\n      <td>6.25</td>\n      <td>3</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:56:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>-3.50</td>\n      <td>3.50</td>\n      <td>19</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:57:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>-7.25</td>\n      <td>7.25</td>\n      <td>3</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:58:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>0.00</td>\n      <td>0.0</td>\n      <td>-4.00</td>\n      <td>3.75</td>\n      <td>20</td>\n    </tr>\n    <tr>\n      <th>2023-08-29 00:59:00</th>\n      <th>NQ-USD-FUT</th>\n      <td>-0.50</td>\n      <td>0.0</td>\n      <td>-3.00</td>\n      <td>3.00</td>\n      <td>1</td>\n    </tr>\n  </tbody>\n</table>\n<p>420 rows × 5 columns</p>\n</div>"}, "metadata": {}, "output_type": "display_data"}], "source": ["# 比较df、df_frd、df_zhida的数据（以symbol、datetime为索引），输出：df中有，df_frd和df_zhida中均没有的数据，df_frd中有，df和df_zhida中均没有的数据，df_zhida中有，df和df_frd中均没有的数据；df、df_frd、df_zhida中都有的数据则作差（输出到3个sheet，分别命名为df_diff_df_frd、df_diff_df_zhida、df_frd_diff_df_zhida），输出不为0的差值\n", "# df中有，df_frd和df_zhida中均没有的数据\n", "df_diff_df_frd_zhida = df[~df.index.isin(df_frd.index) & ~df.index.isin(df_zhida.index)]\n", "# 打印df中有，df_frd和df_zhida中均没有的数据的个数\n", "print(f'df中有，df_frd和df_zhida中均没有的数据的个数：{len(df_diff_df_frd_zhida)}')\n", "# 打印df中有，df_frd和df_zhida中均没有的数据\n", "display(df_diff_df_frd_zhida)\n", "# df_frd中有，df和df_zhida中均没有的数据\n", "df_frd_diff_df_zhida = df_frd[~df_frd.index.isin(df.index) & ~df_frd.index.isin(df_zhida.index)]\n", "# 打印df_frd中有，df和df_zhida中均没有的数据的个数\n", "print(f'df_frd中有，df和df_zhida中均没有的数据的个数：{len(df_frd_diff_df_zhida)}')\n", "# 打印df_frd中有，df和df_zhida中均没有的数据\n", "display(df_frd_diff_df_zhida)\n", "# df_zhida中有，df和df_frd中均没有的数据\n", "df_zhida_diff_df_frd = df_zhida[~df_zhida.index.isin(df.index) & ~df_zhida.index.isin(df_frd.index)]\n", "# 打印df_zhida中有，df和df_frd中均没有的数据的个数\n", "print(f'df_zhida中有，df和df_frd中均没有的数据的个数：{len(df_zhida_diff_df_frd)}')\n", "# 打印df_zhida中有，df和df_frd中均没有的数据\n", "display(df_zhida_diff_df_frd)\n", "# df中df_frd、df_zhida中都有的数据的索引\n", "df_diff_same_index = df.index.intersection(df_frd.index).intersection(df_zhida.index)\n", "\n", "# 不为0的差值：df与df_frd的差值\n", "df_diff_same_df_frd = df.loc[df_diff_same_index] - df_frd.loc[df_diff_same_index]\n", "# 以datetime、symbol为两层索引\n", "df_diff_same_df_frd = df_diff_same_df_frd.reset_index().set_index(['datetime', 'symbol'])\n", "# 打印不为0的差值：df与df_frd的差值\n", "display(df_diff_same_df_frd)\n", "\n", "# 不为0的差值：df与df_zhida的差值\n", "df_diff_same_df_zhida = df.loc[df_diff_same_index] - df_zhida.loc[df_diff_same_index]\n", "# 以datetime、symbol为两层索引\n", "df_diff_same_df_zhida = df_diff_same_df_zhida.reset_index().set_index(['datetime', 'symbol'])\n", "# 打印不为0的差值：df与df_zhida的差值\n", "display(df_diff_same_df_zhida)\n", "\n", "# 不为0的差值：df_frd与df_zhida的差值\n", "df_diff_same_df_frd_zhida = df_frd.loc[df_diff_same_index] - df_zhida.loc[df_diff_same_index]\n", "# 以datetime、symbol为两层索引\n", "df_diff_same_df_frd_zhida = df_diff_same_df_frd_zhida.reset_index().set_index(['datetime', 'symbol'])\n", "# 打印不为0的差值：df_frd与df_zhida的差值\n", "display(df_diff_same_df_frd_zhida)\n", "\n", "# 保存三个结果到同一个excel文件的不同sheet中，注意为空的情况\n", "with pd.ExcelWriter(f'{today}_ib_frd_zhida_diff_fut.xlsx') as writer:\n", "    # 如果df不为空，则保存到excel中\n", "    if len(df) > 0:\n", "        df.to_excel(writer, sheet_name='df_ib')\n", "    # 如果df_frd不为空，则保存到excel中\n", "    if len(df_frd) > 0:\n", "        df_frd.to_excel(writer, sheet_name='df_frd')\n", "    # 如果df_zhida不为空，则保存到excel中\n", "    if len(df_zhida) > 0:\n", "        df_zhida.to_excel(writer, sheet_name='df_zhida')\n", "    # 如果df_diff_df_frd_zhida不为空，则保存到excel中\n", "    if len(df_diff_df_frd_zhida) > 0:\n", "        df_diff_df_frd_zhida.to_excel(writer, sheet_name='df_diff_df_frd_zhida')\n", "    # 如果df_frd_diff_df_zhida不为空，则保存到excel中\n", "    if len(df_frd_diff_df_zhida) > 0:\n", "        df_frd_diff_df_zhida.to_excel(writer, sheet_name='df_frd_diff_df_zhida')\n", "    # 如果df_zhida_diff_df_frd不为空，则保存到excel中\n", "    if len(df_zhida_diff_df_frd) > 0:\n", "        df_zhida_diff_df_frd.to_excel(writer, sheet_name='df_zhida_diff_df_frd')\n", "    # 如果df_diff_same_df_frd不为空，则保存到excel中\n", "    if len(df_diff_same_df_frd) > 0:\n", "        df_diff_same_df_frd.to_excel(writer, sheet_name='df_diff_same_df_frd')\n", "    # 如果df_diff_same_df_zhida不为空，则保存到excel中\n", "    if len(df_diff_same_df_zhida) > 0:\n", "        df_diff_same_df_zhida.to_excel(writer, sheet_name='df_diff_same_df_zhida')\n", "    # 如果df_diff_same_df_frd_zhida不为空，则保存到excel中\n", "    if len(df_diff_same_df_frd_zhida) > 0:\n", "        df_diff_same_df_frd_zhida.to_excel(writer, sheet_name='df_diff_same_df_frd_zhida')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-08-30T06:52:07.070001700Z", "start_time": "2023-08-30T06:52:06.563357100Z"}}, "id": "44a20d5b5d467e99"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}