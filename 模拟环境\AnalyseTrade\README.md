# AnalyseTrade


################### TradeAnalyse.py ##########################
## 安装步骤
将TradeAnalyse.py放到策略文件夹下，或者自己的site_packages文件夹下

## 脚本运行
不单独运行，基于vnpy的engine为参数运行

## 使用指南
示例：在最后加入：

from TradeAnalyse import TradeAnalyse
user = '胡'
ta = TradeAnalyse()
ta.summary(user, engine)

其中：engine = BacktestingEngine()是跑策略的实例

实际测试下：
1. 策略因为参数异常无交易时，该模块不会报错中断程序，可以在大规模测试中使用
2. 策略正常时，会保存分析文件：
a. 当前文件夹下：成交分析_$日期.xlsx
b../$日期目录下：$user_$vt_symbol_$日期_分析  三个文件.csv, .png, .txt，分别保存了交易记录，交易分笔分析图和成交统计分析的文本
