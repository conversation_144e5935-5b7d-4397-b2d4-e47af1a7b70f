from vnpy.trader.utility import load_json, save_json
import sys
from abc import ABC, abstractmethod
import os
from typing import TYPE_CHECKING, Tuple

from vnpy.usertools.task_db_manager import Todo

if TYPE_CHECKING:
    from .engine_risk import AlgoRiskEngine


class RiskPlugin(ABC):
    """风控插件基类"""
    default_setting = {}

    def __init__(self, risk_engine: "AlgoRiskEngine"):
        """构造函数"""
        self.risk_engine: "AlgoRiskEngine" = risk_engine
        self.name: str = self.__class__.__name__
        
        # 从模块文件名获取配置文件名
        module_path = self.__module__.split('.')[-1]
        self.setting_file_name: str = f"{module_path}.json"
        
        # 加载配置
        self.load_setting()

    def load_setting(self) -> None:
        """
        加载配置
        
        注意：子类应该在这里加载和初始化具体的风控参数
        """
        self.setting = load_json(self.setting_file_name)
        if not self.setting:
            self.setting = self.default_setting
            save_json(self.setting_file_name, self.setting)
            self.write_log(f"配置文件不存在，使用默认配置:{self.setting_file_name}")

    @abstractmethod
    def check_risk(self, todo: Todo) -> Tuple[bool, str]:
        """
        风控检查

        Returns:
            Tuple[bool, str]: (是否通过检查, 拒单原因)
        """
        pass

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        self.risk_engine.write_log(formatted_msg, need_format=False)

    def get_balance(self) -> float:
        """获取账户资金"""
        return self.risk_engine.get_balance()
