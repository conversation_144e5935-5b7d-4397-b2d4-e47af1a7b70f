from datetime import datetime
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from pathlib import Path
from os import listdir
from openpyxl.drawing.image import Image
from openpyxl import load_workbook 


class TradeAnalyse():
    def process_trades(self, trades):
        data = []
        if trades:
            for trades_num in trades.keys():
                if str(trades[trades_num].direction) == 'Direction.LONG':
                    direction = "多"
                else:
                    direction = "空"
                if str(trades[trades_num].offset) == 'Offset.OPEN':
                    offset = '开'
                else:
                    offset = '平'
                data.append([trades[trades_num].symbol, trades[trades_num].datetime, direction, offset, trades[trades_num].price, trades[trades_num].volume])
            # print(data)
            df = pd.DataFrame(data, columns = ['symbol', 'current_time','direction', 'offset','price','volume'])
            return df
        else:
            print("##############    no trades!   #################")
            return pd.DataFrame()

    def get_raw_trade(self, trades, size): 
        # Generate DataFrame with datetime, direction, offset, price, volume
        df = self.process_trades(trades)
        if df.empty:
            return pd.DataFrame()
        # print(df)

        df["last_time"] = df["current_time"].shift(1)
        df["entry_price"] = df["price"].shift(1)
        # Calculate pos, net pos(with direction), acumluation pos(with direction)
        # Calculate trade amount
        df["amount"] = df["price"] * df["volume"] * size
        df["acum_amount"] = df["amount"].cumsum()
        # print(f"=================debug middle1============")
        # print(df)
        def calculate_pos(df):
            if df["direction"] == "多":
                result = df["volume"]
            else:
                result = - df["volume"]
            return result
        
        df["pos"] = df.apply(calculate_pos, axis=1)
        df["net_pos"] = df["pos"].cumsum()     # 净持仓
        df["acum_pos"] = df["volume"].cumsum() # 总交易手数
        # print(f"=================debug middle2============")
        # print(df)
        
        df["result"] = -1 * df["pos"] * df["price"] * size  # 每手交易对balance影响
        df["acum_result"] = df["result"].cumsum()           # 
        
        def get_acum_trade_duration(df):
            if df["net_pos"] == 0:
                return df["current_time"] - df["last_time"]
        df["acum_trade_duration"] = df.apply(get_acum_trade_duration, axis=1)
        
        # Filter column data when net pos comes to zero
        def get_acum_trade_result(df):
            if df["net_pos"] == 0:
                return df["acum_result"]
        df["acum_trade_result"] = df.apply(get_acum_trade_result, axis=1)  # 每次交易平仓后的可用资金（后-前 为pnl）
        
        def get_acum_trade_volume(df):
            if df["net_pos"] == 0:
                return df["acum_pos"]
        df["acum_trade_volume"] = df.apply(get_acum_trade_volume, axis=1) # 总交易手数
        def get_acum_trade_amount(df):
            if df["net_pos"] == 0:
                return df["acum_amount"]
        df["acum_trade_amount"] = df.apply(get_acum_trade_amount, axis=1) # 总交易额
        # df.to_csv("debug1.csv")
        df = df.dropna()
        # df.to_csv("debug2.csv")
        return df

    def generate_trade_df(self, raw_df,size,rate,slippage,capital,remarks: str = ""):
        if raw_df.empty:
            return pd.DataFrame()
        trade_df = pd.DataFrame()
        df = raw_df
        trade_df["close_direction"] = df["direction"]
        trade_df["close_time"] = df["current_time"]
        trade_df["entry_time"] = df["last_time"]
        trade_df["close_price"] = df["price"]
        trade_df["entry_price"] = df["entry_price"]
        trade_df["pnl"] = df["acum_trade_result"] - df["acum_trade_result"].shift(1).fillna(0)

        trade_df["volume"] = df["acum_trade_volume"] - df["acum_trade_volume"].shift(1).fillna(0)
        trade_df["duration"] = df["current_time"] - df["last_time"]
        trade_df["turnover"] = df["acum_trade_amount"] - df["acum_trade_amount"].shift(1).fillna(0)

        trade_df["commission"] = trade_df["turnover"] * rate
        trade_df["slipping"] = trade_df["volume"] * size * slippage

        trade_df["net_pnl"] = trade_df["pnl"] - trade_df["commission"] - trade_df["slipping"]
        # trade_df.to_csv("tradeinfo.csv")
        result = self.calculate_base_net_pnl(trade_df, capital)
        if remarks != "":
            date_str = datetime.now().strftime('%Y%m%d')
            internal_folder = Path(date_str)
            try:
                os.makedirs(str(internal_folder))
            except:
                pass
            filepath_csv = str(internal_folder)+"//"+remarks+".csv"
            result.to_csv(filepath_csv)
        return result

    def calculate_base_net_pnl(self, df, capital):
        """
        Calculate statistic base on net pnl
        """
        df["acum_pnl"] = df["net_pnl"].cumsum()
        df["balance"] = df["acum_pnl"] + capital
        df["return"] = np.log(
            df["balance"] / df["balance"].shift(1)
        ).fillna(0)
        df["highlevel"] = (df["balance"].rolling(min_periods=1, window=len(df), center=False).max())
        df.loc[df["highlevel"]<capital, 'highlevel'] = capital
        # print(df['highlevel'])
        df["drawdown"] = df["balance"] - df["highlevel"]
        df["ddpercent"] = df["drawdown"] / df["highlevel"] * 100
        df.reset_index(drop=True, inplace=True)
        return df

    def write_to_file(self, filename, text):
        with open(filename, 'a', encoding='utf-8') as file:
            file.write(text)

    def statistics_trade_result(self, df, capital, show_chart=True, remarks="remarks_"):
        """"""
        # print(df)
        if df.empty:
            end_balance = capital
            max_drawdown = 0
            max_ddpercent = 0
            pnl_medio = 0
            trade_count = 0
            duration_medio = 0
            commission_medio = 0
            slipping_medio = 0
            win_amount = 0
            win_pnl_medio = 0
            win_duration_medio = 0
            win_count = 0
            loss_amount = 0
            loss_pnl_medio = 0
            loss_duration_medio = 0
            loss_count = 0
            winning_rate = 0
            win_loss_pnl_ratio = 0
            total_return = 0
            return_drawdown_ratio = 0
        else:
            end_balance = df["balance"].iloc[-1]
            max_drawdown = df["drawdown"].min()
            max_ddpercent = df["ddpercent"].min()

            pnl_medio = df["net_pnl"].mean()
            trade_count = len(df)
            duration_medio = df["duration"].mean().total_seconds()/3600
            commission_medio = df["commission"].mean()
            slipping_medio = df["slipping"].mean()

            win = df[df["net_pnl"] > 0]
            win_amount = win["net_pnl"].sum()
            win_pnl_medio = win["net_pnl"].mean()
            win_duration_medio = win["duration"].mean().total_seconds()/3600
            win_count = len(win)

            loss = df[df["net_pnl"] < 0]
            loss_amount = loss["net_pnl"].sum()
            loss_pnl_medio = loss["net_pnl"].mean()
            loss_duration_medio = loss["duration"].mean().total_seconds()/3600
            loss_count = len(loss)

            winning_rate = win_count / trade_count
            win_loss_pnl_ratio = - win_pnl_medio / loss_pnl_medio

            total_return = (end_balance / capital - 1) * 100
            return_drawdown_ratio = -total_return / max_ddpercent
        
        date_str = datetime.now().strftime('%Y%m%d')
        file_name = date_str +"//"+remarks+".txt"
        
        self.output(f"起始资金:\t{capital:,.2f}",file_name)
        self.output(f"结束资金:\t{end_balance:,.2f}",file_name)
        self.output(f"总收益率:\t{total_return:,.2f}%",file_name)
        self.output(f"最大回撤: \t{max_drawdown:,.2f}",file_name)
        self.output(f"百分比最大回撤: {max_ddpercent:,.2f}%",file_name)
        self.output(f"收益回撤比:\t{return_drawdown_ratio:,.2f}",file_name)

        self.output(f"总成交次数:\t{trade_count}",file_name)
        self.output(f"盈利成交次数:\t{win_count}",file_name)
        self.output(f"亏损成交次数:\t{loss_count}",file_name)
        self.output(f"胜率:\t\t{winning_rate:,.2f}",file_name)
        self.output(f"盈亏比:\t\t{win_loss_pnl_ratio:,.2f}",file_name)

        self.output(f"平均每笔盈亏:\t{pnl_medio:,.2f}",file_name)
        self.output(f"平均持仓小时:\t{duration_medio:,.2f}",file_name)
        self.output(f"平均每笔手续费:\t{commission_medio:,.2f}",file_name)
        self.output(f"平均每笔滑点:\t{slipping_medio:,.2f}",file_name)

        self.output(f"总盈利金额:\t{win_amount:,.2f}",file_name)
        self.output(f"盈利交易均值:\t{win_pnl_medio:,.2f}",file_name)
        self.output(f"盈利持仓小时:\t{win_duration_medio:,.2f}",file_name)

        self.output(f"总亏损金额:\t{loss_amount:,.2f}",file_name)
        self.output(f"亏损交易均值:\t{loss_pnl_medio:,.2f}",file_name)
        self.output(f"亏损持仓小时:\t{loss_duration_medio:,.2f}",file_name)

        if not show_chart:
            return

        if df.empty:
            return
        
        plt.figure(figsize=(10, 12))
        acum_pnl_plot = plt.subplot(3, 1, 1)
        acum_pnl_plot.set_title("Balance Plot")
        df["balance"].plot(legend=True,grid=True)

        pnl_plot = plt.subplot(3, 1, 2)
        pnl_plot.set_title("Pnl Per Trade")
        df["net_pnl"].plot(legend=True,grid=True,kind="bar")

        distribution_plot = plt.subplot(3, 1, 3)
        distribution_plot.set_title("Trade Pnl Distribution")
        df["net_pnl"].hist(bins=100,grid=True)

        # plt.show()
        plt.savefig(date_str+"//"+remarks+".png")

    def output(self, msg,outputfile=""):
        """
        Output message with datetime.
        """
        print(f"{datetime.now()}\t{msg}")
        if outputfile!="":
            self.write_to_file(outputfile,msg+"\n")

    def exhaust_trade_result(
        self,
        trades,
        capital: int = 1000000,
        remarks=""
    ):
        """
        Exhaust all trade result.
        """

        self.output("总体情况")
        self.output("-----------------------")
        total_trades = trades
        self.statistics_trade_result(total_trades, capital,remarks=remarks)

    def read_file(self, filename):
        with open(filename, encoding='utf-8') as file:
            return file.readlines()

    def read_file2df(self,filename):
        all_lines = self.read_file(filename)
        all_key =[]
        all_value=[]
        for elem in all_lines:
            key,value = elem.split(":")
            all_key.append(key)
            all_value.append(value)
        df = pd.DataFrame()
        df["指标"]=all_key
        df["值"]=all_value
        return df

    def summary(self, user, engine):
        # 获取策略属性
        symbol = engine.vt_symbol
        trades = engine.trades
        capital = engine.capital
        size = engine.size
        slippage = engine.slippage
        rate = engine.rate

        date_str = datetime.now().strftime('%Y%m%d')
        remarks = f"{user}_{symbol}_{date_str}_分析"
        df = self.get_raw_trade(trades,size)
        # print(df)
        if df.empty:
            return
        trade_df=self.generate_trade_df(df,size,rate,slippage,capital,remarks=remarks)
        # print(trade_df)
        self.exhaust_trade_result(trade_df, capital, remarks=remarks)
        

        # 合并文本 
        internal_folder = Path(date_str)
        outfile_name = "成交分析_"+date_str+'.xlsx'
        writer=pd.ExcelWriter(outfile_name)
        if not  internal_folder.exists():
            print(f"{internal_folder} does not exist! quit")
            exit()
        all_file = listdir(internal_folder)
        all_sheet_name = []
        for elem in all_file:
            sheet = elem[:-4]
            if sheet in all_sheet_name:
                pass
            else:
                all_sheet_name.append(sheet)
        # print(all_sheet_name)
        for elem in all_sheet_name:
            sub_df = self.read_file2df(str(internal_folder)+"//"+elem+".txt")
            # print(sub_df)
            sub_df.to_excel(writer,sheet_name = elem)

        writer.save()
        writer.close()
        
        # 插入图片
        workbook  = load_workbook(outfile_name)
        for elem in all_sheet_name:
            ws = workbook[elem]
            img = Image(str(internal_folder)+"//"+elem+".png")
            ws.add_image(img,"E2")
        workbook.save(outfile_name)


if __name__=="__main__":
    ta = TradeAnalyse()
    # ta.summary()