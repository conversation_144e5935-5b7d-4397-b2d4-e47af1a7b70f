from datetime import datetime
from typing import List

from peewee import (
    AutoField,
    Char<PERSON><PERSON>,
    DateTimeField,
    DoubleField,
    Model,
    fn
)
from vnpy_mysql.mysql_database import (
    ReconnectMySQLDatabase,
    BaseDatabase,
    DB_TZ,
    convert_tz,
    DbBarData
)
from vnpy.trader.constant import Exchange
from vnpy.trader.setting import SETTINGS

# 数据库连接
db = ReconnectMySQLDatabase(
    database=SETTINGS["database.database"],
    user=SETTINGS["database.user"],
    password=SETTINGS["database.password"],
    host=SETTINGS["database.host"],
    port=SETTINGS["database.port"]
)

class DbAdjustmentFactor(Model):
    """复权因子数据表"""    
    # 基础信息
    symbol: str = CharField()
    exchange: str = CharField()
    ex_div_date: datetime = DateTimeField()
    
    # 拆合股信息
    split_ratio: float = DoubleField(null=True)         # 拆合股比例
    
    # 现金分红
    cash_dividend: float = DoubleField(null=True)       # 每股派现
    special_dividend: float = DoubleField(null=True)    # 特别股息
    
    # 送股转增
    bonus_ratio: float = DoubleField(null=True)         # 送股比例
    transfer_ratio: float = DoubleField(null=True)      # 转增股比例
    
    # 配股增发
    allotment_ratio: float = DoubleField(null=True)     # 配股比例
    allotment_price: float = DoubleField(null=True)     # 配股价
    spo_ratio: float = DoubleField(null=True)          # 增发比例
    spo_price: float = DoubleField(null=True)          # 增发价
    
    # 复权因子
    forward_factor_a: float = DoubleField()             # 前复权因子A
    forward_factor_b: float = DoubleField()             # 前复权因子B
    backward_factor_a: float = DoubleField()            # 后复权因子A
    backward_factor_b: float = DoubleField()            # 后复权因子B

    class Meta:
        database = db
        indexes = (
            (("symbol", "exchange", "ex_div_date"), True),
        )

class DbAdjustedBar(Model):
    """复权K线数据物化表"""    
    # 基础信息
    symbol: str = CharField()
    exchange: str = CharField()
    datetime: datetime = DateTimeField()
    
    # 原始价格
    open_price: float = DoubleField()
    high_price: float = DoubleField()
    low_price: float = DoubleField()
    close_price: float = DoubleField()
    
    # 前复权价格
    forward_open: float = DoubleField()
    forward_high: float = DoubleField()
    forward_low: float = DoubleField()
    forward_close: float = DoubleField()
    
    # 后复权价格
    backward_open: float = DoubleField()
    backward_high: float = DoubleField()
    backward_low: float = DoubleField()
    backward_close: float = DoubleField()
    
    # 其他数据
    volume: float = DoubleField()
    turnover: float = DoubleField()
    
    # 最后更新时间
    updated_time: datetime = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (("symbol", "exchange", "datetime"), True),
        )

class AdjustedDatabase(BaseDatabase):
    """包含复权支持的数据库"""
    
    def __init__(self) -> None:
        """构造函数"""
        self.db = db
        self.db.connect()
        
        # 为现有的DbBarData表添加复权价格字段
        self._add_adjustment_fields()
        
        # 创建复权因子表
        self.db.create_tables([DbAdjustmentFactor])
    
    def _add_adjustment_fields(self) -> None:
        """为现有数据表添加复权相关字段"""
        # 检查字段是否存在，不存在则添加
        fields_to_add = [
            ("forward_price", "DOUBLE NULL COMMENT '前复权价'"),
            ("backward_price", "DOUBLE NULL COMMENT '后复权价'"),
            ("last_adjustment", "DATETIME NULL COMMENT '最后复权更新时间'")
        ]
        
        for field_name, field_def in fields_to_add:
            try:
                self.db.execute_sql(
                    f"ALTER TABLE dbbardata ADD COLUMN {field_name} {field_def}"
                )
            except:
                pass  # 字段已存在，跳过
    
    def init_adjustment_factors(self, factors: List[dict]) -> None:
        """初始化复权因子数据"""
        with self.db.atomic():
            # 清空现有复权因子
            DbAdjustmentFactor.delete().execute()
            
            # 批量插入新的复权因子
            for chunk in self._chunked(factors, 1000):
                DbAdjustmentFactor.insert_many(chunk).execute()
    
    def update_forward_prices(self, symbol: str, exchange: str) -> None:
        """更新前复权价格（需要更新全部历史数据）"""
        # 获取复权因子
        factors = list(DbAdjustmentFactor
                      .select()
                      .where(
                          (DbAdjustmentFactor.symbol == symbol) &
                          (DbAdjustmentFactor.exchange == exchange)
                      )
                      .order_by(DbAdjustmentFactor.ex_div_date)
                      .dicts())
        
        # 批量更新前复权价格
        with self.db.atomic():
            bars = DbBarData.select().where(
                (DbBarData.symbol == symbol) &
                (DbBarData.exchange == exchange)
            )
            
            for bar in bars:
                factor = self._get_forward_factor(bar.datetime, factors)
                forward_price = bar.close_price * factor["a"] + factor["b"]
                
                (DbBarData
                 .update(
                     forward_price=forward_price,
                     last_adjustment=datetime.now()
                 )
                 .where(DbBarData.id == bar.id)
                 .execute())
    
    def update_backward_prices(self, symbol: str, exchange: str, start_time: datetime = None) -> None:
        """更新后复权价格（只更新最新数据）"""
        # 获取复权因子
        factors = list(DbAdjustmentFactor
                      .select()
                      .where(
                          (DbAdjustmentFactor.symbol == symbol) &
                          (DbAdjustmentFactor.exchange == exchange)
                      )
                      .order_by(DbAdjustmentFactor.ex_div_date.desc())
                      .dicts())
        
        # 构建查询条件
        query = (DbBarData.symbol == symbol) & (DbBarData.exchange == exchange)
        if start_time:
            query &= (DbBarData.datetime >= start_time)
        
        # 批量更新后复权价格
        with self.db.atomic():
            bars = DbBarData.select().where(query)
            
            for bar in bars:
                factor = self._get_backward_factor(bar.datetime, factors)
                backward_price = bar.close_price * factor["a"] + factor["b"]
                
                (DbBarData
                 .update(
                     backward_price=backward_price,
                     last_adjustment=datetime.now()
                 )
                 .where(DbBarData.id == bar.id)
                 .execute())
    
    def add_new_factor(self, factor: dict) -> None:
        """添加新的复权因子并更新相关价格"""
        with self.db.atomic():
            # 保存新的复权因子
            DbAdjustmentFactor.create(**factor)
            
            # 更新前复权价格（全量更新）
            self.update_forward_prices(factor["symbol"], factor["exchange"])
            
            # 更新后复权价格（只更新最新数据）
            self.update_backward_prices(
                factor["symbol"], 
                factor["exchange"],
                start_time=factor["ex_div_date"]
            )
    
    def _get_forward_factor(self, dt: datetime, factors: List[dict]) -> dict:
        """获取前复权因子"""
        for factor in factors:
            if factor["ex_div_date"] > dt:
                return {
                    "a": factor["forward_factor_a"],
                    "b": factor["forward_factor_b"]
                }
        return {"a": 1.0, "b": 0.0}
    
    def _get_backward_factor(self, dt: datetime, factors: List[dict]) -> dict:
        """获取后复权因子"""
        for factor in reversed(factors):
            if factor["ex_div_date"] <= dt:
                return {
                    "a": factor["backward_factor_a"],
                    "b": factor["backward_factor_b"]
                }
        return {"a": 1.0, "b": 0.0}
    
    def _chunked(self, lst: list, chunk_size: int):
        """将列表分块"""
        for i in range(0, len(lst), chunk_size):
            yield lst[i:i + chunk_size]