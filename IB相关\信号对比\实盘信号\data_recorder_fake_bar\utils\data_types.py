"""
数据类型定义模块
避免循环导入问题
"""
from dataclasses import dataclass
from datetime import datetime
from vnpy.trader.constant import Exchange
from vnpy.trader.object import BaseData


@dataclass
class OrderErrorData(BaseData):
    symbol: str
    exchange: Exchange
    error_code: int
    error_msg: str
    orderid: str = None
    create_date: datetime = datetime.now()
    username: str = None
    todo_id: str = None
    fix_date: datetime = None
    remarks: str = None
    ext1: str = None
    ext2: str = None