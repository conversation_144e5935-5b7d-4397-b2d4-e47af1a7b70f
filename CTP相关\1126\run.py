import sys
import signal
from datetime import time, datetime
from logging import INFO
from time import sleep

from vnpy.event import EventEngine
from vnpy.trader.constant import Exchange
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import SubscribeRequest
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json
from vnpy.trader.event import EVENT_TICK

from vnpy_ctp.gateway.ctp_gateway import CtpGateway
from vnpy_ctp.gateway.barGen_redis_engine import BarGenEngine

# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True

# Trading hours configuration
trading_hours = [
    (time(8, 30), time(15, 15)), 
    (time(20, 30), time.max),
    (time.min, time(2, 45))
]

# Configuration file
connect_filename = 'connect_ctp_simnow.json'

def check_trading_period():
    """Check if current time is within trading hours"""
    trading = False
    current_time = datetime.now().time()
    for start, end in trading_hours:
        if start <= current_time <= end:
            trading = True
    return trading

def process_tick(event):
    """Process tick data update event"""
    tick = event.data
    print(f"Tick received - Symbol: {tick.vt_symbol}, Time: {tick.datetime}, Last Price: {tick.last_price}")

def signal_handler(signum, frame):
    """Handle exit signals"""
    print("\nReceived signal to exit. Cleaning up...")
    if 'main_engine' in globals():
        main_engine.write_log("Closing connection...")
        main_engine.close()
    sys.exit(0)

def run_test():
    """Main test function"""
    global main_engine  # Make it accessible to signal handler
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create event engine
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    
    # Register tick event handler
    event_engine.register(EVENT_TICK, process_tick)
    main_engine.write_log("Tick event handler registered")
    
    # Add gateway
    main_engine.add_gateway(CtpGateway)
    main_engine.write_log("Gateway added successfully")
    
    # Add BarGen engine
    main_engine.add_engine(BarGenEngine)
    main_engine.write_log("BarGen engine added successfully")
    
    # Connect to gateway
    setting = load_json(connect_filename)
    main_engine.write_log("Gateway configuration loaded")
    main_engine.connect(setting, "CTP")
    main_engine.write_log("Connected to CTP gateway")
    
    # Wait for connection
    sleep(3)
    
    # Simple subscription
    vt_symbol = "rb2505.SHFE"
    req = SubscribeRequest(
        symbol=vt_symbol.split(".")[0],    # "rb2505"
        exchange=Exchange(vt_symbol.split(".")[1])    # "SHFE"
    )
    main_engine.subscribe(req, "CTP")
    main_engine.write_log(f"Subscribed to {vt_symbol}")
    
    main_engine.write_log("***Querying account and position***")
    main_engine.write_log(str(main_engine.get_all_accounts()))
    main_engine.write_log(str(main_engine.get_all_positions()))
    
    while True:
        sleep(1)
        if not check_trading_period():
            main_engine.write_log("Not in trading period, closing...")
            main_engine.close()
            sys.exit(0)

if __name__ == "__main__":
    run_test() 