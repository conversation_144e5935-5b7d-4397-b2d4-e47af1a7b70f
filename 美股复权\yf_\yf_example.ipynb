{"cells": [{"cell_type": "code", "id": "edf693f90a719611", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-16T14:15:44.289002Z", "start_time": "2025-06-16T14:15:43.092663Z"}}, "source": ["import yfinance as yf\n", "import matplotlib.pyplot as plt\n", "import urllib.request, os\n", "import numpy as np\n", "import pandas as pd\n", "import datetime\n", "\n", "def get_system_proxy() -> dict:\n", "    \"\"\"获取系统代理设置\"\"\"\n", "    proxy_handler = urllib.request.ProxyHandler()\n", "    proxies = {}\n", "\n", "    # 从系统获取代理设置\n", "    for protocol in ['http', 'https']:\n", "        if proxy := proxy_handler.proxies.get(protocol):\n", "            proxies[protocol] = proxy\n", "            os.environ[f\"{protocol}_proxy\"] = proxy\n", "\n", "    return proxies\n", "proxies = get_system_proxy()\n", "print(proxies)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}\n"]}], "execution_count": 1}, {"cell_type": "markdown", "id": "a9878612", "metadata": {}, "source": ["## download\n", "\n", "### single"]}, {"cell_type": "code", "execution_count": 2, "id": "7f90c559052c8b72", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T05:36:38.080699Z", "start_time": "2025-06-16T05:36:35.181442Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_23416\\1499817750.py:4: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  gold_data = yf.download(gold_ticker, start=\"2022-01-01\")\n", "[*********************100%***********************]  1 of 1 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>GC=F</th>\n", "      <th>GC=F</th>\n", "      <th>GC=F</th>\n", "      <th>GC=F</th>\n", "      <th>GC=F</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-01-03</th>\n", "      <td>1799.400024</td>\n", "      <td>1830.099976</td>\n", "      <td>1798.800049</td>\n", "      <td>1830.099976</td>\n", "      <td>116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-04</th>\n", "      <td>1814.000000</td>\n", "      <td>1815.300049</td>\n", "      <td>1800.000000</td>\n", "      <td>1800.500000</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-05</th>\n", "      <td>1824.599976</td>\n", "      <td>1824.599976</td>\n", "      <td>1813.099976</td>\n", "      <td>1813.099976</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-06</th>\n", "      <td>1788.699951</td>\n", "      <td>1791.300049</td>\n", "      <td>1787.099976</td>\n", "      <td>1787.099976</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-07</th>\n", "      <td>1797.000000</td>\n", "      <td>1797.000000</td>\n", "      <td>1784.400024</td>\n", "      <td>1791.800049</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>3320.899902</td>\n", "      <td>3344.300049</td>\n", "      <td>3302.000000</td>\n", "      <td>3302.000000</td>\n", "      <td>65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>3321.300049</td>\n", "      <td>3356.000000</td>\n", "      <td>3321.300049</td>\n", "      <td>3328.000000</td>\n", "      <td>2106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>3380.899902</td>\n", "      <td>3395.899902</td>\n", "      <td>3353.399902</td>\n", "      <td>3363.000000</td>\n", "      <td>1818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>3431.199951</td>\n", "      <td>3444.000000</td>\n", "      <td>3407.300049</td>\n", "      <td>3407.300049</td>\n", "      <td>1818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-16</th>\n", "      <td>3434.100098</td>\n", "      <td>3476.300049</td>\n", "      <td>3433.000000</td>\n", "      <td>3473.000000</td>\n", "      <td>59684</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>867 rows × 5 columns</p>\n", "</div>"], "text/plain": ["Price             Close         High          Low         Open Volume\n", "Ticker             GC=F         GC=F         GC=F         GC=F   GC=F\n", "Date                                                                 \n", "2022-01-03  1799.400024  1830.099976  1798.800049  1830.099976    116\n", "2022-01-04  1814.000000  1815.300049  1800.000000  1800.500000     38\n", "2022-01-05  1824.599976  1824.599976  1813.099976  1813.099976      8\n", "2022-01-06  1788.699951  1791.300049  1787.099976  1787.099976     30\n", "2022-01-07  1797.000000  1797.000000  1784.400024  1791.800049     17\n", "...                 ...          ...          ...          ...    ...\n", "2025-06-10  3320.899902  3344.300049  3302.000000  3302.000000     65\n", "2025-06-11  3321.300049  3356.000000  3321.300049  3328.000000   2106\n", "2025-06-12  3380.899902  3395.899902  3353.399902  3363.000000   1818\n", "2025-06-13  3431.199951  3444.000000  3407.300049  3407.300049   1818\n", "2025-06-16  3434.100098  3476.300049  3433.000000  3473.000000  59684\n", "\n", "[867 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Use the correct ticker symbol for gold, for example, 'GC=F' (Gold Futures)\n", "gold_ticker = 'GC=F'# = means \n", "# Fetch historical data\n", "gold_data = yf.download(gold_ticker, start=\"2022-01-01\")\n", "gold_data"]}, {"cell_type": "markdown", "id": "9891d77b", "metadata": {}, "source": ["### multiple\n", "\n", "Fetching data for multiple tickers"]}, {"cell_type": "code", "execution_count": 3, "id": "dad6fdde", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T05:17:56.267631Z", "start_time": "2025-06-16T05:17:56.174799Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_41276\\1733937794.py:1: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  data = yf.download(\"AMZN AAPL TSLA\", period=\"ytd\", group_by='ticker', actions=False)\n", "Failed to get ticker 'TSLA' reason: Failed to perform, curl: (35) TLS connect error: error:00000000:invalid library (0):OPENSSL_internal:invalid library (0). See https://curl.se/libcurl/c/libcurl-errors.html first for more details.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Failed to get ticker 'AMZN' reason: Failed to perform, curl: (35) TLS connect error: error:00000000:invalid library (0):OPENSSL_internal:invalid library (0). See https://curl.se/libcurl/c/libcurl-errors.html first for more details.\n", "Failed to get ticker 'AAPL' reason: Failed to perform, curl: (35) TLS connect error: error:00000000:invalid library (0):OPENSSL_internal:invalid library (0). See https://curl.se/libcurl/c/libcurl-errors.html first for more details.\n", "[*********************100%***********************]  3 of 3 completed\n", "\n", "3 Failed downloads:\n", "['TSLA', 'AMZN', 'AAPL']: SSLError('Failed to perform, curl: (35) TLS connect error: error:00000000:invalid library (0):OPENSSL_internal:invalid library (0). See https://curl.se/libcurl/c/libcurl-errors.html first for more details.')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Price</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [Open, High, Low, Close, Adj Close, Volume]\n", "Index: []"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data = yf.download(\"AMZN AAPL TSLA\", period=\"ytd\", group_by='ticker', actions=False)\n", "amzn=data[\"AMZN\"]\n", "aapl=data[\"AAPL\"]\n", "aapl"]}, {"cell_type": "code", "execution_count": null, "id": "c6f29e7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21172\\100773192.py:1: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  data = yf.download(\"BRK-A AAPL TSLA AMZN\", period=\"ytd\", actions=False)\n", "[*********************100%***********************]  4 of 4 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th colspan=\"4\" halign=\"left\">Close</th>\n", "      <th colspan=\"4\" halign=\"left\">High</th>\n", "      <th colspan=\"4\" halign=\"left\">Low</th>\n", "      <th colspan=\"4\" halign=\"left\">Open</th>\n", "      <th colspan=\"4\" halign=\"left\">Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "      <th>AMZN</th>\n", "      <th>BRK-A</th>\n", "      <th>TSLA</th>\n", "      <th>AAPL</th>\n", "      <th>AMZN</th>\n", "      <th>BRK-A</th>\n", "      <th>TSLA</th>\n", "      <th>AAPL</th>\n", "      <th>AMZN</th>\n", "      <th>BRK-A</th>\n", "      <th>TSLA</th>\n", "      <th>AAPL</th>\n", "      <th>AMZN</th>\n", "      <th>BRK-A</th>\n", "      <th>TSLA</th>\n", "      <th>AAPL</th>\n", "      <th>AMZN</th>\n", "      <th>BRK-A</th>\n", "      <th>TSLA</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>243.263199</td>\n", "      <td>220.220001</td>\n", "      <td>675500.0</td>\n", "      <td>379.279999</td>\n", "      <td>248.500565</td>\n", "      <td>225.149994</td>\n", "      <td>685330.0</td>\n", "      <td>392.730011</td>\n", "      <td>241.238085</td>\n", "      <td>218.190002</td>\n", "      <td>674923.0</td>\n", "      <td>373.040009</td>\n", "      <td>248.330961</td>\n", "      <td>222.029999</td>\n", "      <td>685049.0</td>\n", "      <td>390.100006</td>\n", "      <td>55740700</td>\n", "      <td>33956600</td>\n", "      <td>1900</td>\n", "      <td>109710700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>242.774368</td>\n", "      <td>224.190002</td>\n", "      <td>681460.0</td>\n", "      <td>410.440002</td>\n", "      <td>243.592387</td>\n", "      <td>225.360001</td>\n", "      <td>681583.0</td>\n", "      <td>411.880005</td>\n", "      <td>241.307905</td>\n", "      <td>221.619995</td>\n", "      <td>675120.0</td>\n", "      <td>379.450012</td>\n", "      <td>242.774368</td>\n", "      <td>222.509995</td>\n", "      <td>678874.0</td>\n", "      <td>381.480011</td>\n", "      <td>40244100</td>\n", "      <td>27515600</td>\n", "      <td>1300</td>\n", "      <td>95423300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>244.410416</td>\n", "      <td>227.610001</td>\n", "      <td>676604.0</td>\n", "      <td>411.049988</td>\n", "      <td>246.734810</td>\n", "      <td>228.839996</td>\n", "      <td>684124.0</td>\n", "      <td>426.429993</td>\n", "      <td>242.614744</td>\n", "      <td>224.839996</td>\n", "      <td>675911.0</td>\n", "      <td>401.700012</td>\n", "      <td>243.722074</td>\n", "      <td>226.779999</td>\n", "      <td>681925.0</td>\n", "      <td>423.200012</td>\n", "      <td>45045600</td>\n", "      <td>31849800</td>\n", "      <td>2100</td>\n", "      <td>85516500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>241.627136</td>\n", "      <td>222.110001</td>\n", "      <td>678560.0</td>\n", "      <td>394.359985</td>\n", "      <td>244.959095</td>\n", "      <td>228.380005</td>\n", "      <td>684655.0</td>\n", "      <td>414.329987</td>\n", "      <td>240.769205</td>\n", "      <td>221.460007</td>\n", "      <td>676452.0</td>\n", "      <td>390.000000</td>\n", "      <td>242.395272</td>\n", "      <td>227.899994</td>\n", "      <td>678759.0</td>\n", "      <td>405.829987</td>\n", "      <td>40856000</td>\n", "      <td>28084200</td>\n", "      <td>1200</td>\n", "      <td>75699500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>242.115952</td>\n", "      <td>222.130005</td>\n", "      <td>677925.0</td>\n", "      <td>394.940002</td>\n", "      <td>243.123531</td>\n", "      <td>223.520004</td>\n", "      <td>681074.0</td>\n", "      <td>402.500000</td>\n", "      <td>239.472335</td>\n", "      <td>220.199997</td>\n", "      <td>674550.0</td>\n", "      <td>387.399994</td>\n", "      <td>241.337830</td>\n", "      <td>223.190002</td>\n", "      <td>680601.0</td>\n", "      <td>392.950012</td>\n", "      <td>37628900</td>\n", "      <td>25033300</td>\n", "      <td>1100</td>\n", "      <td>73038800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>201.449997</td>\n", "      <td>216.979996</td>\n", "      <td>739925.0</td>\n", "      <td>308.579987</td>\n", "      <td>206.000000</td>\n", "      <td>217.850006</td>\n", "      <td>741502.0</td>\n", "      <td>309.829987</td>\n", "      <td>200.020004</td>\n", "      <td>212.880005</td>\n", "      <td>733510.0</td>\n", "      <td>281.850006</td>\n", "      <td>204.389999</td>\n", "      <td>214.750000</td>\n", "      <td>741502.0</td>\n", "      <td>285.959991</td>\n", "      <td>72862600</td>\n", "      <td>38102500</td>\n", "      <td>300</td>\n", "      <td>140908900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>202.669998</td>\n", "      <td>217.610001</td>\n", "      <td>736000.0</td>\n", "      <td>326.089996</td>\n", "      <td>204.350006</td>\n", "      <td>217.690002</td>\n", "      <td>739512.0</td>\n", "      <td>327.829987</td>\n", "      <td>200.570007</td>\n", "      <td>214.149994</td>\n", "      <td>734336.0</td>\n", "      <td>310.670013</td>\n", "      <td>200.600006</td>\n", "      <td>216.779999</td>\n", "      <td>737601.0</td>\n", "      <td>314.940002</td>\n", "      <td>54672600</td>\n", "      <td>31303300</td>\n", "      <td>300</td>\n", "      <td>151256500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>198.779999</td>\n", "      <td>213.199997</td>\n", "      <td>732120.0</td>\n", "      <td>326.429993</td>\n", "      <td>204.500000</td>\n", "      <td>218.399994</td>\n", "      <td>739205.0</td>\n", "      <td>335.500000</td>\n", "      <td>198.410004</td>\n", "      <td>212.889999</td>\n", "      <td>730230.0</td>\n", "      <td>322.500000</td>\n", "      <td>203.500000</td>\n", "      <td>217.410004</td>\n", "      <td>737408.0</td>\n", "      <td>334.399994</td>\n", "      <td>60989900</td>\n", "      <td>39326000</td>\n", "      <td>300</td>\n", "      <td>122611400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>199.199997</td>\n", "      <td>213.240005</td>\n", "      <td>735000.0</td>\n", "      <td>319.109985</td>\n", "      <td>199.679993</td>\n", "      <td>213.580002</td>\n", "      <td>735000.0</td>\n", "      <td>332.559998</td>\n", "      <td>197.360001</td>\n", "      <td>211.330002</td>\n", "      <td>726821.0</td>\n", "      <td>316.859985</td>\n", "      <td>199.080002</td>\n", "      <td>211.779999</td>\n", "      <td>729200.0</td>\n", "      <td>323.079987</td>\n", "      <td>43904600</td>\n", "      <td>27640000</td>\n", "      <td>200</td>\n", "      <td>105127500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>196.449997</td>\n", "      <td>212.100006</td>\n", "      <td>731220.0</td>\n", "      <td>325.309998</td>\n", "      <td>200.369995</td>\n", "      <td>214.050003</td>\n", "      <td>736313.0</td>\n", "      <td>332.989990</td>\n", "      <td>195.699997</td>\n", "      <td>209.619995</td>\n", "      <td>727532.0</td>\n", "      <td>313.299988</td>\n", "      <td>199.729996</td>\n", "      <td>209.960007</td>\n", "      <td>727532.0</td>\n", "      <td>313.970001</td>\n", "      <td>51362400</td>\n", "      <td>29300100</td>\n", "      <td>400</td>\n", "      <td>128495300</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 20 columns</p>\n", "</div>"], "text/plain": ["Price            Close                                          High  \\\n", "Ticker            AAPL        AMZN     BRK-A        TSLA        AAPL   \n", "Date                                                                   \n", "2025-01-02  243.263199  220.220001  675500.0  379.279999  248.500565   \n", "2025-01-03  242.774368  224.190002  681460.0  410.440002  243.592387   \n", "2025-01-06  244.410416  227.610001  676604.0  411.049988  246.734810   \n", "2025-01-07  241.627136  222.110001  678560.0  394.359985  244.959095   \n", "2025-01-08  242.115952  222.130005  677925.0  394.940002  243.123531   \n", "...                ...         ...       ...         ...         ...   \n", "2025-06-09  201.449997  216.979996  739925.0  308.579987  206.000000   \n", "2025-06-10  202.669998  217.610001  736000.0  326.089996  204.350006   \n", "2025-06-11  198.779999  213.199997  732120.0  326.429993  204.500000   \n", "2025-06-12  199.199997  213.240005  735000.0  319.109985  199.679993   \n", "2025-06-13  196.449997  212.100006  731220.0  325.309998  200.369995   \n", "\n", "Price                                                Low              \\\n", "Ticker            AMZN     BRK-A        TSLA        AAPL        AMZN   \n", "Date                                                                   \n", "2025-01-02  225.149994  685330.0  392.730011  241.238085  218.190002   \n", "2025-01-03  225.360001  681583.0  411.880005  241.307905  221.619995   \n", "2025-01-06  228.839996  684124.0  426.429993  242.614744  224.839996   \n", "2025-01-07  228.380005  684655.0  414.329987  240.769205  221.460007   \n", "2025-01-08  223.520004  681074.0  402.500000  239.472335  220.199997   \n", "...                ...       ...         ...         ...         ...   \n", "2025-06-09  217.850006  741502.0  309.829987  200.020004  212.880005   \n", "2025-06-10  217.690002  739512.0  327.829987  200.570007  214.149994   \n", "2025-06-11  218.399994  739205.0  335.500000  198.410004  212.889999   \n", "2025-06-12  213.580002  735000.0  332.559998  197.360001  211.330002   \n", "2025-06-13  214.050003  736313.0  332.989990  195.699997  209.619995   \n", "\n", "Price                                   Open                        \\\n", "Ticker         BRK-A        TSLA        AAPL        AMZN     BRK-A   \n", "Date                                                                 \n", "2025-01-02  674923.0  373.040009  248.330961  222.029999  685049.0   \n", "2025-01-03  675120.0  379.450012  242.774368  222.509995  678874.0   \n", "2025-01-06  675911.0  401.700012  243.722074  226.779999  681925.0   \n", "2025-01-07  676452.0  390.000000  242.395272  227.899994  678759.0   \n", "2025-01-08  674550.0  387.399994  241.337830  223.190002  680601.0   \n", "...              ...         ...         ...         ...       ...   \n", "2025-06-09  733510.0  281.850006  204.389999  214.750000  741502.0   \n", "2025-06-10  734336.0  310.670013  200.600006  216.779999  737601.0   \n", "2025-06-11  730230.0  322.500000  203.500000  217.410004  737408.0   \n", "2025-06-12  726821.0  316.859985  199.080002  211.779999  729200.0   \n", "2025-06-13  727532.0  313.299988  199.729996  209.960007  727532.0   \n", "\n", "Price                     Volume                             \n", "Ticker            TSLA      AAPL      AMZN BRK-A       TSLA  \n", "Date                                                         \n", "2025-01-02  390.100006  55740700  33956600  1900  109710700  \n", "2025-01-03  381.480011  40244100  27515600  1300   95423300  \n", "2025-01-06  423.200012  45045600  31849800  2100   85516500  \n", "2025-01-07  405.829987  40856000  28084200  1200   75699500  \n", "2025-01-08  392.950012  37628900  25033300  1100   73038800  \n", "...                ...       ...       ...   ...        ...  \n", "2025-06-09  285.959991  72862600  38102500   300  140908900  \n", "2025-06-10  314.940002  54672600  31303300   300  151256500  \n", "2025-06-11  334.399994  60989900  39326000   300  122611400  \n", "2025-06-12  323.079987  43904600  27640000   200  105127500  \n", "2025-06-13  313.970001  51362400  29300100   400  128495300  \n", "\n", "[112 rows x 20 columns]"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["data = yf.download(\"BRK-A AAPL TSLA AMZN\", period=\"ytd\", actions=False)\n", "data"]}, {"cell_type": "code", "execution_count": 128, "id": "7d302e88", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>243.263199</td>\n", "      <td>248.500565</td>\n", "      <td>241.238085</td>\n", "      <td>248.330961</td>\n", "      <td>55740700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>242.774368</td>\n", "      <td>243.592387</td>\n", "      <td>241.307905</td>\n", "      <td>242.774368</td>\n", "      <td>40244100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>244.410416</td>\n", "      <td>246.734810</td>\n", "      <td>242.614744</td>\n", "      <td>243.722074</td>\n", "      <td>45045600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>241.627136</td>\n", "      <td>244.959095</td>\n", "      <td>240.769205</td>\n", "      <td>242.395272</td>\n", "      <td>40856000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>242.115952</td>\n", "      <td>243.123531</td>\n", "      <td>239.472335</td>\n", "      <td>241.337830</td>\n", "      <td>37628900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>201.449997</td>\n", "      <td>206.000000</td>\n", "      <td>200.020004</td>\n", "      <td>204.389999</td>\n", "      <td>72862600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>202.669998</td>\n", "      <td>204.350006</td>\n", "      <td>200.570007</td>\n", "      <td>200.600006</td>\n", "      <td>54672600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>198.779999</td>\n", "      <td>204.500000</td>\n", "      <td>198.410004</td>\n", "      <td>203.500000</td>\n", "      <td>60989900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>199.199997</td>\n", "      <td>199.679993</td>\n", "      <td>197.360001</td>\n", "      <td>199.080002</td>\n", "      <td>43904600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>196.449997</td>\n", "      <td>200.369995</td>\n", "      <td>195.699997</td>\n", "      <td>199.729996</td>\n", "      <td>51362400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 5 columns</p>\n", "</div>"], "text/plain": ["Price            Close        High         Low        Open    Volume\n", "Date                                                                \n", "2025-01-02  243.263199  248.500565  241.238085  248.330961  55740700\n", "2025-01-03  242.774368  243.592387  241.307905  242.774368  40244100\n", "2025-01-06  244.410416  246.734810  242.614744  243.722074  45045600\n", "2025-01-07  241.627136  244.959095  240.769205  242.395272  40856000\n", "2025-01-08  242.115952  243.123531  239.472335  241.337830  37628900\n", "...                ...         ...         ...         ...       ...\n", "2025-06-09  201.449997  206.000000  200.020004  204.389999  72862600\n", "2025-06-10  202.669998  204.350006  200.570007  200.600006  54672600\n", "2025-06-11  198.779999  204.500000  198.410004  203.500000  60989900\n", "2025-06-12  199.199997  199.679993  197.360001  199.080002  43904600\n", "2025-06-13  196.449997  200.369995  195.699997  199.729996  51362400\n", "\n", "[112 rows x 5 columns]"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_data = data.xs('AAPL', axis=1, level=1)\n", "apple_data"]}, {"cell_type": "code", "execution_count": null, "id": "6555e5e8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21172\\4221952574.py:1: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  data = yf.download(\"BRK-A AAPL TSLA AMZN\", period=\"ytd\",\n", "[*********************100%***********************]  4 of 4 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th colspan=\"5\" halign=\"left\">AAPL</th>\n", "      <th colspan=\"5\" halign=\"left\">TSLA</th>\n", "      <th colspan=\"5\" halign=\"left\">BRK-A</th>\n", "      <th colspan=\"5\" halign=\"left\">AMZN</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>248.330961</td>\n", "      <td>248.500565</td>\n", "      <td>241.238085</td>\n", "      <td>243.263199</td>\n", "      <td>55740700</td>\n", "      <td>390.100006</td>\n", "      <td>392.730011</td>\n", "      <td>373.040009</td>\n", "      <td>379.279999</td>\n", "      <td>109710700</td>\n", "      <td>685049.0</td>\n", "      <td>685330.0</td>\n", "      <td>674923.0</td>\n", "      <td>675500.0</td>\n", "      <td>1900</td>\n", "      <td>222.029999</td>\n", "      <td>225.149994</td>\n", "      <td>218.190002</td>\n", "      <td>220.220001</td>\n", "      <td>33956600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>242.774368</td>\n", "      <td>243.592387</td>\n", "      <td>241.307905</td>\n", "      <td>242.774368</td>\n", "      <td>40244100</td>\n", "      <td>381.480011</td>\n", "      <td>411.880005</td>\n", "      <td>379.450012</td>\n", "      <td>410.440002</td>\n", "      <td>95423300</td>\n", "      <td>678874.0</td>\n", "      <td>681583.0</td>\n", "      <td>675120.0</td>\n", "      <td>681460.0</td>\n", "      <td>1300</td>\n", "      <td>222.509995</td>\n", "      <td>225.360001</td>\n", "      <td>221.619995</td>\n", "      <td>224.190002</td>\n", "      <td>27515600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>243.722074</td>\n", "      <td>246.734810</td>\n", "      <td>242.614744</td>\n", "      <td>244.410416</td>\n", "      <td>45045600</td>\n", "      <td>423.200012</td>\n", "      <td>426.429993</td>\n", "      <td>401.700012</td>\n", "      <td>411.049988</td>\n", "      <td>85516500</td>\n", "      <td>681925.0</td>\n", "      <td>684124.0</td>\n", "      <td>675911.0</td>\n", "      <td>676604.0</td>\n", "      <td>2100</td>\n", "      <td>226.779999</td>\n", "      <td>228.839996</td>\n", "      <td>224.839996</td>\n", "      <td>227.610001</td>\n", "      <td>31849800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>242.395272</td>\n", "      <td>244.959095</td>\n", "      <td>240.769205</td>\n", "      <td>241.627136</td>\n", "      <td>40856000</td>\n", "      <td>405.829987</td>\n", "      <td>414.329987</td>\n", "      <td>390.000000</td>\n", "      <td>394.359985</td>\n", "      <td>75699500</td>\n", "      <td>678759.0</td>\n", "      <td>684655.0</td>\n", "      <td>676452.0</td>\n", "      <td>678560.0</td>\n", "      <td>1200</td>\n", "      <td>227.899994</td>\n", "      <td>228.380005</td>\n", "      <td>221.460007</td>\n", "      <td>222.110001</td>\n", "      <td>28084200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>241.337815</td>\n", "      <td>243.123515</td>\n", "      <td>239.472320</td>\n", "      <td>242.115936</td>\n", "      <td>37628900</td>\n", "      <td>392.950012</td>\n", "      <td>402.500000</td>\n", "      <td>387.399994</td>\n", "      <td>394.940002</td>\n", "      <td>73038800</td>\n", "      <td>680601.0</td>\n", "      <td>681074.0</td>\n", "      <td>674550.0</td>\n", "      <td>677925.0</td>\n", "      <td>1100</td>\n", "      <td>223.190002</td>\n", "      <td>223.520004</td>\n", "      <td>220.199997</td>\n", "      <td>222.130005</td>\n", "      <td>25033300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>204.389999</td>\n", "      <td>206.000000</td>\n", "      <td>200.020004</td>\n", "      <td>201.449997</td>\n", "      <td>72862600</td>\n", "      <td>285.959991</td>\n", "      <td>309.829987</td>\n", "      <td>281.850006</td>\n", "      <td>308.579987</td>\n", "      <td>140908900</td>\n", "      <td>741502.0</td>\n", "      <td>741502.0</td>\n", "      <td>733510.0</td>\n", "      <td>739925.0</td>\n", "      <td>300</td>\n", "      <td>214.750000</td>\n", "      <td>217.850006</td>\n", "      <td>212.880005</td>\n", "      <td>216.979996</td>\n", "      <td>38102500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>200.600006</td>\n", "      <td>204.350006</td>\n", "      <td>200.570007</td>\n", "      <td>202.669998</td>\n", "      <td>54672600</td>\n", "      <td>314.940002</td>\n", "      <td>327.829987</td>\n", "      <td>310.670013</td>\n", "      <td>326.089996</td>\n", "      <td>151256500</td>\n", "      <td>737601.0</td>\n", "      <td>739512.0</td>\n", "      <td>734336.0</td>\n", "      <td>736000.0</td>\n", "      <td>300</td>\n", "      <td>216.779999</td>\n", "      <td>217.690002</td>\n", "      <td>214.149994</td>\n", "      <td>217.610001</td>\n", "      <td>31303300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>203.500000</td>\n", "      <td>204.500000</td>\n", "      <td>198.410004</td>\n", "      <td>198.779999</td>\n", "      <td>60989900</td>\n", "      <td>334.399994</td>\n", "      <td>335.500000</td>\n", "      <td>322.500000</td>\n", "      <td>326.429993</td>\n", "      <td>122611400</td>\n", "      <td>737408.0</td>\n", "      <td>739205.0</td>\n", "      <td>730230.0</td>\n", "      <td>732120.0</td>\n", "      <td>300</td>\n", "      <td>217.410004</td>\n", "      <td>218.399994</td>\n", "      <td>212.889999</td>\n", "      <td>213.199997</td>\n", "      <td>39326000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>199.080002</td>\n", "      <td>199.679993</td>\n", "      <td>197.360001</td>\n", "      <td>199.199997</td>\n", "      <td>43904600</td>\n", "      <td>323.079987</td>\n", "      <td>332.559998</td>\n", "      <td>316.859985</td>\n", "      <td>319.109985</td>\n", "      <td>105127500</td>\n", "      <td>729200.0</td>\n", "      <td>735000.0</td>\n", "      <td>726821.0</td>\n", "      <td>735000.0</td>\n", "      <td>200</td>\n", "      <td>211.779999</td>\n", "      <td>213.580002</td>\n", "      <td>211.330002</td>\n", "      <td>213.240005</td>\n", "      <td>27640000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>199.729996</td>\n", "      <td>200.369995</td>\n", "      <td>195.699997</td>\n", "      <td>196.449997</td>\n", "      <td>51362400</td>\n", "      <td>313.970001</td>\n", "      <td>332.989990</td>\n", "      <td>313.299988</td>\n", "      <td>325.309998</td>\n", "      <td>128495300</td>\n", "      <td>727532.0</td>\n", "      <td>736313.0</td>\n", "      <td>727532.0</td>\n", "      <td>731220.0</td>\n", "      <td>400</td>\n", "      <td>209.960007</td>\n", "      <td>214.050003</td>\n", "      <td>209.619995</td>\n", "      <td>212.100006</td>\n", "      <td>29300100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 20 columns</p>\n", "</div>"], "text/plain": ["Ticker            AAPL                                                \\\n", "Price             Open        High         Low       Close    Volume   \n", "Date                                                                   \n", "2025-01-02  248.330961  248.500565  241.238085  243.263199  55740700   \n", "2025-01-03  242.774368  243.592387  241.307905  242.774368  40244100   \n", "2025-01-06  243.722074  246.734810  242.614744  244.410416  45045600   \n", "2025-01-07  242.395272  244.959095  240.769205  241.627136  40856000   \n", "2025-01-08  241.337815  243.123515  239.472320  242.115936  37628900   \n", "...                ...         ...         ...         ...       ...   \n", "2025-06-09  204.389999  206.000000  200.020004  201.449997  72862600   \n", "2025-06-10  200.600006  204.350006  200.570007  202.669998  54672600   \n", "2025-06-11  203.500000  204.500000  198.410004  198.779999  60989900   \n", "2025-06-12  199.080002  199.679993  197.360001  199.199997  43904600   \n", "2025-06-13  199.729996  200.369995  195.699997  196.449997  51362400   \n", "\n", "Ticker            TSLA                                                 \\\n", "Price             Open        High         Low       Close     Volume   \n", "Date                                                                    \n", "2025-01-02  390.100006  392.730011  373.040009  379.279999  109710700   \n", "2025-01-03  381.480011  411.880005  379.450012  410.440002   95423300   \n", "2025-01-06  423.200012  426.429993  401.700012  411.049988   85516500   \n", "2025-01-07  405.829987  414.329987  390.000000  394.359985   75699500   \n", "2025-01-08  392.950012  402.500000  387.399994  394.940002   73038800   \n", "...                ...         ...         ...         ...        ...   \n", "2025-06-09  285.959991  309.829987  281.850006  308.579987  140908900   \n", "2025-06-10  314.940002  327.829987  310.670013  326.089996  151256500   \n", "2025-06-11  334.399994  335.500000  322.500000  326.429993  122611400   \n", "2025-06-12  323.079987  332.559998  316.859985  319.109985  105127500   \n", "2025-06-13  313.970001  332.989990  313.299988  325.309998  128495300   \n", "\n", "Ticker         BRK-A                                             AMZN  \\\n", "Price           Open      High       Low     Close Volume        Open   \n", "Date                                                                    \n", "2025-01-02  685049.0  685330.0  674923.0  675500.0   1900  222.029999   \n", "2025-01-03  678874.0  681583.0  675120.0  681460.0   1300  222.509995   \n", "2025-01-06  681925.0  684124.0  675911.0  676604.0   2100  226.779999   \n", "2025-01-07  678759.0  684655.0  676452.0  678560.0   1200  227.899994   \n", "2025-01-08  680601.0  681074.0  674550.0  677925.0   1100  223.190002   \n", "...              ...       ...       ...       ...    ...         ...   \n", "2025-06-09  741502.0  741502.0  733510.0  739925.0    300  214.750000   \n", "2025-06-10  737601.0  739512.0  734336.0  736000.0    300  216.779999   \n", "2025-06-11  737408.0  739205.0  730230.0  732120.0    300  217.410004   \n", "2025-06-12  729200.0  735000.0  726821.0  735000.0    200  211.779999   \n", "2025-06-13  727532.0  736313.0  727532.0  731220.0    400  209.960007   \n", "\n", "Ticker                                                    \n", "Price             High         Low       Close    Volume  \n", "Date                                                      \n", "2025-01-02  225.149994  218.190002  220.220001  33956600  \n", "2025-01-03  225.360001  221.619995  224.190002  27515600  \n", "2025-01-06  228.839996  224.839996  227.610001  31849800  \n", "2025-01-07  228.380005  221.460007  222.110001  28084200  \n", "2025-01-08  223.520004  220.199997  222.130005  25033300  \n", "...                ...         ...         ...       ...  \n", "2025-06-09  217.850006  212.880005  216.979996  38102500  \n", "2025-06-10  217.690002  214.149994  217.610001  31303300  \n", "2025-06-11  218.399994  212.889999  213.199997  39326000  \n", "2025-06-12  213.580002  211.330002  213.240005  27640000  \n", "2025-06-13  214.050003  209.619995  212.100006  29300100  \n", "\n", "[112 rows x 20 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data = yf.download(\"BRK-A AAPL TSLA AMZN\", period=\"ytd\", group_by='ticker', actions=False)\n", "data"]}, {"cell_type": "code", "execution_count": 165, "id": "00626bda", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21172\\2793770369.py:2: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  data = yf.download(tickers, period=\"ytd\", group_by='ticker', actions=False)\n", "[*********************100%***********************]  4 of 4 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Price</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>248.330961</td>\n", "      <td>248.500565</td>\n", "      <td>241.238085</td>\n", "      <td>243.263199</td>\n", "      <td>55740700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>242.774368</td>\n", "      <td>243.592387</td>\n", "      <td>241.307905</td>\n", "      <td>242.774368</td>\n", "      <td>40244100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>243.722074</td>\n", "      <td>246.734810</td>\n", "      <td>242.614744</td>\n", "      <td>244.410416</td>\n", "      <td>45045600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>242.395272</td>\n", "      <td>244.959095</td>\n", "      <td>240.769205</td>\n", "      <td>241.627136</td>\n", "      <td>40856000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>241.337830</td>\n", "      <td>243.123531</td>\n", "      <td>239.472335</td>\n", "      <td>242.115952</td>\n", "      <td>37628900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>204.389999</td>\n", "      <td>206.000000</td>\n", "      <td>200.020004</td>\n", "      <td>201.449997</td>\n", "      <td>72862600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>200.600006</td>\n", "      <td>204.350006</td>\n", "      <td>200.570007</td>\n", "      <td>202.669998</td>\n", "      <td>54672600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>203.500000</td>\n", "      <td>204.500000</td>\n", "      <td>198.410004</td>\n", "      <td>198.779999</td>\n", "      <td>60989900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>199.080002</td>\n", "      <td>199.679993</td>\n", "      <td>197.360001</td>\n", "      <td>199.199997</td>\n", "      <td>43904600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>199.729996</td>\n", "      <td>200.369995</td>\n", "      <td>195.699997</td>\n", "      <td>196.449997</td>\n", "      <td>51362400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 5 columns</p>\n", "</div>"], "text/plain": ["Price             Open        High         Low       Close    Volume\n", "Date                                                                \n", "2025-01-02  248.330961  248.500565  241.238085  243.263199  55740700\n", "2025-01-03  242.774368  243.592387  241.307905  242.774368  40244100\n", "2025-01-06  243.722074  246.734810  242.614744  244.410416  45045600\n", "2025-01-07  242.395272  244.959095  240.769205  241.627136  40856000\n", "2025-01-08  241.337830  243.123531  239.472335  242.115952  37628900\n", "...                ...         ...         ...         ...       ...\n", "2025-06-09  204.389999  206.000000  200.020004  201.449997  72862600\n", "2025-06-10  200.600006  204.350006  200.570007  202.669998  54672600\n", "2025-06-11  203.500000  204.500000  198.410004  198.779999  60989900\n", "2025-06-12  199.080002  199.679993  197.360001  199.199997  43904600\n", "2025-06-13  199.729996  200.369995  195.699997  196.449997  51362400\n", "\n", "[112 rows x 5 columns]"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["tickers = ['BRK-A', 'US0378331005', 'TSLA', 'AMZN']\n", "data = yf.download(tickers, period=\"ytd\", group_by='ticker', actions=False)\n", "amzn=data[\"AMZN\"]\n", "US0378331005=data[\"US0378331005\"] # aapl\n", "US0378331005"]}, {"cell_type": "code", "execution_count": 16, "id": "b74fe69d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'US0846701086': yfinance.Ticker object <BRH.DU>, 'US0378331005': yfinance.Ticker object <AAPL>}\n", "{'maxAge': 86400, 'priceHint': 2, 'previousClose': 629000.0, 'open': 623000.0, 'dayLow': 622000.0, 'dayHigh': 625000.0, 'regularMarketPreviousClose': 629000.0, 'regularMarketOpen': 623000.0, 'regularMarketDayLow': 622000.0, 'regularMarketDayHigh': 625000.0, 'volume': 0, 'regularMarketVolume': 0, 'averageVolume': 0, 'averageVolume10days': 0, 'averageDailyVolume10Day': 0, 'bid': 629000.0, 'ask': 636500.0, 'bidSize': 0, 'askSize': 0, 'fiftyTwoWeekLow': 411.36, 'fiftyTwoWeekHigh': 741500.0, 'fiftyDayAverage': 673470.0, 'twoHundredDayAverage': 637541.44, 'currency': 'EUR', 'tradeable': False, 'quoteType': 'EQUITY', 'symbol': 'BRH.DU', 'language': 'en-US', 'region': 'US', 'typeDisp': 'Equity', 'quoteSourceName': 'Delayed Quote', 'triggerable': False, 'customPriceAlertConfidence': 'LOW', 'hasPrePostMarketData': False, 'firstTradeDateMilliseconds': 1025848800000, 'marketState': 'CLOSED', 'regularMarketPrice': 631000.0, 'regularMarketChange': 2000.0, 'regularMarketDayRange': '622000.0 - 625000.0', 'fullExchangeName': 'Dusseldorf', 'averageDailyVolume3Month': 0, 'fiftyTwoWeekLowChange': 630588.6, 'fiftyTwoWeekLowChangePercent': 1532.9363, 'fiftyTwoWeekRange': '411.36 - 741500.0', 'fiftyTwoWeekHighChange': -110500.0, 'fiftyTwoWeekHighChangePercent': -0.14902225, 'fiftyTwoWeekChangePercent': 11.1894245, 'earningsTimestamp': 1746273600, 'earningsTimestampStart': 1754055000, 'earningsTimestampEnd': 1754400600, 'isEarningsDateEstimate': True, 'fiftyDayAverageChange': -42470.0, 'fiftyDayAverageChangePercent': -0.06306146, 'twoHundredDayAverageChange': -6541.4375, 'twoHundredDayAverageChangePercent': -0.010260412, 'sourceInterval': 15, 'exchangeDataDelayedBy': 15, 'cryptoTradeable': False, 'regularMarketChangePercent': 0.31796503, 'corporateActions': [], 'regularMarketTime': 1749835817, 'exchange': 'DUS', 'exchangeTimezoneName': 'Europe/Berlin', 'exchangeTimezoneShortName': 'CEST', 'gmtOffSetMilliseconds': 7200000, 'market': 'dr_market', 'esgPopulated': False, 'shortName': 'BERKSHIRE HATHAWAY INC.       R', 'longName': 'Berkshire Hathaway Inc', 'trailingPegRatio': None}\n", "BRH.DU\n"]}], "source": ["# BRK-A US0846701086\n", "# AAPL US0378331005\n", "tickers = yf.Tickers('US0846701086 US0378331005')\n", "print(tickers.tickers) # {'US0846701086': yfinance.Ticker object <BRK-A>, 'US0378331005': yfinance.Ticker object <AAPL>}\n", "print(tickers.tickers['US0846701086'].info) # {'address1': 'One Apple Park Way', 'city': 'Cupertino', 'state': 'CA', 'zip': '95014', 'country': 'United States', 'phone': '(*************', 'website': 'https://www.apple.com', 'industry': 'Consumer Electronics', 'industryKey': 'consumer-electronics', 'industryDisp': 'Consumer Electronics', 'sector': 'Technology', 'sectorKey': 'technology', 'sectorDisp': 'Technology', 'longBusinessSummary': 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide. The company offers iPhone, a line of smartphones; Mac, a line of personal computers; iPad, a line of multi-purpose tablets; and wearables, home, and accessories comprising AirPods, Apple TV, Apple Watch, Beats products, and HomePod. It also provides AppleCare support and cloud services; and operates various platforms, including the App Store that allow customers to discover and download applications and digital content, such as books, music, video, games, and podcasts, as well as advertising services include third-party licensing arrangements and its own advertising platforms. In addition, the company offers various subscription-based services, such as Apple Arcade, a game subscription service; Apple Fitness+, a personalized fitness service; Apple Music, which offers users a curated listening experience with on-demand radio stations; Apple News+, a subscription news and magazine service; Apple TV+, which offers exclusive original content; Apple Card, a co-branded credit card; and Apple Pay, a cashless payment service, as well as licenses its intellectual property. The company serves consumers, and small and mid-sized businesses; and the education, enterprise, and government markets. It distributes third-party applications for its products through the App Store. The company also sells its products through its retail and online stores, and direct sales force; and third-party cellular network carriers, wholesalers, retailers, and resellers. Apple Inc. was founded in 1976 and is headquartered in Cupertino, California.', 'fullTimeEmployees': 164000, 'companyOfficers': [{'maxAge': 1, 'name': 'Mr. Timothy D. Cook', 'age': 63, 'title': 'CEO & Director', 'yearBorn': 1961, 'fiscalYear': 2024, 'totalPay': 16520856, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Mr. Jeffrey E. Williams', 'age': 60, 'title': 'Chief Operating Officer', 'yearBorn': 1964, 'fiscalYear': 2024, 'totalPay': 5020737, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Ms. Katherine L. Adams', 'age': 60, 'title': 'Senior VP, General Counsel & Secretary', 'yearBorn': 1964, 'fiscalYear': 2024, 'totalPay': 5022182, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': \"Ms. Deirdre  O'Brien\", 'age': 57, 'title': 'Chief People Officer & Senior VP of Retail', 'yearBorn': 1967, 'fiscalYear': 2024, 'totalPay': 5022182, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Mr. Kevan  Parekh', 'age': 52, 'title': 'Senior VP & CFO', 'yearBorn': 1972, 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Mr. Chris  Kondo', 'title': 'Senior Director of Corporate Accounting', 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Suhasini  Chandramouli', 'title': 'Director of Investor Relations', 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Ms. Kristin Huguet Quayle', 'title': 'Vice President of Worldwide Communications', 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Mr. Greg  Joswiak', 'title': 'Senior Vice President of Worldwide Marketing', 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}, {'maxAge': 1, 'name': 'Mr. Adrian  Perica', 'age': 50, 'title': 'Vice President of Corporate Development', 'yearBorn': 1974, 'fiscalYear': 2024, 'exercisedValue': 0, 'unexercisedValue': 0}], 'auditRisk': 7, 'boardRisk': 1, 'compensationRisk': 3, 'shareHolderRightsRisk': 1, 'overallRisk': 1, 'governanceEpochDate': **********, 'compensationAsOfEpochDate': **********, 'irWebsite': 'http://investor.apple.com/', 'executiveTeam': [], 'maxAge': 86400, 'priceHint': 2, 'previousClose': 199.2, 'open': 199.73, 'dayLow': 195.77, 'dayHigh': 200.37, 'regularMarketPreviousClose': 199.2, 'regularMarketOpen': 199.73, 'regularMarketDayLow': 195.77, 'regularMarketDayHigh': 200.37, 'dividendRate': 1.04, 'dividendYield': 0.53, 'exDividendDate': 1747008000, 'payoutRatio': 0.1558, 'fiveYearAvgDividendYield': 0.56, 'beta': 1.211, 'trailingPE': 30.599688, 'forwardPE': 23.64019, 'volume': 50778564, 'regularMarketVolume': 50778564, 'averageVolume': 61214365, 'averageVolume10days': 51093480, 'averageDailyVolume10Day': 51093480, 'bid': 196.21, 'ask': 196.7, 'bidSize': 4, 'askSize': 4, 'marketCap': 2934137946112, 'fiftyTwoWeekLow': 169.21, 'fiftyTwoWeekHigh': 260.1, 'priceToSalesTrailing12Months': 7.328639, 'fiftyDayAverage': 201.4312, 'twoHundredDayAverage': 224.45325, 'trailingAnnualDividendRate': 1.0, 'trailingAnnualDividendYield': 0.0050200806, 'currency': 'USD', 'tradeable': False, 'enterpriseValue': 3024904519680, 'profitMargins': 0.24301, 'floatShares': 14911480604, 'sharesOutstanding': 14935799808, 'sharesShort': 94828443, 'sharesShortPriorMonth': 108598767, 'sharesShortPreviousMonthDate': 1745971200, 'dateShortInterest': 1748563200, 'sharesPercentSharesOut': 0.0063, 'heldPercentInsiders': 0.02085, 'heldPercentInstitutions': 0.62893003, 'shortRatio': 1.67, 'shortPercentOfFloat': 0.0064, 'impliedSharesOutstanding': 15118999552, 'bookValue': 4.471, 'priceToBook': 43.938713, 'lastFiscalYearEnd': 1727481600, 'nextFiscalYearEnd': 1759017600, 'mostRecentQuarter': 1743206400, 'earningsQuarterlyGrowth': 0.048, 'netIncomeToCommon': 97294000128, 'trailingEps': 6.42, 'forwardEps': 8.31, 'lastSplitFactor': '4:1', 'lastSplitDate': 1598832000, 'enterpriseToRevenue': 7.555, 'enterpriseToEbitda': 21.783, '52WeekChange': -0.09332162, 'SandP52WeekChange': 0.09203708, 'lastDividendValue': 0.26, 'lastDividendDate': 1747008000, 'quoteType': 'EQUITY', 'currentPrice': 196.45, 'targetHighPrice': 300.0, 'targetLowPrice': 170.62, 'targetMeanPrice': 228.85326, 'targetMedianPrice': 232.5, 'recommendationMean': 2.1087, 'recommendationKey': 'buy', 'numberOfAnalystOpinions': 40, 'totalCash': 48497999872, 'totalCashPerShare': 3.247, 'ebitda': 138865999872, 'totalDebt': 98186002432, 'quickRatio': 0.68, 'currentRatio': 0.821, 'totalRevenue': 400366010368, 'debtToEquity': 146.994, 'revenuePerShare': 26.455, 'returnOnAssets': 0.23809999, 'returnOnEquity': 1.38015, 'grossProfits': 186699005952, 'freeCashflow': 97251500032, 'operatingCashflow': 109555998720, 'earningsGrowth': 0.078, 'revenueGrowth': 0.051, 'grossMargins': 0.46632, 'ebitdaMargins': 0.34685, 'operatingMargins': 0.31028998, 'financialCurrency': 'USD', 'symbol': 'AAPL', 'language': 'en-US', 'region': 'US', 'typeDisp': 'Equity', 'quoteSourceName': 'Delayed Quote', 'triggerable': True, 'customPriceAlertConfidence': 'HIGH', 'shortName': 'Apple Inc.', 'longName': 'Apple Inc.', 'corporateActions': [], 'postMarketTime': 1749859199, 'regularMarketTime': 1749844801, 'exchange': 'NMS', 'messageBoardId': 'finmb_24937', 'exchangeTimezoneName': 'America/New_York', 'exchangeTimezoneShortName': 'EDT', 'gmtOffSetMilliseconds': -14400000, 'market': 'us_market', 'esgPopulated': False, 'regularMarketChangePercent': -1.38052, 'regularMarketPrice': 196.45, 'marketState': 'CLOSED', 'hasPrePostMarketData': True, 'firstTradeDateMilliseconds': 345479400000, 'postMarketChangePercent': -0.025453323, 'postMarketPrice': 196.4, 'postMarketChange': -0.05000305, 'regularMarketChange': -2.75, 'regularMarketDayRange': '195.77 - 200.37', 'fullExchangeName': 'NasdaqGS', 'averageDailyVolume3Month': 61214365, 'fiftyTwoWeekLowChange': 27.23999, 'fiftyTwoWeekLowChangePercent': 0.16098332, 'fiftyTwoWeekRange': '169.21 - 260.1', 'fiftyTwoWeekHighChange': -63.65001, 'fiftyTwoWeekHighChangePercent': -0.2447136, 'fiftyTwoWeekChangePercent': -9.332162, 'dividendDate': 1747267200, 'earningsTimestamp': 1746131400, 'earningsTimestampStart': 1753873140, 'earningsTimestampEnd': 1754308800, 'earningsCallTimestampStart': 1746133200, 'earningsCallTimestampEnd': 1746133200, 'isEarningsDateEstimate': True, 'epsTrailingTwelveMonths': 6.42, 'epsForward': 8.31, 'epsCurrentYear': 7.18956, 'priceEpsCurrentYear': 27.324343, 'fiftyDayAverageChange': -4.981201, 'fiftyDayAverageChangePercent': -0.024729045, 'twoHundredDayAverageChange': -28.00325, 'twoHundredDayAverageChangePercent': -0.124762066, 'sourceInterval': 15, 'exchangeDataDelayedBy': 0, 'averageAnalystRating': '2.1 - Buy', 'cryptoTradeable': False, 'displayName': 'Apple', 'trailingPegRatio': 1.7837}\n", "print(tickers.tickers['US0846701086'].ticker) # AAPL"]}, {"cell_type": "code", "execution_count": 177, "id": "3dd108d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  2 of 2 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th colspan=\"7\" halign=\"left\">BRK-A</th>\n", "      <th colspan=\"7\" halign=\"left\">US0378331005</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>685049.0</td>\n", "      <td>685330.0</td>\n", "      <td>674923.0</td>\n", "      <td>675500.0</td>\n", "      <td>1900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>248.330961</td>\n", "      <td>248.500565</td>\n", "      <td>241.238085</td>\n", "      <td>243.263199</td>\n", "      <td>55740700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>678874.0</td>\n", "      <td>681583.0</td>\n", "      <td>675120.0</td>\n", "      <td>681460.0</td>\n", "      <td>1300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>242.774368</td>\n", "      <td>243.592387</td>\n", "      <td>241.307905</td>\n", "      <td>242.774368</td>\n", "      <td>40244100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>681925.0</td>\n", "      <td>684124.0</td>\n", "      <td>675911.0</td>\n", "      <td>676604.0</td>\n", "      <td>2100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>243.722074</td>\n", "      <td>246.734810</td>\n", "      <td>242.614744</td>\n", "      <td>244.410416</td>\n", "      <td>45045600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>678759.0</td>\n", "      <td>684655.0</td>\n", "      <td>676452.0</td>\n", "      <td>678560.0</td>\n", "      <td>1200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>242.395272</td>\n", "      <td>244.959095</td>\n", "      <td>240.769205</td>\n", "      <td>241.627136</td>\n", "      <td>40856000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>680601.0</td>\n", "      <td>681074.0</td>\n", "      <td>674550.0</td>\n", "      <td>677925.0</td>\n", "      <td>1100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>241.337815</td>\n", "      <td>243.123515</td>\n", "      <td>239.472320</td>\n", "      <td>242.115936</td>\n", "      <td>37628900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>741502.0</td>\n", "      <td>741502.0</td>\n", "      <td>733510.0</td>\n", "      <td>739925.0</td>\n", "      <td>300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>204.389999</td>\n", "      <td>206.000000</td>\n", "      <td>200.020004</td>\n", "      <td>201.449997</td>\n", "      <td>72862600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>737601.0</td>\n", "      <td>739512.0</td>\n", "      <td>734336.0</td>\n", "      <td>736000.0</td>\n", "      <td>300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>200.600006</td>\n", "      <td>204.350006</td>\n", "      <td>200.570007</td>\n", "      <td>202.669998</td>\n", "      <td>54672600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>737408.0</td>\n", "      <td>739205.0</td>\n", "      <td>730230.0</td>\n", "      <td>732120.0</td>\n", "      <td>300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>203.500000</td>\n", "      <td>204.500000</td>\n", "      <td>198.410004</td>\n", "      <td>198.779999</td>\n", "      <td>60989900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>729200.0</td>\n", "      <td>735000.0</td>\n", "      <td>726821.0</td>\n", "      <td>735000.0</td>\n", "      <td>200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>199.080002</td>\n", "      <td>199.679993</td>\n", "      <td>197.360001</td>\n", "      <td>199.199997</td>\n", "      <td>43904600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>727532.0</td>\n", "      <td>736313.0</td>\n", "      <td>727532.0</td>\n", "      <td>731220.0</td>\n", "      <td>400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>199.729996</td>\n", "      <td>200.369995</td>\n", "      <td>195.699997</td>\n", "      <td>196.449997</td>\n", "      <td>51362400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 14 columns</p>\n", "</div>"], "text/plain": ["Ticker         BRK-A                                                 \\\n", "Price           Open      High       Low     Close Volume Dividends   \n", "Date                                                                  \n", "2025-01-02  685049.0  685330.0  674923.0  675500.0   1900       0.0   \n", "2025-01-03  678874.0  681583.0  675120.0  681460.0   1300       0.0   \n", "2025-01-06  681925.0  684124.0  675911.0  676604.0   2100       0.0   \n", "2025-01-07  678759.0  684655.0  676452.0  678560.0   1200       0.0   \n", "2025-01-08  680601.0  681074.0  674550.0  677925.0   1100       0.0   \n", "...              ...       ...       ...       ...    ...       ...   \n", "2025-06-09  741502.0  741502.0  733510.0  739925.0    300       0.0   \n", "2025-06-10  737601.0  739512.0  734336.0  736000.0    300       0.0   \n", "2025-06-11  737408.0  739205.0  730230.0  732120.0    300       0.0   \n", "2025-06-12  729200.0  735000.0  726821.0  735000.0    200       0.0   \n", "2025-06-13  727532.0  736313.0  727532.0  731220.0    400       0.0   \n", "\n", "Ticker                  US0378331005                                      \\\n", "Price      Stock Splits         Open        High         Low       Close   \n", "Date                                                                       \n", "2025-01-02          0.0   248.330961  248.500565  241.238085  243.263199   \n", "2025-01-03          0.0   242.774368  243.592387  241.307905  242.774368   \n", "2025-01-06          0.0   243.722074  246.734810  242.614744  244.410416   \n", "2025-01-07          0.0   242.395272  244.959095  240.769205  241.627136   \n", "2025-01-08          0.0   241.337815  243.123515  239.472320  242.115936   \n", "...                 ...          ...         ...         ...         ...   \n", "2025-06-09          0.0   204.389999  206.000000  200.020004  201.449997   \n", "2025-06-10          0.0   200.600006  204.350006  200.570007  202.669998   \n", "2025-06-11          0.0   203.500000  204.500000  198.410004  198.779999   \n", "2025-06-12          0.0   199.080002  199.679993  197.360001  199.199997   \n", "2025-06-13          0.0   199.729996  200.369995  195.699997  196.449997   \n", "\n", "Ticker                                       \n", "Price         Volume Dividends Stock Splits  \n", "Date                                         \n", "2025-01-02  55740700       0.0          0.0  \n", "2025-01-03  40244100       0.0          0.0  \n", "2025-01-06  45045600       0.0          0.0  \n", "2025-01-07  40856000       0.0          0.0  \n", "2025-01-08  37628900       0.0          0.0  \n", "...              ...       ...          ...  \n", "2025-06-09  72862600       0.0          0.0  \n", "2025-06-10  54672600       0.0          0.0  \n", "2025-06-11  60989900       0.0          0.0  \n", "2025-06-12  43904600       0.0          0.0  \n", "2025-06-13  51362400       0.0          0.0  \n", "\n", "[112 rows x 14 columns]"]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["data = tickers.history(period=\"ytd\", group_by='ticker')\n", "data"]}, {"cell_type": "code", "execution_count": 178, "id": "3c649b4b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  2 of 2 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th colspan=\"2\" halign=\"left\">Close</th>\n", "      <th colspan=\"2\" halign=\"left\">Dividends</th>\n", "      <th colspan=\"2\" halign=\"left\">High</th>\n", "      <th colspan=\"2\" halign=\"left\">Low</th>\n", "      <th colspan=\"2\" halign=\"left\">Open</th>\n", "      <th colspan=\"2\" halign=\"left\">Stock Splits</th>\n", "      <th colspan=\"2\" halign=\"left\">Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "      <th>BRK-A</th>\n", "      <th>US0378331005</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>675500.0</td>\n", "      <td>243.263199</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>685330.0</td>\n", "      <td>248.500565</td>\n", "      <td>674923.0</td>\n", "      <td>241.238085</td>\n", "      <td>685049.0</td>\n", "      <td>248.330961</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1900</td>\n", "      <td>55740700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>681460.0</td>\n", "      <td>242.774368</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>681583.0</td>\n", "      <td>243.592387</td>\n", "      <td>675120.0</td>\n", "      <td>241.307905</td>\n", "      <td>678874.0</td>\n", "      <td>242.774368</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1300</td>\n", "      <td>40244100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>676604.0</td>\n", "      <td>244.410416</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>684124.0</td>\n", "      <td>246.734810</td>\n", "      <td>675911.0</td>\n", "      <td>242.614744</td>\n", "      <td>681925.0</td>\n", "      <td>243.722074</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2100</td>\n", "      <td>45045600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-07</th>\n", "      <td>678560.0</td>\n", "      <td>241.627136</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>684655.0</td>\n", "      <td>244.959095</td>\n", "      <td>676452.0</td>\n", "      <td>240.769205</td>\n", "      <td>678759.0</td>\n", "      <td>242.395272</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1200</td>\n", "      <td>40856000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-08</th>\n", "      <td>677925.0</td>\n", "      <td>242.115936</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>681074.0</td>\n", "      <td>243.123515</td>\n", "      <td>674550.0</td>\n", "      <td>239.472320</td>\n", "      <td>680601.0</td>\n", "      <td>241.337815</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1100</td>\n", "      <td>37628900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09</th>\n", "      <td>739925.0</td>\n", "      <td>201.449997</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>741502.0</td>\n", "      <td>206.000000</td>\n", "      <td>733510.0</td>\n", "      <td>200.020004</td>\n", "      <td>741502.0</td>\n", "      <td>204.389999</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>300</td>\n", "      <td>72862600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10</th>\n", "      <td>736000.0</td>\n", "      <td>202.669998</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>739512.0</td>\n", "      <td>204.350006</td>\n", "      <td>734336.0</td>\n", "      <td>200.570007</td>\n", "      <td>737601.0</td>\n", "      <td>200.600006</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>300</td>\n", "      <td>54672600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11</th>\n", "      <td>732120.0</td>\n", "      <td>198.779999</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>739205.0</td>\n", "      <td>204.500000</td>\n", "      <td>730230.0</td>\n", "      <td>198.410004</td>\n", "      <td>737408.0</td>\n", "      <td>203.500000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>300</td>\n", "      <td>60989900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12</th>\n", "      <td>735000.0</td>\n", "      <td>199.199997</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>735000.0</td>\n", "      <td>199.679993</td>\n", "      <td>726821.0</td>\n", "      <td>197.360001</td>\n", "      <td>729200.0</td>\n", "      <td>199.080002</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>200</td>\n", "      <td>43904600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13</th>\n", "      <td>731220.0</td>\n", "      <td>196.449997</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>736313.0</td>\n", "      <td>200.369995</td>\n", "      <td>727532.0</td>\n", "      <td>195.699997</td>\n", "      <td>727532.0</td>\n", "      <td>199.729996</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>400</td>\n", "      <td>51362400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 14 columns</p>\n", "</div>"], "text/plain": ["Price          Close              Dividends                   High  \\\n", "Ticker         BRK-A US0378331005     BRK-A US0378331005     BRK-A   \n", "Date                                                                 \n", "2025-01-02  675500.0   243.263199       0.0          0.0  685330.0   \n", "2025-01-03  681460.0   242.774368       0.0          0.0  681583.0   \n", "2025-01-06  676604.0   244.410416       0.0          0.0  684124.0   \n", "2025-01-07  678560.0   241.627136       0.0          0.0  684655.0   \n", "2025-01-08  677925.0   242.115936       0.0          0.0  681074.0   \n", "...              ...          ...       ...          ...       ...   \n", "2025-06-09  739925.0   201.449997       0.0          0.0  741502.0   \n", "2025-06-10  736000.0   202.669998       0.0          0.0  739512.0   \n", "2025-06-11  732120.0   198.779999       0.0          0.0  739205.0   \n", "2025-06-12  735000.0   199.199997       0.0          0.0  735000.0   \n", "2025-06-13  731220.0   196.449997       0.0          0.0  736313.0   \n", "\n", "Price                         Low                   Open               \\\n", "Ticker     US0378331005     BRK-A US0378331005     BRK-A US0378331005   \n", "Date                                                                    \n", "2025-01-02   248.500565  674923.0   241.238085  685049.0   248.330961   \n", "2025-01-03   243.592387  675120.0   241.307905  678874.0   242.774368   \n", "2025-01-06   246.734810  675911.0   242.614744  681925.0   243.722074   \n", "2025-01-07   244.959095  676452.0   240.769205  678759.0   242.395272   \n", "2025-01-08   243.123515  674550.0   239.472320  680601.0   241.337815   \n", "...                 ...       ...          ...       ...          ...   \n", "2025-06-09   206.000000  733510.0   200.020004  741502.0   204.389999   \n", "2025-06-10   204.350006  734336.0   200.570007  737601.0   200.600006   \n", "2025-06-11   204.500000  730230.0   198.410004  737408.0   203.500000   \n", "2025-06-12   199.679993  726821.0   197.360001  729200.0   199.080002   \n", "2025-06-13   200.369995  727532.0   195.699997  727532.0   199.729996   \n", "\n", "Price      Stock Splits              Volume               \n", "Ticker            BRK-A US0378331005  BRK-A US0378331005  \n", "Date                                                      \n", "2025-01-02          0.0          0.0   1900     55740700  \n", "2025-01-03          0.0          0.0   1300     40244100  \n", "2025-01-06          0.0          0.0   2100     45045600  \n", "2025-01-07          0.0          0.0   1200     40856000  \n", "2025-01-08          0.0          0.0   1100     37628900  \n", "...                 ...          ...    ...          ...  \n", "2025-06-09          0.0          0.0    300     72862600  \n", "2025-06-10          0.0          0.0    300     54672600  \n", "2025-06-11          0.0          0.0    300     60989900  \n", "2025-06-12          0.0          0.0    200     43904600  \n", "2025-06-13          0.0          0.0    400     51362400  \n", "\n", "[112 rows x 14 columns]"]}, "execution_count": 178, "metadata": {}, "output_type": "execute_result"}], "source": ["data = tickers.history(period=\"ytd\")\n", "data"]}, {"cell_type": "markdown", "id": "30ebd5c728725e6d", "metadata": {}, "source": ["# Ticker\n", "\n", "Getting information using Ticker module\n", "\n", "## info\n"]}, {"cell_type": "code", "execution_count": 9, "id": "42a0d5eaa72753c6", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:23:29.438906Z", "start_time": "2025-06-16T03:23:27.343631Z"}}, "outputs": [{"data": {"text/plain": ["{'maxAge': 86400,\n", " 'priceHint': 2,\n", " 'previousClose': 3452.8,\n", " 'open': 3473.0,\n", " 'dayLow': 3444.0,\n", " 'dayHigh': 3476.3,\n", " 'regularMarketPreviousClose': 3452.8,\n", " 'regularMarketOpen': 3473.0,\n", " 'regularMarketDayLow': 3444.0,\n", " 'regularMarketDayHigh': 3476.3,\n", " 'volume': 38634,\n", " 'regularMarketVolume': 38634,\n", " 'averageVolume': 6155,\n", " 'averageVolume10days': 1609,\n", " 'averageDailyVolume10Day': 1609,\n", " 'bid': 3455.0,\n", " 'ask': 3456.5,\n", " 'bidSize': 14,\n", " 'askSize': 8,\n", " 'expireDate': 1756252800,\n", " 'openInterest': 326434,\n", " 'fiftyTwoWeekLow': 2295.0,\n", " 'fiftyTwoWeekHigh': 3485.6,\n", " 'fiftyDayAverage': 3272.5,\n", " 'twoHundredDayAverage': 2874.5195,\n", " 'currency': 'USD',\n", " 'tradeable': <PERSON><PERSON><PERSON>,\n", " 'quoteType': 'FUTURE',\n", " 'symbol': 'GC=F',\n", " 'language': 'en-US',\n", " 'region': 'US',\n", " 'typeDisp': 'Futures',\n", " 'quoteSourceName': 'Delayed Quote',\n", " 'triggerable': <PERSON><PERSON><PERSON>,\n", " 'customPriceAlertConfidence': 'NONE',\n", " 'headSymbolAsString': 'GC=F',\n", " 'contractSymbol': F<PERSON>e,\n", " 'marketState': 'REGULAR',\n", " 'shortName': 'Gold Aug 25',\n", " 'underlyingSymbol': 'GC.CMX',\n", " 'underlyingExchangeSymbol': 'GCQ25.CMX',\n", " 'exchange': 'CMX',\n", " 'exchangeTimezoneName': 'America/New_York',\n", " 'exchangeTimezoneShortName': 'EDT',\n", " 'gmtOffSetMilliseconds': -14400000,\n", " 'market': 'us24_market',\n", " 'esgPopulated': <PERSON><PERSON><PERSON>,\n", " 'regularMarketChangePercent': 0.13611999,\n", " 'regularMarketPrice': 3457.5,\n", " 'cryptoTradeable': <PERSON><PERSON><PERSON>,\n", " 'hasPrePostMarketData': F<PERSON>e,\n", " 'firstTradeDateMilliseconds': 967608000000,\n", " 'regularMarketChange': 4.699951,\n", " 'regularMarketDayRange': '3444.0 - 3476.3',\n", " 'fullExchangeName': 'COMEX',\n", " 'averageDailyVolume3Month': 6155,\n", " 'fiftyTwoWeekLowChange': 1162.5,\n", " 'fiftyTwoWeekLowChangePercent': 0.50653595,\n", " 'fiftyTwoWeekRange': '2295.0 - 3485.6',\n", " 'fiftyTwoWeekHighChange': -28.100098,\n", " 'fiftyTwoWeekHighChangePercent': -0.008061768,\n", " 'fiftyTwoWeekChangePercent': 48.38263,\n", " 'expireIsoDate': '2025-08-27T00:00:00Z',\n", " 'fiftyDayAverageChange': 185.0,\n", " 'fiftyDayAverageChangePercent': 0.056531705,\n", " 'twoHundredDayAverageChange': 582.98047,\n", " 'twoHundredDayAverageChangePercent': 0.2028097,\n", " 'sourceInterval': 15,\n", " 'exchangeDataDelayedBy': 10,\n", " 'corporateActions': [],\n", " 'regularMarketTime': 1750043596,\n", " 'trailingPegRatio': None}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["gold = yf.Ticker(gold_ticker)\n", "aapl = yf.Ticker('AAPL')\n", "gold.info"]}, {"cell_type": "code", "execution_count": 11, "id": "8b558eb929b899eb", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:31:08.364768Z", "start_time": "2025-06-16T03:31:08.357533Z"}}, "outputs": [{"data": {"text/plain": ["{'address1': 'One Apple Park Way',\n", " 'city': 'Cupertino',\n", " 'state': 'CA',\n", " 'zip': '95014',\n", " 'country': 'United States',\n", " 'phone': '(*************',\n", " 'website': 'https://www.apple.com',\n", " 'industry': 'Consumer Electronics',\n", " 'industryKey': 'consumer-electronics',\n", " 'industryDisp': 'Consumer Electronics',\n", " 'sector': 'Technology',\n", " 'sectorKey': 'technology',\n", " 'sectorDisp': 'Technology',\n", " 'longBusinessSummary': 'Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide. The company offers iPhone, a line of smartphones; Mac, a line of personal computers; iPad, a line of multi-purpose tablets; and wearables, home, and accessories comprising AirPods, Apple TV, Apple Watch, Beats products, and HomePod. It also provides AppleCare support and cloud services; and operates various platforms, including the App Store that allow customers to discover and download applications and digital content, such as books, music, video, games, and podcasts, as well as advertising services include third-party licensing arrangements and its own advertising platforms. In addition, the company offers various subscription-based services, such as Apple Arcade, a game subscription service; Apple Fitness+, a personalized fitness service; Apple Music, which offers users a curated listening experience with on-demand radio stations; Apple News+, a subscription news and magazine service; Apple TV+, which offers exclusive original content; Apple Card, a co-branded credit card; and Apple Pay, a cashless payment service, as well as licenses its intellectual property. The company serves consumers, and small and mid-sized businesses; and the education, enterprise, and government markets. It distributes third-party applications for its products through the App Store. The company also sells its products through its retail and online stores, and direct sales force; and third-party cellular network carriers, wholesalers, retailers, and resellers. Apple Inc. was founded in 1976 and is headquartered in Cupertino, California.',\n", " 'fullTimeEmployees': 164000,\n", " 'companyOfficers': [{'maxAge': 1,\n", "   'name': 'Mr. <PERSON>',\n", "   'age': 63,\n", "   'title': 'CEO & Director',\n", "   'yearBorn': 1961,\n", "   'fiscalYear': 2024,\n", "   'totalPay': 16520856,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': 'Mr. <PERSON>',\n", "   'age': 60,\n", "   'title': 'Chief Operating Officer',\n", "   'yearBorn': 1964,\n", "   'fiscalYear': 2024,\n", "   'totalPay': 5020737,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': '<PERSON>. <PERSON>',\n", "   'age': 60,\n", "   'title': 'Senior VP, General Counsel & Secretary',\n", "   'yearBorn': 1964,\n", "   'fiscalYear': 2024,\n", "   'totalPay': 5022182,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': \"<PERSON><PERSON> <PERSON><PERSON>\",\n", "   'age': 57,\n", "   'title': 'Chief People Officer & Senior VP of Retail',\n", "   'yearBorn': 1967,\n", "   'fiscalYear': 2024,\n", "   'totalPay': 5022182,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': 'Mr. <PERSON><PERSON>',\n", "   'age': 52,\n", "   'title': 'Senior VP & CFO',\n", "   'yearBorn': 1972,\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': 'Mr. <PERSON>',\n", "   'title': 'Senior Director of Corporate Accounting',\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': '<PERSON><PERSON><PERSON>',\n", "   'title': 'Director of Investor Relations',\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': '<PERSON>. <PERSON><PERSON>',\n", "   'title': 'Vice President of Worldwide Communications',\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': 'Mr. <PERSON>',\n", "   'title': 'Senior Vice President of Worldwide Marketing',\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0},\n", "  {'maxAge': 1,\n", "   'name': 'Mr. <PERSON>',\n", "   'age': 50,\n", "   'title': 'Vice President of Corporate Development',\n", "   'yearBorn': 1974,\n", "   'fiscalYear': 2024,\n", "   'exercisedValue': 0,\n", "   'unexercisedValue': 0}],\n", " 'auditRisk': 7,\n", " 'boardRisk': 1,\n", " 'compensationRisk': 3,\n", " 'shareHolderRightsRisk': 1,\n", " 'overallRisk': 1,\n", " 'governanceEpochDate': **********,\n", " 'compensationAsOfEpochDate': **********,\n", " 'irWebsite': 'http://investor.apple.com/',\n", " 'executiveTeam': [],\n", " 'maxAge': 86400,\n", " 'priceHint': 2,\n", " 'previousClose': 199.2,\n", " 'open': 199.73,\n", " 'dayLow': 195.77,\n", " 'dayHigh': 200.37,\n", " 'regularMarketPreviousClose': 199.2,\n", " 'regularMarketOpen': 199.73,\n", " 'regularMarketDayLow': 195.77,\n", " 'regularMarketDayHigh': 200.37,\n", " 'dividendRate': 1.04,\n", " 'dividendYield': 0.53,\n", " 'exDividendDate': 1747008000,\n", " 'payoutRatio': 0.1558,\n", " 'fiveYearAvgDividendYield': 0.56,\n", " 'beta': 1.211,\n", " 'trailingPE': 30.599688,\n", " 'forwardPE': 23.64019,\n", " 'volume': 50778564,\n", " 'regularMarketVolume': 50778564,\n", " 'averageVolume': 61214365,\n", " 'averageVolume10days': 51093480,\n", " 'averageDailyVolume10Day': 51093480,\n", " 'bid': 196.21,\n", " 'ask': 196.7,\n", " 'bidSize': 4,\n", " 'askSize': 4,\n", " 'marketCap': 2934137946112,\n", " 'fiftyTwoWeekLow': 169.21,\n", " 'fiftyTwoWeekHigh': 260.1,\n", " 'priceToSalesTrailing12Months': 7.328639,\n", " 'fiftyDayAverage': 201.4312,\n", " 'twoHundredDayAverage': 224.45325,\n", " 'trailingAnnualDividendRate': 1.0,\n", " 'trailingAnnualDividendYield': 0.0050200806,\n", " 'currency': 'USD',\n", " 'tradeable': <PERSON><PERSON><PERSON>,\n", " 'enterpriseValue': 2983831011328,\n", " 'profitMargins': 0.24301,\n", " 'floatShares': 14911480604,\n", " 'sharesOutstanding': 14935799808,\n", " 'sharesShort': 94828443,\n", " 'sharesShortPriorMonth': 108598767,\n", " 'sharesShortPreviousMonthDate': 1745971200,\n", " 'dateShortInterest': 1748563200,\n", " 'sharesPercentSharesOut': 0.0063,\n", " 'heldPercentInsiders': 0.02085,\n", " 'heldPercentInstitutions': 0.62893003,\n", " 'shortRatio': 1.67,\n", " 'shortPercentOfFloat': 0.0064,\n", " 'impliedSharesOutstanding': 15118999552,\n", " 'bookValue': 4.471,\n", " 'priceToBook': 43.938713,\n", " 'lastFiscalYearEnd': 1727481600,\n", " 'nextFiscalYearEnd': 1759017600,\n", " 'mostRecentQuarter': 1743206400,\n", " 'earningsQuarterlyGrowth': 0.048,\n", " 'netIncomeToCommon': 97294000128,\n", " 'trailingEps': 6.42,\n", " 'forwardEps': 8.31,\n", " 'lastSplitFactor': '4:1',\n", " 'lastSplitDate': 1598832000,\n", " 'enterpriseToRevenue': 7.453,\n", " 'enterpriseToEbitda': 21.487,\n", " '52WeekChange': -0.09332162,\n", " 'SandP52WeekChange': 0.09203708,\n", " 'lastDividendValue': 0.26,\n", " 'lastDividendDate': 1747008000,\n", " 'quoteType': 'EQUITY',\n", " 'currentPrice': 196.45,\n", " 'targetHighPrice': 300.0,\n", " 'targetLowPrice': 170.62,\n", " 'targetMeanPrice': 228.85326,\n", " 'targetMedianPrice': 232.5,\n", " 'recommendationMean': 2.1087,\n", " 'recommendation<PERSON>ey': 'buy',\n", " 'numberOfAnalystOpinions': 40,\n", " 'totalCash': 48497999872,\n", " 'totalCashPerShare': 3.247,\n", " 'ebitda': 138865999872,\n", " 'totalDebt': 98186002432,\n", " 'quickRatio': 0.68,\n", " 'currentRatio': 0.821,\n", " 'totalRevenue': 400366010368,\n", " 'debtToEquity': 146.994,\n", " 'revenuePerShare': 26.455,\n", " 'returnOnAssets': 0.23809999,\n", " 'returnOnEquity': 1.38015,\n", " 'grossProfits': 186699005952,\n", " 'freeCashflow': 97251500032,\n", " 'operatingCashflow': 109555998720,\n", " 'earningsGrowth': 0.078,\n", " 'revenueGrowth': 0.051,\n", " 'grossMargins': 0.46632,\n", " 'ebitdaMargins': 0.34685,\n", " 'operatingMargins': 0.31028998,\n", " 'financialCurrency': 'USD',\n", " 'symbol': 'AAPL',\n", " 'language': 'en-US',\n", " 'region': 'US',\n", " 'typeDisp': 'Equity',\n", " 'quoteSourceName': 'Nasdaq Real Time Price',\n", " 'triggerable': True,\n", " 'customPriceAlertConfidence': 'HIGH',\n", " 'shortName': 'Apple Inc.',\n", " 'corporateActions': [],\n", " 'postMarketTime': 1749859199,\n", " 'regularMarketTime': 1749844801,\n", " 'exchange': 'NMS',\n", " 'messageBoardId': 'finmb_24937',\n", " 'exchangeTimezoneName': 'America/New_York',\n", " 'exchangeTimezoneShortName': 'EDT',\n", " 'gmtOffSetMilliseconds': -14400000,\n", " 'market': 'us_market',\n", " 'esgPopulated': <PERSON><PERSON><PERSON>,\n", " 'hasPrePostMarketData': True,\n", " 'firstTradeDateMilliseconds': 345479400000,\n", " 'postMarketChangePercent': -0.0254533,\n", " 'postMarketPrice': 196.4,\n", " 'postMarketChange': -0.0500031,\n", " 'regularMarketChange': -2.75,\n", " 'regularMarketDayRange': '195.77 - 200.37',\n", " 'fullExchangeName': 'NasdaqGS',\n", " 'averageDailyVolume3Month': 61214365,\n", " 'fiftyTwoWeekLowChange': 27.23999,\n", " 'fiftyTwoWeekLowChangePercent': 0.16098332,\n", " 'fiftyTwoWeekRange': '169.21 - 260.1',\n", " 'fiftyTwoWeekHighChange': -63.65001,\n", " 'fiftyTwoWeekHighChangePercent': -0.2447136,\n", " 'fiftyTwoWeekChangePercent': -9.332162,\n", " 'dividendDate': 1747267200,\n", " 'earningsTimestamp': 1746131400,\n", " 'earningsTimestampStart': 1753873140,\n", " 'earningsTimestampEnd': 1754308800,\n", " 'earningsCallTimestampStart': 1746133200,\n", " 'earningsCallTimestampEnd': 1746133200,\n", " 'isEarningsDateEstimate': True,\n", " 'epsTrailingTwelveMonths': 6.42,\n", " 'epsForward': 8.31,\n", " 'epsCurrentYear': 7.18956,\n", " 'priceEpsCurrentYear': 27.324343,\n", " 'fiftyDayAverageChange': -4.981201,\n", " 'fiftyDayAverageChangePercent': -0.024729045,\n", " 'twoHundredDayAverageChange': -28.00325,\n", " 'twoHundredDayAverageChangePercent': -0.124762066,\n", " 'sourceInterval': 15,\n", " 'exchangeDataDelayedBy': 0,\n", " 'averageAnalystRating': '2.1 - Buy',\n", " 'cryptoTradeable': <PERSON><PERSON><PERSON>,\n", " 'regularMarketChangePercent': -1.3805221,\n", " 'regularMarketPrice': 196.45,\n", " 'marketState': 'CLOSED',\n", " 'longName': 'Apple Inc.',\n", " 'displayName': 'Apple',\n", " 'trailingPegRatio': 1.7837}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["aapl.info"]}, {"cell_type": "code", "execution_count": 26, "id": "ccb3b2e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BRH.DU\n"]}], "source": ["brk_a_isin = 'US0846701086'\n", "brk_a = yf.Ticker(brk_a_isin)\n", "print(brk_a.ticker)"]}, {"cell_type": "code", "execution_count": 27, "id": "64657581", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'maxAge': 86400,\n", " 'priceHint': 2,\n", " 'previousClose': 629000.0,\n", " 'open': 623000.0,\n", " 'dayLow': 622000.0,\n", " 'dayHigh': 625000.0,\n", " 'regularMarketPreviousClose': 629000.0,\n", " 'regularMarketOpen': 623000.0,\n", " 'regularMarketDayLow': 622000.0,\n", " 'regularMarketDayHigh': 625000.0,\n", " 'volume': 0,\n", " 'regularMarketVolume': 0,\n", " 'averageVolume': 0,\n", " 'averageVolume10days': 0,\n", " 'averageDailyVolume10Day': 0,\n", " 'bid': 629000.0,\n", " 'ask': 636500.0,\n", " 'bidSize': 0,\n", " 'askSize': 0,\n", " 'fiftyTwoWeekLow': 411.36,\n", " 'fiftyTwoWeekHigh': 741500.0,\n", " 'fiftyDayAverage': 673470.0,\n", " 'twoHundredDayAverage': 637541.44,\n", " 'currency': 'EUR',\n", " 'tradeable': <PERSON><PERSON><PERSON>,\n", " 'quoteType': 'EQUITY',\n", " 'symbol': 'BRH.DU',\n", " 'language': 'en-US',\n", " 'region': 'US',\n", " 'typeDisp': 'Equity',\n", " 'quoteSourceName': 'Delayed Quote',\n", " 'triggerable': <PERSON><PERSON><PERSON>,\n", " 'customPriceAlertConfidence': 'LOW',\n", " 'hasPrePostMarketData': F<PERSON>e,\n", " 'firstTradeDateMilliseconds': 1025848800000,\n", " 'marketState': 'CLOSED',\n", " 'regularMarketPrice': 631000.0,\n", " 'regularMarketChange': 2000.0,\n", " 'regularMarketDayRange': '622000.0 - 625000.0',\n", " 'fullExchangeName': 'Dusseldorf',\n", " 'averageDailyVolume3Month': 0,\n", " 'fiftyTwoWeekLowChange': 630588.6,\n", " 'fiftyTwoWeekLowChangePercent': 1532.9363,\n", " 'fiftyTwoWeekRange': '411.36 - 741500.0',\n", " 'fiftyTwoWeekHighChange': -110500.0,\n", " 'fiftyTwoWeekHighChangePercent': -0.14902225,\n", " 'fiftyTwoWeekChangePercent': 11.1894245,\n", " 'earningsTimestamp': 1746273600,\n", " 'earningsTimestampStart': 1754055000,\n", " 'earningsTimestampEnd': 1754400600,\n", " 'isEarningsDateEstimate': True,\n", " 'fiftyDayAverageChange': -42470.0,\n", " 'fiftyDayAverageChangePercent': -0.06306146,\n", " 'twoHundredDayAverageChange': -6541.4375,\n", " 'twoHundredDayAverageChangePercent': -0.010260412,\n", " 'sourceInterval': 15,\n", " 'exchangeDataDelayedBy': 15,\n", " 'cryptoTradeable': <PERSON><PERSON><PERSON>,\n", " 'regularMarketChangePercent': 0.31796503,\n", " 'corporateActions': [],\n", " 'regularMarketTime': 1749835817,\n", " 'exchange': 'DUS',\n", " 'exchangeTimezoneName': 'Europe/Berlin',\n", " 'exchangeTimezoneShortName': 'CEST',\n", " 'gmtOffSetMilliseconds': 7200000,\n", " 'market': 'dr_market',\n", " 'esgPopulated': <PERSON><PERSON><PERSON>,\n", " 'shortName': 'BERKSHIRE HATHAWAY INC.       R',\n", " 'longName': 'Berkshire Hathaway Inc',\n", " 'trailingPegRatio': None}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["brk_a.info"]}, {"cell_type": "code", "execution_count": 28, "id": "30c71227", "metadata": {}, "outputs": [], "source": ["# 保存到 JSON 文件\n", "import json\n", "with open(f'{brk_a_isin}.json', 'w') as f:\n", "    json.dump(brk_a.info, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "f15551e3", "metadata": {}, "outputs": [], "source": ["aapl_isin = 'US0378331005'\n", "aapl = yf.Ticker(aapl_isin)\n", "print(aapl.ticker)\n", "\n", "# 保存到 JSON 文件\n", "import json\n", "with open('aapl_info.json', 'w') as f:\n", "    json.dump(aapl.info, f, indent=4)"]}, {"cell_type": "code", "execution_count": 6, "id": "580129c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Dividend Date': datetime.date(2025, 5, 15),\n", " 'Ex-Dividend Date': datetime.date(2025, 5, 12),\n", " 'Earnings Date': [datetime.date(2025, 7, 30), datetime.date(2025, 8, 4)],\n", " 'Earnings High': 1.51,\n", " 'Earnings Low': 1.34,\n", " 'Earnings Average': 1.42413,\n", " 'Revenue High': 90104000000,\n", " 'Revenue Low': 86919000000,\n", " 'Revenue Average': 88630845280}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["aapl.calendar"]}, {"cell_type": "markdown", "id": "5e4911f0", "metadata": {}, "source": ["### shares"]}, {"cell_type": "code", "execution_count": 7, "id": "62286b21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('floatShares', 14911480604), ('sharesOutstanding', 14935799808), ('shares', 14935799808), ('impliedSharesOutstanding', 15118999552)]\n", "marketCap: 2934137946112\n"]}], "source": ["shares = {}\n", "shares['floatShares'] = aapl.info['floatShares']                           # 自由流通股数量\n", "shares['sharesOutstanding'] = aapl.info['sharesOutstanding']               # 总发行在外普通股数量\n", "shares['shares'] = aapl.fast_info.shares                                   # 总发行在外普通股数量\n", "shares['impliedSharesOutstanding'] = aapl.info['impliedSharesOutstanding'] # 隐含的发行在外股数（用于某些估值推导）\n", "# 从小到大排序\n", "shares = sorted(shares.items(), key=lambda x: x[1])\n", "print(shares)\n", "\n", "print(f'marketCap: {aapl.info['marketCap']}')"]}, {"cell_type": "markdown", "id": "7669d5eb", "metadata": {}, "source": ["### dates"]}, {"cell_type": "code", "execution_count": 12, "id": "59aa7e15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["firstTradeDateMilliseconds: 2000-08-30T12:00:00\n", "regularMarketTime: 2025-06-14T04:59:59\n", "expireDate: 2025-08-27T08:00:00\n"]}], "source": ["# 筛选所有以 Epoch 或 Date 结尾并是整数的字段\n", "datetimes = {}\n", "for key, value in gold.info.items():\n", "    if isinstance(value, (int, float)) and (\n", "        'date' in key.lower() or\n", "        'time' in key.lower()\n", "    ) and key not in ['fullTimeEmployees', 'isEarningsDateEstimate']:\n", "        # 判断是秒还是毫秒\n", "        timestamp = value / 1000 if value > 1e11 else value\n", "        dt = datetime.datetime.fromtimestamp(timestamp)\n", "        datetimes[key] = dt\n", "# 排序后打印\n", "for key, value in sorted(datetimes.items(), key=lambda x: x[1]):\n", "    print(f\"{key}: {value.isoformat()}\")"]}, {"cell_type": "code", "id": "30932de9", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T14:26:45.971693Z", "start_time": "2025-06-16T14:26:43.358788Z"}}, "source": ["# 筛选所有以 Epoch 或 Date 结尾并是整数的字段\n", "import yfinance as yf\n", "aapl = yf.Ticker('AAPL')\n", "datetimes = {}\n", "for key, value in aapl.info.items():\n", "    if isinstance(value, (int, float)) and (\n", "        'date' in key.lower() or\n", "        'time' in key.lower()\n", "    ) and key not in ['fullTimeEmployees', 'isEarningsDateEstimate']:\n", "        # 判断是秒还是毫秒\n", "        timestamp = value / 1000 if value > 1e11 else value\n", "        if timestamp > 0:\n", "            dt = datetime.datetime.fromtimestamp(timestamp)\n", "            datetimes[key] = dt\n", "        else:\n", "            dt = datetime.datetime.fromtimestamp(0) - datetime.timedelta(milliseconds=-value)\n", "            datetimes[key] = dt\n", "# 排序后打印\n", "for key, value in sorted(datetimes.items(), key=lambda x: x[1]):\n", "    print(f\"{key}: {value.isoformat()}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["firstTradeDateMilliseconds: 1962-01-02T22:30:00\n", "lastSplitDate: 2021-11-04T08:00:00\n", "compensationAsOfEpochDate: 2024-12-31T08:00:00\n", "sharesShortPreviousMonthDate: 2025-04-30T08:00:00\n", "exDividendDate: 2025-05-09T08:00:00\n", "lastDividendDate: 2025-05-09T08:00:00\n", "dateShortInterest: 2025-05-30T08:00:00\n", "governanceEpochDate: 2025-06-01T08:00:00\n", "dividendDate: 2025-06-10T08:00:00\n", "regularMarketTime: 2025-06-16T22:25:52\n", "earningsTimestamp: 2025-07-24T04:00:00\n", "earningsTimestampStart: 2025-07-24T04:00:00\n", "earningsTimestampEnd: 2025-07-24T04:00:00\n", "earningsCallTimestampStart: 2025-07-24T05:00:00\n", "earningsCallTimestampEnd: 2025-07-24T05:00:00\n"]}], "execution_count": 16}, {"cell_type": "code", "execution_count": 14, "id": "ae001299", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.098597</td>\n", "      <td>0.099025</td>\n", "      <td>0.098597</td>\n", "      <td>0.098597</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.093881</td>\n", "      <td>0.093881</td>\n", "      <td>0.093453</td>\n", "      <td>0.093453</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.087022</td>\n", "      <td>0.087022</td>\n", "      <td>0.086594</td>\n", "      <td>0.086594</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.088737</td>\n", "      <td>0.089165</td>\n", "      <td>0.088737</td>\n", "      <td>0.088737</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.091310</td>\n", "      <td>0.091738</td>\n", "      <td>0.091310</td>\n", "      <td>0.091310</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>204.389999</td>\n", "      <td>206.000000</td>\n", "      <td>200.020004</td>\n", "      <td>201.449997</td>\n", "      <td>72862600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10 00:00:00-04:00</th>\n", "      <td>200.600006</td>\n", "      <td>204.350006</td>\n", "      <td>200.570007</td>\n", "      <td>202.669998</td>\n", "      <td>54672600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11 00:00:00-04:00</th>\n", "      <td>203.500000</td>\n", "      <td>204.500000</td>\n", "      <td>198.410004</td>\n", "      <td>198.779999</td>\n", "      <td>60989900</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12 00:00:00-04:00</th>\n", "      <td>199.080002</td>\n", "      <td>199.679993</td>\n", "      <td>197.360001</td>\n", "      <td>199.199997</td>\n", "      <td>43904600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>199.729996</td>\n", "      <td>200.369995</td>\n", "      <td>195.699997</td>\n", "      <td>196.449997</td>\n", "      <td>51362400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>11217 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.098597    0.099025    0.098597    0.098597   \n", "1980-12-15 00:00:00-05:00    0.093881    0.093881    0.093453    0.093453   \n", "1980-12-16 00:00:00-05:00    0.087022    0.087022    0.086594    0.086594   \n", "1980-12-17 00:00:00-05:00    0.088737    0.089165    0.088737    0.088737   \n", "1980-12-18 00:00:00-05:00    0.091310    0.091738    0.091310    0.091310   \n", "...                               ...         ...         ...         ...   \n", "2025-06-09 00:00:00-04:00  204.389999  206.000000  200.020004  201.449997   \n", "2025-06-10 00:00:00-04:00  200.600006  204.350006  200.570007  202.669998   \n", "2025-06-11 00:00:00-04:00  203.500000  204.500000  198.410004  198.779999   \n", "2025-06-12 00:00:00-04:00  199.080002  199.679993  197.360001  199.199997   \n", "2025-06-13 00:00:00-04:00  199.729996  200.369995  195.699997  196.449997   \n", "\n", "                              Volume  Dividends  Stock Splits  \n", "Date                                                           \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0  \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0  \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0  \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0  \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0  \n", "...                              ...        ...           ...  \n", "2025-06-09 00:00:00-04:00   72862600        0.0           0.0  \n", "2025-06-10 00:00:00-04:00   54672600        0.0           0.0  \n", "2025-06-11 00:00:00-04:00   60989900        0.0           0.0  \n", "2025-06-12 00:00:00-04:00   43904600        0.0           0.0  \n", "2025-06-13 00:00:00-04:00   51362400        0.0           0.0  \n", "\n", "[11217 rows x 7 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["his_aapl = aapl.history(period='max')\n", "his_aapl"]}, {"cell_type": "markdown", "id": "602156ef", "metadata": {}, "source": ["## history"]}, {"cell_type": "code", "execution_count": 100, "id": "7f9e2626246adb5b", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T05:54:53.713144Z", "start_time": "2024-04-12T05:54:53.574256Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2006-09-15 00:00:00-04:00</th>\n", "      <td>5.884771</td>\n", "      <td>6.427766</td>\n", "      <td>5.882450</td>\n", "      <td>6.126102</td>\n", "      <td>2122800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-18 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.566996</td>\n", "      <td>6.174833</td>\n", "      <td>6.450971</td>\n", "      <td>1936800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-19 00:00:00-04:00</th>\n", "      <td>6.555394</td>\n", "      <td>6.569317</td>\n", "      <td>6.149307</td>\n", "      <td>6.160910</td>\n", "      <td>1153200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-20 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.399920</td>\n", "      <td>6.149307</td>\n", "      <td>6.323344</td>\n", "      <td>774000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-21 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.430087</td>\n", "      <td>6.207320</td>\n", "      <td>6.221242</td>\n", "      <td>621200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>47.520000</td>\n", "      <td>47.820000</td>\n", "      <td>47.040001</td>\n", "      <td>47.389999</td>\n", "      <td>848200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10 00:00:00-04:00</th>\n", "      <td>48.410000</td>\n", "      <td>48.750000</td>\n", "      <td>48.040001</td>\n", "      <td>48.619999</td>\n", "      <td>948800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11 00:00:00-04:00</th>\n", "      <td>48.849998</td>\n", "      <td>49.029999</td>\n", "      <td>47.860001</td>\n", "      <td>48.080002</td>\n", "      <td>770000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12 00:00:00-04:00</th>\n", "      <td>47.590000</td>\n", "      <td>47.830002</td>\n", "      <td>47.180000</td>\n", "      <td>47.619999</td>\n", "      <td>667500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>47.290001</td>\n", "      <td>47.290001</td>\n", "      <td>46.240002</td>\n", "      <td>46.509998</td>\n", "      <td>741600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4716 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2006-09-15 00:00:00-04:00   5.884771   6.427766   5.882450   6.126102   \n", "2006-09-18 00:00:00-04:00   6.265332   6.566996   6.174833   6.450971   \n", "2006-09-19 00:00:00-04:00   6.555394   6.569317   6.149307   6.160910   \n", "2006-09-20 00:00:00-04:00   6.265332   6.399920   6.149307   6.323344   \n", "2006-09-21 00:00:00-04:00   6.265332   6.430087   6.207320   6.221242   \n", "...                              ...        ...        ...        ...   \n", "2025-06-09 00:00:00-04:00  47.520000  47.820000  47.040001  47.389999   \n", "2025-06-10 00:00:00-04:00  48.410000  48.750000  48.040001  48.619999   \n", "2025-06-11 00:00:00-04:00  48.849998  49.029999  47.860001  48.080002   \n", "2025-06-12 00:00:00-04:00  47.590000  47.830002  47.180000  47.619999   \n", "2025-06-13 00:00:00-04:00  47.290001  47.290001  46.240002  46.509998   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2006-09-15 00:00:00-04:00  2122800        0.0           0.0  \n", "2006-09-18 00:00:00-04:00  1936800        0.0           0.0  \n", "2006-09-19 00:00:00-04:00  1153200        0.0           0.0  \n", "2006-09-20 00:00:00-04:00   774000        0.0           0.0  \n", "2006-09-21 00:00:00-04:00   621200        0.0           0.0  \n", "...                            ...        ...           ...  \n", "2025-06-09 00:00:00-04:00   848200        0.0           0.0  \n", "2025-06-10 00:00:00-04:00   948800        0.0           0.0  \n", "2025-06-11 00:00:00-04:00   770000        0.0           0.0  \n", "2025-06-12 00:00:00-04:00   667500        0.0           0.0  \n", "2025-06-13 00:00:00-04:00   741600        0.0           0.0  \n", "\n", "[4716 rows x 7 columns]"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data from the date of IPO can be accessed by setting period = ‘max’\n", "# Valid inputs for period are:\n", "# Days : 1d,5d\n", "# Months : 1mo,3mo,6mo\n", "# Years : 1y,2y,5y,10y\n", "# Year-to-date (current year): ytd\n", "edu = yf.Ticker('EDU')\n", "his_data = edu.history(end=\"2025-07-21\", interval='1m', start='2025-07-13')\n", "his_data"]}, {"cell_type": "code", "execution_count": 56, "id": "163415716098f566", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T05:58:26.399562Z", "start_time": "2024-04-12T05:58:26.378564Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2012-08-29 00:00:00-04:00</th>\n", "      <td>12.813099</td>\n", "      <td>12.889029</td>\n", "      <td>12.528363</td>\n", "      <td>12.604293</td>\n", "      <td>1491100</td>\n", "      <td>0.30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-09-04 00:00:00-04:00</th>\n", "      <td>20.552519</td>\n", "      <td>20.677958</td>\n", "      <td>20.031470</td>\n", "      <td>20.417433</td>\n", "      <td>704400</td>\n", "      <td>0.35</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-09-02 00:00:00-04:00</th>\n", "      <td>19.486813</td>\n", "      <td>19.920071</td>\n", "      <td>19.142175</td>\n", "      <td>19.801910</td>\n", "      <td>511900</td>\n", "      <td>0.40</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-09-01 00:00:00-04:00</th>\n", "      <td>81.299571</td>\n", "      <td>83.893714</td>\n", "      <td>81.210455</td>\n", "      <td>83.289734</td>\n", "      <td>1361100</td>\n", "      <td>0.45</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-09 00:00:00-04:00</th>\n", "      <td>59.900002</td>\n", "      <td>61.139999</td>\n", "      <td>59.500000</td>\n", "      <td>60.860001</td>\n", "      <td>1039500</td>\n", "      <td>0.60</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2012-08-29 00:00:00-04:00  12.813099  12.889029  12.528363  12.604293   \n", "2013-09-04 00:00:00-04:00  20.552519  20.677958  20.031470  20.417433   \n", "2015-09-02 00:00:00-04:00  19.486813  19.920071  19.142175  19.801910   \n", "2017-09-01 00:00:00-04:00  81.299571  83.893714  81.210455  83.289734   \n", "2024-09-09 00:00:00-04:00  59.900002  61.139999  59.500000  60.860001   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2012-08-29 00:00:00-04:00  1491100       0.30           0.0  \n", "2013-09-04 00:00:00-04:00   704400       0.35           0.0  \n", "2015-09-02 00:00:00-04:00   511900       0.40           0.0  \n", "2017-09-01 00:00:00-04:00  1361100       0.45           0.0  \n", "2024-09-09 00:00:00-04:00  1039500       0.60           0.0  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["# Data from the date of IPO can be accessed by setting period = ‘max’\n", "# Valid inputs for period are:\n", "# Days : 1d,5d\n", "# Months : 1mo,3mo,6mo\n", "# Years : 1y,2y,5y,10y\n", "# Year-to-date (current year): ytd\n", "edu = yf.Ticker('EDU')\n", "his_data = edu.history(end=\"2025-07-13\", interval='1m', start='2025-07-05')\n", "his_data"]}, {"cell_type": "code", "execution_count": 57, "id": "26b9741d19f72831", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T05:58:43.566978Z", "start_time": "2024-04-12T05:58:43.558994Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2011-08-19 00:00:00-04:00</th>\n", "      <td>25.776042</td>\n", "      <td>26.982698</td>\n", "      <td>25.441891</td>\n", "      <td>25.525429</td>\n", "      <td>927400</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-07 00:00:00-04:00</th>\n", "      <td>12.277611</td>\n", "      <td>12.277611</td>\n", "      <td>11.683534</td>\n", "      <td>11.782546</td>\n", "      <td>2483530</td>\n", "      <td>0.0</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-08 00:00:00-04:00</th>\n", "      <td>11.396396</td>\n", "      <td>11.980573</td>\n", "      <td>10.871627</td>\n", "      <td>11.911263</td>\n", "      <td>13039700</td>\n", "      <td>0.0</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2011-08-19 00:00:00-04:00   25.776042   26.982698   25.441891   25.525429   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2022-04-07 00:00:00-04:00   12.277611   12.277611   11.683534   11.782546   \n", "2022-04-08 00:00:00-04:00   11.396396   11.980573   10.871627   11.911263   \n", "\n", "                             Volume  Dividends  Stock Splits  \n", "Date                                                          \n", "2011-08-19 00:00:00-04:00    927400        0.0           4.0  \n", "2021-03-12 00:00:00-05:00   1296580        0.0          10.0  \n", "2022-04-07 00:00:00-04:00   2483530        0.0           0.1  \n", "2022-04-08 00:00:00-04:00  13039700        0.0           0.1  "]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看股息和股票拆分不为0的数据\n", "his_data[his_data['Dividends'] != 0]"]}, {"cell_type": "code", "execution_count": 63, "id": "5aa8eda3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2012-08-28 00:00:00-04:00</th>\n", "      <td>12.809124</td>\n", "      <td>12.957635</td>\n", "      <td>12.604920</td>\n", "      <td>12.632766</td>\n", "      <td>1419600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-29 00:00:00-04:00</th>\n", "      <td>12.813099</td>\n", "      <td>12.889029</td>\n", "      <td>12.528363</td>\n", "      <td>12.604293</td>\n", "      <td>1491100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-30 00:00:00-04:00</th>\n", "      <td>12.623276</td>\n", "      <td>13.031396</td>\n", "      <td>12.604293</td>\n", "      <td>12.964958</td>\n", "      <td>1354700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-31 00:00:00-04:00</th>\n", "      <td>12.964958</td>\n", "      <td>13.278167</td>\n", "      <td>12.908011</td>\n", "      <td>13.192746</td>\n", "      <td>1838300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-09-04 00:00:00-04:00</th>\n", "      <td>13.002921</td>\n", "      <td>13.202236</td>\n", "      <td>12.585309</td>\n", "      <td>12.661239</td>\n", "      <td>1968100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-09-05 00:00:00-04:00</th>\n", "      <td>12.585310</td>\n", "      <td>12.993431</td>\n", "      <td>12.585310</td>\n", "      <td>12.879537</td>\n", "      <td>1475500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close   Volume\n", "Date                                                                          \n", "2012-08-28 00:00:00-04:00  12.809124  12.957635  12.604920  12.632766  1419600\n", "2012-08-29 00:00:00-04:00  12.813099  12.889029  12.528363  12.604293  1491100\n", "2012-08-30 00:00:00-04:00  12.623276  13.031396  12.604293  12.964958  1354700\n", "2012-08-31 00:00:00-04:00  12.964958  13.278167  12.908011  13.192746  1838300\n", "2012-09-04 00:00:00-04:00  13.002921  13.202236  12.585309  12.661239  1968100\n", "2012-09-05 00:00:00-04:00  12.585310  12.993431  12.585310  12.879537  1475500"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": "his_data[his_data['Stock Splits'] != 0]"}, {"cell_type": "code", "execution_count": 64, "id": "4742224e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2012-08-28 00:00:00-04:00</th>\n", "      <td>12.809124</td>\n", "      <td>12.957635</td>\n", "      <td>12.604920</td>\n", "      <td>12.632766</td>\n", "      <td>1419600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-29 00:00:00-04:00</th>\n", "      <td>12.813099</td>\n", "      <td>12.889029</td>\n", "      <td>12.528363</td>\n", "      <td>12.604293</td>\n", "      <td>1491100</td>\n", "      <td>0.3</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-30 00:00:00-04:00</th>\n", "      <td>12.623276</td>\n", "      <td>13.031396</td>\n", "      <td>12.604293</td>\n", "      <td>12.964958</td>\n", "      <td>1354700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-31 00:00:00-04:00</th>\n", "      <td>12.964958</td>\n", "      <td>13.278167</td>\n", "      <td>12.908011</td>\n", "      <td>13.192746</td>\n", "      <td>1838300</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-09-04 00:00:00-04:00</th>\n", "      <td>13.002921</td>\n", "      <td>13.202236</td>\n", "      <td>12.585309</td>\n", "      <td>12.661239</td>\n", "      <td>1968100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2012-08-28 00:00:00-04:00  12.809124  12.957635  12.604920  12.632766   \n", "2012-08-29 00:00:00-04:00  12.813099  12.889029  12.528363  12.604293   \n", "2012-08-30 00:00:00-04:00  12.623276  13.031396  12.604293  12.964958   \n", "2012-08-31 00:00:00-04:00  12.964958  13.278167  12.908011  13.192746   \n", "2012-09-04 00:00:00-04:00  13.002921  13.202236  12.585309  12.661239   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2012-08-28 00:00:00-04:00  1419600        0.0           0.0  \n", "2012-08-29 00:00:00-04:00  1491100        0.3           0.0  \n", "2012-08-30 00:00:00-04:00  1354700        0.0           0.0  \n", "2012-08-31 00:00:00-04:00  1838300        0.0           0.0  \n", "2012-09-04 00:00:00-04:00  1968100        0.0           0.0  "]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# 不需要股息和股票拆分\n", "his_data2 = edu.history(period='20y', actions=False)\n", "his_data2['2012-08-28':'2012-09-05']\n"]}, {"cell_type": "code", "execution_count": null, "id": "86a46286", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2006-09-15 00:00:00-04:00</th>\n", "      <td>5.884771</td>\n", "      <td>6.427767</td>\n", "      <td>5.882451</td>\n", "      <td>6.126102</td>\n", "      <td>2122800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-18 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.566996</td>\n", "      <td>6.174833</td>\n", "      <td>6.450971</td>\n", "      <td>1936800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-19 00:00:00-04:00</th>\n", "      <td>6.555394</td>\n", "      <td>6.569317</td>\n", "      <td>6.149307</td>\n", "      <td>6.160910</td>\n", "      <td>1153200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-20 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.399920</td>\n", "      <td>6.149307</td>\n", "      <td>6.323344</td>\n", "      <td>774000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-21 00:00:00-04:00</th>\n", "      <td>6.265332</td>\n", "      <td>6.430087</td>\n", "      <td>6.207320</td>\n", "      <td>6.221243</td>\n", "      <td>621200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>47.520000</td>\n", "      <td>47.820000</td>\n", "      <td>47.040001</td>\n", "      <td>47.389999</td>\n", "      <td>848200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10 00:00:00-04:00</th>\n", "      <td>48.410000</td>\n", "      <td>48.750000</td>\n", "      <td>48.040001</td>\n", "      <td>48.619999</td>\n", "      <td>948800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11 00:00:00-04:00</th>\n", "      <td>48.849998</td>\n", "      <td>49.029999</td>\n", "      <td>47.860001</td>\n", "      <td>48.080002</td>\n", "      <td>770000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12 00:00:00-04:00</th>\n", "      <td>47.590000</td>\n", "      <td>47.830002</td>\n", "      <td>47.180000</td>\n", "      <td>47.619999</td>\n", "      <td>667500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>47.290001</td>\n", "      <td>47.290001</td>\n", "      <td>46.240002</td>\n", "      <td>46.509998</td>\n", "      <td>741600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4716 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2006-09-15 00:00:00-04:00   5.884771   6.427767   5.882451   6.126102   \n", "2006-09-18 00:00:00-04:00   6.265332   6.566996   6.174833   6.450971   \n", "2006-09-19 00:00:00-04:00   6.555394   6.569317   6.149307   6.160910   \n", "2006-09-20 00:00:00-04:00   6.265332   6.399920   6.149307   6.323344   \n", "2006-09-21 00:00:00-04:00   6.265332   6.430087   6.207320   6.221243   \n", "...                              ...        ...        ...        ...   \n", "2025-06-09 00:00:00-04:00  47.520000  47.820000  47.040001  47.389999   \n", "2025-06-10 00:00:00-04:00  48.410000  48.750000  48.040001  48.619999   \n", "2025-06-11 00:00:00-04:00  48.849998  49.029999  47.860001  48.080002   \n", "2025-06-12 00:00:00-04:00  47.590000  47.830002  47.180000  47.619999   \n", "2025-06-13 00:00:00-04:00  47.290001  47.290001  46.240002  46.509998   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2006-09-15 00:00:00-04:00  2122800        0.0           0.0  \n", "2006-09-18 00:00:00-04:00  1936800        0.0           0.0  \n", "2006-09-19 00:00:00-04:00  1153200        0.0           0.0  \n", "2006-09-20 00:00:00-04:00   774000        0.0           0.0  \n", "2006-09-21 00:00:00-04:00   621200        0.0           0.0  \n", "...                            ...        ...           ...  \n", "2025-06-09 00:00:00-04:00   848200        0.0           0.0  \n", "2025-06-10 00:00:00-04:00   948800        0.0           0.0  \n", "2025-06-11 00:00:00-04:00   770000        0.0           0.0  \n", "2025-06-12 00:00:00-04:00   667500        0.0           0.0  \n", "2025-06-13 00:00:00-04:00   741600        0.0           0.0  \n", "\n", "[4716 rows x 7 columns]"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["his_data3 = edu.history(start='2012-08-28', end='2012-09-05',actions=True,back_adjust=True)\n", "his_data3"]}, {"cell_type": "code", "execution_count": 118, "id": "1d50bad8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2011-08-19 00:00:00-04:00</th>\n", "      <td>25.776041</td>\n", "      <td>26.982696</td>\n", "      <td>25.441889</td>\n", "      <td>25.525427</td>\n", "      <td>927400</td>\n", "      <td>0.00</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-29 00:00:00-04:00</th>\n", "      <td>12.813099</td>\n", "      <td>12.889029</td>\n", "      <td>12.528363</td>\n", "      <td>12.604293</td>\n", "      <td>1491100</td>\n", "      <td>0.30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-09-04 00:00:00-04:00</th>\n", "      <td>20.552517</td>\n", "      <td>20.677956</td>\n", "      <td>20.031468</td>\n", "      <td>20.417431</td>\n", "      <td>704400</td>\n", "      <td>0.35</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-09-02 00:00:00-04:00</th>\n", "      <td>19.486811</td>\n", "      <td>19.920069</td>\n", "      <td>19.142173</td>\n", "      <td>19.801908</td>\n", "      <td>511900</td>\n", "      <td>0.40</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-09-01 00:00:00-04:00</th>\n", "      <td>81.299571</td>\n", "      <td>83.893714</td>\n", "      <td>81.210455</td>\n", "      <td>83.289734</td>\n", "      <td>1361100</td>\n", "      <td>0.45</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.00</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-07 00:00:00-04:00</th>\n", "      <td>12.277611</td>\n", "      <td>12.277611</td>\n", "      <td>11.683534</td>\n", "      <td>11.782546</td>\n", "      <td>2483530</td>\n", "      <td>0.00</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-08 00:00:00-04:00</th>\n", "      <td>11.396396</td>\n", "      <td>11.980573</td>\n", "      <td>10.871627</td>\n", "      <td>11.911263</td>\n", "      <td>13039700</td>\n", "      <td>0.00</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-09 00:00:00-04:00</th>\n", "      <td>59.900002</td>\n", "      <td>61.139999</td>\n", "      <td>59.500000</td>\n", "      <td>60.860001</td>\n", "      <td>1039500</td>\n", "      <td>0.60</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2011-08-19 00:00:00-04:00   25.776041   26.982696   25.441889   25.525427   \n", "2012-08-29 00:00:00-04:00   12.813099   12.889029   12.528363   12.604293   \n", "2013-09-04 00:00:00-04:00   20.552517   20.677956   20.031468   20.417431   \n", "2015-09-02 00:00:00-04:00   19.486811   19.920069   19.142173   19.801908   \n", "2017-09-01 00:00:00-04:00   81.299571   83.893714   81.210455   83.289734   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2022-04-07 00:00:00-04:00   12.277611   12.277611   11.683534   11.782546   \n", "2022-04-08 00:00:00-04:00   11.396396   11.980573   10.871627   11.911263   \n", "2024-09-09 00:00:00-04:00   59.900002   61.139999   59.500000   60.860001   \n", "\n", "                             Volume  Dividends  Stock Splits  \n", "Date                                                          \n", "2011-08-19 00:00:00-04:00    927400       0.00           4.0  \n", "2012-08-29 00:00:00-04:00   1491100       0.30           0.0  \n", "2013-09-04 00:00:00-04:00    704400       0.35           0.0  \n", "2015-09-02 00:00:00-04:00    511900       0.40           0.0  \n", "2017-09-01 00:00:00-04:00   1361100       0.45           0.0  \n", "2021-03-12 00:00:00-05:00   1296580       0.00          10.0  \n", "2022-04-07 00:00:00-04:00   2483530       0.00           0.1  \n", "2022-04-08 00:00:00-04:00  13039700       0.00           0.1  \n", "2024-09-09 00:00:00-04:00   1039500       0.60           0.0  "]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["his_data_max_adj = edu.history(period='max')\n", "his_data_max_adj"]}, {"cell_type": "code", "execution_count": 119, "id": "2213b87e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2011-08-19 00:00:00-04:00</th>\n", "      <td>25.776041</td>\n", "      <td>26.982696</td>\n", "      <td>25.441889</td>\n", "      <td>25.525427</td>\n", "      <td>927400</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  Volume  \\\n", "Date                                                                            \n", "2011-08-19 00:00:00-04:00  25.776041  26.982696  25.441889  25.525427  927400   \n", "\n", "                           Dividends  Stock Splits  \n", "Date                                                \n", "2011-08-19 00:00:00-04:00        0.0           4.0  "]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": "his_data_max_adj[(his_data_max_adj['Dividends'] != 0) | (his_data_max_adj['Stock Splits'] != 0)]"}, {"cell_type": "code", "execution_count": null, "id": "420b4360", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2006-09-15 00:00:00-04:00</th>\n", "      <td>6.340000</td>\n", "      <td>6.925000</td>\n", "      <td>6.337500</td>\n", "      <td>6.600000</td>\n", "      <td>6.126102</td>\n", "      <td>2122800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-18 00:00:00-04:00</th>\n", "      <td>6.750000</td>\n", "      <td>7.075000</td>\n", "      <td>6.652500</td>\n", "      <td>6.950000</td>\n", "      <td>6.450971</td>\n", "      <td>1936800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-19 00:00:00-04:00</th>\n", "      <td>7.062500</td>\n", "      <td>7.077500</td>\n", "      <td>6.625000</td>\n", "      <td>6.637500</td>\n", "      <td>6.160910</td>\n", "      <td>1153200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-20 00:00:00-04:00</th>\n", "      <td>6.750000</td>\n", "      <td>6.895000</td>\n", "      <td>6.625000</td>\n", "      <td>6.812500</td>\n", "      <td>6.323344</td>\n", "      <td>774000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2006-09-21 00:00:00-04:00</th>\n", "      <td>6.750000</td>\n", "      <td>6.927500</td>\n", "      <td>6.687500</td>\n", "      <td>6.702500</td>\n", "      <td>6.221242</td>\n", "      <td>621200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>47.520000</td>\n", "      <td>47.820000</td>\n", "      <td>47.040001</td>\n", "      <td>47.389999</td>\n", "      <td>47.389999</td>\n", "      <td>848200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10 00:00:00-04:00</th>\n", "      <td>48.410000</td>\n", "      <td>48.750000</td>\n", "      <td>48.040001</td>\n", "      <td>48.619999</td>\n", "      <td>48.619999</td>\n", "      <td>948800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11 00:00:00-04:00</th>\n", "      <td>48.849998</td>\n", "      <td>49.029999</td>\n", "      <td>47.860001</td>\n", "      <td>48.080002</td>\n", "      <td>48.080002</td>\n", "      <td>770000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12 00:00:00-04:00</th>\n", "      <td>47.590000</td>\n", "      <td>47.830002</td>\n", "      <td>47.180000</td>\n", "      <td>47.619999</td>\n", "      <td>47.619999</td>\n", "      <td>667500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>47.290001</td>\n", "      <td>47.290001</td>\n", "      <td>46.240002</td>\n", "      <td>46.509998</td>\n", "      <td>46.509998</td>\n", "      <td>741600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4716 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2006-09-15 00:00:00-04:00   6.340000   6.925000   6.337500   6.600000   \n", "2006-09-18 00:00:00-04:00   6.750000   7.075000   6.652500   6.950000   \n", "2006-09-19 00:00:00-04:00   7.062500   7.077500   6.625000   6.637500   \n", "2006-09-20 00:00:00-04:00   6.750000   6.895000   6.625000   6.812500   \n", "2006-09-21 00:00:00-04:00   6.750000   6.927500   6.687500   6.702500   \n", "...                              ...        ...        ...        ...   \n", "2025-06-09 00:00:00-04:00  47.520000  47.820000  47.040001  47.389999   \n", "2025-06-10 00:00:00-04:00  48.410000  48.750000  48.040001  48.619999   \n", "2025-06-11 00:00:00-04:00  48.849998  49.029999  47.860001  48.080002   \n", "2025-06-12 00:00:00-04:00  47.590000  47.830002  47.180000  47.619999   \n", "2025-06-13 00:00:00-04:00  47.290001  47.290001  46.240002  46.509998   \n", "\n", "                           Adj Close   Volume  Dividends  Stock Splits  \n", "Date                                                                    \n", "2006-09-15 00:00:00-04:00   6.126102  2122800        0.0           0.0  \n", "2006-09-18 00:00:00-04:00   6.450971  1936800        0.0           0.0  \n", "2006-09-19 00:00:00-04:00   6.160910  1153200        0.0           0.0  \n", "2006-09-20 00:00:00-04:00   6.323344   774000        0.0           0.0  \n", "2006-09-21 00:00:00-04:00   6.221242   621200        0.0           0.0  \n", "...                              ...      ...        ...           ...  \n", "2025-06-09 00:00:00-04:00  47.389999   848200        0.0           0.0  \n", "2025-06-10 00:00:00-04:00  48.619999   948800        0.0           0.0  \n", "2025-06-11 00:00:00-04:00  48.080002   770000        0.0           0.0  \n", "2025-06-12 00:00:00-04:00  47.619999   667500        0.0           0.0  \n", "2025-06-13 00:00:00-04:00  46.509998   741600        0.0           0.0  \n", "\n", "[4716 rows x 8 columns]"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": "his_data_max_adj['2011-08-19':'2011-08-20']"}, {"cell_type": "code", "execution_count": 121, "id": "b4e6f4ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2011-08-19 00:00:00-04:00</th>\n", "      <td>27.770000</td>\n", "      <td>29.070000</td>\n", "      <td>27.410000</td>\n", "      <td>27.500000</td>\n", "      <td>25.525427</td>\n", "      <td>927400</td>\n", "      <td>0.00</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2012-08-29 00:00:00-04:00</th>\n", "      <td>13.500000</td>\n", "      <td>13.580000</td>\n", "      <td>13.200000</td>\n", "      <td>13.280000</td>\n", "      <td>12.604293</td>\n", "      <td>1491100</td>\n", "      <td>0.30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-09-04 00:00:00-04:00</th>\n", "      <td>21.299999</td>\n", "      <td>21.430000</td>\n", "      <td>20.760000</td>\n", "      <td>21.160000</td>\n", "      <td>20.417431</td>\n", "      <td>704400</td>\n", "      <td>0.35</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-09-02 00:00:00-04:00</th>\n", "      <td>19.790001</td>\n", "      <td>20.230000</td>\n", "      <td>19.440001</td>\n", "      <td>20.110001</td>\n", "      <td>19.801910</td>\n", "      <td>511900</td>\n", "      <td>0.40</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-09-01 00:00:00-04:00</th>\n", "      <td>82.110001</td>\n", "      <td>84.730003</td>\n", "      <td>82.019997</td>\n", "      <td>84.120003</td>\n", "      <td>83.289734</td>\n", "      <td>1361100</td>\n", "      <td>0.45</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.00</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-07 00:00:00-04:00</th>\n", "      <td>12.400000</td>\n", "      <td>12.400000</td>\n", "      <td>11.800000</td>\n", "      <td>11.900000</td>\n", "      <td>11.782546</td>\n", "      <td>2483530</td>\n", "      <td>0.00</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-04-08 00:00:00-04:00</th>\n", "      <td>11.510000</td>\n", "      <td>12.100000</td>\n", "      <td>10.980000</td>\n", "      <td>12.030000</td>\n", "      <td>11.911263</td>\n", "      <td>13039700</td>\n", "      <td>0.00</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-09 00:00:00-04:00</th>\n", "      <td>59.900002</td>\n", "      <td>61.139999</td>\n", "      <td>59.500000</td>\n", "      <td>60.860001</td>\n", "      <td>60.860001</td>\n", "      <td>1039500</td>\n", "      <td>0.60</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2011-08-19 00:00:00-04:00   27.770000   29.070000   27.410000   27.500000   \n", "2012-08-29 00:00:00-04:00   13.500000   13.580000   13.200000   13.280000   \n", "2013-09-04 00:00:00-04:00   21.299999   21.430000   20.760000   21.160000   \n", "2015-09-02 00:00:00-04:00   19.790001   20.230000   19.440001   20.110001   \n", "2017-09-01 00:00:00-04:00   82.110001   84.730003   82.019997   84.120003   \n", "2021-03-12 00:00:00-05:00  170.100006  180.000000  169.699997  176.399994   \n", "2022-04-07 00:00:00-04:00   12.400000   12.400000   11.800000   11.900000   \n", "2022-04-08 00:00:00-04:00   11.510000   12.100000   10.980000   12.030000   \n", "2024-09-09 00:00:00-04:00   59.900002   61.139999   59.500000   60.860001   \n", "\n", "                            Adj Close    Volume  Dividends  Stock Splits  \n", "Date                                                                      \n", "2011-08-19 00:00:00-04:00   25.525427    927400       0.00           4.0  \n", "2012-08-29 00:00:00-04:00   12.604293   1491100       0.30           0.0  \n", "2013-09-04 00:00:00-04:00   20.417431    704400       0.35           0.0  \n", "2015-09-02 00:00:00-04:00   19.801910    511900       0.40           0.0  \n", "2017-09-01 00:00:00-04:00   83.289734   1361100       0.45           0.0  \n", "2021-03-12 00:00:00-05:00  174.658920   1296580       0.00          10.0  \n", "2022-04-07 00:00:00-04:00   11.782546   2483530       0.00           0.1  \n", "2022-04-08 00:00:00-04:00   11.911263  13039700       0.00           0.1  \n", "2024-09-09 00:00:00-04:00   60.860001   1039500       0.60           0.0  "]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["his_data_max = edu.history(period='max', auto_adjust=False)\n", "his_data_max"]}, {"cell_type": "code", "execution_count": 120, "id": "ff46b877", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2011-08-19 00:00:00-04:00</th>\n", "      <td>27.77</td>\n", "      <td>29.07</td>\n", "      <td>27.41</td>\n", "      <td>27.5</td>\n", "      <td>25.525427</td>\n", "      <td>927400</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            Open   High    Low  Close  Adj Close  Volume  \\\n", "Date                                                                       \n", "2011-08-19 00:00:00-04:00  27.77  29.07  27.41   27.5  25.525427  927400   \n", "\n", "                           Dividends  Stock Splits  \n", "Date                                                \n", "2011-08-19 00:00:00-04:00        0.0           4.0  "]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": "his_data_max[(his_data_max['Dividends'] != 0) | (his_data_max['Stock Splits'] != 0)]"}, {"cell_type": "code", "execution_count": null, "id": "a2af46a4a26eb6e0", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:00:58.571138Z", "start_time": "2024-04-12T06:00:58.553101Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-10 00:00:00-04:00</th>\n", "      <td>215.667189</td>\n", "      <td>216.556022</td>\n", "      <td>208.147051</td>\n", "      <td>213.210419</td>\n", "      <td>121475900</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-17 00:00:00-04:00</th>\n", "      <td>213.030635</td>\n", "      <td>218.553392</td>\n", "      <td>209.695013</td>\n", "      <td>217.984146</td>\n", "      <td>287881900</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-24 00:00:00-04:00</th>\n", "      <td>220.710570</td>\n", "      <td>224.725310</td>\n", "      <td>217.394911</td>\n", "      <td>217.614624</td>\n", "      <td>190172600</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-31 00:00:00-04:00</th>\n", "      <td>216.725796</td>\n", "      <td>225.324521</td>\n", "      <td>187.094654</td>\n", "      <td>188.133301</td>\n", "      <td>366947800</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-07 00:00:00-04:00</th>\n", "      <td>176.967928</td>\n", "      <td>200.347273</td>\n", "      <td>168.988402</td>\n", "      <td>197.890488</td>\n", "      <td>675037600</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-14 00:00:00-04:00</th>\n", "      <td>211.163101</td>\n", "      <td>212.661136</td>\n", "      <td>192.118067</td>\n", "      <td>196.722031</td>\n", "      <td>263763500</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-21 00:00:00-04:00</th>\n", "      <td>193.016893</td>\n", "      <td>209.475306</td>\n", "      <td>189.561418</td>\n", "      <td>209.005920</td>\n", "      <td>238181400</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-28 00:00:00-04:00</th>\n", "      <td>209.724974</td>\n", "      <td>214.279000</td>\n", "      <td>201.895245</td>\n", "      <td>205.081070</td>\n", "      <td>286233500</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-05 00:00:00-04:00</th>\n", "      <td>202.834027</td>\n", "      <td>203.832717</td>\n", "      <td>192.996920</td>\n", "      <td>198.270004</td>\n", "      <td>275704500</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-12 00:00:00-04:00</th>\n", "      <td>210.693709</td>\n", "      <td>213.659820</td>\n", "      <td>206.479234</td>\n", "      <td>210.983322</td>\n", "      <td>264778300</td>\n", "      <td>0.26</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-19 00:00:00-04:00</th>\n", "      <td>207.910004</td>\n", "      <td>209.479996</td>\n", "      <td>193.460007</td>\n", "      <td>195.270004</td>\n", "      <td>273024200</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-26 00:00:00-04:00</th>\n", "      <td>198.300003</td>\n", "      <td>203.809998</td>\n", "      <td>196.779999</td>\n", "      <td>200.850006</td>\n", "      <td>223844900</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-02 00:00:00-04:00</th>\n", "      <td>200.279999</td>\n", "      <td>206.240005</td>\n", "      <td>200.119995</td>\n", "      <td>203.919998</td>\n", "      <td>227142700</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>204.389999</td>\n", "      <td>206.000000</td>\n", "      <td>195.699997</td>\n", "      <td>196.449997</td>\n", "      <td>334570664</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2025-03-10 00:00:00-04:00  215.667189  216.556022  208.147051  213.210419   \n", "2025-03-17 00:00:00-04:00  213.030635  218.553392  209.695013  217.984146   \n", "2025-03-24 00:00:00-04:00  220.710570  224.725310  217.394911  217.614624   \n", "2025-03-31 00:00:00-04:00  216.725796  225.324521  187.094654  188.133301   \n", "2025-04-07 00:00:00-04:00  176.967928  200.347273  168.988402  197.890488   \n", "2025-04-14 00:00:00-04:00  211.163101  212.661136  192.118067  196.722031   \n", "2025-04-21 00:00:00-04:00  193.016893  209.475306  189.561418  209.005920   \n", "2025-04-28 00:00:00-04:00  209.724974  214.279000  201.895245  205.081070   \n", "2025-05-05 00:00:00-04:00  202.834027  203.832717  192.996920  198.270004   \n", "2025-05-12 00:00:00-04:00  210.693709  213.659820  206.479234  210.983322   \n", "2025-05-19 00:00:00-04:00  207.910004  209.479996  193.460007  195.270004   \n", "2025-05-26 00:00:00-04:00  198.300003  203.809998  196.779999  200.850006   \n", "2025-06-02 00:00:00-04:00  200.279999  206.240005  200.119995  203.919998   \n", "2025-06-09 00:00:00-04:00  204.389999  206.000000  195.699997  196.449997   \n", "\n", "                              Volume  Dividends  Stock Splits  \n", "Date                                                           \n", "2025-03-10 00:00:00-04:00  121475900       0.00           0.0  \n", "2025-03-17 00:00:00-04:00  287881900       0.00           0.0  \n", "2025-03-24 00:00:00-04:00  190172600       0.00           0.0  \n", "2025-03-31 00:00:00-04:00  366947800       0.00           0.0  \n", "2025-04-07 00:00:00-04:00  675037600       0.00           0.0  \n", "2025-04-14 00:00:00-04:00  263763500       0.00           0.0  \n", "2025-04-21 00:00:00-04:00  238181400       0.00           0.0  \n", "2025-04-28 00:00:00-04:00  286233500       0.00           0.0  \n", "2025-05-05 00:00:00-04:00  275704500       0.00           0.0  \n", "2025-05-12 00:00:00-04:00  264778300       0.26           0.0  \n", "2025-05-19 00:00:00-04:00  273024200       0.00           0.0  \n", "2025-05-26 00:00:00-04:00  223844900       0.00           0.0  \n", "2025-06-02 00:00:00-04:00  227142700       0.00           0.0  \n", "2025-06-09 00:00:00-04:00  334570664       0.00           0.0  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": "his_data_max['2011-08-19':'2011-08-20']"}, {"cell_type": "code", "execution_count": 69, "id": "c1fcbc1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-14 07:22:37.701049+00:00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>0.065</td>\n", "      <td>0.065</td>\n", "      <td>0.065</td>\n", "      <td>0.065</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            Open   High    Low  Close  Volume  Dividends  \\\n", "Date                                                                       \n", "2025-06-13 00:00:00-04:00  0.065  0.065  0.065  0.065       0        0.0   \n", "\n", "                           Stock Splits  \n", "Date                                     \n", "2025-06-13 00:00:00-04:00          0.04  "]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# Valid inputs for interval are:\n", "# Minutes : 1m,2m,5m,15m,30m,60m,90m\n", "# Hours : 1h\n", "# Days : 1d,5d\n", "# Weeks : 1wk\n", "# Months : 1mo,3mo\n", "hist_data = aapl.history(period='3mo', interval='1wk')\n", "hist_data"]}, {"cell_type": "code", "id": "882f5ff0", "metadata": {}, "source": ["NULGF = yf.Ticker('NULGF')\n", "print(datetime.datetime.now(datetime.timezone.utc))\n", "NULGF.history(period='1d')\n"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "a9df5584", "metadata": {}, "source": "## factor"}, {"cell_type": "code", "execution_count": 141, "id": "bce7c73d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21172\\717704275.py:2: FutureWarning: YF.download() has changed argument auto_adjust default to True\n", "  adjusted_data = yf.download('AAPL', start=\"2024-01-01\", end=\"2024-12-31\")\n", "[*********************100%***********************]  1 of 1 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>184.290421</td>\n", "      <td>187.070068</td>\n", "      <td>182.553143</td>\n", "      <td>185.789438</td>\n", "      <td>82488700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>182.910522</td>\n", "      <td>184.528677</td>\n", "      <td>182.096477</td>\n", "      <td>182.880742</td>\n", "      <td>58414500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>180.587540</td>\n", "      <td>181.758954</td>\n", "      <td>179.565029</td>\n", "      <td>180.825785</td>\n", "      <td>71983600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>179.862823</td>\n", "      <td>181.431339</td>\n", "      <td>178.860172</td>\n", "      <td>180.666948</td>\n", "      <td>62303300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>184.210983</td>\n", "      <td>184.250701</td>\n", "      <td>180.180502</td>\n", "      <td>180.766209</td>\n", "      <td>59144500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Price            Close        High         Low        Open    Volume\n", "Ticker            AAPL        AAPL        AAPL        AAPL      AAPL\n", "Date                                                                \n", "2024-01-02  184.290421  187.070068  182.553143  185.789438  82488700\n", "2024-01-03  182.910522  184.528677  182.096477  182.880742  58414500\n", "2024-01-04  180.587540  181.758954  179.565029  180.825785  71983600\n", "2024-01-05  179.862823  181.431339  178.860172  180.666948  62303300\n", "2024-01-08  184.210983  184.250701  180.180502  180.766209  59144500"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "      <th>AAPL</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>184.290421</td>\n", "      <td>185.639999</td>\n", "      <td>188.440002</td>\n", "      <td>183.889999</td>\n", "      <td>187.149994</td>\n", "      <td>82488700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>182.910522</td>\n", "      <td>184.250000</td>\n", "      <td>185.880005</td>\n", "      <td>183.429993</td>\n", "      <td>184.220001</td>\n", "      <td>58414500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>180.587540</td>\n", "      <td>181.910004</td>\n", "      <td>183.089996</td>\n", "      <td>180.880005</td>\n", "      <td>182.149994</td>\n", "      <td>71983600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>179.862823</td>\n", "      <td>181.179993</td>\n", "      <td>182.759995</td>\n", "      <td>180.169998</td>\n", "      <td>181.990005</td>\n", "      <td>62303300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>184.210983</td>\n", "      <td>185.559998</td>\n", "      <td>185.600006</td>\n", "      <td>181.500000</td>\n", "      <td>182.089996</td>\n", "      <td>59144500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Price        Adj Close       Close        High         Low        Open  \\\n", "Ticker            AAPL        AAPL        AAPL        AAPL        AAPL   \n", "Date                                                                     \n", "2024-01-02  184.290421  185.639999  188.440002  183.889999  187.149994   \n", "2024-01-03  182.910522  184.250000  185.880005  183.429993  184.220001   \n", "2024-01-04  180.587540  181.910004  183.089996  180.880005  182.149994   \n", "2024-01-05  179.862823  181.179993  182.759995  180.169998  181.990005   \n", "2024-01-08  184.210983  185.559998  185.600006  181.500000  182.089996   \n", "\n", "Price         Volume  \n", "Ticker          AAPL  \n", "Date                  \n", "2024-01-02  82488700  \n", "2024-01-03  58414500  \n", "2024-01-04  71983600  \n", "2024-01-05  62303300  \n", "2024-01-08  59144500  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Ticker</th>\n", "      <th>AAPL</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>0.99273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>0.99273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>0.99273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>0.99273</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>0.99273</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Ticker         AAPL\n", "Date               \n", "2024-01-02  0.99273\n", "2024-01-03  0.99273\n", "2024-01-04  0.99273\n", "2024-01-05  0.99273\n", "2024-01-08  0.99273"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Data with automatic adjustment enabled\n", "adjusted_data = aapl.history(start=\"2024-01-01\", end=\"2024-12-31\")\n", "display(adjusted_data.head())\n", "\n", "# Data without automatic adjustment\n", "raw_data = aapl.history(start=\"2024-01-01\", end=\"2024-12-31\", auto_adjust=False)\n", "display(raw_data.head())\n", "\n", "factor = adjusted_data['Close'] / raw_data['Close']\n", "display(factor.head())"]}, {"cell_type": "code", "execution_count": 157, "id": "3e08df92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2021-03-10 00:00:00-05:00  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  185.830002  188.270004  157.639999  159.750000   \n", "2021-03-11 00:00:00-05:00  169.929993  171.869995  161.679993  168.720001   \n", "2021-03-12 00:00:00-05:00  170.100006  180.000000  169.699997  176.399994   \n", "2021-03-15 00:00:00-04:00  176.000000  176.800003  170.500000  172.600006   \n", "2021-03-16 00:00:00-04:00  172.699997  174.399994  169.600006  170.600006   \n", "2021-03-17 00:00:00-04:00  167.000000  168.800003  161.800003  167.199997   \n", "2021-03-18 00:00:00-04:00  167.000000  167.300003  163.699997  165.000000   \n", "2021-03-19 00:00:00-04:00  162.800003  165.199997  158.100006  164.699997   \n", "\n", "                            Adj Close   Volume  Dividends  Stock Splits  \n", "Date                                                                     \n", "2021-03-10 00:00:00-05:00  158.173264  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  167.054733  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  174.658920  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00  170.896439   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00  168.916168   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00  165.549728   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00  163.371445   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00  163.074402   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"7\" halign=\"left\">adj</th>\n", "      <th colspan=\"8\" halign=\"left\">raw</th>\n", "      <th>factor</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  adj                                      \\\n", "                                 Open        High         Low       Close   \n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                                                                  raw  \\\n", "                            Volume Dividends Stock Splits        Open   \n", "Date                                                                    \n", "2021-03-10 00:00:00-05:00  3635100       0.0          0.0  185.830002   \n", "2021-03-11 00:00:00-05:00  3004400       0.0          0.0  169.929993   \n", "2021-03-12 00:00:00-05:00  1296580       0.0         10.0  170.100006   \n", "2021-03-15 00:00:00-04:00   715230       0.0          0.0  176.000000   \n", "2021-03-16 00:00:00-04:00   786380       0.0          0.0  172.699997   \n", "2021-03-17 00:00:00-04:00   818950       0.0          0.0  167.000000   \n", "2021-03-18 00:00:00-04:00   820450       0.0          0.0  167.000000   \n", "2021-03-19 00:00:00-04:00   862630       0.0          0.0  162.800003   \n", "\n", "                                                                           \\\n", "                                 High         Low       Close   Adj Close   \n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  188.270004  157.639999  159.750000  158.173264   \n", "2021-03-11 00:00:00-05:00  171.869995  161.679993  168.720001  167.054733   \n", "2021-03-12 00:00:00-05:00  180.000000  169.699997  176.399994  174.658920   \n", "2021-03-15 00:00:00-04:00  176.800003  170.500000  172.600006  170.896439   \n", "2021-03-16 00:00:00-04:00  174.399994  169.600006  170.600006  168.916168   \n", "2021-03-17 00:00:00-04:00  168.800003  161.800003  167.199997  165.549728   \n", "2021-03-18 00:00:00-04:00  167.300003  163.699997  165.000000  163.371445   \n", "2021-03-19 00:00:00-04:00  165.199997  158.100006  164.699997  163.074402   \n", "\n", "                                                            factor  \n", "                            Volume Dividends Stock Splits           \n", "Date                                                                \n", "2021-03-10 00:00:00-05:00  3635100       0.0          0.0  0.99013  \n", "2021-03-11 00:00:00-05:00  3004400       0.0          0.0  0.99013  \n", "2021-03-12 00:00:00-05:00  1296580       0.0         10.0  0.99013  \n", "2021-03-15 00:00:00-04:00   715230       0.0          0.0  0.99013  \n", "2021-03-16 00:00:00-04:00   786380       0.0          0.0  0.99013  \n", "2021-03-17 00:00:00-04:00   818950       0.0          0.0  0.99013  \n", "2021-03-18 00:00:00-04:00   820450       0.0          0.0  0.99013  \n", "2021-03-19 00:00:00-04:00   862630       0.0          0.0  0.99013  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Data with automatic adjustment enabled\n", "adjusted_data = yf.download('AAPL', start=\"2024-01-01\", end=\"2024-12-31\")\n", "display(adjusted_data.head())\n", "\n", "# Data without automatic adjustment\n", "raw_data = yf.download('AAPL', start=\"2024-01-01\", end=\"2024-12-31\", auto_adjust=False)\n", "display(raw_data.head())\n", "\n", "factor = adjusted_data['Close'] / raw_data['Close']\n", "display(factor.head())"]}, {"cell_type": "code", "execution_count": 158, "id": "69ea9bd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2021-03-10 00:00:00-05:00  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  185.830002  188.270004  157.639999  159.750000   \n", "2021-03-11 00:00:00-05:00  169.929993  171.869995  161.679993  168.720001   \n", "2021-03-12 00:00:00-05:00  170.100006  180.000000  169.699997  176.399994   \n", "2021-03-15 00:00:00-04:00  176.000000  176.800003  170.500000  172.600006   \n", "2021-03-16 00:00:00-04:00  172.699997  174.399994  169.600006  170.600006   \n", "2021-03-17 00:00:00-04:00  167.000000  168.800003  161.800003  167.199997   \n", "2021-03-18 00:00:00-04:00  167.000000  167.300003  163.699997  165.000000   \n", "2021-03-19 00:00:00-04:00  162.800003  165.199997  158.100006  164.699997   \n", "\n", "                            Adj Close   Volume  Dividends  Stock Splits  \n", "Date                                                                     \n", "2021-03-10 00:00:00-05:00  158.173264  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  167.054733  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  174.658920  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00  170.896439   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00  168.916168   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00  165.549728   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00  163.371445   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00  163.074402   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open_adj</th>\n", "      <th>High_adj</th>\n", "      <th>Low_adj</th>\n", "      <th>Close_adj</th>\n", "      <th>Volume_adj</th>\n", "      <th>Dividends_adj</th>\n", "      <th>Stock Splits_adj</th>\n", "      <th>Open_raw</th>\n", "      <th>High_raw</th>\n", "      <th>Low_raw</th>\n", "      <th>Close_raw</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume_raw</th>\n", "      <th>Dividends_raw</th>\n", "      <th>Stock Splits_raw</th>\n", "      <th>factor</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             Open_adj    High_adj     Low_adj   Close_adj  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                           Volume_adj  Dividends_adj  Stock Splits_adj  \\\n", "Date                                                                     \n", "2021-03-10 00:00:00-05:00     3635100            0.0               0.0   \n", "2021-03-11 00:00:00-05:00     3004400            0.0               0.0   \n", "2021-03-12 00:00:00-05:00     1296580            0.0              10.0   \n", "2021-03-15 00:00:00-04:00      715230            0.0               0.0   \n", "2021-03-16 00:00:00-04:00      786380            0.0               0.0   \n", "2021-03-17 00:00:00-04:00      818950            0.0               0.0   \n", "2021-03-18 00:00:00-04:00      820450            0.0               0.0   \n", "2021-03-19 00:00:00-04:00      862630            0.0               0.0   \n", "\n", "                             Open_raw    High_raw     Low_raw   Close_raw  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  185.830002  188.270004  157.639999  159.750000   \n", "2021-03-11 00:00:00-05:00  169.929993  171.869995  161.679993  168.720001   \n", "2021-03-12 00:00:00-05:00  170.100006  180.000000  169.699997  176.399994   \n", "2021-03-15 00:00:00-04:00  176.000000  176.800003  170.500000  172.600006   \n", "2021-03-16 00:00:00-04:00  172.699997  174.399994  169.600006  170.600006   \n", "2021-03-17 00:00:00-04:00  167.000000  168.800003  161.800003  167.199997   \n", "2021-03-18 00:00:00-04:00  167.000000  167.300003  163.699997  165.000000   \n", "2021-03-19 00:00:00-04:00  162.800003  165.199997  158.100006  164.699997   \n", "\n", "                            Adj Close  Volume_raw  Dividends_raw  \\\n", "Date                                                               \n", "2021-03-10 00:00:00-05:00  158.173264     3635100            0.0   \n", "2021-03-11 00:00:00-05:00  167.054733     3004400            0.0   \n", "2021-03-12 00:00:00-05:00  174.658920     1296580            0.0   \n", "2021-03-15 00:00:00-04:00  170.896439      715230            0.0   \n", "2021-03-16 00:00:00-04:00  168.916168      786380            0.0   \n", "2021-03-17 00:00:00-04:00  165.549728      818950            0.0   \n", "2021-03-18 00:00:00-04:00  163.371445      820450            0.0   \n", "2021-03-19 00:00:00-04:00  163.074402      862630            0.0   \n", "\n", "                           Stock Splits_raw   factor  \n", "Date                                                  \n", "2021-03-10 00:00:00-05:00               0.0  0.99013  \n", "2021-03-11 00:00:00-05:00               0.0  0.99013  \n", "2021-03-12 00:00:00-05:00              10.0  0.99013  \n", "2021-03-15 00:00:00-04:00               0.0  0.99013  \n", "2021-03-16 00:00:00-04:00               0.0  0.99013  \n", "2021-03-17 00:00:00-04:00               0.0  0.99013  \n", "2021-03-18 00:00:00-04:00               0.0  0.99013  \n", "2021-03-19 00:00:00-04:00               0.0  0.99013  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["his_data_max_adj = edu.history(period='max')\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n", "his_data_max = edu.history(period='max', auto_adjust=False)\n", "display(his_data_max['2021-03-10':'2021-03-20'])\n", "\n", "# 横向合并\n", "his_data_max_adj = pd.concat([his_data_max_adj, his_data_max], axis=1, keys=['adj', 'raw'])\n", "\n", "# factor = his_data_max_adj['adj']['Close']/his_data_max_adj['raw']['Close']\n", "# display(factor['2011-03-10':'2011-03-20'])\n", "his_data_max_adj['factor'] = his_data_max_adj['adj']['Close']/his_data_max_adj['raw']['Close']\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n"]}, {"cell_type": "code", "execution_count": null, "id": "f56d0fff657dbc37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2021-03-10 00:00:00-05:00  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  185.830002  188.270004  157.639999  159.750000   \n", "2021-03-11 00:00:00-05:00  169.929993  171.869995  161.679993  168.720001   \n", "2021-03-12 00:00:00-05:00  170.100006  180.000000  169.699997  176.399994   \n", "2021-03-15 00:00:00-04:00  176.000000  176.800003  170.500000  172.600006   \n", "2021-03-16 00:00:00-04:00  172.699997  174.399994  169.600006  170.600006   \n", "2021-03-17 00:00:00-04:00  167.000000  168.800003  161.800003  167.199997   \n", "2021-03-18 00:00:00-04:00  167.000000  167.300003  163.699997  165.000000   \n", "2021-03-19 00:00:00-04:00  162.800003  165.199997  158.100006  164.699997   \n", "\n", "                            Adj Close   Volume  Dividends  Stock Splits  \n", "Date                                                                     \n", "2021-03-10 00:00:00-05:00  158.173264  3635100        0.0           0.0  \n", "2021-03-11 00:00:00-05:00  167.054733  3004400        0.0           0.0  \n", "2021-03-12 00:00:00-05:00  174.658920  1296580        0.0          10.0  \n", "2021-03-15 00:00:00-04:00  170.896439   715230        0.0           0.0  \n", "2021-03-16 00:00:00-04:00  168.916168   786380        0.0           0.0  \n", "2021-03-17 00:00:00-04:00  165.549728   818950        0.0           0.0  \n", "2021-03-18 00:00:00-04:00  163.371445   820450        0.0           0.0  \n", "2021-03-19 00:00:00-04:00  163.074402   862630        0.0           0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"7\" halign=\"left\">adj</th>\n", "      <th colspan=\"8\" halign=\"left\">raw</th>\n", "      <th>factor</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-10 00:00:00-05:00</th>\n", "      <td>183.995855</td>\n", "      <td>186.411775</td>\n", "      <td>156.084089</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>185.830002</td>\n", "      <td>188.270004</td>\n", "      <td>157.639999</td>\n", "      <td>159.750000</td>\n", "      <td>158.173264</td>\n", "      <td>3635100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11 00:00:00-05:00</th>\n", "      <td>168.252782</td>\n", "      <td>170.173637</td>\n", "      <td>160.084210</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>169.929993</td>\n", "      <td>171.869995</td>\n", "      <td>161.679993</td>\n", "      <td>168.720001</td>\n", "      <td>167.054733</td>\n", "      <td>3004400</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12 00:00:00-05:00</th>\n", "      <td>168.421114</td>\n", "      <td>178.223394</td>\n", "      <td>168.025053</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>170.100006</td>\n", "      <td>180.000000</td>\n", "      <td>169.699997</td>\n", "      <td>176.399994</td>\n", "      <td>174.658920</td>\n", "      <td>1296580</td>\n", "      <td>0.0</td>\n", "      <td>10.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15 00:00:00-04:00</th>\n", "      <td>174.262874</td>\n", "      <td>175.054981</td>\n", "      <td>168.817160</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>176.000000</td>\n", "      <td>176.800003</td>\n", "      <td>170.500000</td>\n", "      <td>172.600006</td>\n", "      <td>170.896439</td>\n", "      <td>715230</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16 00:00:00-04:00</th>\n", "      <td>170.995432</td>\n", "      <td>172.678650</td>\n", "      <td>167.926038</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>172.699997</td>\n", "      <td>174.399994</td>\n", "      <td>169.600006</td>\n", "      <td>170.600006</td>\n", "      <td>168.916168</td>\n", "      <td>786380</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>167.133942</td>\n", "      <td>160.203033</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>168.800003</td>\n", "      <td>161.800003</td>\n", "      <td>167.199997</td>\n", "      <td>165.549728</td>\n", "      <td>818950</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18 00:00:00-04:00</th>\n", "      <td>165.351705</td>\n", "      <td>165.648747</td>\n", "      <td>162.084273</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>167.000000</td>\n", "      <td>167.300003</td>\n", "      <td>163.699997</td>\n", "      <td>165.000000</td>\n", "      <td>163.371445</td>\n", "      <td>820450</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19 00:00:00-04:00</th>\n", "      <td>161.193161</td>\n", "      <td>163.569467</td>\n", "      <td>156.539553</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>162.800003</td>\n", "      <td>165.199997</td>\n", "      <td>158.100006</td>\n", "      <td>164.699997</td>\n", "      <td>163.074402</td>\n", "      <td>862630</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.99013</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  adj                                      \\\n", "                                 Open        High         Low       Close   \n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  183.995855  186.411775  156.084089  158.173264   \n", "2021-03-11 00:00:00-05:00  168.252782  170.173637  160.084210  167.054733   \n", "2021-03-12 00:00:00-05:00  168.421114  178.223394  168.025053  174.658920   \n", "2021-03-15 00:00:00-04:00  174.262874  175.054981  168.817160  170.896439   \n", "2021-03-16 00:00:00-04:00  170.995432  172.678650  167.926038  168.916168   \n", "2021-03-17 00:00:00-04:00  165.351705  167.133942  160.203033  165.549728   \n", "2021-03-18 00:00:00-04:00  165.351705  165.648747  162.084273  163.371445   \n", "2021-03-19 00:00:00-04:00  161.193161  163.569467  156.539553  163.074402   \n", "\n", "                                                                  raw  \\\n", "                            Volume Dividends Stock Splits        Open   \n", "Date                                                                    \n", "2021-03-10 00:00:00-05:00  3635100       0.0          0.0  185.830002   \n", "2021-03-11 00:00:00-05:00  3004400       0.0          0.0  169.929993   \n", "2021-03-12 00:00:00-05:00  1296580       0.0         10.0  170.100006   \n", "2021-03-15 00:00:00-04:00   715230       0.0          0.0  176.000000   \n", "2021-03-16 00:00:00-04:00   786380       0.0          0.0  172.699997   \n", "2021-03-17 00:00:00-04:00   818950       0.0          0.0  167.000000   \n", "2021-03-18 00:00:00-04:00   820450       0.0          0.0  167.000000   \n", "2021-03-19 00:00:00-04:00   862630       0.0          0.0  162.800003   \n", "\n", "                                                                           \\\n", "                                 High         Low       Close   Adj Close   \n", "Date                                                                        \n", "2021-03-10 00:00:00-05:00  188.270004  157.639999  159.750000  158.173264   \n", "2021-03-11 00:00:00-05:00  171.869995  161.679993  168.720001  167.054733   \n", "2021-03-12 00:00:00-05:00  180.000000  169.699997  176.399994  174.658920   \n", "2021-03-15 00:00:00-04:00  176.800003  170.500000  172.600006  170.896439   \n", "2021-03-16 00:00:00-04:00  174.399994  169.600006  170.600006  168.916168   \n", "2021-03-17 00:00:00-04:00  168.800003  161.800003  167.199997  165.549728   \n", "2021-03-18 00:00:00-04:00  167.300003  163.699997  165.000000  163.371445   \n", "2021-03-19 00:00:00-04:00  165.199997  158.100006  164.699997  163.074402   \n", "\n", "                                                            factor  \n", "                            Volume Dividends Stock Splits           \n", "Date                                                                \n", "2021-03-10 00:00:00-05:00  3635100       0.0          0.0  0.99013  \n", "2021-03-11 00:00:00-05:00  3004400       0.0          0.0  0.99013  \n", "2021-03-12 00:00:00-05:00  1296580       0.0         10.0  0.99013  \n", "2021-03-15 00:00:00-04:00   715230       0.0          0.0  0.99013  \n", "2021-03-16 00:00:00-04:00   786380       0.0          0.0  0.99013  \n", "2021-03-17 00:00:00-04:00   818950       0.0          0.0  0.99013  \n", "2021-03-18 00:00:00-04:00   820450       0.0          0.0  0.99013  \n", "2021-03-19 00:00:00-04:00   862630       0.0          0.0  0.99013  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["his_data_max_adj = edu.history(period='max')\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n", "his_data_max = edu.history(period='max', auto_adjust=False)\n", "display(his_data_max['2021-03-10':'2021-03-20'])\n", "\n", "# 横向合并\n", "his_data_max_adj = pd.merge(his_data_max_adj, his_data_max, \n", "                           left_index=True, right_index=True,\n", "                           suffixes=('_adj', '_raw'))\n", "\n", "# factor = his_data_max_adj['Close_adj']/his_data_max_adj['Close_raw']\n", "# display(factor['2011-03-10':'2011-03-20'])\n", "his_data_max_adj['factor'] = his_data_max_adj['Close_adj']/his_data_max_adj['Close_raw']\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n"]}, {"cell_type": "code", "id": "c8b7b047e25371ae", "metadata": {}, "source": ["his_data_max_adj = edu.history(period='max')\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n", "his_data_max = edu.history(period='max', auto_adjust=False)\n", "display(his_data_max['2021-03-10':'2021-03-20'])\n", "\n", "# 横向合并\n", "his_data_max_adj = pd.concat([his_data_max_adj, his_data_max], axis=1, keys=['adj', 'raw'])\n", "\n", "# factor = his_data_max_adj['adj']['Close']/his_data_max_adj['raw']['Close']\n", "# display(factor['2011-03-10':'2011-03-20'])\n", "his_data_max_adj['factor'] = his_data_max_adj['adj']['Close']/his_data_max_adj['raw']['Close']\n", "display(his_data_max_adj['2021-03-10':'2021-03-20'])\n"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "f890b2e8bf5f4116", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T05:39:12.503036Z", "start_time": "2025-06-16T05:39:11.853613Z"}}, "source": ["## Other functions in Ticker module\n", "\n", "### actions"]}, {"cell_type": "code", "id": "720c57c2", "metadata": {}, "source": ["CHNR = yf.Ticker('RGC')\n", "CHNR.actions.tail(5)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "930fea98", "metadata": {}, "source": "#### dividends"}, {"cell_type": "code", "id": "823a5ef4", "metadata": {}, "source": "CHNR.dividends", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "126829c6", "metadata": {}, "source": "#### splits"}, {"cell_type": "code", "execution_count": 3, "id": "0f2a4338", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-08-13 00:00:00-04:00</th>\n", "      <td>0.0</td>\n", "      <td>0.076923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-20 00:00:00-04:00</th>\n", "      <td>0.0</td>\n", "      <td>0.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-04 00:00:00-05:00</th>\n", "      <td>0.0</td>\n", "      <td>0.050000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           Dividends  Stock Splits\n", "Date                                              \n", "2021-08-13 00:00:00-04:00        0.0      0.076923\n", "2024-05-20 00:00:00-04:00        0.0      0.200000\n", "2025-02-04 00:00:00-05:00        0.0      0.050000"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": "CHNR.splits"}, {"cell_type": "code", "id": "64fefd7e", "metadata": {}, "source": ["NSKFF = yf.Ticker('GCTK')\n", "NSKFF.actions.tail(5)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "61c37eed", "metadata": {}, "source": "### isin"}, {"cell_type": "code", "id": "9d5ec220", "metadata": {}, "source": "aapl.isin", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "c63e4444f57702e2", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:08:04.456225Z", "start_time": "2024-04-12T06:08:02.720260Z"}}, "source": "### financials"}, {"cell_type": "code", "id": "ffcf2481", "metadata": {}, "source": "aapl.financials", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "25a2a75b", "metadata": {}, "source": "### balance_sheet"}, {"cell_type": "code", "id": "13861943", "metadata": {}, "source": "aapl.balance_sheet", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "5e7c3e5f", "metadata": {}, "source": "### cashflow"}, {"cell_type": "code", "id": "d22a3736", "metadata": {}, "source": "aapl.cashflow", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "427aedfb", "metadata": {}, "source": "### income_stmt"}, {"cell_type": "code", "id": "3599ca12", "metadata": {}, "source": "aapl.income_stmt ", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "da365680d906ec48", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:08:18.563419Z", "start_time": "2024-04-12T06:08:18.093024Z"}}, "source": "### quarterly_financials"}, {"cell_type": "code", "id": "68bd930e", "metadata": {}, "source": "aapl.quarterly_financials", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "51a4e1501b48e9d6", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:10:56.526668Z", "start_time": "2024-04-16T09:10:56.257629Z"}}, "source": "### quarterly_income_stmt"}, {"cell_type": "code", "execution_count": 33, "id": "18b28f3e25a2a9b2", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:12:54.690773Z", "start_time": "2024-04-16T09:12:54.682863Z"}}, "outputs": [{"data": {"text/plain": ["14994082000.0"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": "aapl.quarterly_income_stmt"}, {"cell_type": "code", "execution_count": 34, "id": "e3849ec52a6eb286", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:14:05.178735Z", "start_time": "2024-04-16T09:14:04.915671Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-28 00:00:00-04:00</th>\n", "      <td>2.677126e+12</td>\n", "      <td>2.684559e+12</td>\n", "      <td>2.654234e+12</td>\n", "      <td>2.678613e+12</td>\n", "      <td>6.570512e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-29 00:00:00-04:00</th>\n", "      <td>2.671329e+12</td>\n", "      <td>2.748629e+12</td>\n", "      <td>2.668356e+12</td>\n", "      <td>2.737034e+12</td>\n", "      <td>7.947448e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-30 00:00:00-04:00</th>\n", "      <td>2.749224e+12</td>\n", "      <td>2.792483e+12</td>\n", "      <td>2.746251e+12</td>\n", "      <td>2.789509e+12</td>\n", "      <td>9.118486e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-31 00:00:00-04:00</th>\n", "      <td>2.792334e+12</td>\n", "      <td>2.811362e+12</td>\n", "      <td>2.786982e+12</td>\n", "      <td>2.792780e+12</td>\n", "      <td>9.115577e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-01 00:00:00-04:00</th>\n", "      <td>2.816862e+12</td>\n", "      <td>2.823254e+12</td>\n", "      <td>2.798875e+12</td>\n", "      <td>2.816416e+12</td>\n", "      <td>6.857184e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-05 00:00:00-04:00</th>\n", "      <td>2.798875e+12</td>\n", "      <td>2.824146e+12</td>\n", "      <td>2.788915e+12</td>\n", "      <td>2.819984e+12</td>\n", "      <td>6.789320e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-06 00:00:00-04:00</th>\n", "      <td>2.800659e+12</td>\n", "      <td>2.807348e+12</td>\n", "      <td>2.697641e+12</td>\n", "      <td>2.719047e+12</td>\n", "      <td>1.225853e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-07 00:00:00-04:00</th>\n", "      <td>2.604137e+12</td>\n", "      <td>2.649180e+12</td>\n", "      <td>2.579758e+12</td>\n", "      <td>2.639517e+12</td>\n", "      <td>1.686666e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-08 00:00:00-04:00</th>\n", "      <td>2.651260e+12</td>\n", "      <td>2.679356e+12</td>\n", "      <td>2.642936e+12</td>\n", "      <td>2.648733e+12</td>\n", "      <td>9.828816e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-11 00:00:00-04:00</th>\n", "      <td>2.676829e+12</td>\n", "      <td>2.680248e+12</td>\n", "      <td>2.636246e+12</td>\n", "      <td>2.666275e+12</td>\n", "      <td>8.839476e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-12 00:00:00-04:00</th>\n", "      <td>2.668207e+12</td>\n", "      <td>2.677721e+12</td>\n", "      <td>2.598785e+12</td>\n", "      <td>2.620786e+12</td>\n", "      <td>1.355018e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-13 00:00:00-04:00</th>\n", "      <td>2.623907e+12</td>\n", "      <td>2.635651e+12</td>\n", "      <td>2.586298e+12</td>\n", "      <td>2.589717e+12</td>\n", "      <td>1.263520e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-14 00:00:00-04:00</th>\n", "      <td>2.586596e+12</td>\n", "      <td>2.617813e+12</td>\n", "      <td>2.580352e+12</td>\n", "      <td>2.612462e+12</td>\n", "      <td>9.130766e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-15 00:00:00-04:00</th>\n", "      <td>2.623462e+12</td>\n", "      <td>2.623759e+12</td>\n", "      <td>2.583920e+12</td>\n", "      <td>2.601610e+12</td>\n", "      <td>1.637430e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-18 00:00:00-04:00</th>\n", "      <td>2.623462e+12</td>\n", "      <td>2.666572e+12</td>\n", "      <td>2.618854e+12</td>\n", "      <td>2.645612e+12</td>\n", "      <td>1.008466e+18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-19 00:00:00-04:00</th>\n", "      <td>2.638922e+12</td>\n", "      <td>2.670288e+12</td>\n", "      <td>2.633125e+12</td>\n", "      <td>2.661964e+12</td>\n", "      <td>7.770968e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-20 00:00:00-04:00</th>\n", "      <td>2.664788e+12</td>\n", "      <td>2.671329e+12</td>\n", "      <td>2.607407e+12</td>\n", "      <td>2.608745e+12</td>\n", "      <td>8.761972e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-21 00:00:00-04:00</th>\n", "      <td>2.594772e+12</td>\n", "      <td>2.620787e+12</td>\n", "      <td>2.584515e+12</td>\n", "      <td>2.585555e+12</td>\n", "      <td>9.453454e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-22 00:00:00-04:00</th>\n", "      <td>2.596555e+12</td>\n", "      <td>2.632381e+12</td>\n", "      <td>2.587339e+12</td>\n", "      <td>2.598339e+12</td>\n", "      <td>8.505453e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-25 00:00:00-04:00</th>\n", "      <td>2.589569e+12</td>\n", "      <td>2.630746e+12</td>\n", "      <td>2.588825e+12</td>\n", "      <td>2.617516e+12</td>\n", "      <td>6.923172e+17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   Open          High           Low  \\\n", "Date                                                                  \n", "2023-08-28 00:00:00-04:00  2.677126e+12  2.684559e+12  2.654234e+12   \n", "2023-08-29 00:00:00-04:00  2.671329e+12  2.748629e+12  2.668356e+12   \n", "2023-08-30 00:00:00-04:00  2.749224e+12  2.792483e+12  2.746251e+12   \n", "2023-08-31 00:00:00-04:00  2.792334e+12  2.811362e+12  2.786982e+12   \n", "2023-09-01 00:00:00-04:00  2.816862e+12  2.823254e+12  2.798875e+12   \n", "2023-09-05 00:00:00-04:00  2.798875e+12  2.824146e+12  2.788915e+12   \n", "2023-09-06 00:00:00-04:00  2.800659e+12  2.807348e+12  2.697641e+12   \n", "2023-09-07 00:00:00-04:00  2.604137e+12  2.649180e+12  2.579758e+12   \n", "2023-09-08 00:00:00-04:00  2.651260e+12  2.679356e+12  2.642936e+12   \n", "2023-09-11 00:00:00-04:00  2.676829e+12  2.680248e+12  2.636246e+12   \n", "2023-09-12 00:00:00-04:00  2.668207e+12  2.677721e+12  2.598785e+12   \n", "2023-09-13 00:00:00-04:00  2.623907e+12  2.635651e+12  2.586298e+12   \n", "2023-09-14 00:00:00-04:00  2.586596e+12  2.617813e+12  2.580352e+12   \n", "2023-09-15 00:00:00-04:00  2.623462e+12  2.623759e+12  2.583920e+12   \n", "2023-09-18 00:00:00-04:00  2.623462e+12  2.666572e+12  2.618854e+12   \n", "2023-09-19 00:00:00-04:00  2.638922e+12  2.670288e+12  2.633125e+12   \n", "2023-09-20 00:00:00-04:00  2.664788e+12  2.671329e+12  2.607407e+12   \n", "2023-09-21 00:00:00-04:00  2.594772e+12  2.620787e+12  2.584515e+12   \n", "2023-09-22 00:00:00-04:00  2.596555e+12  2.632381e+12  2.587339e+12   \n", "2023-09-25 00:00:00-04:00  2.589569e+12  2.630746e+12  2.588825e+12   \n", "\n", "                                  Close        Volume  Dividends  Stock Splits  \n", "Date                                                                            \n", "2023-08-28 00:00:00-04:00  2.678613e+12  6.570512e+17        0.0           0.0  \n", "2023-08-29 00:00:00-04:00  2.737034e+12  7.947448e+17        0.0           0.0  \n", "2023-08-30 00:00:00-04:00  2.789509e+12  9.118486e+17        0.0           0.0  \n", "2023-08-31 00:00:00-04:00  2.792780e+12  9.115577e+17        0.0           0.0  \n", "2023-09-01 00:00:00-04:00  2.816416e+12  6.857184e+17        0.0           0.0  \n", "2023-09-05 00:00:00-04:00  2.819984e+12  6.789320e+17        0.0           0.0  \n", "2023-09-06 00:00:00-04:00  2.719047e+12  1.225853e+18        0.0           0.0  \n", "2023-09-07 00:00:00-04:00  2.639517e+12  1.686666e+18        0.0           0.0  \n", "2023-09-08 00:00:00-04:00  2.648733e+12  9.828816e+17        0.0           0.0  \n", "2023-09-11 00:00:00-04:00  2.666275e+12  8.839476e+17        0.0           0.0  \n", "2023-09-12 00:00:00-04:00  2.620786e+12  1.355018e+18        0.0           0.0  \n", "2023-09-13 00:00:00-04:00  2.589717e+12  1.263520e+18        0.0           0.0  \n", "2023-09-14 00:00:00-04:00  2.612462e+12  9.130766e+17        0.0           0.0  \n", "2023-09-15 00:00:00-04:00  2.601610e+12  1.637430e+18        0.0           0.0  \n", "2023-09-18 00:00:00-04:00  2.645612e+12  1.008466e+18        0.0           0.0  \n", "2023-09-19 00:00:00-04:00  2.661964e+12  7.770968e+17        0.0           0.0  \n", "2023-09-20 00:00:00-04:00  2.608745e+12  8.761972e+17        0.0           0.0  \n", "2023-09-21 00:00:00-04:00  2.585555e+12  9.453454e+17        0.0           0.0  \n", "2023-09-22 00:00:00-04:00  2.598339e+12  8.505453e+17        0.0           0.0  \n", "2023-09-25 00:00:00-04:00  2.617516e+12  6.923172e+17        0.0           0.0  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dates = aapl.quarterly_income_stmt.columns\n", "total_shares = aapl.quarterly_income_stmt[all_dates[0]]['Basic Average Shares']\n", "total_shares"]}, {"cell_type": "code", "execution_count": 43, "id": "72a32733a8f50226", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:22:25.828015Z", "start_time": "2024-04-16T09:22:25.362363Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Stock</th>\n", "      <th>Marketcap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>XP</td>\n", "      <td>10396570624</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>STNE</td>\n", "      <td>3785293824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PAGS</td>\n", "      <td>2544733696</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Stock    Marketcap\n", "0    XP  10396570624\n", "1  STNE   3785293824\n", "2  PAGS   2544733696"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_price = aapl.history(start='2023-08-26', end='2023-09-26')\n", "market_cap = stock_price * total_shares\n", "market_cap"]}, {"cell_type": "code", "id": "5860ae5f", "metadata": {}, "source": ["stocks = ['XP','STNE','PAGS']\n", "\n", "df = pd.DataFrame(columns=['Stock','Marketcap'])\n", "df.columns = ['Stock','Marketcap']\n", "\n", "for stock in stocks: \n", "        info = yf.Ticker(stock).info\n", "        marketcap = info['marketCap']    \n", "        df = pd.concat([df, pd.DataFrame({'Stock':[stock], 'Marketcap':[marketcap]})], ignore_index=True)\n", "\n", "df"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "dc770fc3a749b50", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:27:56.352958Z", "start_time": "2024-04-16T09:27:54.565581Z"}}, "source": "### fast_info"}, {"cell_type": "code", "execution_count": 79, "id": "c7bef3ca209046d9", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:29:47.065629Z", "start_time": "2024-04-16T09:29:47.045159Z"}}, "outputs": [{"data": {"text/plain": ["lazy-loading dict with keys = ['currency', 'dayHigh', 'dayLow', 'exchange', 'fiftyDayAverage', 'lastPrice', 'lastVolume', 'marketCap', 'open', 'previousClose', 'quoteType', 'regularMarketPreviousClose', 'shares', 'tenDayAverageVolume', 'threeMonthAverageVolume', 'timezone', 'twoHundredDayAverage', 'yearChange', 'yearHigh', 'yearLow']"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["for stock in stocks: \n", "    info = yf.Ticker(stock).fast_info\n", "    marketcap = info['market_cap']\n", "    df = pd.concat([df, pd.DataFrame({'Stock':[stock], 'Marketcap':[marketcap]})], ignore_index=True)\n", "\n", "display(df)"]}, {"cell_type": "code", "id": "dfed6662", "metadata": {}, "source": "aapl.fast_info", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "906f521d3bbb0189", "metadata": {"ExecuteTime": {"end_time": "2024-04-16T09:34:16.497693Z", "start_time": "2024-04-16T09:34:16.112058Z"}}, "source": "#### shares"}, {"cell_type": "code", "id": "aad3192a", "metadata": {}, "source": "aapl.fast_info.shares", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "ed5b1e99c4107700", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:09:24.406004Z", "start_time": "2024-04-12T06:09:23.998726Z"}}, "source": "### recommendations"}, {"cell_type": "code", "execution_count": null, "id": "8e64e3b6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date Reported</th>\n", "      <th>Holder</th>\n", "      <th>pctHeld</th>\n", "      <th>Shares</th>\n", "      <th>Value</th>\n", "      <th>pctChange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-31</td>\n", "      <td>Vanguard Group Inc</td>\n", "      <td>0.0938</td>\n", "      <td>**********</td>\n", "      <td>************</td>\n", "      <td>0.0036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-03-31</td>\n", "      <td>Blackrock Inc.</td>\n", "      <td>0.0763</td>\n", "      <td>**********</td>\n", "      <td>************</td>\n", "      <td>0.0149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-03-31</td>\n", "      <td>State Street Corporation</td>\n", "      <td>0.0399</td>\n", "      <td>*********</td>\n", "      <td>************</td>\n", "      <td>0.0009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-03-31</td>\n", "      <td>Geode Capital Management, LLC</td>\n", "      <td>0.0234</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>0.0283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-03-31</td>\n", "      <td>FMR, LLC</td>\n", "      <td>0.0220</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>-0.0396</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-03-31</td>\n", "      <td>Berkshire Hathaway, Inc</td>\n", "      <td>0.0201</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-03-31</td>\n", "      <td>Morgan Stanley</td>\n", "      <td>0.0162</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>0.0124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-03-31</td>\n", "      <td>Price (<PERSON><PERSON>) Associates Inc</td>\n", "      <td>0.0144</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>-0.0246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-03-31</td>\n", "      <td>JPMORGAN CHASE &amp; CO</td>\n", "      <td>0.0133</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>0.0860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-12-31</td>\n", "      <td>NORGES BANK</td>\n", "      <td>0.0125</td>\n", "      <td>*********</td>\n", "      <td>***********</td>\n", "      <td>0.0542</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Date Reported                         Holder  pctHeld      Shares  \\\n", "0    2025-03-31             Vanguard Group Inc   0.0938  **********   \n", "1    2025-03-31                 Blackrock Inc.   0.0763  **********   \n", "2    2025-03-31       State Street Corporation   0.0399   *********   \n", "3    2025-03-31  Geode Capital Management, LLC   0.0234   *********   \n", "4    2025-03-31                       FMR, LLC   0.0220   *********   \n", "5    2025-03-31        Berkshire Hathaway, Inc   0.0201   *********   \n", "6    2025-03-31                 <PERSON>   0.0162   *********   \n", "7    2025-03-31  <PERSON> (<PERSON><PERSON>) Associates Inc   0.0144   *********   \n", "8    2025-03-31            JPMORGAN CHASE & CO   0.0133   *********   \n", "9    2024-12-31                    NORGES BANK   0.0125   *********   \n", "\n", "          Value  pctChange  \n", "0  ************     0.0036  \n", "1  ************     0.0149  \n", "2  ************     0.0009  \n", "3   ***********     0.0283  \n", "4   ***********    -0.0396  \n", "5   ***********     0.0000  \n", "6   ***********     0.0124  \n", "7   ***********    -0.0246  \n", "8   ***********     0.0860  \n", "9   ***********     0.0542  "]}, "metadata": {}, "output_type": "display_data"}], "source": "aapl.recommendations.tail(5)"}, {"cell_type": "code", "execution_count": null, "id": "53bd8636", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Breakdown</th>\n", "      <th>Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>insidersPercentHeld</th>\n", "      <td>0.02085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>institutionsPercentHeld</th>\n", "      <td>0.62893</td>\n", "    </tr>\n", "    <tr>\n", "      <th>institutionsFloatPercentHeld</th>\n", "      <td>0.64232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>institutionsCount</th>\n", "      <td>7020.00000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Breakdown                          Value\n", "insidersPercentHeld              0.02085\n", "institutionsPercentHeld          0.62893\n", "institutionsFloatPercentHeld     0.64232\n", "institutionsCount             7020.00000"]}, "metadata": {}, "output_type": "display_data"}], "source": "aapl.institutional_holders"}, {"cell_type": "code", "id": "ce3349a4", "metadata": {}, "source": "aapl.major_holders", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "f39bcc502fc9b54a", "metadata": {}, "source": "### sustainability"}, {"cell_type": "code", "id": "bcd8b29a", "metadata": {}, "source": ["esg_data = aapl.sustainability\n", "esg_data"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "c1e9743166a41897", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:10:17.746319Z", "start_time": "2024-04-12T06:10:16.850820Z"}}, "source": "### news"}, {"cell_type": "code", "id": "e98652d1", "metadata": {}, "source": "aapl.news", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "085cafda", "metadata": {}, "source": "### options"}, {"cell_type": "code", "id": "a7afd7e5", "metadata": {}, "source": "aapl.options", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "5fc3bd8802ec7c89", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:11:54.196172Z", "start_time": "2024-04-12T06:11:53.824545Z"}}, "source": "### option_chain"}, {"cell_type": "code", "execution_count": 54, "id": "64dcda4be8fcdba7", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:12:10.299484Z", "start_time": "2024-04-12T06:12:10.157727Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>contractSymbol</th>\n", "      <th>lastTradeDate</th>\n", "      <th>strike</th>\n", "      <th>lastPrice</th>\n", "      <th>bid</th>\n", "      <th>ask</th>\n", "      <th>change</th>\n", "      <th>percentChange</th>\n", "      <th>volume</th>\n", "      <th>openInterest</th>\n", "      <th>impliedVolatility</th>\n", "      <th>inTheMoney</th>\n", "      <th>contractSize</th>\n", "      <th>currency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AAPL250919P00005000</td>\n", "      <td>2025-05-23 14:08:09+00:00</td>\n", "      <td>5.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>3299</td>\n", "      <td>2.187505</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AAPL250919P00010000</td>\n", "      <td>2025-04-07 14:51:12+00:00</td>\n", "      <td>10.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>16</td>\n", "      <td>1.843751</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AAPL250919P00015000</td>\n", "      <td>2024-08-07 16:33:48+00:00</td>\n", "      <td>15.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.44</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>11</td>\n", "      <td>2.191411</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AAPL250919P00020000</td>\n", "      <td>2025-04-11 15:40:00+00:00</td>\n", "      <td>20.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>112</td>\n", "      <td>1.312503</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AAPL250919P00025000</td>\n", "      <td>2025-05-23 16:37:49+00:00</td>\n", "      <td>25.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>23</td>\n", "      <td>1.312503</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>AAPL250919P00320000</td>\n", "      <td>2025-03-18 13:44:06+00:00</td>\n", "      <td>320.0</td>\n", "      <td>105.90</td>\n", "      <td>121.85</td>\n", "      <td>123.10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>AAPL250919P00330000</td>\n", "      <td>2025-05-05 16:08:55+00:00</td>\n", "      <td>330.0</td>\n", "      <td>130.00</td>\n", "      <td>125.65</td>\n", "      <td>128.25</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>AAPL250919P00340000</td>\n", "      <td>2024-11-22 20:35:57+00:00</td>\n", "      <td>340.0</td>\n", "      <td>110.48</td>\n", "      <td>83.90</td>\n", "      <td>84.95</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>AAPL250919P00350000</td>\n", "      <td>2024-11-08 20:41:46+00:00</td>\n", "      <td>350.0</td>\n", "      <td>122.84</td>\n", "      <td>99.80</td>\n", "      <td>101.65</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>AAPL250919P00390000</td>\n", "      <td>2025-06-09 19:09:59+00:00</td>\n", "      <td>390.0</td>\n", "      <td>188.70</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15.0</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>65 rows × 14 columns</p>\n", "</div>"], "text/plain": ["         contractSymbol             lastTradeDate  strike  lastPrice     bid  \\\n", "0   AAPL250919P00005000 2025-05-23 14:08:09+00:00     5.0       0.01    0.00   \n", "1   AAPL250919P00010000 2025-04-07 14:51:12+00:00    10.0       0.01    0.00   \n", "2   AAPL250919P00015000 2024-08-07 16:33:48+00:00    15.0       0.01    0.00   \n", "3   AAPL250919P00020000 2025-04-11 15:40:00+00:00    20.0       0.01    0.00   \n", "4   AAPL250919P00025000 2025-05-23 16:37:49+00:00    25.0       0.01    0.00   \n", "..                  ...                       ...     ...        ...     ...   \n", "60  AAPL250919P00320000 2025-03-18 13:44:06+00:00   320.0     105.90  121.85   \n", "61  AAPL250919P00330000 2025-05-05 16:08:55+00:00   330.0     130.00  125.65   \n", "62  AAPL250919P00340000 2024-11-22 20:35:57+00:00   340.0     110.48   83.90   \n", "63  AAPL250919P00350000 2024-11-08 20:41:46+00:00   350.0     122.84   99.80   \n", "64  AAPL250919P00390000 2025-06-09 19:09:59+00:00   390.0     188.70    0.00   \n", "\n", "       ask  change  percentChange  volume  openInterest  impliedVolatility  \\\n", "0     0.01     0.0            0.0     5.0          3299           2.187505   \n", "1     0.02     0.0            0.0     3.0            16           1.843751   \n", "2     0.44     0.0            0.0     NaN            11           2.191411   \n", "3     0.01     0.0            0.0     1.0           112           1.312503   \n", "4     0.03     0.0            0.0    20.0            23           1.312503   \n", "..     ...     ...            ...     ...           ...                ...   \n", "60  123.10     0.0            0.0     2.0             0           0.000010   \n", "61  128.25     0.0            0.0     1.0             0           0.000010   \n", "62   84.95     0.0            0.0     2.0             0           0.000010   \n", "63  101.65     0.0            0.0     1.0             0           0.000010   \n", "64    0.00     0.0            0.0    15.0             0           0.000010   \n", "\n", "    inTheMoney contractSize currency  \n", "0        False      REGULAR      USD  \n", "1        False      REGULAR      USD  \n", "2        False      REGULAR      USD  \n", "3        False      REGULAR      USD  \n", "4        False      REGULAR      USD  \n", "..         ...          ...      ...  \n", "60        True      REGULAR      USD  \n", "61        True      REGULAR      USD  \n", "62        True      REGULAR      USD  \n", "63        True      REGULAR      USD  \n", "64        True      REGULAR      USD  \n", "\n", "[65 rows x 14 columns]"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["#All options of AAPL expiring on 26th February 2021\n", "aapl.option_chain('2025-09-19')"]}, {"cell_type": "code", "execution_count": 56, "id": "8fac8206daf3ca63", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:12:11.048276Z", "start_time": "2024-04-12T06:12:10.931348Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>contractSymbol</th>\n", "      <th>lastTradeDate</th>\n", "      <th>strike</th>\n", "      <th>lastPrice</th>\n", "      <th>bid</th>\n", "      <th>ask</th>\n", "      <th>change</th>\n", "      <th>percentChange</th>\n", "      <th>volume</th>\n", "      <th>openInterest</th>\n", "      <th>impliedVolatility</th>\n", "      <th>inTheMoney</th>\n", "      <th>contractSize</th>\n", "      <th>currency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AAPL250919C00005000</td>\n", "      <td>2025-05-12 13:30:07+00:00</td>\n", "      <td>5.0</td>\n", "      <td>205.05</td>\n", "      <td>196.55</td>\n", "      <td>198.40</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AAPL250919C00010000</td>\n", "      <td>2025-04-15 13:30:03+00:00</td>\n", "      <td>10.0</td>\n", "      <td>192.10</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.000010</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AAPL250919C00015000</td>\n", "      <td>2025-06-05 17:04:46+00:00</td>\n", "      <td>15.0</td>\n", "      <td>186.75</td>\n", "      <td>179.80</td>\n", "      <td>183.30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.984375</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AAPL250919C00020000</td>\n", "      <td>2025-06-12 19:39:18+00:00</td>\n", "      <td>20.0</td>\n", "      <td>179.00</td>\n", "      <td>175.00</td>\n", "      <td>178.30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1.908204</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AAPL250919C00025000</td>\n", "      <td>2025-06-11 19:05:18+00:00</td>\n", "      <td>25.0</td>\n", "      <td>174.57</td>\n", "      <td>169.90</td>\n", "      <td>173.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>0.500005</td>\n", "      <td>True</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>AAPL250919C00350000</td>\n", "      <td>2025-06-13 15:01:05+00:00</td>\n", "      <td>350.0</td>\n", "      <td>0.03</td>\n", "      <td>0.02</td>\n", "      <td>0.04</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>195</td>\n", "      <td>3928</td>\n", "      <td>0.396490</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>AAPL250919C00360000</td>\n", "      <td>2025-06-10 19:01:18+00:00</td>\n", "      <td>360.0</td>\n", "      <td>0.02</td>\n", "      <td>0.02</td>\n", "      <td>0.03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>39</td>\n", "      <td>3328</td>\n", "      <td>0.402350</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>AAPL250919C00370000</td>\n", "      <td>2025-06-11 15:44:25+00:00</td>\n", "      <td>370.0</td>\n", "      <td>0.02</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "      <td>902</td>\n", "      <td>0.417975</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>AAPL250919C00380000</td>\n", "      <td>2025-06-11 14:22:10+00:00</td>\n", "      <td>380.0</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>1580</td>\n", "      <td>0.417975</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>AAPL250919C00390000</td>\n", "      <td>2025-06-10 13:39:41+00:00</td>\n", "      <td>390.0</td>\n", "      <td>0.02</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>65</td>\n", "      <td>4651</td>\n", "      <td>0.433599</td>\n", "      <td>False</td>\n", "      <td>REGULAR</td>\n", "      <td>USD</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>70 rows × 14 columns</p>\n", "</div>"], "text/plain": ["         contractSymbol             lastTradeDate  strike  lastPrice     bid  \\\n", "0   AAPL250919C00005000 2025-05-12 13:30:07+00:00     5.0     205.05  196.55   \n", "1   AAPL250919C00010000 2025-04-15 13:30:03+00:00    10.0     192.10    0.00   \n", "2   AAPL250919C00015000 2025-06-05 17:04:46+00:00    15.0     186.75  179.80   \n", "3   AAPL250919C00020000 2025-06-12 19:39:18+00:00    20.0     179.00  175.00   \n", "4   AAPL250919C00025000 2025-06-11 19:05:18+00:00    25.0     174.57  169.90   \n", "..                  ...                       ...     ...        ...     ...   \n", "65  AAPL250919C00350000 2025-06-13 15:01:05+00:00   350.0       0.03    0.02   \n", "66  AAPL250919C00360000 2025-06-10 19:01:18+00:00   360.0       0.02    0.02   \n", "67  AAPL250919C00370000 2025-06-11 15:44:25+00:00   370.0       0.02    0.00   \n", "68  AAPL250919C00380000 2025-06-11 14:22:10+00:00   380.0       0.01    0.00   \n", "69  AAPL250919C00390000 2025-06-10 13:39:41+00:00   390.0       0.02    0.00   \n", "\n", "       ask  change  percentChange  volume  openInterest  impliedVolatility  \\\n", "0   198.40     0.0            0.0       2             0           0.000000   \n", "1     0.00     0.0            0.0       1             0           0.000010   \n", "2   183.30     0.0            0.0       1             1           1.984375   \n", "3   178.30     0.0            0.0       1             4           1.908204   \n", "4   173.00     0.0            0.0       2             1           0.500005   \n", "..     ...     ...            ...     ...           ...                ...   \n", "65    0.04     0.0            0.0     195          3928           0.396490   \n", "66    0.03     0.0            0.0      39          3328           0.402350   \n", "67    0.03     0.0            0.0       1           902           0.417975   \n", "68    0.02     0.0            0.0       5          1580           0.417975   \n", "69    0.02     0.0            0.0      65          4651           0.433599   \n", "\n", "    inTheMoney contractSize currency  \n", "0         True      REGULAR      USD  \n", "1         True      REGULAR      USD  \n", "2         True      REGULAR      USD  \n", "3         True      REGULAR      USD  \n", "4         True      REGULAR      USD  \n", "..         ...          ...      ...  \n", "65       False      REGULAR      USD  \n", "66       False      REGULAR      USD  \n", "67       False      REGULAR      USD  \n", "68       False      REGULAR      USD  \n", "69       False      REGULAR      USD  \n", "\n", "[70 rows x 14 columns]"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": "aapl.option_chain('2025-09-19').puts           # Put options only"}, {"cell_type": "code", "execution_count": 172, "id": "e19f9d1e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Holding Percent</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Symbol</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>MSFT</th>\n", "      <td>Microsoft Corp</td>\n", "      <td>0.068137</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NVDA</th>\n", "      <td>NVIDIA Corp</td>\n", "      <td>0.065888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AAPL</th>\n", "      <td>Apple Inc</td>\n", "      <td>0.060071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AMZN</th>\n", "      <td>Amazon.com Inc</td>\n", "      <td>0.038498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>META</th>\n", "      <td>Meta Platforms Inc Class A</td>\n", "      <td>0.028230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AVGO</th>\n", "      <td>Broadcom Inc</td>\n", "      <td>0.022589</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GOOGL</th>\n", "      <td>Alphabet Inc Class A</td>\n", "      <td>0.019943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TSLA</th>\n", "      <td>Tesla Inc</td>\n", "      <td>0.019301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BRK-B</th>\n", "      <td>Berkshire Hathaway Inc Class B</td>\n", "      <td>0.018390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GOOG</th>\n", "      <td>Alphabet Inc Class C</td>\n", "      <td>0.016267</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  Name  Holding Percent\n", "Symbol                                                 \n", "MSFT                    Microsoft Corp         0.068137\n", "NVDA                       NVIDIA Corp         0.065888\n", "AAPL                         Apple Inc         0.060071\n", "AMZN                    Amazon.com Inc         0.038498\n", "META        Meta Platforms Inc Class A         0.028230\n", "AVGO                      Broadcom Inc         0.022589\n", "GOOGL             Alphabet Inc Class A         0.019943\n", "TSLA                         Tesla Inc         0.019301\n", "BRK-B   Berkshire Hathaway Inc Class B         0.018390\n", "GOOG              Alphabet Inc Class C         0.016267"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": "aapl.option_chain('2025-09-19').calls          # Call options only"}, {"cell_type": "code", "id": "94507436", "metadata": {}, "source": ["spy = yf.Ticker('SPY').funds_data\n", "spy.description\n", "spy.top_holdings"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:13:31.024Z", "start_time": "2024-04-12T06:13:30.828700Z"}}, "source": "### *plot"}, {"cell_type": "code", "id": "51ca5961", "metadata": {}, "source": ["# Plotting\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(gold_data['Close'], label='Gold Price', color='gold')\n", "plt.title('Gold Price Trend Over Time')\n", "plt.xlabel('Date')\n", "plt.ylabel('Gold Price (USD)')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "c6fd9768", "metadata": {}, "source": "### live"}, {"cell_type": "code", "id": "d782cbbb", "metadata": {}, "source": ["gold = yf.Ticker(gold_ticker)\n", "gold.live()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "1478ec78", "metadata": {}, "source": "## index future"}, {"cell_type": "code", "execution_count": 59, "id": "2493dabba9888930", "metadata": {"ExecuteTime": {"end_time": "2024-04-12T06:17:15.063064Z", "start_time": "2024-04-12T06:17:10.120006Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-06-14 00:00:00-04:00</th>\n", "      <td>2037.000000</td>\n", "      <td>2038.500000</td>\n", "      <td>1999.099976</td>\n", "      <td>2008.000000</td>\n", "      <td>277013</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-17 00:00:00-04:00</th>\n", "      <td>2010.000000</td>\n", "      <td>2029.900024</td>\n", "      <td>1993.500000</td>\n", "      <td>2024.199951</td>\n", "      <td>267472</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-18 00:00:00-04:00</th>\n", "      <td>2025.000000</td>\n", "      <td>2036.400024</td>\n", "      <td>2014.599976</td>\n", "      <td>2026.599976</td>\n", "      <td>142325</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-20 00:00:00-04:00</th>\n", "      <td>2028.900024</td>\n", "      <td>2037.599976</td>\n", "      <td>2010.900024</td>\n", "      <td>2016.900024</td>\n", "      <td>52425</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-21 00:00:00-04:00</th>\n", "      <td>2017.000000</td>\n", "      <td>2022.400024</td>\n", "      <td>2005.000000</td>\n", "      <td>2015.540039</td>\n", "      <td>154456</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-09 00:00:00-04:00</th>\n", "      <td>2136.000000</td>\n", "      <td>2157.500000</td>\n", "      <td>2131.399902</td>\n", "      <td>2147.399902</td>\n", "      <td>150058</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-10 00:00:00-04:00</th>\n", "      <td>2147.699951</td>\n", "      <td>2168.600098</td>\n", "      <td>2142.500000</td>\n", "      <td>2159.399902</td>\n", "      <td>137266</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-11 00:00:00-04:00</th>\n", "      <td>2159.699951</td>\n", "      <td>2194.100098</td>\n", "      <td>2146.800049</td>\n", "      <td>2150.399902</td>\n", "      <td>196984</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-12 00:00:00-04:00</th>\n", "      <td>2147.100098</td>\n", "      <td>2151.300049</td>\n", "      <td>2124.600098</td>\n", "      <td>2141.199951</td>\n", "      <td>141049</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-13 00:00:00-04:00</th>\n", "      <td>2139.100098</td>\n", "      <td>2139.100098</td>\n", "      <td>2071.800049</td>\n", "      <td>2101.500000</td>\n", "      <td>141049</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>251 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                  Open         High          Low        Close  \\\n", "Date                                                                            \n", "2024-06-14 00:00:00-04:00  2037.000000  2038.500000  1999.099976  2008.000000   \n", "2024-06-17 00:00:00-04:00  2010.000000  2029.900024  1993.500000  2024.199951   \n", "2024-06-18 00:00:00-04:00  2025.000000  2036.400024  2014.599976  2026.599976   \n", "2024-06-20 00:00:00-04:00  2028.900024  2037.599976  2010.900024  2016.900024   \n", "2024-06-21 00:00:00-04:00  2017.000000  2022.400024  2005.000000  2015.540039   \n", "...                                ...          ...          ...          ...   \n", "2025-06-09 00:00:00-04:00  2136.000000  2157.500000  2131.399902  2147.399902   \n", "2025-06-10 00:00:00-04:00  2147.699951  2168.600098  2142.500000  2159.399902   \n", "2025-06-11 00:00:00-04:00  2159.699951  2194.100098  2146.800049  2150.399902   \n", "2025-06-12 00:00:00-04:00  2147.100098  2151.300049  2124.600098  2141.199951   \n", "2025-06-13 00:00:00-04:00  2139.100098  2139.100098  2071.800049  2101.500000   \n", "\n", "                           Volume  Dividends  Stock Splits  \n", "Date                                                        \n", "2024-06-14 00:00:00-04:00  277013        0.0           0.0  \n", "2024-06-17 00:00:00-04:00  267472        0.0           0.0  \n", "2024-06-18 00:00:00-04:00  142325        0.0           0.0  \n", "2024-06-20 00:00:00-04:00   52425        0.0           0.0  \n", "2024-06-21 00:00:00-04:00  154456        0.0           0.0  \n", "...                           ...        ...           ...  \n", "2025-06-09 00:00:00-04:00  150058        0.0           0.0  \n", "2025-06-10 00:00:00-04:00  137266        0.0           0.0  \n", "2025-06-11 00:00:00-04:00  196984        0.0           0.0  \n", "2025-06-12 00:00:00-04:00  141049        0.0           0.0  \n", "2025-06-13 00:00:00-04:00  141049        0.0           0.0  \n", "\n", "[251 rows x 7 columns]"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["russell = yf.Ticker('^RUT')# ^ means index # Russell 2000指数\n", "russell_data = russell.history(period='1y')\n", "russell_data"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["rty_f = yf.Ticker('RTY=F') # RTY 是Russell 2000指数的期货合约\n", "rty_data = rty_f.history(period='1y')\n", "rty_data"], "id": "abceae6b878862bc"}], "metadata": {"kernelspec": {"display_name": "test", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}