from flask import jsonify
from app.exceptions import ValidationError
from . import api
from ..logConf import logger


def bad_request(message):
    response = jsonify({'error': 'bad request', 'message': message})
    response.status_code = 400
    logger.error(f'bad request: {message}')
    return response


def unauthorized(message):
    response = jsonify({'error': 'unauthorized', 'message': message})
    response.status_code = 401
    logger.error(f'unauthorized: {message}')
    return response


def forbidden(message):
    response = jsonify({'error': 'forbidden', 'message': message})
    response.status_code = 403
    logger.error(f'forbidden: {message}')
    return response

#404
def page_not_found(message):
    response = jsonify({'error': 'page not found', 'message': message})
    response.status_code = 404
    logger.error(f'page not found: {message}')
    return response

#201
def created(message):
    response = jsonify({'error': 'created', 'message': message})
    response.status_code = 201
    logger.info(f'created: {message}')
    return response

@api.errorhandler(ValidationError)
def validation_error(e):
    logger.error(f'validation error: {e.args[0]}')
    return bad_request(e.args[0])
