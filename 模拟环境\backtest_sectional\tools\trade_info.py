from datetime import datetime
import requests


class TradeInfo:
    symbol: str
    author: str
    direction: str
    offset: str
    price: float = 0
    volume: float = 0
    create_date: str
    datetime: str
    pnl: float = 0
    remarks: str = None
    strategy_name: str = None


def send_trades_info(trades_dict, strategy_class, flask_backend_url='http://118.89.200.89:5000/api/v1/update/'):
    # {}为空，{'FG888.CZCE': {}, 'SR888.CZCE': {}, 'p888.DCE': {}, 'y888.DCE': {}}也是为空
    if trades_dict and sum(len(v) for v in trades_dict.values()) > 0:
        trades_infos = {}
        for elem, trades in trades_dict.items():
            trades_info = {}
            try:
                create_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                for i, v in enumerate(trades.values()):
                    trade_info = TradeInfo()
                    trade_info.symbol = v.symbol
                    trade_info.author = strategy_class.author
                    trade_info.direction = v.direction.value  # name属性返回枚举成员的名称，value属性返回枚举成员的值
                    trade_info.offset = v.offset.value
                    trade_info.price = v.price
                    trade_info.volume = v.volume
                    trade_info.create_date = create_date
                    trade_info.datetime = v.datetime.strftime('%Y-%m-%d %H:%M:%S')
                    trade_info.pnl = 0
                    trade_info.remarks = None
                    trade_info.strategy_name = strategy_class.strategy_name
                    trades_info[i] = trade_info.__dict__
            except Exception as e:
                print(f'Error occurred while creating trades info for {elem}: {str(e)}')
            trades_infos[elem] = trades_info
        response = requests.post(flask_backend_url, json=trades_infos)
        if response.status_code == 200:
            print(f'Successfully updated {strategy_class.strategy_name} trades info.')
        else:
            print(f'Failed to update {strategy_class.strategy_name} trades info.')
    else:
        # 向'http://118.89.200.89:5000/api/v1/delete/'发送strategy_name
        strategy_name = strategy_class.strategy_name
        flask_backend_delete_url = flask_backend_url.replace('update', 'delete')
        response = requests.post(flask_backend_delete_url, json={'strategy_name': strategy_name})
        if response.status_code == 200:
            print(f'Successfully deleted {strategy_class.strategy_name} trades info.')
        else:
            print(f'Failed to delete {strategy_class.strategy_name} trades info.')