{% extends "base.html" %}

{% block content %}
<h2>User Management</h2>
<table class="table">
    <thead>
        <tr>
            <th>Username</th>
            <th>Admin</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for user in users %}
        <tr>
            <td>{{ user.username }}</td>
            <td>{{ 'Yes' if user.is_admin else 'No' }}</td>
            <td>
                {% if user.username != 'root' %}
                <form action="{{ url_for('delete_user', user_id=user.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this user?');">Delete</button>
                </form>
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<a href="{{ url_for('add_user') }}" class="btn btn-primary">Add User</a>
{% endblock %}
