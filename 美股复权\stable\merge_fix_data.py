#!/usr/bin/env python3
"""
数据修复合并脚本
用于将hisfix文件夹中的修复数据替换his文件夹中对应的原始数据
"""

import os
import pandas as pd
from pathlib import Path
import shutil
from typing import Dict, List, Set
import typer
from loguru import logger
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志

from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from tqdm import tqdm

# 添加项目根目录到 Python 路径
import sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.mixin import NA_VALUES
import sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.excel_utils import auto_adjust_worksheet
from utils.mixin import NA_VALUES

app = typer.Typer()

def backup_file(file_path: Path) -> Path:
    """备份原文件"""
    backup_path = file_path.with_suffix('.backup' + file_path.suffix)
    shutil.copy2(file_path, backup_path)
    logger.info(f"已备份原文件: {backup_path}")
    return backup_path

def get_matching_files(hisfix_folder: Path, his_folder: Path) -> List[tuple]:
    """获取匹配的文件对"""
    matching_files = []
    
    if not hisfix_folder.exists():
        logger.error(f"hisfix文件夹不存在: {hisfix_folder}")
        return matching_files
    
    if not his_folder.exists():
        logger.error(f"his文件夹不存在: {his_folder}")
        return matching_files
    
    # 获取hisfix文件夹中的所有xlsx文件
    hisfix_files = list(hisfix_folder.glob("*.xlsx"))
    
    for hisfix_file in hisfix_files:
        # 查找his文件夹中的同名文件
        his_file = his_folder / hisfix_file.name
        if his_file.exists():
            matching_files.append((hisfix_file, his_file))
            logger.info(f"找到匹配文件对: {hisfix_file.name}")
        else:
            logger.warning(f"在his文件夹中未找到对应文件: {hisfix_file.name}")
    
    return matching_files

def merge_sheet_data(fix_df: pd.DataFrame, original_df: pd.DataFrame, key_column: str = 'conid', sheet_name: str = '', process_id: str = None, target_file: str = None, session_timestamp: str = None) -> pd.DataFrame:
    """合并单个sheet的数据"""
    if fix_df.empty:
        logger.warning("修复数据为空，跳过合并")
        return original_df
    
    if key_column not in fix_df.columns:
        logger.error(f"修复数据中缺少关键列: {key_column}")
        return original_df
    
    if key_column not in original_df.columns:
        logger.error(f"原始数据中缺少关键列: {key_column}")
        return original_df
    
    # 获取需要替换的conid列表
    fix_conids = set(fix_df[key_column].dropna())
    logger.info(f"需要替换的{key_column}数量: {len(fix_conids)}")
    
    # 复制原始数据作为结果
    merged_df = original_df.copy()
    
    # 创建变更日志列表
    change_logs = []
    
    # 对每个需要修复的conid，替换对应行的数据（除ID列外）
    updated_count = 0
    for _, fix_row in fix_df.iterrows():
        conid_value = fix_row[key_column]
        if pd.isna(conid_value):
            continue
            
        # 找到原始数据中对应的行
        mask = merged_df[key_column] == conid_value
        matching_rows = merged_df[mask]
        
        if not matching_rows.empty:
            # 获取原始行数据
            original_row = matching_rows.iloc[0]
            
            # 获取要更新的列
            if sheet_name == '稳定ID':
                # 稳定ID sheet只更新指定字段
                allowed_columns = ['TRADING', '剔除', '成交额>1500', '券池>100', '无相同实体', '类型白名单', 
                                 '起始时间_db', '结束时间_db', '日均成交额（万美元）_1M', '收盘价_1M', '中文名_futu']
                update_columns = [col for col in fix_df.columns if col in allowed_columns]
            else:
                # 其他sheet除ID列外的所有列
                update_columns = [col for col in fix_df.columns if col != 'ID']
            
            # 记录每列的变更
            row_changes = []
            for col in update_columns:
                if col in merged_df.columns:
                    old_value = original_row[col]
                    new_value = fix_row[col]
                    
                    # 只记录实际发生变更的列
                    if pd.isna(old_value) and pd.isna(new_value):
                        continue  # 都是NaN，不算变更
                    elif old_value != new_value:
                        row_changes.append({
                            'target_file': target_file,
                            'sheet': sheet_name,
                            'conid': conid_value,
                            'column': col,
                            'old_value': old_value,
                            'new_value': new_value
                        })
                        
                        # 更新数据
                        merged_df.loc[mask, col] = new_value
            
            if row_changes:
                change_logs.extend(row_changes)
                updated_count += 1
                logger.debug(f"已更新{key_column}={conid_value}的数据，变更了{len(row_changes)}个字段")
        else:
            # 如果原始数据中没有对应的conid，添加新行
            # merged_df = pd.concat([merged_df, fix_row.to_frame().T], ignore_index=True)
            # logger.debug(f"添加新行: {key_column}={conid_value}")
            #
            # # 记录新增行
            # change_logs.append({
            #     'target_file': target_file,
            #     'sheet': sheet_name,
            #     'conid': conid_value,
            #     'column': 'NEW_ROW',
            #     'old_value': None,
            #     'new_value': '新增整行数据'
            # })

            # 修复模式：如果原始数据中没有对应的conid，跳过而不是添加新行
            logger.warning(f"原始数据中未找到{key_column}={conid_value}，跳过修复")
    
    # 如果有ID列，按ID排序
    if 'ID' in merged_df.columns:
        merged_df = merged_df.sort_values('ID').reset_index(drop=True)
    
    # 保存变更日志到文件
    if change_logs:
        save_change_log(change_logs, sheet_name, process_id, session_timestamp)
    
    logger.info(f"合并完成: 更新了{updated_count}行数据，总行数: {len(merged_df)}，记录了{len(change_logs)}个变更")
    return merged_df

def save_change_log(change_logs: List[Dict], sheet_name: str, process_id: str = None, session_timestamp: str = None):
    """保存变更日志到文件"""
    from datetime import datetime
    import os
    
    # 创建日志文件夹和子文件夹
    log_folder = Path("merge_logs")
    log_folder.mkdir(exist_ok=True)
    
    # 使用传入的会话时间戳，如果没有则生成新的
    if session_timestamp is None:
        session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    sub_folder = log_folder / session_timestamp
    sub_folder.mkdir(exist_ok=True)
    
    # 生成日志文件名，包含进程ID
    if process_id is None:
        process_id = str(os.getpid())
    
    log_file = sub_folder / f"merge_changes_{process_id}.csv"
    
    # 转换为DataFrame并保存
    if change_logs:
        df_changes = pd.DataFrame(change_logs)
        
        # 如果文件已存在，追加数据
        if log_file.exists():
            df_changes.to_csv(log_file, mode='a', header=False, index=False, encoding='utf-8-sig')
        else:
            df_changes.to_csv(log_file, index=False, encoding='utf-8-sig')
        
        logger.info(f"变更日志已保存到: {log_file}")
        
        # 同时输出到控制台日志
        logger.info(f"Sheet '{sheet_name}' 的详细变更:")
        for change in change_logs:
            if change['column'] == 'NEW_ROW':
                logger.info(f"  conid={change['conid']}: {change['new_value']}")
            else:
                logger.info(f"  conid={change['conid']}, 列={change['column']}: '{change['old_value']}' -> '{change['new_value']}'")
    
    return log_file if change_logs else None

column_configs = {
    5: [  # 价格相关列
        '最高价_1M', '最低价_1M', '均价_1M', '收盘价_1M', 'ATR_1M',
        '最高价_3M', '最低价_3M', '均价_3M', 'ATR_3M',
        '最高价_6M', '最低价_6M', '均价_6M', 'ATR_6M',
        '最高价_1Y', '最低价_1Y', '均价_1Y', 'ATR_1Y',
        '最高价_2Y', '最低价_2Y', '均价_2Y', 'ATR_2Y',
        '最高价_3Y', '最低价_3Y', '均价_3Y', 'ATR_3Y',
        '目标均价_yahoo', '目标中位价_yahoo',
        '月均中间价_ib', '绝对盘口_ib', '月均中间价_yahoo', '绝对盘口_yahoo'
    ],
    4: [  # 亿相关列
        '总股本（亿）', '总股本（亿）_ib', '总股本（亿）_yahoo', '总股本（亿）_wind',
        '流通股本（亿）', '流通股本（亿）_ib', '流通股本（亿）_yahoo',
        '市值（亿美元）', '市值（亿美元）_ib', '市值（亿美元）_yahoo',
        '总收入（亿美元）', '总收入（亿美元）_ib', '总收入（亿美元）_yahoo',
        '毛利（亿美元）_yahoo', '总现金（亿美元）_yahoo', '总债务（亿美元）_yahoo',
        '月均现金利率-融券费率（%）', '月均融券费率（%）', '现金利率-融券费率（%）', '融券费率（%）'
    ],
    3: [  # 比率和成交量相关列
        '市盈率', '市盈率_ib', '市盈率_yahoo', '市盈率_wind',
        '市净率', '市净率_ib', '市净率_yahoo', '市净率_wind',
        '换手率（%）_wind',
        '空头占总股本比例（%）_yahoo', '空头占流通股本比例（%）_yahoo',
        '日均成交量（万）_1M', '日均成交量（万）_3M', '日均成交量（万）_6M',
        '日均成交量（万）_1Y', '日均成交量（万）_2Y', '日均成交量（万）_3Y',
        '日均成交额（万美元）_1M', '日均成交额（万美元）_3M', '日均成交额（万美元）_6M',
        '日均成交额（万美元）_1Y', '日均成交额（万美元）_2Y', '日均成交额（万美元）_3Y'
    ],
    2: [  # 百分比相关列
        '股息收益率（%）', '股息收益率（%）_yahoo', '股息收益率（%）_wind',
        '空头比率（空头持仓/日成交量）_yahoo',
        '流通股本占总股本比例（%）',
        '盘口跳数_ib', '相对盘口（1/2000）_ib', '相对盘口（万一）_ib',
        '盘口跳数_yahoo', '相对盘口（1/2000）_yahoo', '相对盘口（万一）_yahoo'
    ]
}

def format_merged_data(merged_sheets: Dict[str, pd.DataFrame]):
    """对合并后的数据应用小数位数格式化，与stk_data_summary_Semaphore.py保持一致"""
    # 定义列配置：小数位数和列名列表（与原脚本保持一致）
    
    # 统一处理小数位数格式化
    for decimal_places, columns in column_configs.items():
        for col in columns:
            for sheet_name, df in merged_sheets.items():
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:.{decimal_places}f}" if pd.notnull(x) else x)
    
    logger.info("已应用数据格式化")

def apply_excel_formatting(excel_file: Path):
    """应用Excel格式化，参考stk_data_summary_Semaphore.py的格式"""
    try:
        # 构建需要右对齐的列列表
        right_align_cols = []
        for cols in column_configs.values():
            right_align_cols.extend(cols)
        right_align_cols.extend(['分析师数量_yahoo'])
        
        # 加载工作簿并应用格式
        workbook = load_workbook(excel_file)
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            
            # 创建自定义对齐方式配置
            column_alignments = {}
            for col_idx, column in enumerate(worksheet.columns, 1):
                header_value = column[0].value
                if header_value in right_align_cols:
                    column_alignments[get_column_letter(col_idx)] = 'right'
            
            # 应用自动调整和对齐
            auto_adjust_worksheet(worksheet, column_alignments=column_alignments)
        
        workbook.save(excel_file)
        logger.info(f"已应用Excel格式化: {excel_file}")
        
    except Exception as e:
        logger.warning(f"应用Excel格式化时发生错误: {e}")
        # 格式化失败不影响数据合并的成功

def merge_all_change_logs():
    """合并所有变更日志CSV文件到一个Excel文件"""
    try:
        from datetime import datetime
        
        log_folder = Path("merge_logs")
        if not log_folder.exists():
            logger.info("没有找到merge_logs文件夹，跳过日志合并")
            return
        
        # 查找最新的子文件夹
        sub_folders = [f for f in log_folder.iterdir() if f.is_dir()]
        if not sub_folders:
            logger.info("没有找到日志子文件夹，跳过日志合并")
            return
        
        # 获取最新的子文件夹
        latest_folder = max(sub_folders, key=lambda x: x.name)
        logger.info(f"正在合并日志文件夹: {latest_folder}")
        
        # 收集所有CSV文件
        csv_files = list(latest_folder.glob("*.csv"))
        if not csv_files:
            logger.info("没有找到CSV文件，跳过日志合并")
            return
        
        # 读取并合并所有CSV文件
        all_changes = []
        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                if not df.empty:
                    all_changes.append(df)
                    logger.debug(f"读取CSV文件: {csv_file.name}, 行数: {len(df)}")
            except Exception as e:
                logger.warning(f"读取CSV文件 {csv_file} 失败: {str(e)}")
        
        if not all_changes:
            logger.info("没有有效的变更数据，跳过日志合并")
            return
        
        # 合并所有数据
        merged_df = pd.concat(all_changes, ignore_index=True)
        logger.info(f"合并了 {len(csv_files)} 个CSV文件，总计 {len(merged_df)} 条变更记录")
        
        # 按target_file、sheet和conid排序
        sort_columns = []
        if 'target_file' in merged_df.columns:
            sort_columns.append('target_file')
        if 'sheet' in merged_df.columns:
            sort_columns.append('sheet')
        if 'conid' in merged_df.columns:
            sort_columns.append('conid')
        
        if sort_columns:
            merged_df = merged_df.sort_values(sort_columns)
        
        # 生成Excel文件名
        excel_file = latest_folder / f"merged_changes.xlsx"
        
        # 保存为Excel文件
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            merged_df.to_excel(writer, sheet_name='变更记录', index=False)
        
        # 应用Excel格式化
        try:
            workbook = load_workbook(excel_file)
            worksheet = workbook['变更记录']
            
            # 使用auto_adjust_worksheet进行格式化
            auto_adjust_worksheet(
                worksheet,
                adjust_width=True,
                center_align=True,
                max_width=50,
                extra_width=2
            )
            
            workbook.save(excel_file)
            logger.info(f"变更日志已合并并保存为Excel文件: {excel_file}")
            
        except Exception as e:
            logger.warning(f"Excel格式化失败: {str(e)}，但文件已保存")
        
        # 删除原始CSV文件（可选）
        try:
            for csv_file in csv_files:
                csv_file.unlink()
            logger.info(f"已清理 {len(csv_files)} 个临时CSV文件")
        except Exception as e:
            logger.warning(f"清理CSV文件时发生错误: {str(e)}")
            
    except Exception as e:
        logger.error(f"合并变更日志时发生错误: {str(e)}")

def merge_single_file(file_pair: tuple) -> tuple:
    """合并单个文件对，用于多进程处理"""
    hisfix_file, his_file, backup, process_id, session_timestamp = file_pair
    try:
        result = merge_excel_files(hisfix_file, his_file, backup, process_id, session_timestamp)
        return (hisfix_file.name, result, None)
    except Exception as e:
        return (hisfix_file.name, False, str(e))

def merge_excel_files(hisfix_file: Path, his_file: Path, backup: bool = True, process_id: str = None, session_timestamp: str = None) -> bool:
    """合并Excel文件"""
    try:
        logger.info(f"开始合并文件: {hisfix_file.name}")
        
        # 备份原文件
        if backup:
            backup_file(his_file)
        
        # 读取修复文件的所有sheet，使用NA_VALUES避免NA股票被误识别
        fix_sheets = pd.read_excel(hisfix_file, sheet_name=None, engine='openpyxl', 
                                  na_values=NA_VALUES, keep_default_na=False)
        
        # 读取原始文件的所有sheet，使用NA_VALUES避免NA股票被误识别
        original_sheets = pd.read_excel(his_file, sheet_name=None, engine='openpyxl',
                                       na_values=NA_VALUES, keep_default_na=False)
        
        # 合并数据
        merged_sheets = {}
        
        for sheet_name, fix_df in fix_sheets.items():
            if sheet_name in original_sheets:
                logger.info(f"处理sheet: {sheet_name}")
                original_df = original_sheets[sheet_name]
                
                # 根据sheet类型选择合适的key列
                if sheet_name == '稳定ID':
                    key_column = 'conid'
                elif sheet_name.startswith('cor_'):
                    # 相关性矩阵，直接替换整个sheet
                    merged_sheets[sheet_name] = fix_df
                    logger.info(f"相关性矩阵sheet直接替换: {sheet_name}")
                    continue
                else:
                    # 其他sheet（周期数据）
                    key_column = 'conid'
                
                merged_df = merge_sheet_data(fix_df, original_df, key_column, sheet_name, process_id, his_file.name, session_timestamp)
                merged_sheets[sheet_name] = merged_df
            else:
                logger.warning(f"原始文件中不存在sheet: {sheet_name}")
                merged_sheets[sheet_name] = fix_df
        
        # 添加原始文件中存在但修复文件中不存在的sheet
        for sheet_name, original_df in original_sheets.items():
            if sheet_name not in merged_sheets:
                merged_sheets[sheet_name] = original_df
                logger.info(f"保留原始sheet: {sheet_name}")
        
        # 应用数据格式化（在写入Excel之前）
        format_merged_data(merged_sheets)
        
        # 写入合并后的数据并应用格式
        with pd.ExcelWriter(his_file, engine='openpyxl') as writer:
            for sheet_name, df in merged_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 应用Excel格式化
        apply_excel_formatting(his_file)
        
        logger.info(f"文件合并完成: {his_file}")
        return True
        
    except Exception as e:
        logger.error(f"合并文件时发生错误: {e}")
        return False

@app.command()
def merge(
    hisfix_folder: str = typer.Option("hisfix", "--hisfix", "-f", help="修复数据文件夹路径"),
    his_folder: str = typer.Option("his", "--his", "-h", help="原始数据文件夹路径"),
    backup: bool = typer.Option(True, "--backup/--no-backup", "-b", help="是否备份原文件"),
    max_workers: int = typer.Option(None, "--max-workers", "-w", help="最大并发进程数，默认为CPU核心数")
) -> None:
    """合并修复数据到原始文件"""
    from datetime import datetime
    
    hisfix_path = Path(hisfix_folder)
    his_path = Path(his_folder)
    
    # 获取匹配的文件对
    matching_files = get_matching_files(hisfix_path, his_path)
    
    if not matching_files:
        logger.error("未找到任何匹配的文件对")
        raise typer.Exit(1)
    
    logger.info(f"找到 {len(matching_files)} 个文件对需要处理")
    
    # 设置最大工作进程数
    if max_workers is None:
        max_workers = min(cpu_count(), len(matching_files))
    
    logger.info(f"使用 {max_workers} 个进程进行并行处理")
    
    # 生成全局会话时间戳，确保所有进程使用同一个子文件夹
    session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    logger.info(f"会话时间戳: {session_timestamp}")
    
    # 准备文件对数据（添加backup参数、进程ID和会话时间戳）
    file_pairs = [(hisfix_file, his_file, backup, f"proc_{i}", session_timestamp) for i, (hisfix_file, his_file) in enumerate(matching_files)]
    
    # 多进程处理文件
    success_count = 0
    failed_files = []
    
    if len(matching_files) == 1:
        # 单文件直接处理，避免多进程开销
        hisfix_file, his_file = matching_files[0]
        if merge_excel_files(hisfix_file, his_file, backup, "single_proc", session_timestamp):
            success_count = 1
        else:
            failed_files.append(hisfix_file.name)
    else:
        # 多文件使用多进程处理
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(merge_single_file, file_pair): file_pair[0].name 
                             for file_pair in file_pairs}
            
            # 处理完成的任务
            for future in tqdm(as_completed(future_to_file), total=len(file_pairs), desc="合并文件"):
                file_name = future_to_file[future]
                try:
                    result_file_name, success, error_msg = future.result()
                    if success:
                        success_count += 1
                        logger.info(f"合并成功: {result_file_name}")
                    else:
                        failed_files.append(result_file_name)
                        if error_msg:
                            logger.error(f"合并失败: {result_file_name}, 错误: {error_msg}")
                        else:
                            logger.error(f"合并失败: {result_file_name}")
                except Exception as e:
                    failed_files.append(file_name)
                    logger.error(f"处理文件时发生异常: {file_name}, 错误: {str(e)}")
    
    # 输出最终结果
    logger.info(f"合并完成: {success_count}/{len(matching_files)} 个文件成功")
    
    if failed_files:
        logger.error(f"失败的文件: {', '.join(failed_files)}")
    else:
        logger.info("所有文件合并成功！")
    
    # 合并所有变更日志
    merge_all_change_logs()

@app.command()
def list_files(
    hisfix_folder: str = typer.Option("hisfix", "--hisfix", "-f", help="修复数据文件夹路径"),
    his_folder: str = typer.Option("his", "--his", "-h", help="原始数据文件夹路径")
) -> None:
    """列出可以合并的文件"""
    
    hisfix_path = Path(hisfix_folder)
    his_path = Path(his_folder)
    
    matching_files = get_matching_files(hisfix_path, his_path)
    
    if not matching_files:
        logger.info("未找到任何匹配的文件对")
        return
    
    logger.info("可以合并的文件对:")
    for hisfix_file, his_file in matching_files:
        logger.info(f"  {hisfix_file.name} -> {his_file.name}")

if __name__ == "__main__":
    app()