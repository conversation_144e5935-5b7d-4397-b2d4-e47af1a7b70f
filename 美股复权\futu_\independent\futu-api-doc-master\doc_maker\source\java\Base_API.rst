.. note::

  Futu OpenAPI 文档已于2020年10月16日全新升级，请移步至 `新文档 <https://openapi.futunn.com/futu-api-doc/>`_ 

  旧的github文档将不再更新，并于2020年11月16日正式停止访问。


.. role:: strike
    :class: strike
.. role:: red-strengthen
    :class: red-strengthen

========
基础API
========

  .. _getGlobalState: ../protocol/base_define.html#getglobalstate-proto-1002
  .. _sub: ../protocol/quote_protocol.html#qot-sub-proto-3001
  .. _regQotPush: ../protocol/quote_protocol.html#qot-regqotpush-proto-3002
  .. _getSubInfo: ../protocol/quote_protocol.html#qot-getsubinfo-proto-3003
  .. _getTicker: ../protocol/quote_protocol.html#qot-getticker-proto-3010
  .. _getBasicQot: ../protocol/quote_protocol.html#qot-getbasicqot-proto-3004
  .. _getOrderBook: ../protocol/quote_protocol.html#qot-getorderbook-proto-3012
  .. _getKL: ../protocol/quote_protocol.html#qot-getkl-proto-3006k
  .. _getRT: ../protocol/quote_protocol.html#qot-getrt-proto-3008
  .. _getBroker: ../protocol/quote_protocol.html#qot-getbroker-proto-3014
  .. _getRehab: ../protocol/quote_protocol.html#qot-getrehab-proto-3102
  .. _requestRehab: ../protocol/quote_protocol.html#qot-requestrehab-proto-3105
  .. _requestHistoryKL: ../protocol/quote_protocol.html#qot-requesthistorykl-proto-3103k
  .. _requestHistoryKLQuota: ../protocol/quote_protocol.html#qot-requesthistoryklquota-proto-3104k
  .. _getTradeDate: ../protocol/quote_protocol.html#qot-gettradedate-proto-3200
  .. _getStaticInfo: ../protocol/quote_protocol.html#qot-getstaticinfo-proto-3202
  .. _getSecuritySnapshot: ../protocol/quote_protocol.html#qot-getsecuritysnapshot-proto-3203
  .. _getPlateSet: ../protocol/quote_protocol.html#qot-getplateset-proto-3204
  .. _getPlateSecurity: ../protocol/quote_protocol.html#qot-getplatesecurity-proto-3205
  .. _getReference: ../protocol/quote_protocol.html#qot-getreference-proto-3206
  .. _getOwnerPlate: ../protocol/quote_protocol.html#qot-getownerplate-proto-3207
  .. _getHoldingChangeList: ../protocol/quote_protocol.html#qot-getholdingchangelist-proto-3208
  .. _getOptionChain: ../protocol/quote_protocol.html#qot-getoptionchain-proto-3209
  .. _getWarrant: ../protocol/quote_protocol.html#qot-getwarrant-proto-3210
  .. _getCapitalFlow: ../protocol/quote_protocol.html#qot-getcapitalflow-proto-3211
  .. _getCapitalDistribution: ../protocol/quote_protocol.html#qot-getcapitaldistribution-proto-3212
  .. _getUserSecurity: ../protocol/quote_protocol.html#qot-getusersecurity-proto-3213
  .. _modifyUserSecurity: ../protocol/quote_protocol.html#qot-modifyusersecurity-proto-3214
  .. _notify: ../protocol/base_define.html#notify-proto-1003
  .. _updateBasicQot: ../protocol/quote_protocol.html#qot-updatebasicqot-proto-3005
  .. _updateKL: ../protocol/quote_protocol.html#qot-updatekl-proto-3007
  .. _updateRT: ../protocol/quote_protocol.html#qot-updatert-proto-3009
  .. _updateTicker: ../protocol/quote_protocol.html#qot-updateticker-proto-3011
  .. _updateOrderBook: ../protocol/quote_protocol.html#qot-updateorderbook-proto-3013
  .. _updateBroker: ../protocol/quote_protocol.html#qot-updatebroker-proto-3015
  .. _updateOrderDetail: ../protocol/quote_protocol.html#qot-updateorderdetail-proto-3017
  .. _getAccList: ../protocol/trade_protocol.html#trd-getacclist-proto-2001
  .. _unlockTrade: ../protocol/trade_protocol.html#trd-unlocktrade-proto-2005
  .. _subAccPush: ../protocol/trade_protocol.html#trd-subaccpush-proto-2008
  .. _getFunds: ../protocol/trade_protocol.html#trd-getfunds-proto-2101
  .. _getPositionList: ../protocol/trade_protocol.html#trd-getpositionlist-proto-2102
  .. _getMaxTrdQtys: ../protocol/trade_protocol.html#trd-getmaxtrdqtys-proto-2111
  .. _getOrderList: ../protocol/trade_protocol.html#trd-getorderlist-proto-2201
  .. _getOrderFillList: ../protocol/trade_protocol.html#trd-getorderfilllist-proto-2211
  .. _getHistoryOrderList: ../protocol/trade_protocol.html#trd-gethistoryorderlist-proto-2221
  .. _getHistoryOrderFillList: ../protocol/trade_protocol.html#trd-gethistoryorderfilllist-proto-2222
  .. _updateOrder: ../protocol/trade_protocol.html#trd-updateorder-proto-2208
  .. _updateOrderFill: ../protocol/trade_protocol.html#trd-updateorderfill-proto-2218
  .. _rsa: ../intro/FutuOpenDGuide.html#rsa


枚举常量
---------

ConnectFailType - 连接错误码
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

连接错误码

..  cpp:enum:: ConnectFailType

 ..  attribute:: UNKNOWN
 
  未知错误
  
 ..  attribute:: NONE
 
  没有错误
  
 ..  attribute:: CREATEFAILED
 
  socket创建失败

 ..  attribute:: CLOSEFAILED

  socket close错误

 ..  attribute:: SHUTDOWNFAILED

 socket shutdown错误

 ..  attribute:: GETHOSTBYNAMEFAILED

 gethostbyname错误

 ..  attribute:: GETHOSTBYNAMEWRONG

 gethostbyname调用成功，但返回的结果错误

 ..  attribute:: CONNECTFAILED

 连接失败

 ..  attribute:: BINDFAILED

 socket bind失败

 ..  attribute:: LISTENFAILED

 socket listen失败

 ..  attribute:: SELECTRETURNERROR

 socket select错误

 ..  attribute:: SENDFAILED

 socket send失败

 ..  attribute:: RECVFAILED

 socket recv失败
  
--------------------------------------

InitFailType - 初始化连接协议失败
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

初始化连接协议失败，即InitConnect协议相关的错误

..  cpp:enum:: InitFailType

 ..  attribute:: UNKNOWN

 未知错误

 ..  attribute:: TIMEOUT

 超时

 ..  attribute:: DISCONNECT

 连接断开

 ..  attribute:: SERIANONOTMATCH

 序列号不符

 ..  attribute:: SENDINITREQFAILED

 发送初始化协议失败

 ..  attribute:: OPENDREJECT

 FutuOpenD回包指定错误，具体错误看描述

--------------------------------------


主要函数列表
---------------

FTAPI - API全局功能。
--------------------------------------

..  class:: FTAPI

API全局配置类，初始化和全局配置类。

-------------------------------------------------------------------------------------------------

init
~~~~~~~~~~~~~~~~~

..  method:: static void init()

  初始化底层通道，程序启动时首先调用

  :return: void

--------------------------------------------

unInit
~~~~~~~~~~~~~~~~~

..  method:: static void unInit()

  清理底层通道，程序结束时调用

  :return: void

--------------------------------------------


FTAPI_Conn连接层基类
-----------------------

..  class:: FTAPI

API功能基类，提供连接方面公用的功能。FTAPI_Qot（行情）和FTAPI_Trd（交易）都继承该类。

-------------------------------------------------------------------------------------------------

setConnSpi
~~~~~~~~~~~~~~~~~

..  method:: void setConnSpi(FTSPI_Conn callback)

  设置连接相关回调。

  :param callback: 参加下面 `FTSPI_Conn` 的说明
  :return: void

--------------------------------------------

initConnect
~~~~~~~~~~~~~~~~~

..  method:: boolean initConnect(String ip, short port, boolean isEnableEncrypt)

  初始化连接信息。

  :param ip: 连接地址
  :param port: 连接端口号
  :param isEnableEncrypt: 是否允许加密
  :return: bool 初始化失败返回false，其他错误依据callback返回

--------------------------------------------

setRSAPrivateKey
~~~~~~~~~~~~~~~~~

..  method:: void setRSAPrivateKey(String key)

  设置密钥。

  :param key: 加密密钥。格式见 rsa_
  :return: void

--------------------------------------------

getConnectID
~~~~~~~~~~~~~~~~~

..  method:: long getConnectID()

  获取此连接的连接ID，连接的唯一标识，InitConnect协议返回，没有初始化前为0

  :return: long

--------------------------------------------

close
~~~~~~~~~~~~~~~~~

..  method:: void close()

  释放内存。当对象不再使用时调用，否则会有内存泄漏。

  :return: void

--------------------------------------------

FTSPI_Conn - 连接状态回调接口
------------------------------------------

..  class:: interface FTSPI_Conn

当与OpenD的连接状态变化时调用此接口。

------------------------------------


onInitConnect
~~~~~~~~~~~~~~~~~

..  method:: void onInitConnect(FTAPI client, long errCode, String desc)

  初始化连接状态变化。

  :param client: 对应的FTAPI实例
  :param errCode: 错误码。0表示成功，可以进行后续请求。当高32位为在`ConnectFailType` 范围内时，低32位为系统错误码；当高32位等于FTAPI_Conn.INIT_FAIL，则低32位为 `InitFailType` 类型。
  :param desc: 错误描述
  :return: void

--------------------------------------------

onDisConnect
~~~~~~~~~~~~~~~~~

..  method:: void onDisconnect(FTAPI client, long errCode)

  初始化连接状态变化。

  :param client: 对应的FTAPI实例
  :param errCode: 错误码。高32位为 `ConnectFailType` 类型，低32位为系统错误码；
  :return: void

