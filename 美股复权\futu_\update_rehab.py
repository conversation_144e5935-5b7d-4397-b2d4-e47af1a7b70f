from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List, Dict
import typer
import time
import pandas as pd
from collections import deque
from loguru import logger
import threading
import queue
from vnpy.trader.utility import ZoneInfo
NY_TZ = ZoneInfo("America/New_York")
from futu import (
    OpenQuoteContext,
    RET_OK
)
from vnpy.trader.utility import load_json
from peewee import chunked

# 添加项目根目录到 Python 路径
import os, sys
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, FutuRehab, FutuProduct, IbProduct
from utils.wecom_alert import WecomAlertManager
from utils.mysql_database import create_mysql_database
from utils.mixin import NA_VALUES
import traceback
import json
import numpy as np
import pandas_market_calendars as mcal

# 配置loguru
logger.add("futu_rehab_update.log", rotation="10 MB", level="TRACE", format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")

# 加载富途连接配置
connect_futu = load_json("connect_futu.json")

# 创建CLI应用
app = typer.Typer()

class FutuDataManager:
    """富途数据管理器"""
    def __init__(self):
        """初始化"""
        self.quote_ctx = OpenQuoteContext(
            host=connect_futu.get("host", "127.0.0.1"),
            port=connect_futu.get("port", 11111)
        )
        self.rehab_records = []  # 用于批量存储复权数据
        self.max_retries = 3  # 最大重试次数
        
        # 创建消息管理器
        self.wecom_manager = WecomAlertManager()
        self.wecom_manager.start()
        self.wecom_manager2 = WecomAlertManager()
        self.wecom_manager2.key = "b54436b7-2384-4b54-863f-caebc34309a4" #TEST

        self.wecom_manager2.start()
        # 创建IB产品缓存
        self.symbol_conid_map = self._load_ib_products()

        # 定义会导致股数变动的字段
        self.share_change_fields = [
            'split_ratio',
            'per_share_div_ratio',
            'per_share_trans_ratio',
            'allotment_ratio',
            'stk_spo_ratio'
        ]
        self.new_report_main_no_share_change = True #如果需要在大群中报告新增不影响股数的复权记录，则为True

        # 数据库记录缓存
        self.db_rehab_data = {}  # {code: [records]}

        # 加载已删除代码的记录
        self.deleted_codes_dict = self._load_deleted_codes()

        # 加载US股票数据库连接配置
        self.us_stock_settings = load_json("vt_setting_us_stock.json")

        # 初始化US股票数据库管理器
        self.us_db_manager = None
        self._init_us_database()

        # 初始化NYSE交易日历
        self.nyse_trading_days = self._init_nyse_calendar()

        # 是否报告获取不到前收盘价的情况
        self.report_no_prev = False

        self.cutoff_date = datetime(2021, 1, 1)

    def _load_deleted_codes(self) -> dict:
        """加载deleted_codes.json文件"""
        try:
            if os.path.exists("deleted_codes.json"):
                with open("deleted_codes.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    # 将list转换回set
                    for date_str in data:
                        data[date_str] = set(data[date_str])
                    return data
            else:
                logger.info("deleted_codes.json文件不存在，将创建新文件")
                return {}
        except Exception as e:
            logger.error(f"加载deleted_codes.json失败: {str(e)}")
            return {}

    def _init_nyse_calendar(self) -> list:
        """初始化NYSE交易日历（2020年至今）"""
        try:
            # 获取NYSE交易日历
            nyse = mcal.get_calendar('NYSE')

            # 获取2020年1月1日至今的所有交易日
            start_date = '2020-01-01'
            end_date = pd.Timestamp.now().strftime('%Y-%m-%d')

            # 获取交易日历
            schedule = nyse.schedule(start_date=start_date, end_date=end_date)

            # 提取交易日期并排序（已经是按时间排序的）
            trading_days = sorted(schedule.index.date)

            logger.info(f"已加载NYSE交易日历，从{start_date}到{end_date}，共{len(trading_days)}个交易日")
            return trading_days

        except Exception as e:
            logger.error(f"初始化NYSE交易日历失败: {str(e)}")
            return []

    def _init_us_database(self) -> None:
        """初始化US股票数据库连接"""
        try:
            # 使用create_mysql_database创建数据库连接
            self.us_db_manager, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(self.us_stock_settings)
            logger.info("US股票数据库连接初始化成功")
        except Exception as e:
            logger.error(f"US股票数据库连接初始化失败: {str(e)}")
            self.us_db_manager = None

    def _save_deleted_codes(self) -> None:
        """保存deleted_codes到JSON文件"""
        try:
            # 将set转换为list以便JSON序列化
            data_to_save = {}
            for date_str, code_set in self.deleted_codes_dict.items():
                data_to_save[date_str] = list(code_set)

            with open("deleted_codes.json", "w", encoding="utf-8") as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存deleted_codes.json")
        except Exception as e:
            logger.error(f"保存deleted_codes.json失败: {str(e)}")

    def _record_deleted_code(self, code: str) -> None:
        """记录今天删除的code"""
        today = datetime.now().strftime("%Y-%m-%d")

        if today not in self.deleted_codes_dict:
            self.deleted_codes_dict[today] = set()

        self.deleted_codes_dict[today].add(code)
        logger.info(f"已记录删除的code: {code} (日期: {today})")

        # 立即保存到文件
        self._save_deleted_codes()

    def get_db_data(self) -> None:
        """一次性获取所有数据库复权记录并缓存"""
        try:
            logger.info("正在加载数据库复权记录...")
            # 获取所有复权记录
            all_records = FutuRehab.select()

            # 按code分组存储
            for record in all_records:
                if record.code not in self.db_rehab_data:
                    self.db_rehab_data[record.code] = []
                self.db_rehab_data[record.code].append(record)

            # 对每个code的记录按除权日期排序
            for code in self.db_rehab_data:
                self.db_rehab_data[code].sort(key=lambda x: x.ex_div_date, reverse=True)

            logger.info(f"已加载 {len(self.db_rehab_data)} 个股票的复权记录到缓存")

        except Exception as e:
            logger.error(f"加载数据库复权记录失败: {str(e)}\n{traceback.format_exc()}")
            self.db_rehab_data = {}

    def _load_ib_products(self) -> Dict[str, int]:
        """加载IB产品信息，建立symbol到conid的映射"""
        symbol_conid_map = {}
        try:
            query = IbProduct.select(IbProduct.symbol, IbProduct.conid).where(
                IbProduct.is_latest == True
            ).order_by(IbProduct.created_time.asc())
            for record in query:
                if record.symbol and record.conid:
                    symbol_conid_map[record.symbol] = record.conid
            logger.info(f"已加载{len(symbol_conid_map)}个IB产品映射关系")
        except Exception as e:
            logger.error(f"加载IB产品信息时出错: {str(e)}\n{traceback.format_exc()}")
        return symbol_conid_map
        

    def reset_rehab_data(self, code: str) -> None:
        """在数据库中删除需要删除的复权数据

        Args:
            code: 股票代码
        """
        try:
            with db_manager.common_db.atomic():
                # 删除该股票的所有记录
                deleted_count = FutuRehab.delete().where(FutuRehab.code == code).execute()
                logger.info(f"已删除股票 {code} 的 {deleted_count} 条历史记录")

                # 记录到deleted_codes.json
                self._record_deleted_code(code)

        except Exception as e:
            logger.error(f"删除股票 {code} 的复权数据时出错: {str(e)}\n{traceback.format_exc()}")



    def _get_prev_close(self, code: str, ex_div_date: datetime) -> float:
        """获取除权日前一日的收盘价"""
        try:
            # 2021年1月1日之前的数据不处理
            if ex_div_date < self.cutoff_date:
                logger.debug(f"{code} 除权日{ex_div_date.strftime('%Y-%m-%d')}早于2021-01-01，跳过处理")
                return None

            if not self.us_db_manager:
                logger.warning("US股票数据库未初始化")
                if self.report_no_prev:
                    self._report_no_prev_close(code, ex_div_date, "US股票数据库未初始化")
                return None

            # 获取对应的conid
            conid = self._get_ib_conid(code)
            if not conid:
                logger.warning(f"未找到{code}对应的conid")
                if self.report_no_prev:
                    self._report_no_prev_close(code, ex_div_date, "未找到对应的conid")
                return None

            # 使用ORM直接查询最近的交易日收盘价
            from vnpy.trader.constant import Exchange, Interval
            bar_record = (self.DbBarData.select()
                         .where(
                             (self.DbBarData.symbol == str(conid)) &
                             (self.DbBarData.exchange == Exchange.SMART.value) &
                             (self.DbBarData.interval == Interval.DAILY.value) &
                             (self.DbBarData.datetime < ex_div_date)
                         )
                         .order_by(self.DbBarData.datetime.desc())
                         .limit(1)
                         .first())

            if bar_record:
                # 简化的交易日验证
                ex_div_date_obj = ex_div_date.date()
                actual_date = bar_record.datetime.date()

                try:
                    # 获取除权日的前一个交易日
                    if ex_div_date_obj in self.nyse_trading_days:
                        expected_trading_day = self.nyse_trading_days[self.nyse_trading_days.index(ex_div_date_obj) - 1]
                    else:
                        # 如果除权日不是交易日，找到第一个大于除权日的交易日，然后取前一个
                        for i, trading_day in enumerate(self.nyse_trading_days):
                            if trading_day > ex_div_date_obj and i > 0:
                                expected_trading_day = self.nyse_trading_days[i - 1]
                                break
                        else:
                            expected_trading_day = None

                    if expected_trading_day and actual_date != expected_trading_day:
                        # 日期不匹配，发送企业微信报告
                        warning_msg = (
                            f"交易日期不匹配警告\n"
                            f"富途代码: {code} (conid: {conid})\n"
                            f"除权日: {ex_div_date.strftime('%Y-%m-%d')}\n"
                            f"期望的前一交易日: {expected_trading_day.strftime('%Y-%m-%d')}\n"
                            f"实际获取的日期: {actual_date.strftime('%Y-%m-%d')}\n"
                            f"实际获取的收盘价: {bar_record.close_price}"
                        )

                        logger.warning(warning_msg.replace('\n', ' | '))
                        self.wecom_manager2.add_message(warning_msg)

                except (ValueError, IndexError) as e:
                    logger.warning(f"交易日验证失败 {code}: {e}")

                logger.debug(f"获取到{code}({conid})在{bar_record.datetime}的收盘价: {bar_record.close_price}")
                return bar_record.close_price
            else:
                logger.debug(f"未找到{code}({conid})在除权日{ex_div_date}前的日线数据")
                if self.report_no_prev:
                    self._report_no_prev_close(code, ex_div_date, f"未找到日线数据 (conid: {conid})")
                return None

        except Exception as e:
            logger.error(f"获取{code}前一日收盘价失败: {str(e)}")
            if self.report_no_prev:
                self._report_no_prev_close(code, ex_div_date, f"异常: {str(e)}")
            return None

    def _check_and_update_missing_prev_close(self, code: str, existing_records: list) -> None:
        """检查和更新existing_records中缺失的prev_close"""
        try:
            records_need_update = []

            # 找出需要更新prev_close的记录
            for record in existing_records:
                if (record.prev_close is None and
                    record.ex_div_date >= self.cutoff_date and
                    record.ex_div_date <= datetime.now()-timedelta(days=1,hours=14,minutes=30)):
                    records_need_update.append(record)

            if not records_need_update:
                return

            logger.info(f"检查到 {code} 有 {len(records_need_update)} 条记录的前收盘价为空，开始更新")

            # 批量更新
            updates = []
            for record in records_need_update:
                prev_close = self._get_prev_close(code, record.ex_div_date)
                if prev_close is not None:
                    updates.append({
                        'id': record.id,
                        'prev_close': prev_close
                    })
                    # 同时更新内存中的记录，避免后续重复查询
                    record.prev_close = prev_close

                # 控制频率
                time.sleep(0.05)

            # 批量更新数据库
            if updates:
                with db_manager.common_db.atomic():
                    for update_data in updates:
                        FutuRehab.update(prev_close=update_data['prev_close']).where(
                            FutuRehab.id == update_data['id']
                        ).execute()

                logger.info(f"已更新 {code} 的 {len(updates)} 条前收盘价记录")

                # 前收盘价更新成功后，重新计算并更新所有复权因子
                self._recalculate_and_update_factors(code)

        except Exception as e:
            logger.error(f"更新 {code} 前收盘价失败: {str(e)}")

    def _recalculate_and_update_factors(self, code: str) -> None:
        """重新计算并更新指定股票的所有复权因子"""
        try:
            logger.info(f"开始重新计算 {code} 的复权因子...")

            # 1. 从数据库获取该股票的所有复权记录
            query = FutuRehab.select().where(FutuRehab.code == code).order_by(FutuRehab.ex_div_date)

            if not query.exists():
                logger.warning(f"{code} 没有复权记录需要重新计算")
                return

            # 2. 高效转换为DataFrame
            df = pd.DataFrame(list(query.dicts()))
            logger.debug(f"获取到 {len(df)} 条 {code} 的复权记录")

            # 3. 调用_calculate_factors重新计算复权因子
            df_with_factors = self._calculate_factors(df)

            # 4. 批量更新数据库（逐条更新但在同一事务中）
            updated_count = 0
            with db_manager.common_db.atomic():
                for _, row in df_with_factors.iterrows():
                    result = FutuRehab.update(
                        share_factor=float(row.get('share_factor', 1.0)),
                        div_factor=float(row.get('div_factor', 1.0)),
                        combined_factor=float(row.get('combined_factor', 1.0)),
                        share_forward_cumprod=float(row.get('share_forward_cumprod', 1.0)),
                        combined_forward_cumprod=float(row.get('combined_forward_cumprod', 1.0)),
                        update_date=datetime.now()
                    ).where(FutuRehab.id == int(row['id'])).execute()

                    if result > 0:  # 如果更新成功
                        updated_count += 1

            logger.info(f"成功重新计算并更新 {code} 的 {updated_count} 条复权因子记录")

        except Exception as e:
            logger.error(f"重新计算 {code} 复权因子失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _get_prev_close_from_existing_or_db(self, code: str, ex_div_date: pd.Timestamp, existing_records_dict: dict) -> float:
        """从existing_records或数据库获取前收盘价"""
        ex_div_date_key = ex_div_date.date()

        # 优先从existing_records_dict中获取prev_close
        if ex_div_date_key in existing_records_dict:
            existing_record = existing_records_dict[ex_div_date_key]
            if existing_record.prev_close is not None:
                logger.debug(f"从数据库记录获取{code}的前收盘价: {existing_record.prev_close}")
                return existing_record.prev_close

        # 如果从数据库记录中没有获取到或者为None，则查询数据库
        return self._get_prev_close(code, ex_div_date.to_pydatetime())

    def _calculate_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算复权因子"""
        try:
            # 初始化默认值
            data['share_factor'] = 1.0
            data['div_factor'] = 1.0
            data['combined_factor'] = 1.0

            # 1. 计算share_factor
            # 处理split_ratio
            mask_split = data['split_ratio'].notna()
            data.loc[mask_split, 'share_factor'] *= data.loc[mask_split, 'split_ratio']

            # 处理per_share_div_ratio（送股比例）
            mask_div_ratio = data['per_share_div_ratio'].notna()
            per_share_div_ratio = data.loc[mask_div_ratio, 'per_share_div_ratio']
            data.loc[mask_div_ratio, 'share_factor'] *= per_share_div_ratio / (per_share_div_ratio + 1.0)

            # 2. 计算div_factor
            # 计算总股息金额
            dividend_amount = pd.Series(0.0, index=data.index)

            # 每股派现
            mask_cash_div = data['per_cash_div'].notna()
            dividend_amount[mask_cash_div] += data.loc[mask_cash_div, 'per_cash_div']

            # 特别股息
            mask_special_div = data['special_dividend'].notna()
            dividend_amount[mask_special_div] += data.loc[mask_special_div, 'special_dividend']

            # 计算股息调整因子
            mask_prev_close = (data['prev_close'].notna()) & (data['prev_close'] > 0) & (dividend_amount > 0)
            div_factor = (data.loc[mask_prev_close, 'prev_close'] - dividend_amount[mask_prev_close]) / data.loc[mask_prev_close, 'prev_close']

            # 确保因子为正数且合理
            # div_factor = div_factor.clip(lower=0.001, upper=1.0)
            data.loc[mask_prev_close, 'div_factor'] = div_factor

            # 3. 计算总调整因子
            data['combined_factor'] = data['share_factor'] * data['div_factor']

            data_sorted = data.sort_values('ex_div_date', ascending=False).copy()

            # 4. 计算累积因子（使用numpy操作）
            share_factors = data_sorted['share_factor'].fillna(1.0).values
            combined_factors = data_sorted['combined_factor'].fillna(1.0).values

            # 计算累积乘积（从当前位置开始的所有后续因子的乘积）
            data_sorted['share_forward_cumprod'] = np.cumprod(share_factors)
            data_sorted['combined_forward_cumprod'] = np.cumprod(combined_factors)

            # 5. 恢复原始顺序
            data = data_sorted.sort_index()

            logger.debug(f"计算复权因子完成，处理{len(data)}条记录")
            return data

        except Exception as e:
            logger.error(f"计算复权因子失败: {e}")
            # 如果批量计算失败，设置默认值
            data['share_factor'] = 1.0
            data['div_factor'] = 1.0
            data['combined_factor'] = 1.0
            data['share_forward_cumprod'] = 1.0
            data['combined_forward_cumprod'] = 1.0
            return data

    def _report_no_prev_close(self, code: str, ex_div_date: datetime, reason: str) -> None:
        """报告获取不到前收盘价的情况"""
        try:
            conid = self._get_ib_conid(code)
            warning_msg = (
                f"无法获取前收盘价\n"
                f"富途代码: {code}" + (f" (conid: {conid})" if conid else "") + "\n"
                f"除权日: {ex_div_date.strftime('%Y-%m-%d')}\n"
                f"原因: {reason}"
            )

            logger.info(f"报告无前收盘价: {code} {ex_div_date.strftime('%Y-%m-%d')} - {reason}")
            self.wecom_manager2.add_message(warning_msg)

        except Exception as e:
            logger.error(f"发送无前收盘价报告失败: {e}")

    def _get_ib_conid(self, futu_code: str) -> int:
        """根据富途代码获取IB的conid"""
        try:
            # 移除"US."前缀，将点替换为空格
            symbol = futu_code.replace("US.", "").replace(".", " ")
            return self.symbol_conid_map.get(symbol)
        except Exception:
            return None



    def __del__(self):
        """析构函数"""
        if hasattr(self, 'quote_ctx'):
            self.quote_ctx.close()
        if hasattr(self, 'wecom_manager'):
            self.wecom_manager.stop()
        if hasattr(self, 'wecom_manager2'):
            self.wecom_manager2.stop()
        if hasattr(self, 'us_db_manager') and self.us_db_manager:
            try:
                self.us_db_manager.close()
            except:
                pass

    def get_product_key(self, product_data: dict) -> tuple:
        """获取产品数据的唯一键"""
        return (
            product_data["code"],
            product_data["stock_name"],
            product_data["lot_size"],
            product_data["stock_owner"],
            product_data["stock_child_type"],
            product_data["stock_type"],
            product_data["list_time"],
            product_data["stock_id"],
            product_data["main_contract"],
        )

    def get_all_us_stocks(self) -> List[str]:
        """获取并保存所有美股标的,返回股票代码列表"""
        ret, data = self.quote_ctx.get_plate_stock(
            "US.USAALL",  # 使用全部美股板块代码
        )

        if ret != RET_OK:
            logger.error(f"获取美股列表失败: {data}")
            return []

        # 处理日期列
        data['list_time'] = pd.to_datetime(data['list_time'])
        data['last_trade_time'] = pd.to_datetime(data.get('last_trade_time'))

        # 处理其他字段
        data['stock_id'] = data['stock_id'].astype(str)
        data['main_contract'] = data.get('main_contract', False)
        data['created_time'] = datetime.now()  # 添加创建时间

        # 按list_time和code排序
        data = data.sort_values(['list_time', 'code'])

        # 获取现有记录的键集合
        existing_keys = set()
        for record in FutuProduct.select():
            # 构建与新数据相同格式的字典
            record_dict = {
                "code": record.code,
                "stock_name": record.stock_name,
                "lot_size": record.lot_size,
                "stock_owner": record.stock_owner,
                "stock_child_type": record.stock_child_type,
                "stock_type": record.stock_type,
                "list_time": record.list_time,
                "stock_id": record.stock_id,
                "main_contract": record.main_contract,
            }
            # 将NA值转换为None而不是过滤掉
            record_dict = {k: None if pd.isna(v) or v == '' else v for k, v in record_dict.items()}
            key = self.get_product_key(record_dict)
            existing_keys.add(key)

        # 处理新数据
        rows_to_save = []
        for _, row in data.iterrows():
            # 转换为字典并将NA值转换为None
            product_data = {k: None if pd.isna(v) or v == '' else v for k, v in row.items()}
            key = self.get_product_key(product_data)
            if key not in existing_keys:
                rows_to_save.append(product_data)

        # 保存非重复数据
        if rows_to_save:
            with db_manager.common_db.atomic():
                for batch in chunked(rows_to_save, 50):
                    FutuProduct.insert_many(batch).on_conflict_replace().execute()
        else:
            logger.info("没有发现新记录或更新")

        # 将所有获取到的股票的rehab_known字段更新为True
        # 这样可以确保每次获取股票列表时，重置之前可能被标记为未知的股票
        with db_manager.common_db.atomic():
            codes_list = data['code'].tolist()
            # 批量更新以减少数据库交互次数
            for batch_codes in chunked(codes_list, 100):
                FutuProduct.update(rehab_known=True).where(FutuProduct.code.in_(batch_codes)).execute()

        return data['code'].tolist()

    def collect_rehab_data(self, code: str, force_update: bool = False):
        """收集单个股票的复权因子数据"""

        # 获取复权因子
        ret, data = self.quote_ctx.get_rehab(code)
        if ret != RET_OK:
            error_msg = str(data)
            logger.error(f"获取{code}复权因子失败: {error_msg}")
            # 检查是否是未知股票错误
            if "未知股票" in error_msg:
                # 更新数据库中的rehab_known字段为False
                try:
                    with db_manager.common_db.atomic():
                        FutuProduct.update(rehab_known=False).where(FutuProduct.code == code).execute()
                except Exception as e:
                    logger.error(f"更新{code}的rehab_known字段失败: {e}")
            # 检查是否是频率限制错误
            if "频率太高" in error_msg or "频率限制" in error_msg:
                return "RATE_LIMIT"
            return False

        # 获取对应的IB conid（一次性获取，避免重复查询）
        conid = self._get_ib_conid(code)
        current_date = (datetime.now(NY_TZ)-timedelta(days=1)).date()

        # 如果不是强制更新,从缓存中获取已存在的最新日期
        latest_date = None
        existing_records = self.db_rehab_data.get(code, [])
        if not force_update and existing_records:
            latest_date = existing_records[0]  # 已按日期倒序排列，第一个就是最新的

        reset_data = False

        # 检查和更新existing_records中缺失的prev_close
        if existing_records:
            self._check_and_update_missing_prev_close(code, existing_records)
            for rec in existing_records:
                if rec.share_factor == 1 and rec.div_factor == 1 and rec.combined_factor == 1 and rec.combined_forward_cumprod == 1 and rec.ex_div_date > self.cutoff_date:
                    reset_data = True
                    logger.info(f"检测到有未计算的复权因子: {code} {rec.ex_div_date.strftime('%Y-%m-%d')}")
                    break
                if rec.share_factor == 1 and (rec.split_ratio is not None or rec.per_share_div_ratio is not None):
                    reset_data = True
                    logger.info(f"检测到有未正确计算的复权因子: {code} {rec.ex_div_date.strftime('%Y-%m-%d')}")
                    break
        # 在过滤数据之前，检查历史复权记录的变化
        if not data.empty:
            # 确保日期列为datetime类型后再进行比较
            data['ex_div_date'] = pd.to_datetime(data['ex_div_date'])

            # 将数据库记录转换为字典，以ex_div_date为键
            existing_records_dict = {}
            for record in existing_records:
                existing_records_dict[record.ex_div_date.date()] = record

            # 获取data中的所有ex_div_date
            data_dates = set()
            for _, row in data.iterrows():
                ex_div_date = row['ex_div_date'].date()  # 已经是datetime类型，直接转换为date
                data_dates.add(ex_div_date)

            # 获取数据库中的所有历史记录日期
            db_dates = set()
            for date_key in existing_records_dict.keys():
                db_dates.add(date_key)

            msg_parts1 = [f"富途代码: {code}" + (f" (conid: {conid})\n" if conid else "\n")]
            msg_parts2 = [f"富途代码: {code}" + (f" (conid: {conid})\n" if conid else "\n")]
            report = False
            report_no_share_change = False
            # 检查新增和修改
            for _, row in data.iterrows():
                ex_div_date = row['ex_div_date'].date()  # 已经是datetime类型，直接转换为date

                # 只检查历史记录（除权日期早于比较基准日期）

                existing_record = existing_records_dict.get(ex_div_date)
                if ex_div_date > current_date:
                    mark = '✨未来'
                else:
                    mark = '历史'

                if not existing_record:
                    # 检查新增记录是否包含股数变动字段
                    has_share_change = False
                    has_no_share_change = False
                    share_change_info = []
                    no_share_change_info = []
                    # 收集所有非空的股数变动字段
                    for field in data.columns:
                        if field == 'ex_div_date':
                            continue
                        if field in row and pd.notna(row[field]) and row[field] is not None:
                            reset_data = True

                            if field in self.share_change_fields:
                                has_share_change = True
                                share_change_info.append(f"{field}: {row[field]}")
                            else:
                                has_no_share_change = True
                                no_share_change_info.append(f"{field}: {row[field]}")

                    if has_share_change:
                        # 新增复权记录
                        msg_parts1.append(f"新增{mark}复权记录")
                        msg_parts1.append(f"除权除息日: {ex_div_date.strftime('%Y-%m-%d')}")
                        msg_parts1.extend(share_change_info)

                        logger.info(f"检测到新增{mark}复权记录(股数变动): {code} {ex_div_date.strftime('%Y-%m-%d')}")
                        report = True
                    else:
                        if has_no_share_change:
                            # 新增复权记录
                            if self.new_report_main_no_share_change and mark =='✨未来':
                                msg_parts1.append(f"新增{mark}不影响股数复权记录")
                                msg_parts1.append(f"除权除息日: {ex_div_date.strftime('%Y-%m-%d')}")
                                msg_parts1.extend(no_share_change_info)

                                logger.info(f"检测到新增{mark}复权记录(股数不变动): {code} {ex_div_date.strftime('%Y-%m-%d')}")
                                report = True
                            else:
                                msg_parts2.append(f"新增{mark}不影响股数复权记录")
                                msg_parts2.append(f"除权除息日: {ex_div_date.strftime('%Y-%m-%d')}")
                                msg_parts2.extend(no_share_change_info)

                                logger.info(f"检测到新增{mark}复权记录(股数不变动): {code} {ex_div_date.strftime('%Y-%m-%d')}")
                                report_no_share_change = True
                else:
                    # 检查修改的历史复权记录 - 只检查股数变动字段
                    share_changes = []
                    no_share_changes = []
                    # 检查股数变动字段
                    meaningful_change = True
                    prev_close = getattr(existing_record, 'prev_close', None)
                    if prev_close is not None:
                        old_a = existing_record.forward_adj_factorA
                        old_b = existing_record.forward_adj_factorB
                        new_a = row['forward_adj_factorA']
                        new_b = row['forward_adj_factorB']
                        old_adj_price = prev_close * old_a + old_b
                        new_adj_price = prev_close * new_a + new_b
                        if abs(new_adj_price/old_adj_price-1)<0.005:
                            meaningful_change = False

                    for field in data.columns:
                        if field == 'ex_div_date':
                            continue
                        if field in row and pd.notna(row[field]) and row[field] is not None:
                            old_value = getattr(existing_record, field, None)
                            new_value = float(row[field])
                            # 如果old_value为None，直接认为有变化
                            if old_value is None:
                                reset_data = True

                                if field in self.share_change_fields:
                                    share_changes.append(f"{field}: None → {new_value}")
                                else:
                                    no_share_changes.append(f"{field}: {old_value} → {new_value}")

                            else:
                                # old_value不为None，进行数值比较
                                old_value = float(old_value)
                                if new_value!=old_value:
                                    if field not in ['forward_adj_factorA', 'forward_adj_factorB']:
                                        meaningful_change = True
                                    if meaningful_change:
                                        reset_data = True
                                        if field in self.share_change_fields:
                                            share_changes.append(f"{field}: {old_value} → {new_value}")
                                        else:
                                            no_share_changes.append(f"{field}: {old_value} → {new_value}")
                                    else:
                                        logger.info(f" {code} {ex_div_date.strftime('%Y-%m-%d')} - forward_adj_factorAB无意义的更新")

                    # 只有股数变动字段变化时才报告
                    if share_changes:
                        msg_parts1.append(f"修改{mark}复权记录")
                        msg_parts1.append(f"除权除息日: {ex_div_date.strftime('%Y-%m-%d')}")
                        msg_parts1.extend(share_changes)
                        logger.info(f"检测到修改{mark}复权记录(股数变动): {code} {ex_div_date.strftime('%Y-%m-%d')} - {'; '.join(share_changes)}")
                        report = True
                    else:
                        if no_share_changes:
                            msg_parts2.append(f"修改{mark}不影响股数复权记录")
                            msg_parts2.append(f"除权除息日: {ex_div_date.strftime('%Y-%m-%d')}")
                            msg_parts2.extend(no_share_changes)
                            logger.info(f"检测到修改{mark}复权记录(股数不变动): {code} {ex_div_date.strftime('%Y-%m-%d')} - {'; '.join(no_share_changes)}")
                            report_no_share_change = True


            # 检查删除的历史复权记录（数据库中有但data中没有的）
            deleted_dates = db_dates - data_dates
            for deleted_date in sorted(deleted_dates):
                if deleted_date > current_date:
                    mark = '✨未来'
                else:
                    mark = '历史'
                deleted_record = existing_records_dict[deleted_date]

                # 检查被删除的记录是否包含股数变动字段
                has_share_change = False
                has_no_share_change = False
                share_change_info = []
                no_share_change_info = []

                # 收集所有非空的股数变动字段
                for field in data.columns:
                    if field == 'ex_div_date':
                        continue
                    field_value = getattr(deleted_record, field, None)
                    if field_value is not None:
                        reset_data = True
                        if field in self.share_change_fields:
                            has_share_change = True
                            share_change_info.append(f"{field}: {field_value}")
                        else:
                            has_no_share_change = True
                            no_share_change_info.append(f"{field}: {field_value}")

                if has_share_change:
                    msg_parts1.append(f"删除{mark}复权记录")
                    msg_parts1.append(f"除权除息日: {deleted_date.strftime('%Y-%m-%d')}")
                    msg_parts1.extend(share_change_info)

                    logger.info(f"检测到删除{mark}复权记录(股数变动): {code} {deleted_date.strftime('%Y-%m-%d')}")
                    report = True
                else:
                    if has_no_share_change:
                        msg_parts2.append(f"删除不影响股数{mark}复权记录")
                        msg_parts2.append(f"除权除息日: {deleted_date.strftime('%Y-%m-%d')}")
                        msg_parts2.extend(no_share_change_info)

                        logger.info(f"检测到删除{mark}复权记录(股数不变动): {code} {deleted_date.strftime('%Y-%m-%d')}")
                        report_no_share_change = True
            if report:
                message = "\n".join(msg_parts1)
                self.wecom_manager.add_message(message+"\n")
            if report_no_share_change:
                message = "\n".join(msg_parts2)
                self.wecom_manager2.add_message(message+"\n")

        else:
            logger.info(f"股票 {code} 没有复权记录")
            return True

        # 过滤新数据
        if not force_update and latest_date:
            # 只保留新日期的数据
            if not reset_data:
                logger.info(f"股票 {code} 没有新数据，也没有改变历史数据，无需入库")
                return True

        # 添加code列
        data['code'] = code
        # 日期列已在前面转换过了，无需重复转换

        # 1. 批量获取前收盘价
        data['prev_close'] = data['ex_div_date'].apply(
            lambda ex_div_date: self._get_prev_close_from_existing_or_db(code, ex_div_date, existing_records_dict)
        )

        # 2. 批量计算复权因子
        data = self._calculate_factors(data)

        # 3. 将DataFrame转换为字典列表
        records = data.to_dict('records')

        # 4. 处理NaN值
        for record in records:
            for k, v in record.items():
                if pd.isna(v) or v == '':
                    record[k] = None

        # 检查是否需要记录重置SQL（排除之前没有历史记录的情况）
        if reset_data:  # 如果数据库中存在该股票的记录
            if existing_records_dict:
                logger.info(f"检测到股票 {code} 历史复权记录有变化，正在重置数据库...")
                self.reset_rehab_data(code)

        self.rehab_records.extend(records)

        # 记录日志
        logger.info(f"成功获取{code}的复权因子数据: {len(records)}条")
        return True

    def save_rehab_batch(self) -> None:
        """批量保存复权因子数据"""
        if not self.rehab_records:

            return

        try:
            # 批量保存数据
            with db_manager.common_db.atomic():
                for batch in chunked(self.rehab_records, 50):
                    FutuRehab.insert_many(batch).on_conflict_replace().execute()
                    # 发送企业微信通知 - 股数变动事件
                    #for record in batch:
                        ## 过滤出非空字段
                        #non_empty_fields = {k: v for k, v in record.items() if v is not None}
                        
                        ## 获取对应的IB conid
                        #conid = self._get_ib_conid(record['code'])
                        
                        # 构建通知消息
                        #msg_parts = [
                            #f"富途代码: {record['code']}" + (f" (conid: {conid})" if conid else ""),
                            #f"除权除息日: {record['ex_div_date'].strftime('%Y-%m-%d')}"
                        #]
                        
                        ## 检查是否有导致股数变动的字段
                        #has_share_change_event = False
                        #for field in self.share_change_fields:
                            #if field in non_empty_fields:
                                #msg_parts.append(f"{field}: {non_empty_fields[field]}")
                                #has_share_change_event = True
                                
                        ## 只有当存在股数变动事件时才发送通知
                        #if has_share_change_event:
                            ## 将消息添加到队列
                            #message = "\n".join(msg_parts)
                            #self.wecom_manager.add_message(message)
                        
            logger.success(f"成功保存{len(self.rehab_records)}条复权因子数据")
        except Exception as e:
            logger.error(f"保存复权因子数据失败: {e},错误信息: \n{traceback.format_exc()}")
        finally:
            # 清空记录列表
            self.rehab_records = []

    def update_rehab_data(self, codes: List[str], force_update: bool = False) -> None:
        """批量更新复权因子数据"""
        if force_update:
            # 强制更新时批量删除对应股票的复权因子数据
            FutuRehab.delete().where(FutuRehab.code << codes).execute()

        # 使用双端队列存储待处理的代码
        pending_codes = deque(codes)
        retry_counts = {code: 0 for code in codes}  # 记录每个代码的重试次数
        rate_limit_retry_counts = {code: 0 for code in codes}  # 记录频率限制重试次数
        processed_count = 0
        total = len(codes)

        # 跟踪请求频率的变量
        request_times = deque(maxlen=60)  # 存储最近60次请求的时间

        while pending_codes:
            code = pending_codes.popleft()
            processed_count += 1

            # 检查API频率限制 (每30秒最多60次请求)
            current_time = time.time()
            if len(request_times) >= 60:
                oldest_request = request_times[0]
                time_diff = current_time - oldest_request
                if time_diff < 30:
                    # 如果60次请求在不到30秒内完成，需要等待
                    sleep_time = max(30 - time_diff, 0.5)
                    logger.debug(f"频率控制: 休眠 {sleep_time:.2f} 秒")
                    time.sleep(sleep_time)

            # 记录本次请求时间
            request_times.append(time.time())

            # 获取复权数据
            result = self.collect_rehab_data(code, force_update)

            if result == "RATE_LIMIT":
                # 频率限制错误，增加特定的重试计数
                rate_limit_retry_counts[code] += 1
                # 将代码放回队列末尾
                pending_codes.append(code)
                processed_count -= 1  # 调整处理计数

                # 根据重试次数增加等待时间
                wait_time = min(0.5 * (2 ** rate_limit_retry_counts[code]), 30)  # 指数退避，最多等待30秒
                logger.warning(f"{code} 遇到频率限制，等待 {wait_time:.2f} 秒后重试 (频率限制重试: {rate_limit_retry_counts[code]}次)")
                time.sleep(wait_time)

            elif result is False:
                # 其他错误
                retry_counts[code] += 1
                if retry_counts[code] < self.max_retries:
                    # 将失败的代码放到队列末尾重试
                    pending_codes.append(code)
                    processed_count -= 1  # 调整处理计数
                    # logger.warning(f"{code} 将在稍后重试 (已重试 {retry_counts[code]} 次)")
                else:
                    # logger.warning(f"{code} 已达到最大重试次数 {self.max_retries}，跳过处理")
                    pass

            # 每100个代码保存一次
            if processed_count % 100 == 0:
                self.save_rehab_batch()
                # logger.info(f"[{processed_count}/{total}] 保存复权因子数据")

            # 每处理一个代码后休眠一小段时间，避免频率过高
            time.sleep(0.5)

        # 确保最后的数据被保存
        self.save_rehab_batch()

        # 打印重试统计信息
        retry_stats = {count: len([c for c, rc in retry_counts.items() if rc == count])
                      for count in range(1, self.max_retries + 1)}
        rate_limit_stats = {count: len([c for c, rc in rate_limit_retry_counts.items() if rc > 0 and rc == count])
                           for count in range(1, 10)}  # 统计频率限制重试次数

        if any(retry_stats.values()) or any(rate_limit_stats.values()):
            logger.info("\n重试统计:")
            for retry_count, code_count in retry_stats.items():
                if code_count > 0:
                    logger.info(f"一般错误重试{retry_count}次的代码数: {code_count}")

            for retry_count, code_count in rate_limit_stats.items():
                if code_count > 0:
                    logger.info(f"频率限制重试{retry_count}次的代码数: {code_count}")

@app.command()
def main(
    update: bool = typer.Option(False, "--update", "-u", help="是否强制更新"),
    codes: str = typer.Option(None, "--codes", "-c", help="要处理的股票代码列表，用逗号分隔，例如：US.AAPL,US.MSFT"),
    report_no_prev: bool = typer.Option(False, "--report-no-prev", help="是否报告获取不到前收盘价的情况"),
    excel_file: str = typer.Option("美股品种信息.xlsx", "--excel-file", "-e", help="Excel文件路径"),
    sheet_name: str = typer.Option("稳定ID", "--sheet", "-s", help="Excel工作表名称")
) -> None:
    """复权因子数据更新工具"""
    start_time = datetime.now()
    logger.info(f"开始运行: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 创建数据表
    db_manager.common_db.create_tables([FutuRehab, FutuProduct], safe=True)

    # 创建富途数据管理器
    manager = FutuDataManager()
    manager.report_no_prev = report_no_prev

    try:
        # 一次性加载数据库复权记录到缓存
        manager.get_db_data()

        # 获取并保存所有美股信息
        # logger.info("正在获取美股列表...")
        stocks = manager.get_all_us_stocks()
        logger.info(f"获取到 {len(stocks)} 只美股")

        # 获取FutuProduct中所有STOCK类型的股票代码
        #query = FutuProduct.select(FutuProduct.code).where(FutuProduct.stock_type == 'STOCK')
        #all_stock_codes = set(code[0] for code in query.tuples())
        logger.info(f"数据库中共有 {len(stocks)} 只STOCK类型股票")

        # 确定要处理的股票代码列表
        if codes:
            # 如果指定了代码列表，则只处理指定的代码
            specified_codes = [c.strip() for c in codes.split(',') if c.strip()]
            # 过滤出在数据库中存在的代码
            target_codes = [code for code in specified_codes if code in stocks]
            logger.info(f"指定了 {len(specified_codes)} 只股票，其中 {len(target_codes)} 只在数据库中存在")
            stock_codes_to_process = target_codes
        else:
            # 如果没有指定代码，则按Excel文件中的优先级排序
            logger.info(f"未指定代码，将按Excel文件 {excel_file} 中的优先级排序")

            try:
                # 读取Excel文件
                import pandas as pd
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")

                # 检查必需列是否存在
                required_columns = ['TRADING','symbol', '日均成交额（万美元）_1M']
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    logger.error(f"Excel文件缺少必需列: {missing_columns}")
                    raise ValueError(f"Excel文件缺少必需列: {missing_columns}")

                # 按日均成交额从大到小排序
                df_sorted = df.sort_values('日均成交额（万美元）_1M', ascending=False)
                logger.info("已按日均成交额从大到小排序")

                # 构建优先级代码列表
                priority_codes = []
                for _, row in df_sorted.iterrows():
                    if row['symbol'] not in NA_VALUES:
                        code = f"US.{row['symbol'].replace(' ', '.')}"
                        if code in stocks:
                            priority_codes.append(code)

                logger.info(f"从Excel文件中获取到 {len(priority_codes)} 只优先查询股票")

                # 获取剩余的股票代码（不在Excel文件中的）
                remaining_codes = [code for code in stocks if code not in priority_codes]
                logger.info(f"剩余 {len(remaining_codes)} 只股票不在Excel文件中")

                # 合并代码列表：优先级代码 + 剩余代码
                stock_codes_to_process = priority_codes + remaining_codes

                logger.info(f"处理顺序: 优先级股票({len(priority_codes)}只) -> 剩余股票({len(remaining_codes)}只)")

            except Exception as e:
                logger.error(f"读取Excel文件失败: {e}")
                logger.info("将按数据库中的默认顺序处理所有股票")
                stock_codes_to_process = list(stocks)

        logger.info(f"开始获取 {len(stock_codes_to_process)} 只股票的复权因子...")
        manager.update_rehab_data(stock_codes_to_process, update)

    except Exception as e:
        logger.error(f"数据更新失败: {e}")
        raise
    finally:
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"结束运行: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"运行时长: {duration}")
        sys.exit(0)
if __name__ == "__main__":
    app()