"""
从source数据库复制指定conid的K线数据到target数据库
使用SQL直接复制，每批10个conid处理
"""

import os
import typer
from typing import List
from loguru import logger
from peewee import chunked
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import get_file_path
from mysql_database import create_mysql_database
from generate_diff_conids import load_conid_list, get_diff_conids_programmatic

# load_conid_list 函数已从 generate_diff_conids 模块导入

def copy_bardata_by_sql(source_db: str, target_db: str, conids: List[str], batch_size: int = 10, end_time: str = "2025-08-15 04:00:00"):
    """使用SQL直接复制K线数据"""
    # 使用SETTINGS创建数据库连接
    settings = SETTINGS.copy()
    settings["database.database"] = target_db
    database, _, _, _, _ = create_mysql_database(settings)
    
    logger.info(f"开始从 {source_db} 复制到 {target_db}，总共{len(conids)}个conid")
    
    total_copied = 0
    
    # 分批处理
    for batch_conids in chunked(conids, batch_size):
        conid_list = "'" + "','".join(batch_conids) + "'"
        
        sql = f"""
        INSERT IGNORE INTO {target_db}.dbbardata 
        (symbol, exchange, datetime, `interval`, volume, turnover, open_interest, 
         open_price, high_price, low_price, close_price)
        SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest,
               open_price, high_price, low_price, close_price
        FROM {source_db}.dbbardata 
        WHERE symbol IN ({conid_list}) AND datetime < '{end_time}'
        """
        
        try:
            cursor = database.db.execute_sql(sql)
            affected_rows = cursor.rowcount
            total_copied += affected_rows
            logger.info(f"批次复制完成，复制了{affected_rows}条记录")
        except Exception as e:
            logger.error(f"批次复制失败: {e}")
            logger.error(f"SQL: {sql}")
            continue
    
    logger.info(f"复制完成！总共复制了{total_copied}条K线记录")
    
    # 重建目标数据库的dbbaroverview表
    logger.info("开始重建目标数据库的dbbaroverview表...")
    rebuild_sql = f"""
    INSERT INTO {target_db}.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
    SELECT
        symbol,
        exchange,
        `interval`,
        COUNT(id) AS `count`,
        MIN(datetime) AS `start`,
        MAX(datetime) AS `end`
    FROM
        {target_db}.dbbardata
    GROUP BY
        symbol, exchange, `interval`
    ON DUPLICATE KEY UPDATE
        `count` = VALUES(`count`),
        `start` = VALUES(`start`),
        `end` = VALUES(`end`)
    """
    
    try:
        cursor = database.db.execute_sql(rebuild_sql)
        overview_rows = cursor.rowcount
        logger.info(f"重建dbbaroverview表完成，更新了{overview_rows}条汇总记录")
    except Exception as e:
        logger.error(f"重建dbbaroverview表失败: {e}")
        logger.error(f"SQL: {rebuild_sql}")
        raise

# get_diff_conids 函数已从 generate_diff_conids 模块导入

def main(
    source_db: str = typer.Option("vnpy_stk_us_ib_m", help="源数据库名"),
    target_db: str = typer.Option("vnpy_stk_us_ib_m_", help="目标数据库名"),
    conid_file: str = typer.Option("filtered_conid.txt", help="conid文件路径"),
    conid_file2: str = typer.Option("", help="第二个conid文件路径，如果提供则处理差异"),
    diff_output: str = typer.Option("diff_conids.txt", help="差异conids输出文件路径"),
    batch_size: int = typer.Option(20, help="每批处理的conid数量"),
    end_time: str = typer.Option("2025-08-15 04:00:00", help="截止时间，只复制该时间之前的数据")
):
    """复制指定conid的K线数据"""
    try:
        # 使用get_file_path获取文件完整路径
        conid_file_path = get_file_path(conid_file)

        # 如果提供了第二个文件，处理差异
        if conid_file2:
            conid_file2_path = get_file_path(conid_file2)
            logger.info(f"处理差异模式：{conid_file2} - {conid_file}")
            conids = get_diff_conids_programmatic(conid_file_path, conid_file2_path, diff_output)
        else:
            # 读取conid列表
            conids = load_conid_list(conid_file_path)
            logger.info(f"成功读取{len(conids)}个conid")

        if not conids:
            logger.warning("未找到任何conid，退出")
            return

        # 复制数据
        copy_bardata_by_sql(source_db, target_db, conids, batch_size, end_time)

        logger.info("所有操作完成！")

    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        raise

if __name__ == "__main__":
    typer.run(main)