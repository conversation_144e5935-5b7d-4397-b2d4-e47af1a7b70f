#!/bin/bash

# Set your MySQL password here
MYSQL_PASSWORD="p0o9i8u7"

# Function to install MySQL
install_mysql() {
    sudo apt update
    sudo apt install mysql-server
}

# Function to configure MySQL
configure_mysql() {
    echo "Configuring MySQL..."
    mysql -u root -p$MYSQL_PASSWORD <<EOF
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$MYSQL_PASSWORD';
FLUSH PRIVILEGES;
EOF
}

# Function to create the Flask database
create_flask_db() {
    echo "Creating Flask database..."
    mysql -u root -p$MYSQL_PASSWORD <<EOF
CREATE DATABASE IF NOT EXISTS TradeInfoRest;
EOF
}

# Main function to execute all steps
main() {
    install_mysql
    configure_mysql
    create_flask_db
    echo "Database setup completed successfully."
}

# Call the main function
main
