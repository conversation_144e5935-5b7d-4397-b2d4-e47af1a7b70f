import urllib.request
import os

def get_system_proxy() -> dict:
    """获取系统代理设置
    
    Returns:
        dict: 包含http和https代理设置的字典
    """
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}

    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            os.environ[f"{protocol}_proxy"] = proxy

    return proxies