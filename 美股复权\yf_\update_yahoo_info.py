import json
import traceback
from datetime import datetime, timedelta
import os
import sys
from typing import Dict, Any, Optional, Set, List
import queue
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import yfinance as yf
from yfinance.exceptions import YFRateLimitError
import typer
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)

import inflection
from typing_extensions import Annotated
from vnpy.trader.database import ZoneInfo

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, YahooInfo, Ib<PERSON>roduct, YahooQuote
from vnpy.trader.utility import load_json, save_json, get_file_path
from utils.proxy_utils import get_system_proxy

# Yahoo API 返回的时间戳字段列表
# 这些字段在 YahooInfo 模型中都有对应的 DateTimeField
TIMESTAMP_FIELDS = [
    'first_trade_date_milliseconds',  # firstTradeDateMilliseconds
    'earnings_timestamp',             # earningsTimestamp
    'last_fiscal_year_end',          # lastFiscalYearEnd
    'next_fiscal_year_end',          # nextFiscalYearEnd
    'shares_short_previous_month_date',  # sharesShortPreviousMonthDate
    'date_short_interest',           # dateShortInterest
    'ex_dividend_date',              # exDividendDate
    'last_dividend_date',            # lastDividendDate
    'dividend_date',                 # dividendDate
    'last_split_date',               # lastSplitDate
]

# 预定义美东时区对象
TZ_AMERICA_NEWYORK = ZoneInfo('America/New_York')

app = typer.Typer()

def convert_timestamp_to_datetime(value: int) -> Optional[datetime]:
    """
    将时间戳转换为美东时区的datetime对象
    
    Args:
        value: Unix时间戳（秒或毫秒）
        
    Returns:
        datetime: 转换后的datetime对象（美东时区），如果转换失败则返回None
    """
    if not isinstance(value, (int, float)):
        return None
        
    try:
        # 判断是秒还是毫秒
        timestamp = value / 1000 if value > 1e11 else value
        if timestamp > 0:
            # 使用美东时区创建datetime对象
            return datetime.fromtimestamp(timestamp, TZ_AMERICA_NEWYORK)
        else:
            # 处理负数时间戳（从1970年之前开始计算）
            return datetime.fromtimestamp(0, TZ_AMERICA_NEWYORK) - timedelta(milliseconds=-value)
    except (ValueError, OSError, OverflowError):
        return None

def _get_latest_product_isins() -> Dict[str, int]:
    """从IbProduct表获取标记为is_latest=True的isin和conid的映射"""
    logger.info("正在从数据库获取最新的产品isin和conid...")
    isin_conid_map = {}
    try:        
        query = (IbProduct
                .select(IbProduct.isin, IbProduct.conid)
                .where(
                    (IbProduct.is_latest == True) &
                    (IbProduct.isin.is_null(False))
                )
                .tuples())
        for isin, conid in query:
            if isin and conid:
                isin_conid_map[isin] = conid
        logger.info(f"已获取{len(isin_conid_map)}个最新的产品isin。")
    except Exception as e:
        logger.error(f"获取最新产品isin时出错: {str(e)}\n{traceback.format_exc()}")
    return isin_conid_map

class YahooInfoUpdater:
    """Yahoo信息更新器"""
    
    def __init__(self):
        """初始化"""
        # 设置系统代理
        self.proxies = get_system_proxy()
        logger.info(f"系统代理设置: {self.proxies}")
        
        self.failed_isins = set()
        self.info_dir = os.path.join(os.path.dirname(__file__), "info")
        # 确保info_dir目录存在
        os.makedirs(self.info_dir, exist_ok=True)
        self.latest_isin_conid_map = _get_latest_product_isins()
        
        # 添加无效ISIN相关的属性
        self.invalid_isins_file = "invalid_isins.json"  # 只保留文件名，不需要完整路径
        self.invalid_isins = set()
        self._invalid_isins_lock = threading.Lock()
        self._load_invalid_isins()

        # 添加temp目录用于保存临时获取的info数据
        self.temp_dir = os.path.join(os.path.dirname(__file__), "temp")
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def _load_invalid_isins(self):
        """从文件加载无效的ISIN列表"""
        full_file_path = get_file_path(self.invalid_isins_file)
        
        # 检查文件是否存在且修改时间不是今天的，如果是则删除
        if os.path.exists(full_file_path):
            try:
                file_mtime = datetime.fromtimestamp(os.path.getmtime(full_file_path)).date()
                today = datetime.now().date()
                
                if file_mtime != today:
                    os.remove(full_file_path)
                    logger.info(f"已删除过期的无效ISIN文件: {full_file_path} (修改时间: {file_mtime})")
                    self.invalid_isins = set()
                    return
            except Exception as e:
                logger.warning(f"检查或删除过期无效ISIN文件时出错: {e}")
        
        data = load_json(self.invalid_isins_file)
        self.invalid_isins = set(data) if data else set()
        logger.info(f"已从{self.invalid_isins_file}加载{len(self.invalid_isins)}个无效ISIN")

    def _save_invalid_isins(self):
        """保存无效的ISIN列表到文件"""
        with self._invalid_isins_lock:
            # 使用vnpy的save_json函数，保存前先排序
            save_json(self.invalid_isins_file, sorted(list(self.invalid_isins)))
        logger.info(f"已保存{len(self.invalid_isins)}个无效ISIN到{self.invalid_isins_file}")

    def _add_invalid_isin(self, isin: str):
        """添加一个无效的ISIN到集合中"""
        with self._invalid_isins_lock:
            self.invalid_isins.add(isin)

    def _parse_yahoo_info(self, info: Dict[str, Any], isin: str) -> Optional[Dict[str, Any]]:
        """解析Yahoo信息并转换为数据库格式
        
        Args:
            info: Yahoo API返回的原始信息
            isin: ISIN代码
            
        Returns:
            Dict: 转换后的数据，如果解析失败则返回None
        """
        try:
            data = {"isin": isin, "conid": self.latest_isin_conid_map.get(isin)}
            
            # 遍历YahooInfo模型的所有字段
            for field_name in YahooInfo._meta.fields.keys():
                if field_name in ["isin", "conid", "create_time", "update_time"]:
                    continue
                    
                # 将蛇形命名转换为小驼峰
                camel_case = inflection.camelize(field_name, False)
                
                # 特殊字段处理
                special_fields = {
                    'trailing_pe': 'trailingPE',
                    'forward_pe': 'forwardPE'
                }
                
                # 如果是特殊字段，使用特殊映射
                if field_name in special_fields:
                    value = info.get(special_fields[field_name])
                else:
                    value = info.get(camel_case)
                
                # 处理时间戳字段
                if field_name in TIMESTAMP_FIELDS:
                    value = convert_timestamp_to_datetime(value)
                
                data[field_name] = value
                
            return data
            
        except Exception as e:
            logger.error(f"解析isin={isin}的Yahoo信息时出错: {str(e)}\n{traceback.format_exc()}")
            return None

    def _parse_yahoo_quote(self, info: Dict[str, Any], isin: str) -> Optional[Dict[str, Any]]:
        """解析Yahoo信息并提取盘口数据
        
        Args:
            info: Yahoo API返回的原始信息
            isin: ISIN代码
            
        Returns:
            Dict: 转换后的盘口数据，如果解析失败则返回None
        """
        try:
            ask = info.get("ask")
            bid = info.get("bid")
            
            # 检查ask和bid是否有效：不能为None、0或负数
            if not ask or not bid or ask < 0 or bid < 0:
                return None
                
            data = {
                "isin": isin,
                "conid": self.latest_isin_conid_map.get(isin),
                "datetime": datetime.now(), # 记录当前时间
                "ask": ask,
                "ask_size": info.get("askSize"),
                "bid": bid,
                "bid_size": info.get("bidSize"),
            }
            return data
        except Exception as e:
            logger.error(f"解析isin={isin}的Yahoo盘口信息时出错: {str(e)}\n{traceback.format_exc()}")
            return None

    def _get_yahoo_info(self, isin: str, use_local_file: bool = True) -> Optional[Dict[str, Any]]:
        """获取Yahoo信息，优先从本地文件读取

        Args:
            isin: ISIN代码
            use_local_file: 是否优先从本地文件读取，默认为True
        """
        # 检查是否是已知的无效ISIN
        if isin in self.invalid_isins:
            # logger.info(f"isin={isin}: 跳过已知的无效ISIN")
            return None
            
        current_month_str = datetime.now().strftime("%Y%m")
        # info文件路径
        info_file_path = os.path.join(self.info_dir, f"{isin}.json")
        # temp文件路径
        temp_file_path = os.path.join(self.temp_dir, f"{isin}.json")
        
        # 尝试从本地文件读取 (info目录)
        if use_local_file and os.path.exists(info_file_path):
            try:
                file_mtime = datetime.fromtimestamp(os.path.getmtime(info_file_path)).strftime("%Y%m")
                if file_mtime == current_month_str:
                    with open(info_file_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)
                    logger.info(f"isin={isin}: 从本地info文件读取当月Yahoo信息。")
                    return info
                else:
                    logger.info(f"isin={isin}: 本地info文件过旧 ({file_mtime})，将从网络获取。")
            except Exception as e:
                logger.warning(f"读取isin={isin}本地info文件失败: {e}，将从网络获取。")
        
        # 从网络获取，遇到流控错误时无限重试
        while True:
            try:
                ticker = yf.Ticker(isin)
                info = ticker.info
                
                if info:
                    # 保存到对应的本地文件
                    if use_local_file:
                        with open(info_file_path, 'w', encoding='utf-8') as f:
                            json.dump(info, f, indent=4)
                        logger.info(f"isin={isin}: 已从网络获取并保存Yahoo信息到 {info_file_path}")
                    else:
                        with open(temp_file_path, 'w', encoding='utf-8') as f:
                            json.dump(info, f, indent=4)
                        logger.info(f"isin={isin}: 已从网络获取并保存Yahoo信息到 {temp_file_path}")
                    return info
                else:
                    logger.warning(f"isin={isin}未获取到Yahoo信息 (网络请求)。")
                    return None
                    
            except ValueError as e:
                if "Invalid ISIN number" in str(e):
                    logger.warning(f"isin={isin}是无效的ISIN号码，将添加到无效ISIN列表。")
                    self._add_invalid_isin(isin)
                logger.error(f"获取isin={isin}的Yahoo信息时出错 (网络请求): {str(e)}\n{traceback.format_exc()}")
                return None
            except YFRateLimitError as e:
                logger.warning(f"获取isin={isin}的Yahoo信息时遇到流控错误，等待0.1秒后重试: {str(e)}")
                time.sleep(0.1)  # 等待0.1秒后重试
                continue
            except Exception as e:
                logger.error(f"获取isin={isin}的Yahoo信息时出错 (网络请求): {str(e)}\n{traceback.format_exc()}")
                return None

    def _process_isin(self, isin: str, data_queue: queue.Queue) -> None:
        """处理单个ISIN的获取和解析
        
        Args:
            isin: ISIN代码
            data_queue: 数据队列
        """
        try:
            # 获取Yahoo信息
            info = self._get_yahoo_info(isin)
            if not info:
                self.failed_isins.add(isin)
                return

            # 解析数据
            parsed_data = self._parse_yahoo_info(info, isin)
            if parsed_data:
                data_queue.put(parsed_data)
            else:
                self.failed_isins.add(isin)

        except Exception as e:
            logger.error(f"处理isin={isin}时出错: {str(e)}\n{traceback.format_exc()}")
            self.failed_isins.add(isin)

    def _process_quote_isin(self, isin: str, data_queue: queue.Queue) -> None:
        """处理单个ISIN的盘口数据获取和解析
        
        Args:
            isin: ISIN代码
            data_queue: 数据队列
        """
        try:
            # 获取Yahoo信息，不使用本地文件
            info = self._get_yahoo_info(isin, use_local_file=False)
            if not info:
                self.failed_isins.add(isin)
                return

            # 解析盘口数据
            parsed_data = self._parse_yahoo_quote(info, isin)
            if parsed_data:
                data_queue.put(parsed_data)
            else:
                self.failed_isins.add(isin)

        except Exception as e:
            logger.error(f"处理isin={isin}的盘口数据时出错: {str(e)}\n{traceback.format_exc()}")
            self.failed_isins.add(isin)

    def update_yahoo_info(self, isins: Optional[Set[str]] = None, max_workers: int = 5) -> None:
        """更新Yahoo信息
        
        Args:
            isins: ISIN代码集合，如果为None则使用latest_isin_conid_map中的所有ISIN
            max_workers: 最大线程数，默认为5
        """
        try:
            # 确定要处理的isins
            if isins is None:
                isins_to_process = set(self.latest_isin_conid_map.keys())
            else:
                isins_to_process = isins

            if not isins_to_process:
                logger.info("没有指定isin或没有最新的产品isin，跳过Yahoo信息更新。")
                return

            # 重置状态
            self.failed_isins.clear()

            # 获取当前月份
            current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # 从数据库中找出所有update_time是当前月份的isin
            isins_already_up_to_date_in_db = set()
            try:
                query = (
                    YahooInfo.select(YahooInfo.isin)
                    .where(
                        (YahooInfo.update_time >= current_month)
                    )
                    .tuples()
                )
                for isin_tuple in query:
                    isins_already_up_to_date_in_db.add(isin_tuple[0])
            except Exception as e:
                logger.warning(f"查询现有Yahoo信息时出错: {e}，将尝试全部更新。")
                isins_already_up_to_date_in_db.clear()

            # 需要更新的isin是isins_to_process中除去数据库中已是最新isin的差集
            isins_needing_update = isins_to_process - isins_already_up_to_date_in_db

            if not isins_needing_update:
                logger.info("所有Yahoo信息均已是最新，无需更新。")
                return

            logger.info(f"需要获取Yahoo信息的isin数量: {len(isins_needing_update)}")

            # 创建数据队列和完成事件
            data_queue = queue.Queue()
            fetching_completed = threading.Event()

            # 创建并启动保存线程
            save_thread = threading.Thread(
                target=db_manager.process_and_save_queue,
                args=(data_queue, fetching_completed, YahooInfo)
            )
            save_thread.start()

            # 使用线程池并发获取数据
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_isin = {
                    executor.submit(self._process_isin, isin, data_queue): isin
                    for isin in isins_needing_update
                }
                
                # 等待所有任务完成
                for future in as_completed(future_to_isin):
                    isin = future_to_isin[future]
                    try:
                        future.result()  # 获取结果（如果有异常会在这里抛出）
                    except Exception as e:
                        logger.error(f"处理isin={isin}时发生异常: {str(e)}\n{traceback.format_exc()}")
                        self.failed_isins.add(isin)

            # 标记获取完成并等待保存线程结束
            fetching_completed.set()
            save_thread.join()

            # 保存无效ISIN列表
            self._save_invalid_isins()

            # 汇总未获取到的isin
            # if self.failed_isins:
            #     logger.warning(f"以下{len(self.failed_isins)}个isin未能成功获取数据：{sorted(self.failed_isins)}")
                
            # 打印由于其他原因失败的ISIN（在failed_isins中但不在invalid_isins中的）
            other_failed_isins = self.failed_isins - self.invalid_isins
            if other_failed_isins:
                logger.warning(f"以下{len(other_failed_isins)}个isin由于其他原因失败（非无效ISIN）：{sorted(other_failed_isins)}")

        except Exception as e:
            logger.error(f"更新Yahoo信息时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise

    def update_yahoo_quote(self, isins: Optional[Set[str]] = None, max_workers: int = 50) -> None:
        """更新Yahoo盘口信息
        
        Args:
            isins: ISIN代码集合，如果为None则使用latest_isin_conid_map中的所有ISIN
            max_workers: 最大线程数，默认为50
        """
        try:
            # 确定要处理的isins
            if isins is None:
                isins_to_process = set(self.latest_isin_conid_map.keys())
            else:
                isins_to_process = isins

            if not isins_to_process:
                logger.info("没有指定isin或没有最新的产品isin，跳过Yahoo盘口信息更新。")
                return

            # 重置状态
            self.failed_isins.clear()

            logger.info(f"需要获取Yahoo盘口信息的isin数量: {len(isins_to_process)}")

            # 创建数据队列和完成事件
            data_queue = queue.Queue()
            fetching_completed = threading.Event()

            # 创建并启动保存线程，使用replace模式
            save_thread = threading.Thread(
                target=db_manager.process_and_save_queue,
                args=(data_queue, fetching_completed, YahooQuote),
                kwargs={"replace": True}
            )
            save_thread.start()

            # 使用线程池并发获取数据
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_isin = {
                    executor.submit(self._process_quote_isin, isin, data_queue): isin
                    for isin in isins_to_process
                }
                
                # 等待所有任务完成
                for future in as_completed(future_to_isin):
                    isin = future_to_isin[future]
                    try:
                        future.result()  # 获取结果（如果有异常会在这里抛出）
                    except Exception as e:
                        logger.error(f"处理isin={isin}的盘口数据时发生异常: {str(e)}\n{traceback.format_exc()}")
                        self.failed_isins.add(isin)

            # 标记获取完成并等待保存线程结束
            fetching_completed.set()
            save_thread.join()

            # 保存无效ISIN列表
            self._save_invalid_isins()

            # 打印由于其他原因失败的ISIN（在failed_isins中但不在invalid_isins中的）
            other_failed_isins = self.failed_isins - self.invalid_isins
            if other_failed_isins:
                logger.warning(f"以下{len(other_failed_isins)}个isin由于其他原因失败（非无效ISIN）：{sorted(other_failed_isins)}")

        except Exception as e:
            logger.error(f"更新Yahoo盘口信息时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise

@app.command(name="update-all")
def update_all_command(
    isins: Annotated[Optional[str], typer.Option("--isins", "-i", help="指定要更新的ISIN列表（逗号分隔），不指定则更新所有最新产品ISIN")] = None,
    max_workers: Annotated[int, typer.Option("--max-workers", "-w", help="最大线程数")] = 50
):
    """更新所有Yahoo信息"""
    db_manager.common_db.create_tables([YahooInfo], safe=True)
    updater = YahooInfoUpdater()
    
    # 解析逗号分隔的ISIN字符串
    isin_set = None
    if isins:
        isin_set = set(isin.strip() for isin in isins.split(',') if isin.strip())
    
    updater.update_yahoo_info(isins=isin_set, max_workers=max_workers)
    logger.info("所有Yahoo信息更新完成。")

@app.command(name="update-quote")
def update_quote_command(
    isins: Annotated[Optional[str], typer.Option("--isins", "-i", help="指定要更新的ISIN列表（逗号分隔），不指定则更新所有最新产品ISIN")] = None,
    max_workers: Annotated[int, typer.Option("--max-workers", "-w", help="最大线程数")] = 50
):
    """更新所有Yahoo盘口信息"""
    db_manager.common_db.create_tables([YahooQuote], safe=True)
    updater = YahooInfoUpdater()
    
    # 解析逗号分隔的ISIN字符串
    isin_set = None
    if isins:
        isin_set = set(isin.strip() for isin in isins.split(',') if isin.strip())
    
    updater.update_yahoo_quote(isins=isin_set, max_workers=max_workers)
    logger.info("所有Yahoo盘口信息更新完成。")

if __name__ == "__main__":
    app()