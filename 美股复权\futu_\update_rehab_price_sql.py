# 添加项目根目录到 Python 路径
import os, sys

file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager
import time

def update_prev_close():
    """使用SQL批量更新前一日收盘价和交易所前收盘价"""
    
    start_time = time.time()
    
    try:
        # 1. 创建临时表存储需要更新的记录
        print("创建临时表...")
        create_temp_sql = """
        CREATE TEMPORARY TABLE temp_rehab AS
        SELECT r.code, r.ex_div_date, CAST(ip.conid as CHAR) as conid,
               r.per_cash_div, r.special_dividend, r.split_ratio
        FROM futu_rehab r
        INNER JOIN ibproduct ip ON CONCAT('US.', ip.symbol) = r.code
        INNER JOIN dbbaroverview bo ON 
            bo.symbol = CAST(ip.conid as CHAR) AND 
            bo.exchange = 'SMART' AND 
            bo.interval = 'd'
        WHERE (r.prev_close IS NULL OR r.exchange_prev_close IS NULL)
        AND r.ex_div_date >= '2023-01-01'
        """
        db_manager.common_db.execute_sql(create_temp_sql)
        
        # 创建索引
        db_manager.common_db.execute_sql("CREATE INDEX idx_temp_conid ON temp_rehab(conid)")
        db_manager.common_db.execute_sql("CREATE INDEX idx_temp_code_date ON temp_rehab(code, ex_div_date)")
        
        # 获取需要更新的记录数
        cursor = db_manager.common_db.execute_sql("SELECT COUNT(*) FROM temp_rehab")
        total = cursor.fetchone()[0]
        print(f"找到{total}条需要更新的记录")
        
        # 2. 创建临时表存储前一日收盘价
        print("计算前收盘价...")
        create_price_sql = """
        CREATE TEMPORARY TABLE temp_prices AS
        SELECT t.code, t.ex_div_date, t.conid,
               b.close_price as prev_close,
               (b.close_price - COALESCE(t.per_cash_div, 0) - COALESCE(t.special_dividend, 0)) 
               * COALESCE(t.split_ratio, 1) as exchange_prev_close
        FROM temp_rehab t
        INNER JOIN dbbardata b ON 
            b.symbol = t.conid AND
            b.exchange = 'SMART' AND 
            b.interval = 'd' AND
            b.datetime = (
                SELECT MAX(datetime)
                FROM dbbardata b2
                WHERE b2.symbol = t.conid
                AND b2.datetime < t.ex_div_date
            )
        """
        db_manager.common_db.execute_sql(create_price_sql)
        
        # 创建索引
        db_manager.common_db.execute_sql("CREATE INDEX idx_price_code_date ON temp_prices(code, ex_div_date)")
        
        # 3. 批量更新主表
        print("执行批量更新...")
        update_sql = """
        UPDATE futu_rehab r
        INNER JOIN temp_prices p ON 
            r.code = p.code AND 
            r.ex_div_date = p.ex_div_date
        SET 
            r.prev_close = p.prev_close,
            r.exchange_prev_close = p.exchange_prev_close
        """
        cursor = db_manager.common_db.execute_sql(update_sql)
        updated = cursor.rowcount
        
        total_time = time.time() - start_time
        avg_speed = updated / total_time if total_time > 0 else 0
        print(f"共更新了{updated}条记录")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均速度: {avg_speed:.2f}条/秒")
        
    except Exception as e:
        print(f"更新出错: {e}")
        raise
    finally:
        # 清理临时表
        db_manager.common_db.execute_sql("DROP TEMPORARY TABLE IF EXISTS temp_rehab")
        db_manager.common_db.execute_sql("DROP TEMPORARY TABLE IF EXISTS temp_prices")


if __name__ == "__main__":
    update_prev_close()
