import os
import sys
import zipfile
import requests
from typing import Optional, Dict, Any
from pathlib import Path
import shutil
import typer
import traceback
import json
from datetime import datetime

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


class FirstRateDataDownloader:
    """FirstRate数据下载管理器"""

    def __init__(self, data_dir="S:\\firstrate\\stock", userid="YtVEwkCXOU-CykSecfdlTQ", force_update=False):
        """初始化

        Args:
            data_dir: FirstRate数据保存目录
            userid: FirstRate API用户ID
            force_update: 是否强制更新，删除原有文件
        """
        self.base_data_dir = data_dir
        self.userid = userid
        self.force_update = force_update  # 将force_update作为实例变量

    def ensure_dir(self, directory):
        """确保目录存在"""
        os.makedirs(directory, exist_ok=True)

    def clear_directory(self, directory):
        """清空指定目录"""
        if os.path.exists(directory):
            shutil.rmtree(directory)  # 直接使用rmtree删除整个目录
            os.makedirs(directory)  # 重新创建目录
            logger.info(f"已清空并重新创建目录: {directory}")

    def check_last_update(self, data_path):
        """检查最后更新日期
        
        Args:
            data_path: 数据路径标识，用于生成本地文件名
            
        Returns:
            bool: 如果需要更新返回True，否则返回False
        """
        try:
            # 判断是否为full数据
            is_full_update = 'full' in data_path
            
            # 构建API URL
            api_url = f"https://firstratedata.com/api/last_update?type=stock&userid={self.userid}"
            if is_full_update:
                api_url += "&is_full_update=true"
            
            logger.info(f"检查最后更新日期: {api_url}")
            
            # 请求API
            response = requests.get(api_url)
            if response.status_code != 200:
                logger.error(f"获取最后更新日期失败，状态码: {response.status_code}")
                return True  # 如果API请求失败，继续执行下载
            
            # 解析响应
            api_date = response.text.strip()
            logger.info(f"API返回的最后更新日期: {api_date}")
            
            # 构建本地文件路径
            local_file = os.path.join(self.base_data_dir, f"last_update_{data_path}.txt")
            
            # 检查本地文件是否存在
            if not os.path.exists(local_file):
                logger.info(f"本地更新日期文件不存在: {local_file}")
                # 保存新的日期到本地文件
                self.ensure_dir(os.path.dirname(local_file))
                with open(local_file, 'w', encoding='utf-8') as f:
                    f.write(api_date)
                logger.info(f"已保存更新日期到: {local_file}")
                return True
            
            # 读取本地文件中的日期
            with open(local_file, 'r', encoding='utf-8') as f:
                local_date = f.read().strip()
            
            logger.info(f"本地保存的最后更新日期: {local_date}")
            
            # 比较日期
            if api_date != local_date:
                logger.info(f"日期不同，需要更新数据")
                # 保存新的日期到本地文件
                with open(local_file, 'w', encoding='utf-8') as f:
                    f.write(api_date)
                logger.info(f"已更新本地日期文件: {local_file}")
                return True
            else:
                logger.info(f"日期相同，无需更新数据")
                return False
                
        except Exception as e:
            logger.error(f"检查最后更新日期时出错: {e}")
            return True  # 如果检查失败，继续执行下载

    def download_data(self, period='full', ticker_range=None, adjustment='UNADJUSTED', timeframe='1min'):
        """下载FirstRate数据

        Args:
            period: 数据周期，可选值: full, month, week, day
            ticker_range: 股票字母范围，如'A'表示A开头的股票，仅在period='full'时使用
            adjustment: 数据调整类型，可选值: UNADJUSTED, adj_split, adj_splitdiv
            timeframe: 时间周期，可选值: 1min, 5min, 30min, 1hour, 1day

        Returns:
            str: 下载的文件路径，失败则返回None
        """

        # 构建URL
        base_url = "https://firstratedata.com/api/data_file"
        params = {
            "type": "stock",
            "period": period,
            "timeframe": timeframe,
            "adjustment": adjustment,
            "userid": self.userid
        }

        # 仅在请求完整数据时添加ticker_range参数
        if period == 'full' and ticker_range:
            params["ticker_range"] = ticker_range

        # 构建URL
        url = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
        logger.info(f"正在下载: {url}")

        try:
            response = requests.get(url)
            if response.status_code == 200:
                # 获取文件名
                content_disposition = response.headers.get('content-disposition', '')
                if 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"\'')
                else:
                    if ticker_range:
                        filename = f"stock_{ticker_range}_{period}_{timeframe}_{adjustment}.zip"
                    else:
                        filename = f"stock_{period}_{timeframe}_{adjustment}.zip"

                # 保存到本地
                save_path = os.path.join(self.base_data_dir, filename)

                # 如果文件已存在，先删除
                if os.path.exists(save_path):
                    try:
                        os.remove(save_path)
                        logger.info(f"删除已存在的文件: {save_path}")
                    except Exception as e:
                        logger.error(f"删除已存在文件失败: {e}")

                # 保存新下载的文件
                with open(save_path, 'wb') as f:
                    f.write(response.content)

                logger.info(f"下载完成，保存为: {save_path}")
                return save_path
            else:
                logger.error(f"下载失败，状态码: {response.status_code}")
                if response.text:
                    logger.error(f"响应内容: {response.text}")
                return None
        except Exception as e:
            logger.error(f"下载过程中出现错误: {e}")
            return None

    def extract_zip(self, zip_path, extract_to):
        """解压ZIP文件到指定目录

        Args:
            zip_path: ZIP文件路径
            extract_to: 解压目标目录

        Returns:
            bool: 是否成功解压
        """
        try:
            # 确保目标目录存在
            self.ensure_dir(extract_to)

            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)

            logger.info(f"文件已解压到: {extract_to}")
            return True
        except Exception as e:
            logger.error(f"解压文件时出错: {e}")
            return False

    def download_full_data(self, adjustment='UNADJUSTED', timeframe='1min'):
        """下载完整历史数据

        Args:
            adjustment: 数据调整类型
            timeframe: 时间周期

        Returns:
            bool: 是否全部下载成功
        """
        # 确定目标目录
        if adjustment == 'UNADJUSTED':
            target_dir = os.path.join(self.base_data_dir, f"full_{timeframe}")
            self.ensure_dir(target_dir)
        else:
            # 对于调整后的数据，创建带adjustment后缀的目录
            if timeframe == '1min':
                target_dir = os.path.join(self.base_data_dir, f"full_1min_{adjustment}")
            elif timeframe == '1day':
                target_dir = os.path.join(self.base_data_dir, f"full_1day_{adjustment}")
            else:
                target_dir = os.path.join(self.base_data_dir, f"full_{timeframe}_{adjustment}")
            self.ensure_dir(target_dir)

        # 如果需要强制更新，先清空目标目录
        if self.force_update:
            self.clear_directory(target_dir)

        # 定义字母范围并下载
        ticker_ranges = [chr(i) for i in range(ord('A'), ord('Z')+1)]
        for ticker_range in ticker_ranges:
            zip_path = self.download_data(
                period='full',
                ticker_range=ticker_range,
                adjustment=adjustment,
                timeframe=timeframe
            )

            if zip_path:
                # 解压文件
                self.extract_zip(zip_path, target_dir)

                # # 删除ZIP文件以节省空间
                # try:
                #     os.remove(zip_path)
                #     logger.info(f"已删除临时文件: {zip_path}")
                # except Exception as e:
                #     logger.error(f"删除临时文件失败: {e}")
            else:
                logger.error(f"下载{ticker_range}范围的数据失败")

        logger.info("完整历史数据下载完成")
        return True

    def update_data(self, period='month', adjustment='UNADJUSTED', timeframe='1min'):
        """下载更新数据（日/周/月）

        Args:
            period: 数据周期，可选值: month, week, day
            adjustment: 数据调整类型
            timeframe: 时间周期

        Returns:
            bool: 是否下载成功
        """
        # 确定目标目录
        if adjustment == 'UNADJUSTED':

            target_dir = os.path.join(self.base_data_dir, f"{period}_{timeframe}")
            self.ensure_dir(target_dir)
        else:
            # 对于调整后的数据，创建带adjustment后缀的目录
            target_dir = os.path.join(self.base_data_dir, f"{period}_{timeframe}_{adjustment}")
            self.ensure_dir(target_dir)

        # 如果需要强制更新，先清空目标目录
        if self.force_update:
            self.clear_directory(target_dir)

        # 下载数据
        zip_path = self.download_data(
            period=period,
            adjustment=adjustment,
            timeframe=timeframe
        )

        if zip_path:
            # 解压文件
            self.extract_zip(zip_path, target_dir)

            # # 删除ZIP文件以节省空间
            # try:
            #     os.remove(zip_path)
            #     logger.info(f"已删除临时文件: {zip_path}")
            # except Exception as e:
            #     logger.error(f"删除临时文件失败: {e}")

            logger.info(f"{period}数据更新完成")
            return True
        else:
            logger.error(f"下载{period}数据失败")
            return False

app = typer.Typer()

@app.command()
def process(
    data_path: str = typer.Option(
        "month_1min",
        "--data-path",
        "-p",
        help="数据路径，可选: full_1min（完整1分钟数据）, full_1day（完整日线数据）, month_1min（月度1分钟数据）, day_1min（日度1分钟数据）, day_1day（日度日线数据）"
    ),
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    adjustment: str = typer.Option(
        "UNADJUSTED",
        "--adjustment",
        "-a",
        help="数据调整类型，可选: UNADJUSTED（未调整）, adj_split（除权调整）, adj_splitdiv（除权除息调整）"
    ),
    force_update: bool = typer.Option(
        False,
        "--force-update",
        "-f",
        help="是否强制更新，删除原有文件"
    )
):
    """FirstRate数据下载工具"""
    try:
        # 创建下载器实例
        downloader = FirstRateDataDownloader(
            data_dir=data_dir,
            force_update=force_update
        )

        # 构建完整的data_path标识（包含adjustment信息）
        if adjustment == 'UNADJUSTED':
            full_data_path = data_path
        else:
            full_data_path = f"{data_path}_{adjustment}"

        # 检查是否需要更新（除非强制更新）
        if not force_update:
            if not downloader.check_last_update(full_data_path):
                logger.info("数据已是最新，无需下载")
                sys.exit(1)  # 异常退出，阻止后续脚本执行

        # 根据data_path选择下载方式
        if 'full' in data_path:
            timeframe = data_path.split('_')[1]
            logger.info(f"开始下载完整历史数据（{timeframe}，{adjustment}）...")
            downloader.download_full_data(timeframe=timeframe, adjustment=adjustment)
        else:
            period = data_path.split('_')[0]
            timeframe = data_path.split('_')[1]
            logger.info(f"开始下载{period}更新数据（{timeframe}，{adjustment}）...")
            downloader.update_data(period=period, timeframe=timeframe, adjustment=adjustment)

        logger.info("下载完成!")

    except KeyboardInterrupt:
        logger.warning("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}\n{traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    app()

