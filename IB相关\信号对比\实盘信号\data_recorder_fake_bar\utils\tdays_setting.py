from WindPy import *
w.start()



# 当天日期，格式为YYYY-MM-DD
import datetime
today_str = datetime.date.today().strftime("%Y-%m-%d")
# 去年的今天
last_year_today_str = (datetime.date.today() - datetime.timedelta(days=365)).strftime("%Y-%m-%d")
# 第二年的今天
next_year_today_str = (datetime.date.today() + datetime.timedelta(days=365)).strftime("%Y-%m-%d")
# TradingCalendar默认为空，指上海证券交易所
# 上海证券交易所
SH_tdays_list = w.tdays(last_year_today_str, next_year_today_str).Data[0]

NYSE_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=NYSE").Data[0]
AMEX_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=AMEX").Data[0]
NASDAQ_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=NASDAQ").Data[0]
# 三者的并集
SMART_tdays_list = list(set(NYSE_tdays_list + AMEX_tdays_list + NASDAQ_tdays_list))

SHFE_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=SHFE").Data[0]
HKEX_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=HKEX").Data[0]

COMEX_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=COMEX").Data[0]
NYMEX_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=NYMEX").Data[0]
CBOT_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=CBOT").Data[0]
CME_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=CME").Data[0]
LME_tdays_list = w.tdays(last_year_today_str, next_year_today_str, "TradingCalendar=LME").Data[0]



# 保存到json文件，注意，json文件中的日期格式为YYYYMMDD
import json
tdays_dict = {
    "SHSTK": [x.strftime("%Y%m%d") for x in SH_tdays_list],
    "SMART": [x.strftime("%Y%m%d") for x in SMART_tdays_list],
    "COMEX": [x.strftime("%Y%m%d") for x in COMEX_tdays_list],
    "NYMEX": [x.strftime("%Y%m%d") for x in NYMEX_tdays_list],
    "CBOT": [x.strftime("%Y%m%d") for x in CBOT_tdays_list],
    "CME": [x.strftime("%Y%m%d") for x in CME_tdays_list],
    "LME": [x.strftime("%Y%m%d") for x in LME_tdays_list],
    "SHFE": [x.strftime("%Y%m%d") for x in SHFE_tdays_list],
    "HKEX": [x.strftime("%Y%m%d") for x in HKEX_tdays_list]
}

with open("../../.vntrader/tdays_dict.json", "w+") as f:
    json.dump(tdays_dict, f, indent=4)


# # 读取json文件，注意，json文件中的日期格式为YYYYMMDD
# with open("../setting/tdays_dict.json", "r") as f:
#     tdays_dict = json.load(f)
# # 日期格式转换
# TDAYS = {key: [datetime.datetime.strptime(x, "%Y%m%d") for x in tdays_dict[key]] for key in tdays_dict}
# print(TDAYS)