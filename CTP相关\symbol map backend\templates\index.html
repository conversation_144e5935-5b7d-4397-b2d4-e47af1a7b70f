{% extends "base.html" %}

{% block content %}
<h2>Main Contract Management</h2>

<!-- 添加搜索框 -->
<form method="GET" action="{{ url_for('index') }}" class="mb-3">
    <div class="input-group">
        <input type="text" class="form-control" name="search" placeholder="Search main or specific symbol" value="{{ request.args.get('search', '') }}">
        <div class="input-group-append">
            <button class="btn btn-outline-secondary" type="submit">Search</button>
        </div>
    </div>
</form>

<table class="table">
    <thead>
        <tr>
            <th>Main Symbol</th>
            <th>Specific Symbol</th>
            <th>Update Time</th>
            <th>Create User</th>
        </tr>
    </thead>
    <tbody>
        {% for contract in contracts %}
        <tr>
            <td>{{ contract.main_symbol }}</td>
            <td>
                {% if current_user.is_authenticated %}
                    <a href="#" class="editable" data-type="text" data-pk="{{ contract.id }}" data-url="{{ url_for('update_specific_symbol') }}" data-title="Enter specific symbol">{{ contract.specific_symbol }}</a>
                {% else %}
                    {{ contract.specific_symbol }}
                {% endif %}
            </td>
            <td>{{ contract.update_time }}</td>
            <td>{{ contract.createUser }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<a href="{{ url_for('update_contract') }}" class="btn btn-primary">Add/Update Contract</a>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $.fn.editable.defaults.mode = 'inline';
    $('.editable').editable({
        success: function(response, newValue) {
            if(response.status == 'error') return response.msg;
        }
    });
});
</script>
{% endblock %}
