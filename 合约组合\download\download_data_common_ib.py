import configparser
from datetime import datetime
from time import sleep
from retry import retry
from vnpy.event import EventEngine
from vnpy.trader.constant import Interval
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import HistoryRequest
from vnpy.trader.object import SubscribeRequest
from vnpy.trader.utility import extract_vt_symbol
from vnpy_ib.ib_gateway import IbGateway
# Initialize the configparser
config = configparser.ConfigParser()
# Read the INI file
config.read('download_ib.ini')
IB_setting = {"TWS地址": "localhost", "TWS端口": 4002, "客户号": 20, "交易账户": "", "查询期权": "否"}
# Initialize the database
db = get_database()

def download_bar_data(engine, vt_symbol, req, gateway_name='IB'):
    @retry(tries=3, delay=3)
    def _get_contract():
        contract = engine.get_contract(vt_symbol)
        if not contract:
            raise ValueError(f"Contract {vt_symbol} not found")
        return contract
    contract = engine.get_contract(vt_symbol)
    if not contract:
        sub_req = SubscribeRequest(symbol=req.symbol, exchange=exchange)
        engine.subscribe(sub_req, gateway_name)
        sleep(1)
        contract = _get_contract()
    print(f'contract: {contract}')
    data = engine.query_history(req, contract.gateway_name)
    return data

event_engine = EventEngine()
main_engine = MainEngine(event_engine)
main_engine.add_gateway(IbGateway)
main_engine.connect(IB_setting, "IB")

# Iterate through each contract section
for contract_section in config.sections():
    contract_symbol = extract_vt_symbol(contract_section)[0]
    sub_contracts = config.get(contract_section, 'sub_contracts').split(', ')
    history_data = []
    for sub_contract in sub_contracts:
        start_key = f"{sub_contract}.start"
        end_key = f"{sub_contract}.end"
        start = config.get(contract_section, start_key)
        end = config.get(contract_section, end_key)

        # 解析为datetime格式
        start = datetime.strptime(start, '%Y-%m-%d').replace(hour=17, tzinfo=DB_TZ)
        if end == 'now':
            end = datetime.now().replace(tzinfo=DB_TZ)
        else:
            end = datetime.strptime(end, '%Y-%m-%d').replace(hour=17, tzinfo=DB_TZ)

        symbol, exchange = extract_vt_symbol(sub_contract.strip())
        print(f"downloading {symbol} {exchange} {start} {end}")
        his_req = HistoryRequest(symbol=symbol, exchange=exchange, interval=Interval.MINUTE, start=start, end=end, )
        # bars = dfeed.query_bar_history(his_req)
        bars = download_bar_data(main_engine, sub_contract, his_req)
        for bar in bars:
            bar.symbol = contract_symbol
        history_data.extend(bars)

    if history_data:
        db.save_bar_data(history_data)

main_engine.close()
