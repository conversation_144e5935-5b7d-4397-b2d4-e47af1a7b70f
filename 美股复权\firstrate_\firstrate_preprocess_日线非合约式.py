import os
import sys
import warnings
from datetime import datetime
from dateutil.relativedelta import relativedelta
from functools import lru_cache
from typing import Optional, Tuple, List
from collections import defaultdict
from copy import copy
import pandas as pd
import pandas_market_calendars as mcal
import typer
from time import sleep
from loguru import logger

warnings.filterwarnings("ignore")

# 配置loguru
# logger.remove()  # 移除默认的处理器
# 添加文件输出处理器
logger.add(
    "logs/firstrate_preprocess_{time:YYYYMMDD}.log",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",  # 每天零点切换新文件
    retention="30 days"  # 保留7天的日志
)

import queue
import threading
import traceback
import concurrent.futures
import signal

# Global flag to indicate if the program is being terminated
is_terminating = False

# Signal handler function
def handle_termination_signal(signum, frame):
    """Handle termination signals (SIGTERM, SIGINT)"""
    global is_terminating
    logger.warning(f"收到终止信号 {signum}，正在优雅关闭...")
    is_terminating = True
    # 不在这里调用 stop，让主线程处理清理工作

# Register signal handlers
signal.signal(signal.SIGTERM, handle_termination_signal)
signal.signal(signal.SIGINT, handle_termination_signal)

# 引入vnpy和数据库
from vnpy.trader.utility import get_file_path, ZoneInfo
from vnpy.trader.setting import SETTINGS
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData
from vnpy.trader.database import DB_TZ
from vnpy.trader.database import BaseDatabase
from peewee import Model

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

# 引入富途复权因子和FirstRate复权因子
from utils.database_manager import FutuRehab, db_manager, FirstrateRehab, IbProduct

from utils.mysql_database import create_mysql_database

# 导入get_latest_conid中的NA_VALUES
from utils.mixin import NA_VALUES

# 时区定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时间

class DatabaseSaver:
    """数据库保存管理器，使用多线程处理数据保存"""

    def __init__(self, database: BaseDatabase, num_threads=10):
        """初始化"""
        self.queue = queue.Queue()  # 使用单个队列处理所有线程的任务
        self.mysql_database = database
        self.active = False
        self.threads = [
            threading.Thread(target=self._save_worker, args=(i,), daemon=True)
            for i in range(num_threads)
        ]
        self._processed_count = 0
        self._total_count = 0
        self._lock = threading.Lock()  # 用于保护计数器

    def start(self):
        """启动保存线程"""
        if not self.active:
            self.active = True
            for thread in self.threads:
                thread.start()

    def stop(self):
        """停止保存线程"""
        if self.active:
            # 发送停止信号
            for _ in self.threads:
                self.queue.put(None)

            # 等待队列中的所有任务处理完成
            logger.info("等待数据保存完成...")
            self.queue.join()
            logger.info("所有数据已处理完成")

            self.active = False

            # 等待所有线程结束
            for thread in self.threads:
                if thread.is_alive():
                    thread.join()

            logger.info(f"数据保存已完成，共处理 {self._processed_count} 条数据")

    def put_bars(self, bars: List[BarData], symbol: str = None):
        """添加K线数据到保存队列"""
        if not self.active:
            self.start()

        with self._lock:
            self._total_count += len(bars)

        # 如果队列中的任务超过一定数量，则阻塞等待
        while self.queue.qsize() >= 100:
            logger.warning(f"队列中任务数量已达限制，等待处理... 当前队列大小: {self.queue.qsize()}")
            sleep(1)  # 等待1秒

        self.queue.put((bars, symbol))

    def _save_worker(self, worker_id: int):
        """保存工作线程"""
        while self.active:
            try:
                data = self.queue.get(timeout=1)

                if data is None:
                    self.queue.task_done()
                    break

                bars, symbol = data

                # 添加重试逻辑，不限制重试次数
                retry_count = 0
                while True:
                    try:
                        self.mysql_database.save_bar_data(bars)
                        break  # 成功则跳出重试循环
                    except Exception as e:
                        # 检查是否是死锁错误或其他需要重试的错误
                        # if "Deadlock found when trying to get lock" in error_str or "Lock wait timeout exceeded" in error_str:
                        if 'lock' in str(e).lower():
                            retry_count += 1
                            wait_time = min(0.5 * (2 ** retry_count), 30)  # 指数退避策略，最大等待30秒
                            logger.warning(f"线程 {worker_id} 遇到数据库锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                            sleep(wait_time)

                            # 重新创建数据对象
                            for bar in bars:
                                bar.exchange = Exchange(bar.exchange)
                                bar.interval = Interval(bar.interval)
                                bar.gateway_name = None
                                bar.vt_symbol = None
                        else:
                            # 非死锁错误直接抛出
                            raise

                with self._lock:
                    self._processed_count += len(bars)

                # 打印进度
                if symbol:
                    logger.info(f"线程 {worker_id} 已保存 {symbol} 的 {len(bars)} 条数据，总进度: {self._processed_count}/{self._total_count}")

                self.queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"保存数据时出错: {str(e)}\n{traceback.format_exc()}")


class FirstratePreprocessor:
    """FirstRate数据预处理器，负责数据筛选、时间对齐、复权等操作"""

    def __init__(self, data_dir="S:\\firstrate\\stock", data_path="full_1min", start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None, db_threads=10, process_threads=10, ignore_overview=False, use_database=False,
                 interval: Interval = Interval.MINUTE, is_test_mode: bool = False, database_name: Optional[str] = None):
        """初始化

        Args:
            data_dir: FirstRate数据所在的基础目录
            data_path: FirstRate数据文件夹名称，默认为"full_1min"，可选"month_1min"等
            start_date: 开始日期，只处理该日期之后的数据
            end_date: 结束日期，只处理该日期及之前的数据
            db_threads: 数据库保存线程数，默认为10
            process_threads: 标的处理线程数，默认为10
            ignore_overview: 是否忽略数据库中的overview信息，强制全量覆盖
            use_database: 是否使用数据库模式
            interval: 时间间隔，支持分钟线(MINUTE)和日线(DAILY)
            is_test_mode: 是否为测试模式
            database_name: 数据库名称，如果为None则根据interval自动选择
        """
        # 文件路径定义
        self.base_data_dir = data_dir
        self.data_path = data_path
        self.firstrate_dir = os.path.join(self.base_data_dir, self.data_path)
        self.start_date = start_date
        self.end_date = end_date
        self.process_threads = process_threads
        self.ignore_overview = ignore_overview
        self.use_database = use_database
        self.interval = interval
        self.is_test_mode = is_test_mode

        # 创建数据库连接
        settings = SETTINGS.copy()
        if database_name:
            settings['database.database'] = database_name
        elif interval == Interval.DAILY:
            settings['database.database'] = 'vnpy_stk_us_d'
        logger.info(f"使用数据库: {settings['database.database']}")
        
        # 创建数据库实例和获取Model类
        self.database, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(settings)

        # 创建原始数据库连接
        raw_settings = settings.copy()
        raw_settings['database.database'] = 'vnpy_stk_us_ib'
        self.raw_database, self.RawDbBarData, self.RawDbTickData, self.RawDbBarOverview, self.RawDbTickOverview = create_mysql_database(raw_settings)

        self.nyse = mcal.get_calendar('NYSE')

        # 匹配文件
        self.matched_file = f'matched_symbols_{data_path}.csv'

        # 加载匹配的标的
        self.matched_df = self._load_matched_symbols()

        # 加载 FirstrateRehab 数据，用于检查复权因子缺失
        self.firstrate_rehab_symbols = self._load_firstrate_rehab_symbols()

        # 加载复权因子
        self.rehab_factors = self._load_all_rehab_factors()
        logger.debug(f'测试获取AAPL复权因子：')
        logger.debug(self.get_futu_rehab_factors(265598, "AAPL"))

        # 显示时区信息
        logger.info(f"数据库时区: {DB_TZ}")
        logger.info(f"使用的美东时区: {ET_TZ}")
        logger.info(f"数据间隔: {interval.value}")
        if start_date:
            logger.info(f"只处理 {start_date.strftime('%Y-%m-%d')} 之后的数据")
        if end_date:
            logger.info(f"只处理 {end_date.strftime('%Y-%m-%d')} 及之前的数据")

        # 创建数据保存管理器并启动
        self.database_saver = DatabaseSaver(self.database, db_threads)
        self.database_saver.start()
        self.overviews = [] if ignore_overview or self.interval == Interval.DAILY else self.database_saver.mysql_database.get_bar_overview()

    def _load_matched_symbols(self) -> pd.DataFrame:
        """加载匹配后的标的列表"""
        if self.use_database:
            # 从数据库直接获取数据
            query = IbProduct.select().where(IbProduct.is_latest == True)
            rows = [(row.conid, row.symbol, row.symbol) for row in query]
            matched_df = pd.DataFrame(rows, columns=['conid', 'ib_symbol', 'firstrate_symbol'])
            logger.info(f"从数据库加载了 {len(matched_df)} 条匹配记录")
            return matched_df
        else:
            # 从CSV文件加载数据
            matched_file_path = get_file_path(self.matched_file)

            if not os.path.exists(matched_file_path):
                logger.error(f"匹配文件 {matched_file_path} 不存在，请先运行 get_latest_conid.py")
                return pd.DataFrame()

            matched_df = pd.read_csv(
                matched_file_path,
                na_values=NA_VALUES,
                keep_default_na=False
            )

            logger.info(f"从文件加载了 {len(matched_df)} 条匹配记录")
            return matched_df

    @lru_cache(maxsize=999)
    def get_previous_trading_day(self, current_date):
        """获取给定日期的上一个美股交易日"""
        trading_days = self.nyse.schedule(start_date=current_date - pd.Timedelta(days=10), end_date=current_date)
        if len(trading_days) < 2:  # 确保至少有两个交易日
            trading_days = self.nyse.schedule(start_date=current_date - pd.Timedelta(days=20), end_date=current_date)
        return trading_days.index[-2].date()  # 返回倒数第二个交易日

    @lru_cache(maxsize=999)
    def generate_trading_minutes(self, start_date_str: str, end_date_str: str) -> List[pd.Timestamp]:
        """生成指定日期范围内的交易分钟时间戳"""
        # 美股交易日历
        schedule = self.nyse.schedule(start_date=start_date_str, end_date=end_date_str)
        if schedule.empty:
            return []

        minutes = mcal.date_range(schedule, frequency='1Min', closed='left', force_close=False)
        return [ts.astimezone(ET_TZ) for ts in minutes]

    def load_firstrate_data(self, symbol: str, query_start_date: Optional[datetime] = None) -> pd.DataFrame:
        """加载FirstRate数据

        Args:
            symbol: FirstRate标的代码
            query_start_date: 数据查询（美东时区）开始日期，如果指定了，则只加载该日期之后的数据
        """
        file_path = os.path.join(self.firstrate_dir, f"{symbol}_{self.data_path}_UNADJUSTED.txt")

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return pd.DataFrame()

        # 读取数据，格式：DateTime,Open,High,Low,Close,Volume
        df = pd.read_csv(
            file_path,
            header=None,
            names=["datetime", "open", "high", "low", "close", "volume"],
            parse_dates=["datetime"],
            na_values=NA_VALUES,
            keep_default_na=False
        )

        # 规范化日期时间格式，FirstRate数据默认是美东时间
        df["datetime"] = pd.to_datetime(df["datetime"]).dt.tz_localize(ET_TZ)

        # 确定最终的过滤日期（取两者中较晚的日期）
        effective_date = None
        if self.start_date and query_start_date:
            effective_date = max(self.start_date, query_start_date)
        elif self.start_date:
            effective_date = self.start_date
        elif query_start_date:
            effective_date = query_start_date

        # 应用日期过滤条件
        if effective_date:
            df = df[df["datetime"] > effective_date]
            if df.empty:
                logger.warning(f"在 {effective_date} 之后无数据: {symbol}")
                return df

        # 应用结束日期过滤条件
        end_date = self.end_date if self.end_date else datetime.now(ET_TZ)
        df = df[df["datetime"] <= end_date]
        if df.empty:
            logger.warning(f"在 {end_date} 之前无数据: {symbol}")
            return df

        # 在设置索引前处理重复时间戳，保留第一条记录
        df = df.drop_duplicates(subset=["datetime"], keep="first")

        # 设置索引
        df.set_index("datetime", inplace=True)

        return df

    def load_raw_database_data(self, symbol: str, query_start_date: Optional[datetime] = None, exchange: Exchange = Exchange.SMART) -> pd.DataFrame:
        """从数据库加载数据

        Args:
            symbol: 数据库中的symbol
            query_start_date: 数据查询（无时区信息）开始日期，如果指定了，则只加载该日期之后的数据
            exchange: 交易所
        """
        # 确定查询的开始和结束日期
        end_date = self.end_date.astimezone(DB_TZ) if self.end_date else datetime.now(DB_TZ)
        if query_start_date is None:
            # 如果是data_path是full_1min就是从start_date开始，month_1min就是-1个月
            if self.data_path == "full_1min":
                query_start_date = self.start_date.astimezone(DB_TZ)
            elif self.data_path == "month_1min":
                query_start_date = end_date - relativedelta(months=1)
            else:
                # 其他情况
                query_start_date = end_date - relativedelta(days=int(self.data_path))

        # 从数据库加载数据
        bars = self.raw_database.load_bar_data(
            symbol,
            exchange,
            self.interval,
            query_start_date.replace(tzinfo=None),  # 数据库查询需要无时区信息的时间
            end_date.replace(tzinfo=None)
        )
        
        if not bars:
            logger.warning(f"数据库中无数据: {symbol}")
            return pd.DataFrame()
            
        # 检查第一条数据的时间是否等于query_start_date，如果是则删除
        first_bar = bars[0]
        first_bar_time = first_bar.datetime
        if first_bar_time == query_start_date:
            bars = bars[1:]
            if not bars:  # 如果删除后没有数据了
                logger.warning(f"删除起始时间重复数据后无数据: {symbol}")
                return pd.DataFrame()
            
        # 将数据转换为DataFrame
        data = {
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }
        timestamps = []
        
        for bar in bars:
            # 数据库中时间是上海时间(无时区信息)，需要转换为美东时间
            # 先将数据库时间视为上海时间并添加时区信息，然后转换为美东时间
            dt = bar.datetime.replace(tzinfo=DB_TZ).astimezone(ET_TZ)
            timestamps.append(dt)
            data['open'].append(bar.open_price)
            data['high'].append(bar.high_price)
            data['low'].append(bar.low_price)
            data['close'].append(bar.close_price)
            data['volume'].append(bar.volume)
        
        # 创建DataFrame
        df = pd.DataFrame(data, index=timestamps)
        
        # 如果设置了开始日期，先过滤数据
        if self.start_date:
            df = df[df.index >= self.start_date]
            if df.empty:
                logger.warning(f"该时间段内无数据: {symbol}")
                return df
                
        # 处理重复时间戳，保留第一条记录
        df = df[~df.index.duplicated(keep='first')]
        
        # 按时间排序
        df.sort_index(inplace=True)
        
        return df

    def _load_firstrate_rehab_symbols(self) -> set:
        """加载 FirstrateRehab 数据中的所有标的代码，筛选规则与futu一致"""
        query = (FirstrateRehab
                .select(FirstrateRehab.symbol, FirstrateRehab.ex_div_date)
                .where(FirstrateRehab.ex_div_date >= self.start_date.replace(tzinfo=None) if self.start_date else True)
                .distinct())
        return {(record.symbol, record.ex_div_date) for record in query}

    def _load_all_rehab_factors(self) -> pd.DataFrame:
        """一次性加载所有复权因子"""
        # 获取所有需要的标的代码和对应的conid
        symbol_conid_map = {}  # futu_code -> conid的映射
        firstrate_symbol_map = {}  # futu_code -> firstrate_symbol的映射
        for _, row in self.matched_df.iterrows():
            futu_code = f"US.{row['ib_symbol'].replace(' ', '.')}"
            symbol_conid_map[futu_code] = row['conid']
            firstrate_symbol_map[futu_code] = row['firstrate_symbol']

        # 批量查询复权因子
        if not symbol_conid_map:
            return pd.DataFrame()

        # 一次性查询所需列
        query = (FutuRehab
                .select(FutuRehab.code, FutuRehab.ex_div_date, FutuRehab.forward_adj_factorA)
                .where(
                    (FutuRehab.code.in_(list(symbol_conid_map.keys()))) &
                    (FutuRehab.ex_div_date >= self.start_date.replace(tzinfo=None) if self.start_date else True)
                ))

        # 转换为DataFrame
        records = []
        for record in query:
            records.append({
                'conid': symbol_conid_map[record.code],
                'ex_div_date': record.ex_div_date,
                'forward_adj_factorA': record.forward_adj_factorA
            })

        df = pd.DataFrame(records)
        if not df.empty:
            df.set_index(['conid', 'ex_div_date'], inplace=True)
            df.sort_index(inplace=True)

        # 检查哪些标的在firstrate有复权数据但在futu中没有
        futu_symbols_with_rehab = set(df.index.get_level_values(0)) if not df.empty else set()
        firstrate_symbols_with_rehab = self.firstrate_rehab_symbols

        missing_rehab_symbols = []
        for futu_code, firstrate_symbol in firstrate_symbol_map.items():
            if firstrate_symbol in firstrate_symbols_with_rehab:
                conid = symbol_conid_map[futu_code]
                if conid not in futu_symbols_with_rehab:
                    missing_rehab_symbols.append((futu_code, conid, firstrate_symbol))

        logger.info(f"已加载 {len(df)} 条复权因子记录，{len(df.index.unique(level=0))} 个标的")
        logger.debug(f"复权因子数据: {df.head()}")
        if missing_rehab_symbols:
            logger.warning(f"\n警告: 发现 {len(missing_rehab_symbols)} 个标的在FirstRate有复权数据但在富途中缺失:")
            for futu_code, conid, firstrate_symbol in missing_rehab_symbols:
                logger.warning(f"  - {futu_code} (conid: {conid}, firstrate: {firstrate_symbol})")

        return df

    def get_futu_rehab_factors(
        self,
        conid: int,
        firstrate_symbol: str,
        overview_end: Optional[datetime] = None,
        data_end_date: Optional[datetime] = None
    ) -> Tuple[pd.Series, bool]:
        """获取复权因子
        Args:
            conid: 合约ID
            firstrate_symbol: FirstRate标的代码
            overview_end: 数据库中已有数据的结束日期，只返回该日期之后的复权因子
            data_end_date: 数据的最新日期，不返回该日期之后的下一个交易日的复权因子
        Returns:
            Tuple[pd.Series, bool]: (复权因子序列, 是否缺失复权数据)
        """
        factors = pd.Series()
        missing = False

        if not self.rehab_factors.empty and conid in self.rehab_factors.index.get_level_values(0):
            factors = self.rehab_factors.loc[conid, 'forward_adj_factorA']
            # 如果指定了overview_end，只保留复权因子日期前一个交易日大于overview_end的因子
            if overview_end:
                # 修改这里：确保日期类型一致
                overview_end_date = pd.Timestamp(overview_end.date())
                factors = factors[factors.index.map(lambda x: pd.Timestamp(self.get_previous_trading_day(x.date()))) > overview_end_date]
            # 如果指定了data_end_date，只保留复权因子日期的前一个交易日不大于data_end_date的因子
            if data_end_date:
                # 修改这里：确保日期类型一致
                end_date = pd.Timestamp(data_end_date.date())
                factors = factors[factors.index.map(lambda x: pd.Timestamp(self.get_previous_trading_day(x.date()))) <= end_date]
        else:
            # 检查firstrate中是否有对应时间范围的复权数据
            firstrate_rehab_dates = {date for symbol, date in self.firstrate_rehab_symbols if symbol == firstrate_symbol}
            if firstrate_rehab_dates:
                # 如果指定了overview_end，只保留复权因子日期前一个交易日大于overview_end的因子
                if overview_end:
                    # 修改这里：确保日期类型一致
                    overview_end_date = pd.Timestamp(overview_end.date())
                    firstrate_rehab_dates = {date for date in firstrate_rehab_dates
                                          if pd.Timestamp(self.get_previous_trading_day(date.date())) > overview_end_date}
                # 如果指定了data_end_date，只保留复权因子日期的前一个交易日不大于data_end_date的因子
                if data_end_date:
                    # 修改这里：确保日期类型一致
                    end_date = pd.Timestamp(data_end_date.date())
                    firstrate_rehab_dates = {date for date in firstrate_rehab_dates
                                          if pd.Timestamp(self.get_previous_trading_day(date.date())) <= end_date}
                if firstrate_rehab_dates:
                    missing = True

        return factors, missing

    def process_symbol(self, row: pd.Series):
        """处理单个标的数据"""
        conid = row['conid']
        ib_symbol = row['ib_symbol']
        firstrate_symbol = row['firstrate_symbol']
        
        logger.info(f"\n处理标的: {ib_symbol} (conid: {conid}), FirstRate标的: {firstrate_symbol}")

        assert type(conid) == int

        overview = next((o for o in self.overviews if o.symbol == str(conid) and o.exchange == Exchange.SMART and o.interval == self.interval), None)
        overview_end_et = overview_end_et_none = None
        if overview:
            overview_end_et = overview.end.replace(tzinfo=DB_TZ).astimezone(ET_TZ)
            overview_end_et_none = overview_end_et.replace(tzinfo=None)  # 去除时区信息
        
        # 根据模式选择加载数据的方法
        if self.use_database:
            df = self.load_raw_database_data(str(conid), query_start_date=overview.end if overview else None)
        else:
            df = self.load_firstrate_data(firstrate_symbol, query_start_date=overview_end_et)
            
        if df.empty:
            logger.warning(f"无加载数据: {firstrate_symbol}")
            return False

        # 4. 根据时间间隔处理数据
        if self.interval == Interval.MINUTE:
            # 分钟线数据处理
            # 2. 获取数据的日期范围并生成交易分钟
            trading_minutes = self.generate_trading_minutes(
                df.index.min().strftime("%Y-%m-%d"),
                df.index.max().strftime("%Y-%m-%d")
            )

            if not trading_minutes:
                logger.warning(f"该时间段内无交易日: {firstrate_symbol}")
                return False

            # 3. 使用完整时间索引重新索引数据
            full_df = df.reindex(trading_minutes)

            # 4. 按日期分组填充收盘价
            full_df['close'] = full_df.groupby(full_df.index.date)['close'].ffill()
        else:
            full_df = df

        # 5.删除开盘初期没有数据的行（收盘价为NaN的行）
        full_df = full_df.dropna(subset=['close'])

        if full_df.empty:
            logger.warning(f"处理后无有效数据: {firstrate_symbol}")
            return False

        # 6. 用收盘价填充其他价格字段
        for col in ['open', 'high', 'low']:
            full_df[col] = full_df[col].fillna(full_df['close'])

        # 7. 成交量用0填充
        full_df['volume'] = full_df['volume'].fillna(0)

        # 获取数据的最新日期
        data_end_date = full_df.index.max()

        # 保存不复权数据到数据库
        # # 8. 转换为BarData对象
        # all_bars = []
        # for idx, row_data in full_df.iterrows():
        #     bar = BarData(
        #         symbol=str(conid),
        #         exchange=Exchange.SMART,
        #         datetime=idx.to_pydatetime(),
        #         interval=Interval.MINUTE,
        #         volume=float(row_data['volume']),
        #         open_price=float(row_data['open']),
        #         high_price=float(row_data['high']),
        #         low_price=float(row_data['low']),
        #         close_price=float(row_data['close']),
        #         turnover=0.0,
        #         open_interest=0.0,
        #         gateway_name="FIRSTRATE"
        #     )
        #     all_bars.append(bar)

            # # 9. 保存到数据库
        # if all_bars:
        #     self.database_saver.put_bars(all_bars, ib_symbol)
        #     print(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
        #     return True
        # else:
        #     print(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
        #     return False

        # 保存合约式多symbol复权数据到数据库
        '''
        合约式美股复权并入库的代码（基于get_futu_rehab_factors取回来的factors，series的索引应该是ex_div_date，值是forward_adj_factorA），每次前复权多一条symbol的历史数据（命名以'conid_{ex_div_date日期前一个交易日%y%m%d}'：
        0. get_bar_overview获取数据库overview
        1. 如果无factors，symbol无需改名直接入库
        2. 否则：
            2.1. load_bar_data加载symbol数据（如果有）：结束时间(a)为overview的end，起始时间为end的4月前的第一天（- relativedelta(months=4)).replace(day=1)）
                2.1.1. "更新原始数据的symbol"，symbol由"conid"改为"conid_{ex_div_date[0]前一个交易日}"
                2.1.2. 将结束时间(a)到ex_div_date[0]前一个交易日的数据，存入数据库，symbol也为"conid_{ex_div_date[0]前一个交易日}"
            2.2. 开始遍历从"end的4月前的第一天"开始的数据的每行i
                2.2.1. 开始遍历factors序列的每行j
                    2.2.1.1. 如果ex_div_date[j+1]还有，且 "conid_{ex_div_date[j]前一个交易日 的4月前的第一天}" <= i的日期 <ex_div_date[j+1]
                        2.2.1.1.1. 当行开高低收*forward_adj_factorA[j]，voume除以forward_adj_factorA[j]，以symbol为"conid_{ex_div_date[j+1]前一个交易日}"入库
                    2.2.1.2.  如果没有ex_div_date[j+1]了
                        2.2.1.2.1. 如果<ex_div_date[j]：当行开高低收*forward_adj_factorA[j]，voume除以forward_adj_factorA[j]，以symbol为"conid"入库
                        2.2.1.2.2. 如果>=ex_div_date[j]：当行开高低收voume以原始数据，以symbol为"conid"入库
        '''

        # 8. 获取数据库中的数据概览
        if overview:
            # 时区处理：
            # 1. 对于load_bar_data，使用原始的overview.end（带DB_TZ时区）
            # 2. 对于get_futu_rehab_factors，将overview.end转换为ET_TZ后去除时区信息
            factors, missing = self.get_futu_rehab_factors(conid, firstrate_symbol, overview_end_et_none, data_end_date)
        else:
            # 如果没有overview，获取所有复权因子（但仍然需要限制在数据结束日期之前）
            factors, missing = self.get_futu_rehab_factors(conid, firstrate_symbol, None, data_end_date)

        if missing:
            logger.warning(f"缺失复权因子: {firstrate_symbol}")
            return False
        
        if self.is_test_mode:
            logger.info(f"复权因子信息 - {firstrate_symbol} (conid: {conid}): \n{factors}")

        # 9. 如果没有复权因子，直接用原始symbol入库
        if factors.empty: # 情况0
            # 将数据转换为BarData对象并保存
            all_bars = []
            for row in full_df.itertuples():
                bar = BarData(
                    symbol=str(conid),
                    exchange=Exchange.SMART,
                    datetime=row.Index.to_pydatetime(),
                    interval=self.interval,
                    volume=float(row.volume),
                    open_price=float(row.open),
                    high_price=float(row.high),
                    low_price=float(row.low),
                    close_price=float(row.close),
                    turnover=0.0,
                    open_interest=0.0,
                    gateway_name="FIRSTRATE"
                )
                all_bars.append(bar)

            # 保存到数据库
            if all_bars:
                self.database_saver.put_bars(all_bars, ib_symbol)
                logger.info(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
                return True
            else:
                logger.info(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
                return False
            
        if self.interval == Interval.MINUTE:
            # 10. 如果有overview，更新原始数据的symbol
            # 获取第一个除权日前一个交易日
            first_ex_date = factors.index[0]
            first_ex_date_et = first_ex_date.replace(tzinfo=ET_TZ)
            prev_trading_day = self.get_previous_trading_day(first_ex_date)
            new_symbol = f"{conid}_{prev_trading_day.strftime('%y%m%d')}"
            if overview: # 情况1（无交叉）、2（交叉多次后续复权所需窗口）
                # 更新数据库中的symbol
                # 添加重试逻辑，不限制重试次数
                retry_count = 0
                update_count = 0

                while True:
                    try:
                        with self.database_saver.mysql_database.db.atomic():
                            update_count = (self.DbBarData
                            .update(symbol=new_symbol)
                            .where(
                                (self.DbBarData.symbol == str(conid)) &
                                (self.DbBarData.exchange == Exchange.SMART.value))
                            .execute())

                            # 更新视图表
                            (self.DbBarOverview
                            .update(symbol=new_symbol)
                            .where(
                                (self.DbBarOverview.symbol == str(conid)) &
                                (self.DbBarOverview.exchange == Exchange.SMART.value))
                            .execute())
                        break  # 成功则跳出重试循环
                    except Exception as e:
                        # 检查是否是锁错误
                        if 'lock' in str(e).lower():
                            retry_count += 1
                            wait_time = min(0.5 * (2 ** retry_count), 30)  # 指数退避策略，最大等待30秒
                            logger.warning(f"更新symbol时遇到数据库锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                            sleep(wait_time)
                        else:
                            # 非锁错误直接抛出
                            raise
                logger.info(f"更新完成: {conid} -> {new_symbol}, 更新记录数: {update_count}")

            # 保存从full_df的起点到第一个除权日之前的数据到数据库
            # 因为在load_firstrate_data中已经过滤了数据，这里不需要再根据overview_end_et截断
            period_df = full_df[full_df.index < first_ex_date_et]

            if not period_df.empty: # 情况3（数据库无，有新无需复权数据）
                # 将数据转换为BarData对象
                period_bars = []
                for row in period_df.itertuples():
                    bar = BarData(
                        symbol=new_symbol,
                        exchange=Exchange.SMART,
                        datetime=row.Index.to_pydatetime(),
                        interval=self.interval,
                        volume=float(row.volume),
                        open_price=float(row.open),
                        high_price=float(row.high),
                        low_price=float(row.low),
                        close_price=float(row.close),
                        turnover=0.0,
                        open_interest=0.0,
                        gateway_name="FIRSTRATE"
                    )
                    period_bars.append(bar)

                # 保存到数据库
                if period_bars:
                    self.database_saver.put_bars(period_bars, f"{ib_symbol}_{new_symbol}")
                    logger.info(f"已将数据以新symbol {new_symbol} 加入保存队列，数据量: {len(period_bars)}")

            # 11. 初始化存储不同时间段数据的字典
            bars_dict = defaultdict(list)

            # 12. 如果有overview，加载并拼接原始数据
            if overview:
                # 确定加载的时间
                # full_df_min_dbtz = full_df.index.min().astimezone(DB_TZ).replace(tzinfo=None) # 先ET_TZ转换时区为DB_TZ，再去除时区信息
                # end_date = min(overview.end, full_df_min_dbtz)
                # start_date = max(end_date.replace(tzinfo=DB_TZ).astimezone(ET_TZ), first_ex_date_et)
                start_date = max(overview_end_et, first_ex_date_et)
                start_date = (start_date - relativedelta(months=4)).astimezone(DB_TZ).replace(day=1, hour=12, minute=0, second=0, microsecond=0, tzinfo=None)

                # 只有当start_date小于end_date时才加载数据
                if start_date < overview.end:
                    # 加载原始数据，使用new_symbol
                    original_bars = self.database_saver.mysql_database.load_bar_data(
                        new_symbol,
                        Exchange.SMART,
                        self.interval,
                        start_date,
                        overview.end
                    )

                    # 将原始数据转换为DataFrame，并将时区从DB_TZ转换为ET_TZ
                    if original_bars:
                        logger.info(f"加载到原始数据{start_date} -> {overview.end}: {len(original_bars)} 条")
                        original_df = pd.DataFrame({
                            'open': [bar.open_price for bar in original_bars],
                            'high': [bar.high_price for bar in original_bars],
                            'low': [bar.low_price for bar in original_bars],
                            'close': [bar.close_price for bar in original_bars],
                            'volume': [bar.volume for bar in original_bars]
                        }, index=[bar.datetime.replace(tzinfo=DB_TZ).astimezone(ET_TZ) for bar in original_bars])

                        # 拼接数据
                        full_df = pd.concat([original_df, full_df])
                        full_df = full_df[~full_df.index.duplicated(keep='first')]  # 去除重复的时间戳
                        full_df.sort_index(inplace=True)  # 按时间排序

            # 13. 开始处理复权数据
            total_rows = len(full_df)
            logger.info(f"开始处理 {total_rows} 条数据的复权...")

            # 14. 预先计算所有的cutoff_dates以提高性能
            cutoff_dates = {ex_date: (ex_date - relativedelta(months=4)).replace(day=1).date()
                            for ex_date in factors.index}
            # print(factors)

            # 15. 遍历数据并应用复权因子
            for row in full_df.itertuples():
                idx = row.Index
                date = idx.date()

                # 创建基础BarData对象
                base_bar = BarData(
                    symbol=str(conid),  # 临时使用，后面会修改
                    exchange=Exchange.SMART,
                    datetime=idx.to_pydatetime(),
                    interval=self.interval,
                    volume=float(row.volume),
                    open_price=float(row.open),
                    high_price=float(row.high),
                    low_price=float(row.low),
                    close_price=float(row.close),
                    turnover=0.0,
                    open_interest=0.0,
                    gateway_name="FIRSTRATE"
                )

                # 遍历每个复权因子，按时间顺序应用
                for i, (current_ex_date, current_factor) in enumerate(factors.items()):
                    cutoff_date = cutoff_dates[current_ex_date]
                    current_ex_date_ = current_ex_date.date()

                    # 如果还有下一个除权日
                    if i < len(factors) - 1:
                        next_ex_date_ = factors.index[i + 1].date()
                        # 判断完整的日期区间
                        if cutoff_date <= date < current_ex_date_: # 情况4（2的下一轮，无交叉）、5（3的下一轮，与下一轮复权所需时间窗口有交叉）、情况6（5的下一轮）
                            # 情况9（8的下一轮累计复权，仍有后续复权日）
                            # 记录复权前的收盘价
                            # original_close = base_bar.close_price
                            # 累积应用复权因子到base_bar
                            base_bar.volume = base_bar.volume / current_factor
                            base_bar.open_price = base_bar.open_price * current_factor
                            base_bar.high_price = base_bar.high_price * current_factor
                            base_bar.low_price = base_bar.low_price * current_factor
                            base_bar.close_price = base_bar.close_price * current_factor
                            # 只在11点整时打印
                            # if idx.hour == 11 and idx.minute == 0:
                            #     print(f"时间: {idx}, 应用复权因子: {current_ex_date_}({current_factor}), 收盘价: {original_close:.2f} -> {base_bar.close_price:.2f}, 添加到symbol: {conid}_{self.get_previous_trading_day(next_ex_date_).strftime('%y%m%d')}")
                        elif current_ex_date_ <= date < next_ex_date_: # 情况8、11（所属时间窗口的当前轮数据）
                            # 只在11点整时打印
                            # if idx.hour == 11 and idx.minute == 0:
                            #     print(f"时间: {idx}, 无需复权(当前轮数据), 收盘价: {base_bar.close_price:.2f}, 添加到symbol: {conid}_{self.get_previous_trading_day(next_ex_date_).strftime('%y%m%d')}")
                            pass
                        else:
                            continue
                        # 创建base_bar的副本并设置正确的symbol
                        adjusted_bar = copy(base_bar)
                        adjusted_bar.symbol = f"{conid}_{self.get_previous_trading_day(next_ex_date_).strftime('%y%m%d')}"
                        bars_dict[adjusted_bar.symbol].append(adjusted_bar)

                    else:
                        # 最后一个区间需要判断日期与最后一个除权日的关系
                        if cutoff_date <= date < current_ex_date_: # 情况7（6的下一轮，最新复权数据的复权段）、情况10（9的下一轮）、情况12（11的下一轮）
                            # 记录复权前的收盘价
                            # original_close = base_bar.close_price
                            # 如果在最后一个除权日之前，应用复权因子
                            base_bar.volume = base_bar.volume / current_factor
                            base_bar.open_price = base_bar.open_price * current_factor
                            base_bar.high_price = base_bar.high_price * current_factor
                            base_bar.low_price = base_bar.low_price * current_factor
                            base_bar.close_price = base_bar.close_price * current_factor
                            # 只在11点整时打印
                            # if idx.hour == 11 and idx.minute == 0:
                            #     print(f"时间: {idx}, 应用复权因子: {current_ex_date_}({current_factor}), 收盘价: {original_close:.2f} -> {base_bar.close_price:.2f}, 添加到symbol: {conid}")

                            # 创建base_bar的副本并设置正确的symbol
                            adjusted_bar = copy(base_bar)
                            adjusted_bar.symbol = str(conid)  # 最后一个区间使用原始symbol
                            bars_dict[adjusted_bar.symbol].append(adjusted_bar)
                        elif current_ex_date_ <= date: # 情况13（最新复权数据的最新不复权段）
                            # 如果在最后一个除权日之后，使用full_df中的原始数据
                            # 只在11点整时打印
                            # if idx.hour == 11 and idx.minute == 0:
                            #     print(f"时间: {idx}, 无需复权(最新数据), 收盘价: {base_bar.close_price:.2f}, 添加到symbol: {conid}")
                            bars_dict[str(conid)].append(base_bar)

            logger.info(f"复权处理完成，共处理 {total_rows} 条数据")

            # 16. 保存所有时间段的数据
            for symbol, bars in bars_dict.items():
                if bars:
                    self.database_saver.put_bars(bars, f"{ib_symbol}_{symbol}")
                    logger.info(f"已将数据加入保存队列: {ib_symbol}_{symbol}, 数据量: {len(bars)}")

            return True
        
        else:
            # 日线复权实现
            # 遍历数据并应用复权因子
            total_rows = len(full_df)
            logger.info(f"开始处理 {total_rows} 条数据的复权...")

            # 初始化存储复权数据的列表
            all_bars = []

            # 遍历数据并应用复权因子
            for row in full_df.itertuples():
                idx = row.Index
                date = idx.date()

                # 创建基础BarData对象
                base_bar = BarData(
                    symbol=str(conid),  # 日线复权直接使用conid作为symbol
                    exchange=Exchange.SMART,
                    datetime=idx.to_pydatetime(),
                    interval=self.interval,
                    volume=float(row.volume),
                    open_price=float(row.open),
                    high_price=float(row.high),
                    low_price=float(row.low),
                    close_price=float(row.close),
                    turnover=0.0,
                    open_interest=0.0,
                    gateway_name="FIRSTRATE"
                )

                # 遍历每个复权因子，按时间顺序应用
                for i, (current_ex_date, current_factor) in enumerate(factors.items()):
                    current_ex_date_ = current_ex_date.date()

                    # 如果当前日期小于除权日，应用复权因子
                    if date < current_ex_date_:
                        # 应用复权因子
                        base_bar.volume = base_bar.volume / current_factor
                        base_bar.open_price = base_bar.open_price * current_factor
                        base_bar.high_price = base_bar.high_price * current_factor
                        base_bar.low_price = base_bar.low_price * current_factor
                        base_bar.close_price = base_bar.close_price * current_factor
                        # logger.info(f"标的 {conid} 在 {date} 应用除权日 {current_ex_date_} 的复权因子 {current_factor}")

                # 添加到结果列表
                all_bars.append(base_bar)

            # 保存复权后的数据
            if all_bars:
                self.database_saver.put_bars(all_bars, ib_symbol)
                logger.info(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
                return True
            else:
                logger.info(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
                return False

    def process_all(self, conid_list: Optional[List[int]] = None, start_conid: Optional[int] = None,
                   n: Optional[int] = None) -> Tuple[int, int, List[int], List[int]]:
        """处理所有匹配的标的

        Args:
            conid_list: 要处理的conid列表，如果为None则处理所有匹配的标的
            start_conid: 从指定的conid开始处理，用于断点恢复
            n: 处理第1到n个conid（不包含第n个）

        Returns:
            Tuple[int, int, List[int], List[int]]: (成功数量, 失败数量, 成功的conid列表, 失败的conid列表)
        """
        if self.matched_df.empty:
            logger.warning("没有找到匹配的标的")
            return 0, 0, [], []

        # 如果是日线数据，先清空数据库中的日线数据
        if self.interval == Interval.DAILY:
            logger.info("清空数据库中的日线数据...")
            # 使用ORM删除interval为'd'的数据
            with self.database_saver.mysql_database.db.atomic():
                # 删除DbBarData表中的日线数据
                self.DbBarData.delete().where(self.DbBarData.interval == Interval.DAILY.value).execute()
                # 删除DbBarOverview表中的日线数据
                self.DbBarOverview.delete().where(self.DbBarOverview.interval == Interval.DAILY.value).execute()
            logger.info("日线数据清空完成")

        # 如果指定了conid列表，只处理这些conid
        if conid_list:
            self.matched_df = self.matched_df[self.matched_df['conid'].isin(conid_list)]
            if self.matched_df.empty:
                logger.warning(f"未找到指定的conid: {conid_list}")
                return 0, 0, [], []
            logger.info(f"将处理指定的 {len(self.matched_df)} 个标的")
        # 如果指定了start_conid，从该conid开始处理
        elif start_conid:
            start_idx = self.matched_df[self.matched_df['conid'] == start_conid].index
            if len(start_idx) > 0:
                self.matched_df = self.matched_df.loc[start_idx[0]:]
                logger.info(f"从conid {start_conid} 开始处理，剩余 {len(self.matched_df)} 个标的")
            else:
                logger.warning(f"未找到conid {start_conid}，将从头开始处理")
        # 如果指定了n，只处理前n个conid（不包含第n个）
        elif n is not None:
            # 保存第n个conid，用于后续输出
            next_conid = self.matched_df.iloc[n]['conid']
            self.matched_df = self.matched_df.iloc[:n]
            logger.info(f"将处理前 {n} 个标的，共 {len(self.matched_df)} 个")
            # 如果有下一个conid，输出用于继续处理
            logger.info("继续处理后续conid的命令:")
            logger.info(f"-c {next_conid}")

        success_count = 0
        fail_count = 0
        successful_conids = []  # 记录成功的conid
        failed_conids = []  # 记录失败的conid和symbol

        # 是否使用串行处理模式
        use_serial_mode = False
        if len(self.matched_df) <= 3:  # 如果标的数量很少，使用串行模式
            use_serial_mode = True
            logger.info("标的数量较少，使用串行处理模式")

        # 串行处理每个标的
        if use_serial_mode:
            for _, row in self.matched_df.iterrows():
                # 检查是否收到终止信号
                if is_terminating:
                    logger.warning("检测到终止信号，停止提交新任务...")
                    break
                    
                try:
                    if self.process_symbol(row):
                        success_count += 1
                        successful_conids.append(row['conid'])
                    else:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))
                except Exception as e:
                    logger.error(f"处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    fail_count += 1
                    failed_conids.append((row['conid'], row['ib_symbol']))
        else:
            # 并行处理模式
            lock = threading.Lock()

            def process_worker(row):
                nonlocal success_count, fail_count
                try:
                    if self.process_symbol(row):
                        with lock:
                            success_count += 1
                            successful_conids.append(row['conid'])
                    else:
                        with lock:
                            fail_count += 1
                            failed_conids.append((row['conid'], row['ib_symbol']))
                except Exception as e:
                    logger.error(f"处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    with lock:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))

            # 创建线程池
            num_threads = min(self.process_threads, len(self.matched_df))  # 最多self.process_threads个线程
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = []
                for _, row in self.matched_df.iterrows():
                    # 检查是否收到终止信号
                    if is_terminating:
                        logger.warning("检测到终止信号，停止提交新任务...")
                        break
                    futures.append(executor.submit(process_worker, row))
                
                # 等待所有已提交任务完成
                for future in concurrent.futures.as_completed(futures):
                    # 只是等待任务完成，不提交新任务
                    pass

        # 等待所有数据保存完成
        self.database_saver.stop()

        logger.info(f"处理完成. 成功: {success_count}, 失败: {fail_count}")

        # 如果指定了n或者收到了终止信号，输出成功的conid列表
        if n is not None or is_terminating:
            logger.info("成功的conid列表:")
            logger.info(f"  {', '.join(str(conid) for conid in successful_conids)}")

            # 合并成功和失败的conid
            all_conids = successful_conids + [conid for conid, _ in failed_conids]
            if all_conids:
                sql_content = self.generate_cleanup_sql(all_conids)
                sql_file = f"cleanup_to_{n if n is not None else 'interrupted'}.sql"
                with open(sql_file, "w") as f:
                    f.write(sql_content)
                logger.info(f"已生成SQL文件: {sql_file}")

        if failed_conids:
            logger.warning("失败的标的列表:")
            for conid, symbol in failed_conids:
                logger.warning(f"  - {symbol} (conid: {conid})")
            logger.warning("失败的conid列表（用于重试）:")
            logger.warning(f"--conid-list \"{','.join(str(conid) for conid, _ in failed_conids)}\"")

        return success_count, fail_count, successful_conids, [conid for conid, _ in failed_conids]

    def generate_cleanup_sql(self, conids: List[int]) -> str:
        """生成用于清理数据库的SQL语句

        Args:
            conids: 需要清理的conid列表

        Returns:
            str: SQL语句
        """
        if not conids:
            return ""

        # 生成SQL语句
        sql = []
        sql.append("-- 删除DbBarData表中的数据")
        sql.append("DELETE FROM dbbardata ")
        sql.append("WHERE (symbol IN (" + ",".join(f"'{conid}'" for conid in conids) + ")")

        # 添加LIKE条件
        like_conditions = [f"OR symbol LIKE '{conid}_%' " for conid in conids]
        sql.extend(like_conditions)
        sql.append(");")
        sql.append("")

        sql.append("-- 删除DbBarOverview表中的数据")
        sql.append("DELETE FROM dbbaroverview ")
        sql.append("WHERE (symbol IN (" + ",".join(f"'{conid}'" for conid in conids) + ")")

        # 添加LIKE条件
        sql.extend(like_conditions)
        sql.append(");")
        sql.append("")

        # 添加Python命令注释
        sql.append("'''")
        sql.append(f"python firstrate_preprocess.py -l \"{','.join(str(conid) for conid in conids)}\"")
        sql.append("'''")

        return "\n".join(sql)


app = typer.Typer()

@app.command()
def process(
    data_path: str = typer.Option(
        "full_1min",
        "--data-path",
        "-p",
        help="数据路径，可选 full_1min 或 month_1min"
    ),
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    start_date: datetime = typer.Option(
        datetime(2024, 3, 1),
        "--start-date",
        "-s",
        help="开始日期，格式：YYYY-MM-DD，只处理该日期之后的数据",
        formats=["%Y-%m-%d"]
    ),
    end_date: datetime = typer.Option(
        datetime.now(),
        "--end-date",
        "-e",
        help="结束日期，格式：YYYY-MM-DD，只处理该日期及之前的数据",
        formats=["%Y-%m-%d"]
    ),
    start_conid: Optional[int] = typer.Option(
        None,
        "--start-conid",
        "-c",
        help="从指定的conid开始处理，用于断点恢复"
    ),
    n: Optional[int] = typer.Option(
        None,
        "--n",
        "-n",
        help="处理第1到n个conid（不包含第n个）"
    ),
    conid_list: Optional[str] = typer.Option(
        None,
        "--conid-list",
        "-l",
        help="要处理的conid列表，用逗号分隔，例如：95514904,548309229,162225735,705140936,662521562,150304986,252184470"
    ),
    ignore_overview: bool = typer.Option(
        False,
        "--ignore-overview",
        "-f",
        help="忽略数据库中的overview信息，强制全量覆盖"
    ),
    db_threads: int = typer.Option(
        10,
        "--db-threads",
        "-dt",
        help="数据库保存线程数，默认为10"
    ),
    process_threads: int = typer.Option(
        10,
        "--process-threads",
        "-pt",
        help="标的处理线程数，默认为10"
    ),
    use_database: bool = typer.Option(
        False,
        "--use-database",
        "-db",
        help="使用数据库模式加载数据，而不是本地文件"
    ),
    interval: str = typer.Option(
        "1m",
        "--interval",
        "-i",
        help="时间间隔，可选 1m（分钟线）或 d（日线）"
    ),
    database_name: Optional[str] = typer.Option(
        None,
        "--database",
        "-db-name",
        help="数据库名称，如果不指定则根据interval自动选择"
    )
):
    """FirstRate数据预处理和复权工具"""
    try:
        # 获取最新的conid和firstrate_symbol匹配关系
        # 检查当前是否为交易日
        nyse = mcal.get_calendar('NYSE')
        now = datetime.now(ET_TZ)
        today = now.date()
        schedule = nyse.schedule(start_date=today, end_date=today)
        if schedule.empty:
            logger.warning(f"当前日期 {today} 不是交易日，程序退出")
            return
            
        # 直接使用Interval构造
        try:
            interval_enum = Interval(interval)
        except ValueError:
            logger.error(f"不支持的时间间隔: {interval}，可选值: 1m（分钟线）或 d（日线）")
            return

        # 创建处理器
        processor = FirstratePreprocessor(
            data_dir=data_dir,
            data_path=data_path,
            start_date=start_date.replace(tzinfo=ET_TZ),
            end_date=datetime.combine(end_date.date(), datetime.max.time()).replace(tzinfo=ET_TZ),
            db_threads=db_threads,
            process_threads=process_threads,
            ignore_overview=ignore_overview,
            use_database=use_database,
            interval=interval_enum,
            is_test_mode=n is not None,  # 如果指定了n参数，则为测试模式
            database_name=database_name
        )

        # 处理conid列表
        conid_list_to_process = None

        # 如果指定了conid_list，解析并处理
        if conid_list:
            try:
                conid_list_to_process = [int(conid.strip()) for conid in conid_list.split(',')]
                logger.info(f"将处理指定的conid列表: {conid_list_to_process}")
            except ValueError as e:
                logger.error(f"conid列表格式错误: {e}")
                return

        # 处理所有标的
        success, fail, successful_conids, failed_conids = processor.process_all(conid_list_to_process, start_conid, n)
        logger.info(f"处理完成. 成功: {success}, 失败: {fail}")

    except KeyboardInterrupt:
        logger.warning("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}\n{traceback.format_exc()}")
    finally:
        # 确保数据保存线程正确关闭
        if 'processor' in locals() and not is_terminating:
            # 只有在未收到终止信号时才调用stop，避免重复调用
            processor.database_saver.stop()

if __name__ == "__main__":
    app()
