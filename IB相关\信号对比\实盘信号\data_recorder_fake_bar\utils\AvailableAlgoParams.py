"""
Copyright (C) 2019 Interactive Brokers LLC. All rights reserved. This code is subject to the terms
 and conditions of the IB API Non-Commercial License or the IB API Commercial License, as applicable.
"""

from ibapi.object_implem import Object
from ibapi.tag_value import TagValue
from ibapi.order import Order


class AvailableAlgoParams(Object):
    """
    Interactive Brokers算法交易参数设置类
    
    该类提供了IB支持的各种算法交易策略的参数设置方法。以下是支持的算法交易策略:

    1. 自适应算法（Adaptive）
    该算法结合了智能路由功能和用户定义的优先级设置，以实现在执行点更高的成本效率。使用自适应算法平均可以获得比常规限价单或市价单更好的执行价格。

    2. 到达价格算法（Arrival Price）
    到达价格算法订单类型旨在在订单执行过程中达到订单提交时的买卖中间价。该算法设计为保持隐藏订单，这些订单将影响较高比例的平均日成交量（ADV）。执行速度由用户指定的风险规避水平和目标日均成交量百分比决定。

    3. 收盘价算法（Close Price）
    收盘价算法策略旨在帮助投资者在交易日结束时执行订单，通过将大订单分解并确定订单输入时机，以持续执行从而最小化滑点。执行的开始和节奏由用户指定的市场风险水平和目标成交量百分比来决定，同时算法会考虑股票的历史波动性。

    4. 暗冰算法（Dark Ice）
    暗冰算法类似于冰山或储备订单，允许用户指定与订单规模不同的显示规模。此外，算法会根据价格向有利方向移动的概率，将显示规模随机化±50%，并决定是否以限价下单，或对买单在当前卖价低一个价位，对卖单在当前买价高一个价位下单。

    5. 积累/分配算法（Accumulate/Distribute）
    积累/分配算法可以帮助用户在不被市场注意到的情况下为大量订单获得最佳价格，并可以设置用于高频交易。通过将订单切分成在用户定义的时间段内以随机时间间隔释放的较小随机规模订单增量，该算法允许交易大量股票和其他工具而不被市场发现。

    6. 成交量百分比算法（Percentage of Volume）
    成交量百分比算法可以限制订单对整体日均成交量的贡献，以最小化影响。客户可以设置1-50%之间的值来控制在指定开始和结束时间之间的参与度。订单数量和成交量分布是根据目标成交量百分比以及从市场数据计算的持续更新的成交量预测来确定的。

    7. 时间加权平均价格算法（TWAP）
    TWAP算法旨在实现从提交订单到市场收盘的时间加权平均价格。如果选中"允许超过结束时间交易"选项，在规定完成时间结束时未完成的订单将继续成交。

    8. 价格变化成交量百分比算法（Price Variant Percentage of Volume）
    该算法允许用户以根据证券市场价格随时间变化的用户定义比率参与成交量。在价格较低时更积极地买入，在价格上涨时更保守，卖出订单则相反。

    9. 规模变化成交量百分比算法（Size Variant Percentage of Volume）
    该算法策略允许订单在开始时更积极，结束时更保守，反之亦然。它让用户根据剩余订单量随时间变化的用户定义比率参与成交量。

    10. 时间变化成交量百分比算法（Time Variant Percentage of Volume）
    该算法策略允许订单在开始时更积极，结束时更保守，反之亦然。它让用户根据开始和结束时间点的用户定义比率参与成交量。

    11. 成交量加权平均价格算法（VWAP）
    VWAP算法旨在实现或超越从提交订单到市场收盘的成交量加权平均价格。

    12. 平衡影响风险算法（Balance Impact Risk）
    该算法在期权交易的市场影响与订单时间范围内的价格变动风险之间取得平衡。结合用户指定的风险规避水平和目标成交量百分比来确定执行节奏。

    13. 最小化影响算法（Minimize Impact）
    最小化影响算法通过在时间上切分订单来最小化市场影响，在不超过给定最大百分比值的情况下实现市场平均价格。
    """
    # ! [scale_params]
    @staticmethod
    def FillScaleParams(baseOrder: Order, scaleInitLevelSize: int, scaleSubsLevelSize: int, scaleRandomPercent: bool,
                        scalePriceIncrement: float, scalePriceAdjustValue: float, scalePriceAdjustInterval: int,
                        scaleProfitOffset: float, scaleAutoReset: bool, scaleInitPosition: int, scaleInitFillQty: int):
        baseOrder.scaleInitLevelSize = scaleInitLevelSize # Initial Component Size
        baseOrder.scaleSubsLevelSize = scaleSubsLevelSize # Subsequent Comp. Size
        baseOrder.scaleRandomPercent = scaleRandomPercent # Randomize size by +/-55%
        baseOrder.scalePriceIncrement = scalePriceIncrement # Price Increment

        # Auto Price adjustment
        baseOrder.scalePriceAdjustValue = scalePriceAdjustValue # starting price by
        baseOrder.scalePriceAdjustInterval = scalePriceAdjustInterval # in seconds

        # Profit Orders
        baseOrder.scaleProfitOffset = scaleProfitOffset # Create profit taking order Profit Offset
        baseOrder.scaleAutoReset = scaleAutoReset # Restore size after taking profit
        baseOrder.scaleInitPosition = scaleInitPosition # Initial Position
        baseOrder.scaleInitFillQty = scaleInitFillQty # Filled initial Component Size
    # ! [scale_params]

    # ! [arrivalpx_params]
    @staticmethod
    def FillArrivalPriceParams(baseOrder: Order, maxPctVol: float = 0.1,
                              riskAversion: str = "Aggressive",
                              startTime: str = "09:00:00 US/Eastern",
                              endTime: str = "16:00:00 US/Eastern",
                              forceCompletion: bool = True,
                              allowPastTime: bool = True):
        """
        Arrival Price Algorithm: Targets bid/ask midpoint at submission time.
        到达价格算法：目标是在订单提交时实现买卖价中间价的成交。

        Original IB Documentation:
        The Arrival Price algorithmic order type will attempt to achieve, over the course
        of the order, the bid/ask midpoint at the time the order is submitted. The Arrival
        Price algo is designed to keep hidden orders that will impact a high percentage of
        the average daily volume (ADV). The pace of execution is determined by the
        user-assigned level of risk aversion and the user-defined target percent of average
        daily volume. How quickly the order is submitted during the day is determined by
        the level of urgency: the higher the urgency the faster it will execute but will
        expose it to a greater market impact. Market impact can be lessened by assigning
        lesser urgency, which is likely to lengthen the duration of the order. The user
        can set the max percent of ADV from 1 to 50%. The order entry screen allows the
        user to determine when the order will start and end regardless of whether or not
        the full amount of the order has been filled. By checking the box marked Allow
        trading past end time the algo will continue to work past the specified end time
        in an effort to fill the remaining portion of the order.
        到达价格算法会在订单执行期间，力求成交价格接近订单提交时的买卖价中间价。
        该算法适合大单隐藏下单，影响日均成交量较高时使用。
        执行速度由用户设定的风险规避等级和目标成交量百分比决定。
        紧急程度越高，执行越快但市场冲击越大；紧急程度低则执行更慢但冲击更小。
        用户可设置最大参与日均成交量的百分比（1-50%），并可指定订单的开始和结束时间。
        勾选允许超时交易后，算法会在结束时间后继续尝试完成剩余订单。

        Args:
            maxPctVol (float): Maximum percentage of ADV (0.1-0.5)
            riskAversion (str): Urgency level ("Get Done", "Aggressive", "Neutral",
                              "Passive")
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            forceCompletion (bool): Try to complete by day end
            allowPastTime (bool): Allow trading past end time
        """
        baseOrder.algoStrategy = "ArrivalPx"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
        baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("forceCompletion",
                                             int(forceCompletion)))
        baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                             int(allowPastTime)))


    # ! [arrivalpx_params]


    # ! [darkice_params]
    @staticmethod
    def FillDarkIceParams(baseOrder: Order, displaySize: int = 10,
                         startTime: str = "09:00:00 US/Eastern",
                         endTime: str = "16:00:00 US/Eastern",
                         allowPastEndTime: bool = True):
        """
        Dark Ice Algorithm: Hidden order execution with randomized display size.
        暗冰算法：通过随机化显示数量的隐藏订单执行，进一步保护订单隐私。

        Original IB Documentation:
        The Dark Ice order type develops the concept of privacy adopted by orders such as
        Iceberg or Reserve, using a proprietary algorithm to further hide the volume
        displayed to the market by the order. Clients can determine the timeframe an order
        remains live and have the option to allow trading past end time in the event it is
        unfilled by the stated end time. In order to minimize market impact in the event
        of large orders, users can specify a display size to be shown to the market
        different from the actual order size. Additionally, the Dark Ice algo randomizes
        the display size +/- 50% based upon the probability of the price moving
        favourably. Further, using calculated probabilities, the algo decides whether to
        place the order at the limit price or one tick lower than the current offer for
        buy orders and one tick higher than the current bid for sell orders.
        暗冰算法在冰山单/储备单的基础上，进一步通过专有算法隐藏订单真实数量。
        用户可设定订单的显示数量（可与实际下单量不同），
        算法会根据市场行情将显示数量随机化±50%，并根据概率决定下单价格
        （买单可能低于卖价一跳，卖单可能高于买价一跳），以降低大单对市场的冲击。
        可设置订单有效时间及是否允许超时继续交易。

        Args:
            displaySize (int): Order size shown to market
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            allowPastEndTime (bool): Allow trading past end time
        """
        baseOrder.algoStrategy = "DarkIce"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("displaySize", displaySize))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                             int(allowPastEndTime)))


    # ! [darkice_params]


    # ! [pctvol_params]
    @staticmethod
    def FillPctVolParams(baseOrder: Order, pctVol: float = 0.5,
                        startTime: str = "12:00:00 US/Eastern",
                        endTime: str = "14:00:00 US/Eastern",
                        noTakeLiq: bool = True):
        """
        Percentage of Volume Algorithm: Controls participation rate to minimize impact.
        成交量百分比算法：通过控制参与率，减少对市场的影响。

        Original IB Documentation:
        The Percent of Volume algo can limit the contribution of orders to overall average
        daily volume in order to minimize impact. Clients can set a value between 1-50% to
        control their participation between stated start and end times. Order quantity and
        volume distribution over the day is determined using the target percent of volume
        you entered along with continuously updated volume forecasts calculated from TWS
        market data. In addition, the algo can be set to avoid taking liquidity, which
        may help avoid liquidity-taker fees and could result in liquidity-adding rebates.
        By checking the Attempt to never take liquidity box, the algo is discouraged from
        hitting the bid or lifting the offer if possible. However, this may also result
        in greater deviations from the benchmark, and in partial fills, since the posted
        bid/offer may not always get hit as the price moves up/down. IB will use best
        efforts not to take liquidity when this box is checked, however, there will be
        times that it cannot be avoided.
        该算法通过设定目标成交量百分比（1-50%），在指定时间段内控制订单对市场日均成交量的参与比例，
        从而降低市场冲击。订单数量和分布会根据目标比例和实时成交量预测动态调整。
        可选择避免主动吃单（减少流动性费用），但可能导致部分成交或偏离基准价。

        Args:
            pctVol (float): Target percentage (0.1-0.5)
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            noTakeLiq (bool): Avoid taking liquidity
        """
        baseOrder.algoStrategy = "PctVol"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("pctVol", pctVol))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))


    # ! [pctvol_params]


    # ! [twap_params]
    @staticmethod
    def FillTwapParams(baseOrder: Order,
                      strategyType: str = "Marketable",
                      startTime: str = "09:00:00 US/Eastern",
                      endTime: str = "16:00:00 US/Eastern",
                      allowPastEndTime: bool = True):
        """
        TWAP Algorithm: Targets time-weighted average price from start to end.
        时间加权平均价格算法：目标是在指定时间段内实现时间加权平均价格。

        Original IB Documentation:
        The TWAP algo aims to achieve the time-weighted average price calculated from the
        time you submit the order to the time it completes. Incomplete orders at the end
        of the stated completion time will continue to fill if the box 'allow trading
        past end time' is checked. Users can set the order to trade only when specified
        conditions are met. Those user-defined inputs include when the order is
        marketable, when the midpoint matches the required price, when the same side (buy
        or sell) matches to make the order marketable or when the last traded price would
        make the order marketable. For the TWAP algo, the average price calculation is
        calculated from the order entry time through the close of the market and will
        only attempt to execute when the criterion is met. The order may not fill
        throughout its stated duration and so the order is not guaranteed. TWAP is
        available for all US equities.
        TWAP算法会在指定时间段内，按照时间加权平均价格分批执行订单。
        可设置订单仅在满足特定条件时成交（如可成交、价格达到中点等）。
        若勾选允许超时交易，未完成部分会在结束时间后继续成交。
        该算法适用于美股市场，不能保证全部成交。

        Args:
            strategyType (str): Trade strategy ("Marketable", "Matching", "Midpoint",
                              "Matching Same Side", "Matching Last")
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            allowPastEndTime (bool): Allow trading past end time
        """
        baseOrder.algoStrategy = "Twap"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("strategyType", strategyType))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                             int(allowPastEndTime)))

    # ! [twap_params]


    # ! [vwap_params]
    @staticmethod
    def FillVwapParams(baseOrder: Order, maxPctVol: float = 0.2,
                      startTime: str = "09:00:00 US/Eastern",
                      endTime: str = "16:00:00 US/Eastern",
                      allowPastEndTime: bool = True,
                      noTakeLiq: bool = True,
                      # speedUp: bool = True
                       ):
        """
        VWAP Algorithm: Best-efforts volume-weighted average price execution.
        成交量加权平均价格算法：力求实现接近市场VWAP的成交。

        Original IB Documentation:
        IB's best-efforts VWAP algo seeks to achieve the Volume-Weighted Average price
        (VWAP), calculated from the time you submit the order to the close of the market.
        IB的尽力而为VWAP算法旨在实现成交量加权平均价格（VWAP），该价格从您提交订单时起至市场
        收盘时计算。

        Best-efforts VWAP algo is a lower-cost alternative to the Guaranteed VWAP (no
        longer supported) that enables the user to attempt never to take liquidity while
        also trading past the end time. Because the order may not be filled on the bid
        or at the ask prices, there is a trade-off with this algo. The order may not
        fully fill if the user is attempting to avoid liquidity-taking fees and/or
        maximize liquidity-adding rebates, and may miss the benchmark by asking to stay
        on the bid or ask. The user can determine the maximum percentage of average daily
        volume (up to 50%) his order will comprise. The system will generate the VWAP
        from the time the order is entered through the close of trading, and the order
        can be limited to trading over a pre-determined period. The user can request the
        order to continue beyond its stated end time if unfilled at the end of the
        stated period. The best-efforts VWAP algo is available for all US equities.
        VWAP算法会在指定时间段内，力求成交价格接近市场成交量加权平均价（VWAP）。
        用户可设置最大参与日均成交量比例（最高50%），并可选择避免主动吃单（减少费用但可能导致部分成交）。
        订单可设置超时继续执行。该算法适用于美股市场。

        Args:
            maxPctVol (float): Maximum percentage of ADV (0.1-0.5)
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            allowPastEndTime (bool): Allow trading past end time
            noTakeLiq (bool): Avoid taking liquidity
            # speedUp (bool): Compensate for decreased fill rate due to limit price
        """
        baseOrder.algoStrategy = "Vwap"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("allowPastEndTime",
                                             int(allowPastEndTime)))
        baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))
        # baseOrder.algoParams.append(TagValue("speedUp", int(speedUp)))


    # ! [vwap_params]


    # ! [ad_params]
    @staticmethod
    def FillAccumulateDistributeParams(baseOrder: Order, componentSize: int = 10,
                                      timeBetweenOrders: int = 60,
                                      randomizeTime20: bool = True,
                                      randomizeSize55: bool = True,
                                      giveUp: int = 1,
                                      catchUp: bool = True,
                                      waitForFill: bool = True,
                                      startTime: str = "12:00:00",
                                      endTime: str = "16:00:00"):
        """
        Accumulate/Distribute Algorithm: High frequency trading with hidden large orders.
        积累/分配算法：将大单拆分为小单，随机时间和数量高频执行，隐藏真实意图。

        Original IB Documentation:
        The Accumulate/Distribute algo can help you to achieve the best price for a large
        volume order without being noticed in the market, and can be set up for high
        frequency trading. By slicing your order into smaller randomly-sized order
        increments that are released at random time intervals within a user-defined time
        period, the algo allows the trading of large blocks of stock and other
        instruments without being detected in the market. The algo allows limit, market,
        and relative order types. It is important to keep in mind the API A/D algo will
        not have all available parameters of the A/D algos that can be created in TWS.
        Note: The new fields activeTimeStart and activeTimeEnd are used in TWS 971+;
        startTime and endTime were used previously.
        该算法适合大单高频交易，通过将大订单拆分为多个小订单，并在设定时间段内以随机时间间隔和随机数量逐步下单，
        从而隐藏大单意图，减少市场影响。支持限价、市价、相对价等订单类型。
        API参数与TWS界面略有不同，TWS 971+版本使用activeTimeStart/End。

        Args:
            componentSize (int): Quantity of increment (不能超过初始规模)
            timeBetweenOrders (int): Time interval in seconds between each order
            randomizeTime20 (bool): Randomise time period by +/- 20%
            randomizeSize55 (bool): Randomise size by +/- 55%
            giveUp (int): Number associated with the clearing
            catchUp (bool): Catch up in time
            waitForFill (bool): Wait for current order to fill before submitting next order
            startTime (str): Algorithm starting time (YYYYMMDD-hh:mm:ss TMZ)
            endTime (str): Algorithm ending time (YYYYMMDD-hh:mm:ss TMZ)
        """
        baseOrder.algoStrategy = "AD"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("componentSize", componentSize))
        baseOrder.algoParams.append(TagValue("timeBetweenOrders", timeBetweenOrders))
        baseOrder.algoParams.append(TagValue("randomizeTime20",
                                             int(randomizeTime20)))
        baseOrder.algoParams.append(TagValue("randomizeSize55",
                                             int(randomizeSize55)))
        baseOrder.algoParams.append(TagValue("giveUp", giveUp))
        baseOrder.algoParams.append(TagValue("catchUp", int(catchUp)))
        baseOrder.algoParams.append(TagValue("waitForFill", int(waitForFill)))
        baseOrder.algoParams.append(TagValue("activeTimeStart", startTime))
        baseOrder.algoParams.append(TagValue("activeTimeEnd", endTime))

    # ! [ad_params]


    # ! [balanceimpactrisk_params]
    @staticmethod
    def FillBalanceImpactRiskParams(baseOrder: Order, maxPctVol: float = 0.1,
                                   riskAversion: str = "Aggressive",
                                   forceCompletion: bool = True):
        """
        Balance Impact Risk Algorithm: Balances market impact with price risk for options.
        平衡影响风险算法：在期权交易中平衡市场冲击与价格波动风险。

        Original IB Documentation:
        The Balance Impact Risk balances the market impact of trading the option with the
        risk of price change over the time horizon of the order. This strategy considers
        the user-assigned level of risk aversion to define the pace of the execution,
        along with the user-defined target percent of volume.
        该算法在期权交易中，结合用户设定的风险规避等级和目标成交量百分比，
        综合考虑市场冲击和价格波动风险，自动调整下单节奏。

        Args:
            maxPctVol (float): Maximum percentage of ADV (0.1-0.5)
            riskAversion (str): Urgency level ("Get Done", "Aggressive", "Neutral", "Passive")
            forceCompletion (bool): Try to complete by day end
        """
        baseOrder.algoStrategy = "BalanceImpactRisk"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
        baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
        baseOrder.algoParams.append(TagValue("forceCompletion",
                                             int(forceCompletion)))

    # ! [balanceimpactrisk_params]


    # ! [minimpact_params]
    @staticmethod
    def FillMinImpactParams(baseOrder: Order, maxPctVol: float = 0.3):
        """
        Minimize Impact Algorithm: Time-sliced execution to minimize market impact.
        最小化影响算法：通过时间分批下单，降低市场冲击，控制最大参与比例。

        Original IB Documentation:
        The Minimise Impact algo minimises market impact by slicing the order over time to
        achieve a market average without going over the given maximum percentage value.
        该算法通过将订单在时间上分批执行，控制最大参与日均成交量比例，从而尽量减少对市场价格的影响。

        Args:
            maxPctVol (float): Maximum percentage of ADV (0.1-0.5)
        """
        baseOrder.algoStrategy = "MinImpact"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))

    # ! [minimpact_params]


    # ! [adaptive_params]
    @staticmethod
    def FillAdaptiveParams(baseOrder: Order, priority: str = "Normal"):
        """
        Adaptive Algorithm: Smart routing with priority-based execution scanning.
        自适应算法：结合智能路由和优先级设置，自动优化成交效率和价格。

        Original IB Documentation:
        The Adaptive Algo combines IB's Smartrouting capabilities with user-defined priority 
        settings in an effort to achieve further cost efficiency at the point of execution. 
        Using the Adaptive algo leads to better execution prices on average than for regular 
        limit or market orders.
        该算法结合IB智能路由和用户设定的优先级，自动在不同市场和价格间扫描，
        力求获得更优成交价格和成本。相比普通限价单/市价单，平均成交效果更好。

        Args:
            priority (str): Priority level for execution scanning
                Values: "Urgent", "Normal", "Patient"
                - Urgent: Brief scan
                - Normal: Default balance
                - Patient: Longer scan, higher chance of better fill
        """
        baseOrder.algoStrategy = "Adaptive"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("adaptivePriority", priority))

    # ! [adaptive_params]

    # ! [closepx_params]
    @staticmethod
    def FillClosePriceParams(baseOrder: Order, maxPctVol: float = 0.4,
                            riskAversion: str = "Neutral",
                            startTime: str = "09:00:00 US/Eastern",
                            forceCompletion: bool = True):
        """
        Close Price Algorithm: Minimizes slippage in closing auction.
        收盘价算法：分批执行大单，降低收盘集合竞价时的滑点风险。

        Original IB Documentation:
        Investors submitting market or limit orders into the closing auction may adversely 
        affect the closing price, especially when the size of the order is large relative 
        to the average close auction volume. In order to help investors attempting to 
        execute towards the end of the trading session we have developed the Close Price 
        algo Strategy. This algo breaks down large order amounts and determines the timing 
        of order entry so that it will continuously execute in order to minimize slippage. 
        The start and pace of execution are determined by the user who assigns a level of 
        market risk and specifies the target percentage of volume, while the algo 
        considers the prior volatility of the stock.
        该算法适用于收盘集合竞价阶段，通过将大单拆分并合理安排下单时机，持续执行以减少滑点。
        用户可设定市场风险等级和目标成交量百分比，算法会结合股票历史波动性自动调整执行节奏。

        Args:
            maxPctVol (float): Maximum percentage of ADV (0.1-0.5)
            riskAversion (str): Urgency level ("Get Done", "Aggressive", "Neutral", 
                              "Passive")
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ") 
            forceCompletion (bool): Try to complete by day end
        """
        baseOrder.algoStrategy = "ClosePx"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("maxPctVol", maxPctVol))
        baseOrder.algoParams.append(TagValue("riskAversion", riskAversion))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("forceCompletion", int(forceCompletion)))


    # ! [closepx_params]


    # ! [pctvolpx_params]
    @staticmethod
    def FillPriceVariantPctVolParams(baseOrder: Order, pctVol: float = 0.1,
                                     deltaPctVol: float = 0.05, minPctVol4Px: float = 0.01,
                                     maxPctVol4Px: float = 0.2,
                                    startTime: str = "12:00:00 US/Eastern",
                                    endTime: str = "14:00:00 US/Eastern",
                                    noTakeLiq: bool = True):
        """
        Price Variant Percentage of Volume Algorithm: Price-sensitive volume participation.
        价格变化成交量百分比算法：根据市场价格动态调整参与率，低价更积极，高价更保守。

        Original IB Documentation:
        Price Variant Percentage of Volume Strategy - This algo allows you to participate
        in volume at a user-defined rate that varies over time depending on the market
        price of the security. This algo allows you to buy more aggressively when the
        price is low and be more passive as the price increases, and just the opposite
        for sell orders. The order quantity and volume distribution over the time during
        which the order is active is determined using the target percent of volume you
        entered along with continuously updated volume forecasts calculated from TWS
        market data.
        该算法根据证券价格的变化，动态调整订单参与市场成交量的比例。
        价格较低时买单更积极，价格上涨时买单更保守（卖单相反）。
        订单数量和分布会根据目标比例和实时成交量预测自动调整。

        Args:
            pctVol (float): Target percentage (0.1-0.5)
            deltaPctVol	Target Percentage Change Rate	0.1 (10%) - 0.5 (50%)
            minPctVol4Px	Minimum Target Percentage	0.1 (10%) - 0.5 (50%)
            maxPctVol4Px	Maximum Target Percentage	0.1 (10%) - 0.5 (50%)
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            noTakeLiq (bool): Avoid taking liquidity
        """
        baseOrder.algoStrategy = "PctVolPx"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("pctVol", pctVol))
        baseOrder.algoParams.append(TagValue("deltaPctVol", deltaPctVol))
        baseOrder.algoParams.append(TagValue("minPctVol4Px", minPctVol4Px))
        baseOrder.algoParams.append(TagValue("maxPctVol4Px", maxPctVol4Px))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))


    # ! [pctvolpx_params]


    # ! [pctvolsz_params]
    @staticmethod
    def FillSizeVariantPctVolParams(baseOrder: Order, startPctVol: float = 0.2,
                                   endPctVol: float = 0.4,
                                   startTime: str = "12:00:00 US/Eastern",
                                   endTime: str = "14:00:00 US/Eastern",
                                   noTakeLiq: bool = True):
        """
        Size Variant Percentage of Volume Algorithm: Size-based volume participation.
        规模变化成交量百分比算法：根据剩余订单量动态调整参与率，初期更积极，后期更保守。

        Original IB Documentation:
        Size Variant Percentage of Volume Strategy - This algo allows you to participate 
        in volume at a user-defined rate that varies over time depending on the remaining 
        size of the order. Define the target percent rate at the start time (Initial 
        Participation Rate) and at the end time (Terminal Participation Rate), and the 
        algo calculates the participation rate over time between the two based on the 
        remaining order size. This allows the order to be more aggressive initially and 
        less aggressive toward the end, or vice versa.
        该算法根据订单剩余数量，动态调整参与市场成交量的比例。
        用户可设定开始和结束时的目标参与率，算法会根据剩余订单量在两者之间平滑过渡，
        实现前期更积极、后期更保守（或反之）。

        Args:
            startPctVol (float): Starting participation rate (0.1-0.5)
            endPctVol (float): Ending participation rate (0.1-0.5)
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            noTakeLiq (bool): Avoid taking liquidity
        """
        baseOrder.algoStrategy = "PctVolSz"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("startPctVol", startPctVol))
        baseOrder.algoParams.append(TagValue("endPctVol", endPctVol))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))


    # ! [pctvolsz_params]


    # ! [pctvoltm_params]
    @staticmethod
    def FillTimeVariantPctVolParams(baseOrder: Order, startPctVol: float = 0.2,
                                   endPctVol: float = 0.4,
                                   startTime: str = "12:00:00 US/Eastern",
                                   endTime: str = "14:00:00 US/Eastern",
                                   noTakeLiq: bool = True):
        """
        Time Variant Percentage of Volume Algorithm: Time-based volume participation.
        时间变化成交量百分比算法：根据时间推移动态调整参与率，实现前期或后期更积极。

        Original IB Documentation:
        Time Variant Percentage of Volume Strategy - This algo allows you to participate in 
        volume at a user-defined rate that varies with time. Define the target percent rate 
        at the start time and at the end time, and the algo calculates the participation 
        rate over time between the two. This allows the order to be more aggressive 
        initially and less aggressive toward the end, or vice versa.
        该算法根据订单执行时间的推移，动态调整参与市场成交量的比例。
        用户可设定开始和结束时的目标参与率，算法会在两者之间平滑过渡，
        实现前期更积极、后期更保守（或反之）。

        Args:
            startPctVol (float): Starting participation rate (0.1-0.5)
            endPctVol (float): Ending participation rate (0.1-0.5)
            startTime (str): Start time ("hh:mm:ss TMZ" or "YYYYMMDD-hh:mm:ss TMZ")
            endTime (str): End time (same format as startTime)
            noTakeLiq (bool): Avoid taking liquidity
        """
        baseOrder.algoStrategy = "PctVolTm"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("startPctVol", startPctVol))
        baseOrder.algoParams.append(TagValue("endPctVol", endPctVol))
        baseOrder.algoParams.append(TagValue("startTime", startTime))
        baseOrder.algoParams.append(TagValue("endTime", endTime))
        baseOrder.algoParams.append(TagValue("noTakeLiq", int(noTakeLiq)))

    # ! [pctvoltm_params]

    # ! [jefferies_vwap_params]
    @staticmethod
    def FillJefferiesVWAPParams(baseOrder: Order, startTime: str,
                                endTime: str, relativeLimit: float,
                                maxVolumeRate: float, excludeAuctions: str,
                                triggerPrice: float, wowPrice: float,
                                minFillSize: int, wowOrderPct: float,
                                wowMode: str, isBuyBack: bool, wowReference: str):
        # must be direct-routed to "JEFFALGO"
        baseOrder.algoStrategy = "VWAP"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("StartTime", startTime))
        baseOrder.algoParams.append(TagValue("EndTime", endTime))
        baseOrder.algoParams.append(TagValue("RelativeLimit", relativeLimit))
        baseOrder.algoParams.append(TagValue("MaxVolumeRate", maxVolumeRate))
        baseOrder.algoParams.append(TagValue("ExcludeAuctions", excludeAuctions))
        baseOrder.algoParams.append(TagValue("TriggerPrice", triggerPrice))
        baseOrder.algoParams.append(TagValue("WowPrice", wowPrice))
        baseOrder.algoParams.append(TagValue("MinFillSize", minFillSize))
        baseOrder.algoParams.append(TagValue("WowOrderPct", wowOrderPct))
        baseOrder.algoParams.append(TagValue("WowMode", wowMode))
        baseOrder.algoParams.append(TagValue("IsBuyBack", int(isBuyBack)))
        baseOrder.algoParams.append(TagValue("WowReference", wowReference))
    # ! [jefferies_vwap_params]

    # ! [csfb_inline_params]
    @staticmethod
    def FillCSFBInlineParams(baseOrder: Order, startTime: str,
                             endTime: str, execStyle: str,
                             minPercent: int, maxPercent: int,
                             displaySize: int, auction: str,
                             blockFinder: bool, blockPrice: float,
                             minBlockSize: int, maxBlockSize: int, iWouldPrice: float):
        # must be direct-routed to "CSFBALGO"
        baseOrder.algoStrategy = "INLINE"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("StartTime", startTime))
        baseOrder.algoParams.append(TagValue("EndTime", endTime))
        baseOrder.algoParams.append(TagValue("ExecStyle", execStyle))
        baseOrder.algoParams.append(TagValue("MinPercent", minPercent))
        baseOrder.algoParams.append(TagValue("MaxPercent", maxPercent))
        baseOrder.algoParams.append(TagValue("DisplaySize", displaySize))
        baseOrder.algoParams.append(TagValue("Auction", auction))
        baseOrder.algoParams.append(TagValue("BlockFinder", int(blockFinder)))
        baseOrder.algoParams.append(TagValue("BlockPrice", blockPrice))
        baseOrder.algoParams.append(TagValue("MinBlockSize", minBlockSize))
        baseOrder.algoParams.append(TagValue("MaxBlockSize", maxBlockSize))
        baseOrder.algoParams.append(TagValue("IWouldPrice", iWouldPrice))
    # ! [csfb_inline_params]

    # ! [qbalgo_strobe_params]
    @staticmethod
    def FillQBAlgoInLineParams(baseOrder: Order, startTime: str,
                               endTime: str, duration: float,
                               benchmark: str, percentVolume: float,
                               noCleanUp: bool):
        # must be direct-routed to "QBALGO"
        baseOrder.algoStrategy = "STROBE"
        baseOrder.algoParams = []
        baseOrder.algoParams.append(TagValue("StartTime", startTime))
        baseOrder.algoParams.append(TagValue("EndTime", endTime))
        #This example uses endTime instead of duration
        #baseOrder.algoParams.append(TagValue("Duration", str(duration)))
        baseOrder.algoParams.append(TagValue("Benchmark", benchmark))
        baseOrder.algoParams.append(TagValue("PercentVolume", str(percentVolume)))
        baseOrder.algoParams.append(TagValue("NoCleanUp", int(noCleanUp)))
    # ! [qbalgo_strobe_params]


def Test():
    av = AvailableAlgoParams() # @UnusedVariable


if "__main__" == __name__:
    Test()
