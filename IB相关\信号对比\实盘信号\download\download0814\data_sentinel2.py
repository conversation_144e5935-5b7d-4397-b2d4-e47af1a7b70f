import os
import threading
from collections import defaultdict
from datetime import datetime, timedelta
from pathlib import Path
from time import sleep
from vnpy.trader.utility import extract_vt_symbol
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.usertools.db_status_manager import Status
from vnpy_mysql.mysql_database import MysqlDatabase, DB_TZ, Interval
import traceback
from basic_datadownload import sync_download
from ib_async import IB, Contract, util
from zoneinfo import ZoneInfo
SH_TZ = ZoneInfo('Asia/Shanghai')

class Status2(Status):  # 继承自 Status
    class Meta:
        table_name = 'status2'  # 只需修改表名

#程序开始时，将running_status全部归位
def reset_download_status():
    try:
        # 使用update方法批量更新running_status字段
        query = Status2.update(download_status=0)
        # 执行更新
        n = query.execute()
        print(f"Successfully updated {n} records.")
    except Exception as e:
        # 处理可能发生的异常
        msg = f"reset_running_status: An error occurred: {e}"
        print(msg)


# 给指定标的设置1 or 2  
def update_status(symbol_set, status, main_engine):
    main_engine.write_log(f"set download status to {status} for {symbol_set}")
    for elem in symbol_set:
        updated_count = Status2.update({
            Status2.download_status: status,
            Status2.download_updated_time: datetime.now()
        }).where(Status2.content.like(f"{elem}%")).execute()
        main_engine.write_log(f"成功将 {updated_count} 条记录的下载状态更新为 {status}，涉及 symbol: {elem}")


def download_sentinel(main_engine):
    """无限循环，检查 status 表中的 status 字段，启动下载服务并更新状态。"""
    count = 0
    while True:
        # 查询 status 表中 status 字段not 为 2  的记录, 
        count = count + 1
        try:
            if count > 60*20:
                # keep_alive()            
                count=0
            # 0 待下载   1 下载中   2下载完成
            query = Status2.select().where(Status2.download_status == 0).order_by(Status2.content).limit(30)
            contents = defaultdict(set)
            if not query.exists():
                sleep(5)
            else:
                for status in query:
                    vt_symbol, _ = status.content.rsplit('_', 1)
                    symbol, exchange = vt_symbol.rsplit('.', 1)
                    contents[exchange].add(symbol)
                    status.download_status = 1
                    status.download_updated_time = datetime.now()
                    status.save()
                    main_engine.write_log(f"{status.download_updated_time} 准备下载 {status.content} 的历史数据")
                # 开始下载
                test_start_date = datetime(2025, 7, 1, 21, 30, 0, tzinfo=SH_TZ)
                test_end_date = (datetime.now(SH_TZ) + timedelta(days=1)).replace(hour=19, minute=0, second=0, microsecond=0)
                test_interval = Interval.MINUTE
                ib_symbols = contents.get("SMART", set())
                ideal_symbols = contents.get("IDEALPRO", set())

# 使用集合推导式为每个元素添加对应的后缀
                ib_symbol_set = {symbol + ".SMART" for symbol in ib_symbols}
                ideal_symbol_set = {symbol + ".IDEALPRO" for symbol in ideal_symbols}
                symbol_set = ib_symbol_set | ideal_symbol_set
                update_status(symbol_set, 1, main_engine)         
                main_engine.write_log(f"call download 开始下载 {symbol_set} 的历史数据,set status to 1 ")
                result = util.run(sync_download(symbol_set, test_start_date, test_end_date, test_interval))
                if result.success:
                    # 下载结束标识
                    update_status(symbol_set, 2, main_engine)         
                    main_engine.write_log(f"{status.download_updated_time} {symbol_set} related 的历史数据下载完成, set status 2")
                else:
                    main_engine.write_log(f"下载失败: {result.message}")
                    if result.failed_vt_symbols:
                        main_engine.write_log(f"失败的合约: {', '.join(result.failed_vt_symbols)}")
                    
        except Exception:
            msg = f" download_sentinel error 触发异常,{traceback.format_exc()}"
            main_engine.write_log(f"{msg}")
        sleep(1)  

def data_recorder_sentinel(main_engine):
    """每 10 分钟核查各标的数据状态，并根据结果更新 status 状态。"""
    database = MysqlDatabase()

    while True:
        sleep(600)  # 每 10 分钟检查一次
        query = Status2.select(Status2.download_status == 2)

        for status in query:
            vt_symbol, _ = status.content.rsplit('_', 1)
            symbol, exchange = extract_vt_symbol(vt_symbol)
            start = datetime.now().astimezone(DB_TZ)
            end = start - timedelta(minutes=4)
            db_data = database.load_bar_data(symbol=symbol, exchange=exchange, interval=Interval.MINUTE, start=start,
                                             end=end)

            if not db_data:
                status.download_status = 5

            status.download_updated_time = datetime.now()
            status.save()
            main_engine.write_log(f"{status.download_updated_time} {status.content} 的历史数据核查完成")


def main():
    """主函数，启动两个守护线程执行 sentinel 任务。"""
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    reset_download_status()
    #main_engine.write_log(f"wait 3 minute,待实盘记录模块启动后开始下载")
    #sleep(60*3)
    main_engine.write_log(f"开始处理")
    download_thread = threading.Thread(target=download_sentinel, args=(main_engine,), daemon=True)

    download_thread.start()

    download_thread.join()
    sync_setting_thread.join()

if __name__ == "__main__":
    #os.system("nohup python3 /opt/test/run_gateway_sentinel.py >> /dev/null 2>&1 &")
    main()
