import re
import glob
import os

def extract_symbols_from_file(file_path):
    """从SQL文件中提取所有symbol"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式匹配symbol
    # 匹配模式: symbol = '数字'
    pattern = r"symbol\s*=\s*'(\d+)'"
    symbols = set(re.findall(pattern, content))
    
    return symbols

def extract_where_and_symbols():
    """从fixdb目录下的所有fixdb*.sql文件中提取WHERE开头，后面接AND的symbol"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    fixdb_dir = os.path.join(current_dir, 'fixdb')
    
    # 查找所有fixdb*.sql文件
    sql_files = glob.glob(os.path.join(fixdb_dir, 'fixdb*.sql'))
    
    all_symbols = set()
    file_symbols = {}
    
    for sql_file in sql_files:
        filename = os.path.basename(sql_file)
        print(f"正在处理文件: {filename}")
        
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 匹配WHERE symbol = '数字' AND的模式
            # 需要使用多行模式来匹配每一行
            pattern = r"^WHERE\s+symbol\s*=\s*'(\d+)'\s+AND"
            symbols = set(re.findall(pattern, content, re.IGNORECASE | re.MULTILINE))
            
            file_symbols[filename] = symbols
            all_symbols.update(symbols)
            
            print(f"  - 找到 {len(symbols)} 个符合条件的symbol")
            
        except Exception as e:
            print(f"  - 读取文件失败: {e}")
    
    return all_symbols, file_symbols

def main():
    
    # 读取两个文件中的symbol
    if False:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        fixdb_symbols = extract_symbols_from_file(os.path.join(current_dir, 'fixdb', 'fixdb.sql'))
        fixdb2_symbols = extract_symbols_from_file(os.path.join(current_dir, 'fixdb', 'fixdb2.sql'))

        # 计算交集和差集
        intersection = fixdb_symbols & fixdb2_symbols  # 交集
        only_in_fixdb = fixdb_symbols - fixdb2_symbols  # 只在fixdb中的symbol
        only_in_fixdb2 = fixdb2_symbols - fixdb_symbols  # 只在fixdb2中的symbol

        # 输出结果
        print(f"fixdb.sql中处理的symbol数量: {len(fixdb_symbols)}")
        print(f"fixdb2.sql中处理的symbol数量: {len(fixdb2_symbols)}")
        print(f"交集数量: {len(intersection)}")
        print(f"只在fixdb.sql中的symbol数量: {len(only_in_fixdb)}")
        print(f"只在fixdb2.sql中的symbol数量: {len(only_in_fixdb2)}")

        print("\n=== 交集 ===")
        for symbol in sorted(intersection):
            print(symbol)

        print("\n=== 只在fixdb.sql中的symbol ===")
        for symbol in sorted(only_in_fixdb):
            print(symbol)

        print("\n=== 只在fixdb2.sql中的symbol ===")
        for symbol in sorted(only_in_fixdb2):
            print(symbol)

        print("\n" + "="*60)
    print("=== 新功能：提取所有fixdb*.sql文件中WHERE开头，后面接AND的symbol ===")
    
    # 提取WHERE...AND格式的symbol
    all_where_and_symbols, file_symbols = extract_where_and_symbols()
    
    print(f"\n总计找到 {len(all_where_and_symbols)} 个符合'WHERE symbol = 'xxx' AND'格式的symbol")
    
    print("\n=== 按文件分组的结果 ===")
    for filename, symbols in file_symbols.items():
        print(f"\n{filename}: {len(symbols)} 个symbol")
        for symbol in sorted(symbols):
            print(f"  {symbol}")
    
    print("\n=== 所有符合条件的symbol（去重后） ===")
    for symbol in sorted(all_where_and_symbols):
        print(symbol)

if __name__ == "__main__":
    main()