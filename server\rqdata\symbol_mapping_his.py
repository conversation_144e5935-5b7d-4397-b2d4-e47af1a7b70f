# %%
import re
import datetime
from urllib.parse import quote as urlquote

import pandas as pd
import rqdatac as rq
from sqlalchemy import create_engine, text
from symbol_info import all_symbol_pres
from copy import copy
from vnpy.trader.utility import extract_vt_symbol

rq.init('license',
             'hKzEyfcbN4O4B22wGXKfOZnOkVIyQ4fnW7VSUepZ5shkCx3Wpfkb63nMWozKudSUfMCiSx6cuWYasEyqaIVQ7a91WnYFIhxSw39GKxvHmhnlIjaSjBNncRY0Y3ZH3wWYiYbjK25Gxl9FuVkH6sA5VmnbMBmJoQHeT_seHEFYVPw=LVXNtX-oQgO2T9QDKkPx1hhlyjgrkYETwszLKzPA3ItRHWcp4crJu9dlykAOaJv4AtQuPy-THTFzBP4DfcFtIWm-W5vGQNyMMu3lD8cc1u_kxXFfihqajhijKdIi8nJvVrOexx1XVI6Vv-FdzrL0IVNY9e9GCcZ9lavQanQ4BNw=')

today = datetime.datetime.today().strftime('%Y-%m-%d')


# %%
def extract_symbol_pre(symbol_with_number):
    '''
    :param symbol_with_number: 'RB2010'
    :return: rb, 2010, SHFE
    '''
    symbol_pre = re.findall(r'[a-zA-Z]+', symbol_with_number)[0]
    # 根据symbol_pre所在的交易所，将symbol_pre转换为大写或小写：如果symbol_pre不在['CZCE', 'CFFEX']中，则转换为小写
    if symbol_pre not in all_symbol_pres['CZCE'] + all_symbol_pres['CFFEX']:
        symbol_pre = symbol_pre.lower()
    # 根据symbol_pre判断所在的交易所
    for k, v in all_symbol_pres.items():
        if symbol_pre in v:
            exchange = k
    symbol_num = re.findall(r'[0-9]+', symbol_with_number)[0]
    symbol_num_rq = symbol_num  # 米筐格式：字母大写，4位数字
    if exchange == 'CZCE':
        symbol_num = symbol_num[1:]
    return symbol_pre, symbol_num, symbol_num_rq, exchange


# %%
# 包装成函数，输入symbol_pre，输出df
def get_main_ticker(symbol_pre):
    pd_series = rq.futures.get_dominant(symbol_pre
                                        , start_date='2021-01-01'
                                        , end_date=today
                                        )
    if pd_series is None:
        return pd.DataFrame()
    df = pd_series.to_frame()
    df.reset_index(inplace=True)
    df.rename(columns={'dominant': 'specific_symbol', 'date': 'orig_date'}, inplace=True)
    df['symbol_pre'] = df['specific_symbol'].apply(lambda x: extract_symbol_pre(x)[0])
    df['symbol_num'] = df['specific_symbol'].apply(lambda x: extract_symbol_pre(x)[1])
    df['symbol_num_rq'] = df['specific_symbol'].apply(lambda x: extract_symbol_pre(x)[2])
    df['exchange'] = df['specific_symbol'].apply(lambda x: extract_symbol_pre(x)[3])
    df['specific_symbol'] = df['symbol_pre'] + df['symbol_num'] + '.' + df['exchange']
    df['specific_symbol_rq'] = df['symbol_pre'] + df['symbol_num_rq'] + '.' + df['exchange']
    df['main_symbol'] = df['symbol_pre'] + '88.' + df['exchange']
    main_ticker = df[['main_symbol', 'specific_symbol', 'orig_date']]
    main_ticker_rq = df[['main_symbol', 'specific_symbol_rq', 'orig_date']]
    return main_ticker, main_ticker_rq


# get_main_ticker('AP')[0]
# %%
def get_symbol_mapping_his():
    symbol_list = ['a', 'b', 'bb', 'c', 'cs', 'eb', 'eg', 'fb', 'i', 'j', 'jd', 'jm', 'l', 'lh', 'm', 'p', 'pg', 'pp',
                   'rr',
                   'v', 'y', 'ag', 'al', 'ao', 'au', 'br', 'bu', 'cu', 'fu', 'hc', 'ni', 'pb', 'rb', 'ru', 'sn', 'sp',
                   'ss',
                   'wr', 'zn', 'AP', 'CF', 'CJ', 'CY', 'FG', 'JR', 'LR', 'MA', 'OI', 'PF', 'PK', 'PM', 'PX', 'RI', 'RM',
                   'RS', 'SA', 'SF', 'SH', 'SM', 'SR', 'TA', 'UR', 'WH', 'ZC', 'bc', 'ec', 'lu', 'nr', 'sc', 'lc',
                   'si', 'IC', 'IF', 'IH', 'IM', 'T', 'TF', 'TS']
    # 对每个symbol_list，转为大写，获取df，然后合并
    df_list = []
    df_list_rq = []
    for i in symbol_list:
        df, df_rq = get_main_ticker(i.upper())
        df_list.append(df)
        df_list_rq.append(df_rq)
    df_all = pd.concat(df_list)
    df_all_rq = pd.concat(df_list_rq)
    return df_all, df_all_rq


# symbol_mapping_his, symbol_mapping_his_rq = get_symbol_mapping_his()
# symbol_mapping_his
# %%
'''
CREATE TABLE `symbol_mapping_his` (
  `id` int NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '888合约',
  `specific_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '具体合约',
  `orig_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;
'''
# 新结构：
'''
CREATE TABLE `symbol_mapping_his` (
  `id` int NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '888合约',
  `specific_symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '具体合约',
  `orig_date` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uc_main_symbol_orig_date` (`main_symbol`,`orig_date`)
) ENGINE=InnoDB AUTO_INCREMENT=39285 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC
'''
# 主要变化：1.将orig_date的类型改为date；2.添加唯一索引
# 保存到数据库（scout表的symbol_mapping_his中）
# 新新结构
'''
CREATE TABLE `symbol_mapping_switch` (
  `index` bigint NOT NULL AUTO_INCREMENT,
  `main_symbol` varchar(255) NOT NULL,
  `old_symbol` varchar(255) NOT NULL,
  `new_symbol` varchar(255) NOT NULL,
  `old_price` float NOT NULL,
  `new_price` float NOT NULL,
  `orig_date` datetime NOT NULL,
  PRIMARY KEY (`index`),
  UNIQUE KEY `unique_main_symbol_orig_date` (`main_symbol`, `orig_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
'''


# 主要变化：增加old_symbol, old_price, new_price，specific_symbol改为new_symbol
# 表中存储换月信息，记录每个main_symbol的换月日的 原合约名，新合约名。原合约价格，新合约价格。
#    价格取orig_date当日两个合约的10：00的  close值【原始值，不赋权】

# 获取mysql引擎
def get_mysql_engine():
    userName = 'remote'
    password = 'zhP@55word'
    dbHost = 'localhost'
    dbPort = 3308
    dbName = 'common_info'
    DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'
    mysql_engine = create_engine(
        DB_CONNECT,
        max_overflow=50,  # 超过连接池大小外最多创建的连接
        pool_size=50,  # 连接池大小
        pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错
        pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
        # encoding='utf-8',
        echo=False
    )
    return mysql_engine


# %%
def get_symbol_mapping_switch(df):
    # 将df的specific_symbol改为new_symbol
    df.rename(columns={'specific_symbol_rq': 'new_symbol'}, inplace=True)
    # 将new_symbol转为大写
    df['new_symbol'] = df['new_symbol'].apply(lambda x: x.upper())
    # 近180天的数据
    # df = df[df['orig_date'] >= pd.Timestamp(today) - pd.Timedelta(days=180)]
    # 只保留2023年10月28日以后的数据
    df = df[df['orig_date'] >= '2023-10-28']

    order_book_ids = df['new_symbol'].unique().tolist()
    order_book_ids = [extract_vt_symbol(x)[0] for x in order_book_ids]

    # 以main_symbol和orig_date为multiindex
    df = df.set_index(['main_symbol', 'orig_date']).sort_index()
    # old_symbol为new_symbol的组内shift
    df['old_symbol'] = df.groupby('main_symbol')['new_symbol'].shift(1)
    # 只保留new_symbol不等于old_symbol的行
    df = df[df['new_symbol'] != df['old_symbol']].dropna()

    # 取所有order_book_ids，在min(orig_date)到max(orig_date)之间每天上午10:00的收盘价（不复权）
    start_date = df.index.get_level_values('orig_date').min()
    end_date = df.index.get_level_values('orig_date').max()

    price_df = rq.get_price(order_book_ids, start_date=start_date, end_date=end_date, fields='close', frequency='1m',
                            adjust_type='none', expect_df=True, time_slice=(
        datetime.time(10, 0), datetime.time(10, 1)))  # 返回MultiIndex， names=['order_book_id', 'datetime']
    # 移除索引
    price_df.reset_index(inplace=True)
    # 因为time_slice，有10:00:00和10:00:01两个时间点的数据，取10:00:00的数据
    price_df = price_df[price_df['datetime'].dt.time == datetime.time(10, 0)]
    # 将datetime转为datetime64[ns]格式的date
    price_df['date'] = pd.to_datetime(price_df['datetime'].dt.date)
    # 将order_book_id转为vt_symbol
    price_df['vt_symbol'] = price_df['order_book_id'].apply(
        lambda x: extract_symbol_pre(x)[0].upper() + extract_symbol_pre(x)[2] + '.' + extract_symbol_pre(x)[3])

    df = df.reset_index()
    # 合并price_df的close列到df，df以orig_date和new_symbol为key，price_df以date和vt_symbol和为key，新列名为new_price
    df = pd.merge(df, price_df[['date', 'vt_symbol', 'close']], left_on=['orig_date', 'new_symbol'],
                  right_on=['date', 'vt_symbol'], how='left')
    df.rename(columns={'close': 'new_price'}, inplace=True)
    # 合并price_df的close列到df，df以orig_date和old_symbol为key，price_df以date和vt_symbol和为key，新列名为old_price
    df = pd.merge(df, price_df[['date', 'vt_symbol', 'close']], left_on=['orig_date', 'old_symbol'],
                  right_on=['date', 'vt_symbol'], how='left')
    df.rename(columns={'close': 'old_price'}, inplace=True)
    # 保留需要的列，索引multiindex置为main_symbol和orig_date
    df = df[['main_symbol', 'old_symbol', 'new_symbol', 'old_price', 'new_price', 'orig_date']]
    # 将old_symbol和new_symbol转为标准格式
    df['old_symbol'] = df['old_symbol'].apply(lambda x: extract_vt_symbol(x)[0])
    df['old_symbol'] = df['old_symbol'].apply(
        lambda x: extract_symbol_pre(x)[0] + extract_symbol_pre(x)[1] + '.' + extract_symbol_pre(x)[3])
    df['new_symbol'] = df['new_symbol'].apply(lambda x: extract_vt_symbol(x)[0])
    df['new_symbol'] = df['new_symbol'].apply(
        lambda x: extract_symbol_pre(x)[0] + extract_symbol_pre(x)[1] + '.' + extract_symbol_pre(x)[3])
    df.dropna(inplace=True)
    return df


# symbol_mapping_switch = get_symbol_mapping_switch(symbol_mapping_his_rq)
# %%
# #%%
# # 保存到数据库：先删除，再插入
# # # 1.删除
# # sql = f"delete from symbol_mapping_his"
# # with mysql_engine.connect() as conn:
# #     conn.execute(text(sql))
# #     conn.commit()
# #     print(f"symbol_mapping_his删除成功：{sql}")
# # # 2.插入
# # df_all.to_sql('symbol_mapping_his', mysql_engine, if_exists='append', index=False)
#
# # 直接使用if_exists='replace'
def main():
    symbol_mapping_his, symbol_mapping_his_rq = get_symbol_mapping_his()

    if symbol_mapping_his.empty:
        print('[symbol_mapping_his]无数据，请检查')
        raise ValueError
    else:
        mysql_engine = get_mysql_engine()
        symbol_mapping_his.to_sql('symbol_mapping_his',
                                  mysql_engine,
                                  if_exists='replace',
                                  # index=False
                                  )

        symbol_mapping_switch = get_symbol_mapping_switch(symbol_mapping_his_rq)
        symbol_mapping_switch.to_sql('symbol_mapping_switch',
                                     mysql_engine,
                                     if_exists='replace',
                                     # index=False
                                     )


# %%
# main()
# %%
if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")

        # 每日更新
        main()
        print(f"{__file__}: Finished all work!")
    except Exception as e:
        print(e)
        from send_to_wechat import WeChat

        wx = WeChat()
        import requests
        ip = requests.get('https://ifconfig.me').text
        wx.send_data(f"{ip}:{__file__}: An error occurred! ", touser='liaoyuan')