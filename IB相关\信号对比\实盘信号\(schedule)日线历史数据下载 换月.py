import multiprocessing
import os
import sys
from datetime import datetime, timedelta, time
from time import sleep
from retry import retry

import pandas as pd
import pytz
import schedule
from vnpy.trader.object import HistoryRequest, Interval
from vnpy.trader.utility import load_json, save_json
import warnings
warnings.filterwarnings('ignore')

# Define the trading periods in Eastern Time (ET) for US stock market
# US_DAY_START = time(9, 30)  # Regular trading hours start at 9:30 AM ET
# US_DAY_END = time(16, 0)  # Regular trading hours end at 4:00 PM ET

connect_ib_api_setting = {"TWS地址": "localhost", "TWS端口": 7497, "客户号": 13, "交易账户": "", "查询期权": "否"}
# connect_ib_api_setting = {"TWS地址": "**************", "TWS端口": 4002, "客户号": 12, "交易账户": "", "查询期权": "否"}


# 定义函数（查询历史数据并把历史数据BarData输出到csv），便于使用函数循环下载
@retry(tries=3, delay=0.3)
def req_download_historydata(engine, database_manager, vt_symbol, start, end, interval=Interval.MINUTE):
    contract = engine.get_contract(vt_symbol)
    if not contract:
        # 打印时间、函数名、msg
        print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] 找不到合约：{vt_symbol}')
        return
    historyreq = HistoryRequest(symbol=contract.symbol, exchange=contract.exchange, start=start, end=end,
                                interval=interval)
    bardatalist = engine.query_history(historyreq, contract.gateway_name)
    # 把历史数据BarData输出到csv，路径为当前目录的data文件夹下，文件名为合约名、start的日期、end的日期
    # pd.DataFrame(bardatalist).to_csv('data/' + str(historyreq.symbol) + "_" + str(historyreq.start.date()) + "_" + str(
    #     historyreq.end.date()) + ".csv", index=True, header=True)
    # print(
    #     f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] History data export to CSV: {vt_symbol}')
    # 把历史数据BarData放入数据库
    database_manager.save_bar_data(bardatalist)
    print(
        f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [req_download_historydata] History data export to DB: {vt_symbol} interval:{interval.value}')

def run_child():
    """
    Running in the child process.
    """
    # print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] 当前工作目录:{os.getcwd()}')
    # if not os.path.exists("data"):
    #     os.mkdir("data")
    from vnpy.trader.setting import SETTINGS
    SETTINGS["database.database"] = "vnpyibhis"
    from data_recorder_fake_bar.utils.ib_gateway import IbGateway
    from vnpy.event import EventEngine
    from vnpy.trader.database import get_database
    from vnpy.trader.engine import MainEngine
    # 订阅行情
    # 创建事件引擎
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    main_engine.add_gateway(IbGateway)
    main_engine.connect(connect_ib_api_setting, "IB")

    database_manager = get_database()

    # 使用script_engine订阅历史数据是从rqdata获取，vnpy v2.07已经提供历史数据获取，这里创建HistoryRequest来获取,
    # 查询如果没有endtime，默认当前。返回历史数据输出到数据库和csv文件
    # 关于api更多信息可以参见 https://interactivebrokers.github.io/tws-api/historical_bars.html
    print(
        f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] ***从IB读取历史数据, 返回历史数据输出到数据库和csv文件***')

    # 下载昨晚21：30到今天4：00的数据，IB的数据是UTC时间，所以需要转换
    today = datetime.now(pytz.timezone('Asia/Shanghai'))
    yesday = today - timedelta(days=1)
    start = datetime(yesday.year, yesday.month, yesday.day, 4, 0)
    # start = datetime(yesday.year, yesday.month, yesday.day, 3, 59).astimezone(pytz.timezone('UTC'))
    end = datetime(today.year, today.month, today.day, 4, 0)
    # print(start, end)  # 注意下载的数据是左闭右开，不包含end时间点的数据
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] {start} - {end}')

    # 订阅行情
    from vnpy.trader.object import SubscribeRequest
    from vnpy.trader.utility import extract_vt_symbol
    # 下载历史数据
    vn_symbol_fut = load_json("vn_symbol_fut.json")
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] vn_symbol_fut:{vn_symbol_fut}')
    for vt_symbol in vn_symbol_fut:
        symbol, exchange = extract_vt_symbol(vt_symbol)
        req1 = SubscribeRequest(symbol, exchange)  # 创建行情订阅
        main_engine.subscribe(req1, "IB")
    sleep(15)

    # 获取信息并保存到数据库
    from copy import deepcopy
    contract_dict = deepcopy(main_engine.get_gateway('IB').api.contract_dict)
    # print(contract_dict)
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] contract_dict.keys():{contract_dict.keys()}')
    save_json("contract_dict.json", contract_dict)

    for symbol,contract in contract_dict.items():
        for vt_symbol in contract:
            # 先订阅行情
            symbol, exchange = extract_vt_symbol(vt_symbol)
            req1 = SubscribeRequest(symbol, exchange)  # 创建行情订阅
            main_engine.subscribe(req1, "IB")
            sleep(0.3)
            req_download_historydata(main_engine, database_manager, vt_symbol, start, end, interval=Interval.DAILY)
            print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] vt_symbol:{vt_symbol} 下载完成')

    sleep(60 * 5)

    sleep(60)
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child] 关闭子进程')
    main_engine.close()
    sys.exit(0)  # sys.exit(0)表示正常退出程序，sys.exit(1)表示异常退出程序


def run_parent():
    """
    Running in the parent process.
    """
    print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_parent] 启动历史数据下载守护父进程')

    child_process = None

    def run_child_daily():
        """
        This function will be called daily at 5:30 AM.
        """
        print("启动子进程")
        nonlocal child_process

        if child_process is None:
            child_process = multiprocessing.Process(target=run_child)
            child_process.start()
            print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child_daily] 子进程启动成功')

        sleep(60 * 10)

        # 非记录时间则退出子进程
        if child_process is not None:
            if not child_process.is_alive():
                child_process = None
                print(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")} [run_child_daily] 子进程关闭成功')

    run_child_daily()
    schedule.every().day.at("05:30").do(run_child_daily)

    while True:
        schedule.run_pending()
        sleep(60)


if __name__ == "__main__":
    run_parent()
