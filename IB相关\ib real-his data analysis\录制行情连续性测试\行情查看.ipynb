{"cells": [{"cell_type": "code", "id": "d3a6d39100c2545f", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-05-28T08:51:32.036857Z", "start_time": "2024-05-28T08:51:31.854799Z"}}, "source": ["from sqlalchemy import create_engine\n", "from urllib.parse import quote_plus as urlquote\n", "userName = 'root'\n", "password = 'p0o9i8u7'\n", "# dbHost = 'localhost'\n", "dbHost = '************'\n", "dbPort = 3306\n", "dbName = 'vnpyib'\n", "DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/{dbName}?charset=utf8'\n", "engine = create_engine(\n", "    DB_CONNECT,\n", "    max_overflow=50,  # 超过连接池大小外最多创建的连接\n", "    pool_size=50,  # 连接池大小\n", "    pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错\n", "    pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）\n", "    encoding='utf-8',\n", "    echo=False\n", ")"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T08:51:34.256329Z", "start_time": "2024-05-28T08:51:32.038851Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "sql_query = \"select t.* from dbbaroverview t\"\n", "df = pd.read_sql_query(sql_query, engine)\n", "df"], "id": "initial_id", "outputs": [{"data": {"text/plain": ["     id     symbol  exchange interval  count               start  \\\n", "0    87  681320253      HKFE       1m    307 2024-05-28 10:25:00   \n", "1    88  620730945       CME       1m    386 2024-05-28 10:25:00   \n", "2    89  324078607     COMEX       1m    386 2024-05-28 10:25:00   \n", "3    90  620730920       CME       1m    386 2024-05-28 10:25:00   \n", "4    91  551601561       CME       1m    386 2024-05-28 10:25:00   \n", "5    92  531539945      SEHK       1m    287 2024-05-28 10:25:00   \n", "6    93   14016494     SMART       1m    157 2024-05-28 10:25:00   \n", "7    94  563839543     EUREX       1m    386 2024-05-28 10:25:00   \n", "8    95  679628463   SEHKNTL       1m    187 2024-05-28 10:25:00   \n", "9    96  195486659      TWSE       1m    189 2024-05-28 10:25:00   \n", "10   97  619193152  SEHKSTAR       1m    187 2024-05-28 10:25:00   \n", "11   98  131083965     SMART       1m    157 2024-05-28 10:25:00   \n", "12   99   13905884     SMART       1m    157 2024-05-28 10:25:00   \n", "13  100     909271     SMART       1m    111 2024-05-28 15:00:00   \n", "14  101   39234893     SMART       1m    111 2024-05-28 15:00:00   \n", "15  102  542125742     SMART       1m    111 2024-05-28 15:00:00   \n", "16  103  447545380     SMART       1m    111 2024-05-28 15:00:00   \n", "17  104   29622921     SMART       1m    111 2024-05-28 15:00:00   \n", "18  105     909083     SMART       1m    111 2024-05-28 15:00:00   \n", "\n", "                   end  \n", "0  2024-05-28 16:30:00  \n", "1  2024-05-28 16:50:00  \n", "2  2024-05-28 16:50:00  \n", "3  2024-05-28 16:50:00  \n", "4  2024-05-28 16:50:00  \n", "5  2024-05-28 16:10:00  \n", "6  2024-05-28 14:00:00  \n", "7  2024-05-28 16:50:00  \n", "8  2024-05-28 15:00:00  \n", "9  2024-05-28 13:33:00  \n", "10 2024-05-28 15:00:00  \n", "11 2024-05-28 14:00:00  \n", "12 2024-05-28 14:00:00  \n", "13 2024-05-28 16:50:00  \n", "14 2024-05-28 16:50:00  \n", "15 2024-05-28 16:50:00  \n", "16 2024-05-28 16:50:00  \n", "17 2024-05-28 16:50:00  \n", "18 2024-05-28 16:50:00  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>interval</th>\n", "      <th>count</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>87</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>1m</td>\n", "      <td>307</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:30:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>88</td>\n", "      <td>620730945</td>\n", "      <td>CME</td>\n", "      <td>1m</td>\n", "      <td>386</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>89</td>\n", "      <td>324078607</td>\n", "      <td>COMEX</td>\n", "      <td>1m</td>\n", "      <td>386</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>90</td>\n", "      <td>620730920</td>\n", "      <td>CME</td>\n", "      <td>1m</td>\n", "      <td>386</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>91</td>\n", "      <td>551601561</td>\n", "      <td>CME</td>\n", "      <td>1m</td>\n", "      <td>386</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>92</td>\n", "      <td>531539945</td>\n", "      <td>SEHK</td>\n", "      <td>1m</td>\n", "      <td>287</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:10:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>93</td>\n", "      <td>14016494</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>157</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 14:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>94</td>\n", "      <td>563839543</td>\n", "      <td>EUREX</td>\n", "      <td>1m</td>\n", "      <td>386</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>95</td>\n", "      <td>679628463</td>\n", "      <td>SEHKNTL</td>\n", "      <td>1m</td>\n", "      <td>187</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>96</td>\n", "      <td>195486659</td>\n", "      <td>TWSE</td>\n", "      <td>1m</td>\n", "      <td>189</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 13:33:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>97</td>\n", "      <td>619193152</td>\n", "      <td>SEHKSTAR</td>\n", "      <td>1m</td>\n", "      <td>187</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>98</td>\n", "      <td>131083965</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>157</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 14:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>99</td>\n", "      <td>13905884</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>157</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>2024-05-28 14:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>100</td>\n", "      <td>909271</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>101</td>\n", "      <td>39234893</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>102</td>\n", "      <td>542125742</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>103</td>\n", "      <td>447545380</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>104</td>\n", "      <td>29622921</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>105</td>\n", "      <td>909083</td>\n", "      <td>SMART</td>\n", "      <td>1m</td>\n", "      <td>111</td>\n", "      <td>2024-05-28 15:00:00</td>\n", "      <td>2024-05-28 16:50:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T08:51:34.676440Z", "start_time": "2024-05-28T08:51:34.257323Z"}}, "cell_type": "code", "source": ["symbol0, exchange0 = df['symbol'][0], df['exchange'][0]\n", "sql_query = f\"select t.* from dbbardata t where symbol = '{symbol0}' and exchange = '{exchange0}'\"\n", "df = pd.read_sql_query(sql_query, engine)\n", "df"], "id": "34d2abba2b554187", "outputs": [{"data": {"text/plain": ["        id     symbol exchange            datetime interval  volume  turnover  \\\n", "0     6462  681320253     HKFE 2024-05-28 10:25:00       1m   196.0       0.0   \n", "1     6478  681320253     HKFE 2024-05-28 10:26:00       1m   526.0       0.0   \n", "2     6489  681320253     HKFE 2024-05-28 10:27:00       1m   357.0       0.0   \n", "3     6502  681320253     HKFE 2024-05-28 10:28:00       1m   259.0       0.0   \n", "4     6516  681320253     HKFE 2024-05-28 10:29:00       1m   285.0       0.0   \n", "..     ...        ...      ...                 ...      ...     ...       ...   \n", "302  10408  681320253     HKFE 2024-05-28 16:26:00       1m    73.0       0.0   \n", "303  10421  681320253     HKFE 2024-05-28 16:27:00       1m   742.0       0.0   \n", "304  10436  681320253     HKFE 2024-05-28 16:28:00       1m   336.0       0.0   \n", "305  10454  681320253     HKFE 2024-05-28 16:29:00       1m   561.0       0.0   \n", "306  10466  681320253     HKFE 2024-05-28 16:30:00       1m     0.0       0.0   \n", "\n", "     open_interest  open_price  high_price  low_price  close_price  \n", "0              0.0     18963.0     18970.0    18958.0      18958.0  \n", "1              0.0     18954.0     18965.0    18948.0      18952.0  \n", "2              0.0     18953.0     18956.0    18944.0      18954.0  \n", "3              0.0     18953.0     18964.0    18949.0      18955.0  \n", "4              0.0     18957.0     18963.0    18936.0      18936.0  \n", "..             ...         ...         ...        ...          ...  \n", "302            0.0     18793.0     18793.0    18790.0      18793.0  \n", "303            0.0     18792.0     18793.0    18789.0      18792.0  \n", "304            0.0     18791.0     18793.0    18790.0      18791.0  \n", "305            0.0     18790.0     18792.0    18786.0      18791.0  \n", "306            0.0     18791.0     18791.0    18791.0      18791.0  \n", "\n", "[307 rows x 12 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>datetime</th>\n", "      <th>interval</th>\n", "      <th>volume</th>\n", "      <th>turnover</th>\n", "      <th>open_interest</th>\n", "      <th>open_price</th>\n", "      <th>high_price</th>\n", "      <th>low_price</th>\n", "      <th>close_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6462</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>1m</td>\n", "      <td>196.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18963.0</td>\n", "      <td>18970.0</td>\n", "      <td>18958.0</td>\n", "      <td>18958.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6478</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:26:00</td>\n", "      <td>1m</td>\n", "      <td>526.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18954.0</td>\n", "      <td>18965.0</td>\n", "      <td>18948.0</td>\n", "      <td>18952.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6489</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:27:00</td>\n", "      <td>1m</td>\n", "      <td>357.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18953.0</td>\n", "      <td>18956.0</td>\n", "      <td>18944.0</td>\n", "      <td>18954.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6502</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:28:00</td>\n", "      <td>1m</td>\n", "      <td>259.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18953.0</td>\n", "      <td>18964.0</td>\n", "      <td>18949.0</td>\n", "      <td>18955.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6516</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:29:00</td>\n", "      <td>1m</td>\n", "      <td>285.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18957.0</td>\n", "      <td>18963.0</td>\n", "      <td>18936.0</td>\n", "      <td>18936.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>10408</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 16:26:00</td>\n", "      <td>1m</td>\n", "      <td>73.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18793.0</td>\n", "      <td>18793.0</td>\n", "      <td>18790.0</td>\n", "      <td>18793.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>10421</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 16:27:00</td>\n", "      <td>1m</td>\n", "      <td>742.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18792.0</td>\n", "      <td>18793.0</td>\n", "      <td>18789.0</td>\n", "      <td>18792.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>10436</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 16:28:00</td>\n", "      <td>1m</td>\n", "      <td>336.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18791.0</td>\n", "      <td>18793.0</td>\n", "      <td>18790.0</td>\n", "      <td>18791.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>10454</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 16:29:00</td>\n", "      <td>1m</td>\n", "      <td>561.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18790.0</td>\n", "      <td>18792.0</td>\n", "      <td>18786.0</td>\n", "      <td>18791.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>306</th>\n", "      <td>10466</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 16:30:00</td>\n", "      <td>1m</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18791.0</td>\n", "      <td>18791.0</td>\n", "      <td>18791.0</td>\n", "      <td>18791.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>307 rows × 12 columns</p>\n", "</div>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T08:51:34.707408Z", "start_time": "2024-05-28T08:51:34.678408Z"}}, "cell_type": "code", "source": ["# 理论上datetime列的时间上一个和下一个id之间的时间差应该是1分钟，如果不是1分钟，说明数据有问题，查看一下\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "df['diff'] = df['datetime'].diff().dt.total_seconds()\n", "df[df['diff'] != 60]"], "id": "cf19874599038a81", "outputs": [{"data": {"text/plain": ["      id     symbol exchange            datetime interval  volume  turnover  \\\n", "0   6462  681320253     HKFE 2024-05-28 10:25:00       1m   196.0       0.0   \n", "96  8004  681320253     HKFE 2024-05-28 13:00:00       1m   848.0       0.0   \n", "\n", "    open_interest  open_price  high_price  low_price  close_price    diff  \n", "0             0.0     18963.0     18970.0    18958.0      18958.0     NaN  \n", "96            0.0     18908.0     18908.0    18886.0      18886.0  3600.0  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>exchange</th>\n", "      <th>datetime</th>\n", "      <th>interval</th>\n", "      <th>volume</th>\n", "      <th>turnover</th>\n", "      <th>open_interest</th>\n", "      <th>open_price</th>\n", "      <th>high_price</th>\n", "      <th>low_price</th>\n", "      <th>close_price</th>\n", "      <th>diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6462</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 10:25:00</td>\n", "      <td>1m</td>\n", "      <td>196.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18963.0</td>\n", "      <td>18970.0</td>\n", "      <td>18958.0</td>\n", "      <td>18958.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>8004</td>\n", "      <td>681320253</td>\n", "      <td>HKFE</td>\n", "      <td>2024-05-28 13:00:00</td>\n", "      <td>1m</td>\n", "      <td>848.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>18908.0</td>\n", "      <td>18908.0</td>\n", "      <td>18886.0</td>\n", "      <td>18886.0</td>\n", "      <td>3600.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}