{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tick data\n", "\n", "For optimum results this notebook should be run during the Forex trading session."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:21:22.880837Z", "start_time": "2025-06-16T03:21:22.127320Z"}}, "source": ["from ib_async import *\n", "util.startLoop()\n", "\n", "ib = IB()\n", "ib.connect('47.242.117.184', 4022, clientId=13)"], "outputs": [{"data": {"text/plain": ["<IB connected to 47.242.117.184:4012 clientId=13>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:16:31.720474Z", "start_time": "2025-06-16T03:16:31.716524Z"}}, "cell_type": "code", "source": "# ib.reqMarketDataType(4)", "outputs": [], "execution_count": 2}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming tick data\n", "\n", "Create some Forex contracts:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:46:19.896995Z", "start_time": "2024-10-10T08:46:19.186811Z"}}, "source": ["contracts = [Forex(pair) for pair in ('EURUSD', 'USDJPY', 'GBPUSD', 'USDCHF', 'USDCAD', 'AUDUSD')]\n", "ib.qualifyContracts(*contracts)\n", "\n", "eurusd = contracts[0]"], "outputs": [], "execution_count": 32}, {"cell_type": "markdown", "metadata": {}, "source": ["Request streaming ticks for them:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:44:10.702026Z", "start_time": "2024-10-10T08:44:10.689757Z"}}, "source": ["for contract in contracts:\n", "    ib.reqMktData(contract, '', False, False)"], "outputs": [], "execution_count": 15}, {"cell_type": "markdown", "metadata": {}, "source": ["Wait a few seconds for the tickers to get filled."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:45:47.736789Z", "start_time": "2024-10-10T08:45:45.697816Z"}}, "source": ["ticker = ib.ticker(eurusd)\n", "ib.sleep(6)\n", "\n", "ticker"], "outputs": [{"data": {"text/plain": ["Ticker(contract=Forex('EURUSD', conId=12087792, exchange='IDEALPRO', localSymbol='EUR.USD', tradingClass='EUR.USD'), time=datetime.datetime(2024, 10, 10, 8, 45, 46, 489105, tzinfo=datetime.timezone.utc), minTick=1e-05, bid=1.09345, bidSize=1000000.0, ask=1.09346, askSize=2000000.0, prevBid=1.09346, prevBidSize=2000000.0, prevAsk=1.09347, prevAskSize=1000000.0, high=1.0946, low=1.09285, close=1.094)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "execution_count": 26}, {"cell_type": "markdown", "metadata": {}, "source": ["The price of Forex ticks is always nan. To get a midpoint price use ``midpoint()`` or ``marketPrice()``.\n", "\n", "The tickers are kept live updated, try this a few times to see if the price changes:"]}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:37:43.218419Z", "start_time": "2025-06-16T03:37:42.746882Z"}}, "cell_type": "code", "source": ["AAPL = Stock(conId=265598)\n", "ib.qualifyContracts(AAPL)\n", "ib.reqMktData(AAPL, '', True, False) # 89 shortable shares\n", "ticker = ib.ticker(AAPL)\n", "\n", "counter = 0\n", "has_ask_bid = False\n", "\n", "while not ticker.hasBidAsk and counter < 6:\n", "    ib.waitOnUpdate(timeout=10)\n", "    counter += 1\n", "    if ticker.hasBidAsk:\n", "        has_ask_bid = True\n", "        # 入库\n", "        break\n", "\n", "if has_ask_bid:\n", "    for i in range(counter):\n", "        ib.waitOnUpdate(timeout=10)\n", "    # 入库\n", "\n", "    for i in range(counter):\n", "        ib.waitOnUpdate(timeout=10)\n", "    # 入库\n"], "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "execution_count": 26}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:37:43.513984Z", "start_time": "2025-06-16T03:37:43.509911Z"}}, "cell_type": "code", "source": "ticker.ask", "outputs": [{"data": {"text/plain": ["197.07"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "execution_count": 27}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:37:44.418225Z", "start_time": "2025-06-16T03:37:44.413633Z"}}, "cell_type": "code", "source": "ticker.bid", "outputs": [{"data": {"text/plain": ["197.01"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "execution_count": 28}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:25:22.124082Z", "start_time": "2025-06-16T03:25:22.119877Z"}}, "cell_type": "code", "source": "ticker.minTick", "outputs": [{"data": {"text/plain": ["0.01"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:25:25.564492Z", "start_time": "2025-06-16T03:25:25.561887Z"}}, "cell_type": "code", "source": "print(ticker)", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ticker(contract=Stock(conId=265598, symbol='AAPL', exchange='SMART', primaryExchange='NASDAQ', currency='USD', localSymbol='AAPL', tradingClass='NMS'), time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), minTick=0.01, bid=197.07, bidSize=400.0, ask=197.19, askSize=200.0, last=197.0, lastSize=200.0, volume=311.0, close=196.45, halted=0.0, ticks=[TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=4, price=197.0, size=200.0), TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=49, price=0.0, size=0), TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=8, price=-1.0, size=311.0), TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=9, price=196.45, size=0.0), TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=1, price=197.07, size=400.0), TickData(time=datetime.datetime(2025, 6, 16, 3, 25, 21, 526728, tzinfo=datetime.timezone.utc), tickType=2, price=197.19, size=200.0)], bboExchange='9c0001', snapshotPermissions=3)\n"]}], "execution_count": 16}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:03:06.698623Z", "start_time": "2025-06-16T03:03:04.632026Z"}}, "cell_type": "code", "outputs": [{"data": {"text/plain": ["197.06"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2, "source": ["AAPL = Stock('AAPL', 'SMART', 'USD')\n", "ib.qualifyContracts(AAPL)\n", "ib.reqMktData(AAPL, '236', False, False) # 89 shortable shares\n", "ticker = ib.ticker(AAPL)\n", "ib.sleep(2)\n", "ticker.bid"]}, {"metadata": {"ExecuteTime": {"end_time": "2024-10-11T05:53:23.964323Z", "start_time": "2024-10-11T05:53:23.949364Z"}}, "cell_type": "code", "source": "ticker.marketPrice()", "outputs": [{"data": {"text/plain": ["229.06"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "execution_count": 49}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-16T03:09:48.213453Z", "start_time": "2025-06-16T03:09:48.209952Z"}}, "cell_type": "code", "source": "ticker.shortableShares", "outputs": [{"data": {"text/plain": ["nan"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "execution_count": 26}, {"metadata": {"ExecuteTime": {"end_time": "2024-10-11T05:53:44.801376Z", "start_time": "2024-10-11T05:53:44.782430Z"}}, "cell_type": "code", "source": "ib.cancelMktData(AAPL)", "outputs": [], "execution_count": 50}, {"cell_type": "markdown", "metadata": {}, "source": ["The following cell will start a 30 second loop that prints a live updated ticker table.\n", "It is updated on every ticker change."]}, {"cell_type": "code", "metadata": {}, "source": ["from IPython.display import display, clear_output\n", "import pandas as pd\n", "\n", "df = pd.DataFrame(\n", "    index=[c.pair() for c in contracts],\n", "    columns=['bidSize', 'bid', 'ask', 'askSize', 'high', 'low', 'close'])\n", "\n", "def onPendingTickers(tickers):\n", "    for t in tickers:\n", "        df.loc[t.contract.pair()] = (\n", "            t.bidSize, t.bid, t.ask, t.askSize, t.high, t.low, t.close)\n", "        clear_output(wait=True)\n", "    display(df)        \n", "\n", "ib.pendingTickersEvent += onPendingTickers\n", "ib.sleep(30)\n", "ib.pendingTickersEvent -= onPendingTickers"], "execution_count": 11, "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["New tick data is available in the 'ticks' attribute of the pending tickers.\n", "The tick data will be cleared before the next update."]}, {"cell_type": "markdown", "metadata": {}, "source": ["To stop the live tick subscriptions:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:58:12.803988Z", "start_time": "2024-10-10T08:58:12.781581Z"}}, "source": ["for contract in contracts:\n", "    ib.cancelMktData(contract)"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["cancelMktData: No reqId found for contract Forex('EURUSD', conId=12087792, exchange='IDEALPRO', localSymbol='EUR.USD', tradingClass='EUR.USD')\n", "cancelMktData: No reqId found for contract Forex('USDJPY', conId=15016059, exchange='IDEALPRO', localSymbol='USD.JPY', tradingClass='USD.JPY')\n", "cancelMktData: No reqId found for contract Forex('GBPUSD', conId=12087797, exchange='IDEALPRO', localSymbol='GBP.USD', tradingClass='GBP.USD')\n", "cancelMktData: No reqId found for contract Forex('USDCHF', conId=12087820, exchange='IDEALPRO', localSymbol='USD.CHF', tradingClass='USD.CHF')\n", "cancelMktData: No reqId found for contract Forex('USDCAD', conId=15016062, exchange='IDEALPRO', localSymbol='USD.CAD', tradingClass='USD.CAD')\n", "cancelMktData: No reqId found for contract Forex('AUDUSD', conId=14433401, exchange='IDEALPRO', localSymbol='AUD.USD', tradingClass='AUD.USD')\n"]}], "execution_count": 48}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tick by Tick data ###\n", "\n", "The ticks in the previous section are time-sampled by IB in order to cut on bandwidth. So with ``reqMktdData`` not every tick from the exchanges is sent. The promise of ``reqTickByTickData`` is to send every tick, just how it appears in the TWS Time & Sales window. This functionality is severly nerfed by a total of just three simultaneous subscriptions, where bid-ask ticks and sale ticks also use up a subscription each.\n", "\n", "The tick-by-tick updates are available from ``ticker.tickByTicks`` and are signalled by ``ib.pendingTickersEvent`` or ``ticker.updateEvent``."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:42:26.027502Z", "start_time": "2024-10-10T08:42:23.999189Z"}}, "source": ["ticker = ib.reqTickByTickData(eurusd, 'BidAsk')\n", "ib.sleep(2)\n", "print(ticker)\n", "\n", "ib.cancelTickByTickData(ticker.contract, 'BidAsk')"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ticker(contract=Forex('EURUSD', conId=12087792, exchange='IDEALPRO', localSymbol='EUR.USD', tradingClass='EUR.USD'), time=datetime.datetime(2024, 10, 10, 8, 42, 22, 161182, tzinfo=datetime.timezone.utc), minTick=1e-05, bid=1.09348, bidSize=7000000.0, ask=1.0935, askSize=8000000.0, prevBid=1.09347, prevBidSize=6000000.0, prevAsk=1.09349, prevAskSize=11000000.0, high=1.0946, low=1.09285, close=1.094)\n"]}], "execution_count": 13}, {"cell_type": "markdown", "metadata": {}, "source": ["### Historical tick data\n", "\n", "Historical tick data can be fetched with a maximum of 1000 ticks at a time. Either the start time or the end time must be given, and one of them must remain empty:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-10-10T08:42:29.941020Z", "start_time": "2024-10-10T08:42:28.703616Z"}}, "source": ["import datetime\n", "\n", "start = ''\n", "end = datetime.datetime.now()\n", "ticks = ib.reqHistoricalTicks(eurusd, start, end, 1000, 'BID_ASK', useRth=False)\n", "\n", "ticks[-1]"], "outputs": [{"data": {"text/plain": ["HistoricalTickBidAsk(time=datetime.datetime(2024, 10, 10, 8, 42, 26, tzinfo=datetime.timezone.utc), tickAttribBidAsk=TickAttribBidAsk(bidPastLow=False, askPastHigh=False), priceBid=1.09348, priceAsk=1.09349, sizeBid=1000000.0, sizeAsk=2000000.0)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["ib.disconnect()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}