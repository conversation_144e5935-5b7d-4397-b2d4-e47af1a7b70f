from base64 import encode
from datetime import datetime
import pandas as pd
import numpy as np
import re
import json
import random
import time
from peewee import chunked

from loguru import logger

import requests
from bs4 import BeautifulSoup

from utils.database_manager import db_manager, InvestingRehabOriginal, InvestingRehabMerged
from utils.wecom_alert import *

# Configure logger
logger.add("logs/investingcn_div_split.log", rotation="1 day", retention="30 days", level="INFO", encoding="utf-8")


def parse_chinese_date(chinese_date_str):
    """
    Parse Chinese date string like '2025年8月27日' to datetime object
    """
    if not chinese_date_str:
        return np.nan
    
    # Pattern to match Chinese date format: YYYY年MM月DD日
    pattern = r'(\d{4})年(\d{1,2})月(\d{1,2})日'
    match = re.match(pattern, chinese_date_str)
    
    if match:
        year = int(match.group(1))
        month = int(match.group(2))
        day = int(match.group(3))
        return datetime(year, month, day)
    
    return np.nan


def get_split_data(start_date, end_date, max_retries):
    # 经测试验证的最小headers配置
    headers = {
        "x-requested-with": "XMLHttpRequest"
    }

    url = "https://cn.investing.com/stock-split-calendar/Service/getCalendarFilteredData"

    date_range = pd.date_range(start_date, end_date)
    daily_dfs = []
    no_results_days = ['以下日期无拆合股信息:']
    error_days= ['以下日期请求拆合股信息失败:']

    for date in date_range:
        if date.weekday() >= 5:
            continue # sat and sun

        date_str = date.strftime("%Y-%m-%d")
        logger.info(f'fetching split data for {date_str}')

        data = {
            "country[]": [
                "37",
                "36",
                "5",
                "4",
                "39"
            ],
            "dateFrom": date_str,
            "dateTo": date_str,
            "currentTab": "custom",
            "limit_from": "0"
        }

        for i in range(max_retries):
            # 经测试验证，无需cookies即可正常工作
            try:
                response = requests.post(url, headers=headers, data=data)
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                logger.error(f'hard requests error fetching split data for {date_str}: {e}')
                time.sleep(random.normalvariate(5, 1))
                continue
        else:
            logger.error(f'max retries exceeded when fetching split data for {date_str}')
            error_days.append(f'> `{date_str}`')
            continue

        soup = BeautifulSoup(response.json()['data'], 'html.parser')
        if soup.find('td', class_='noResults') is not None:
            logger.info(f'server says no stock split activity on {date_str}')
            no_results_days.append(f'> `{date_str}`')
            continue

        split_data = []
        current_date = ""
        rows = soup.find_all('tr')
        
        for row in rows:
            cells = row.find_all('td')
            if len(cells) == 3:
                # Get the date (first column)
                date_cell = cells[0].get_text(strip=True)
                if date_cell:  # If date is not empty, update current_date
                    current_date = date_cell
                    parsed_date = parse_chinese_date(date_cell)
                else:
                    parsed_date = parse_chinese_date(current_date)
                
                # Get company information (second column)
                company_cell = cells[1]
                company_name = company_cell.find('span', class_='inlineblock')
                company_name = company_name.get_text(strip=True) if company_name else np.nan
                
                # Get region from flag class
                flag_span = company_cell.find('span', class_='ceFlags')
                region = ""
                if flag_span:
                    flag_classes = flag_span.get('class', [])
                    for cls in flag_classes:
                        if cls not in ['ceFlags', 'middle']:
                            region = cls.replace('_', ' ')
                            break
                
                # Get stock symbol from the link
                symbol_link = company_cell.find('a', class_='bold')
                symbol = str(symbol_link.get_text(strip=True)) if symbol_link else np.nan
                
                # Get split ratio (third column)
                split_text = cells[2].get_text(strip=True)
                split = split_text.split(":")
                base = float(split[1])
                ert = float(split[0])
                split_ratio = base / ert
                
                # Only add if we have valid data
                if company_name and symbol and split_ratio and parsed_date:
                    if split_ratio < 1: #分股
                        split_data.append({
                            'ex_div_date': parsed_date,
                            'symbol': symbol,
                            'description': company_name,
                            'region': region,
                            'split_base': base,
                            'split_ert': ert,
                            'join_base': np.nan,
                            'join_ert': np.nan,
                            "base": base,
                            "ert": ert,
                            'split_ratio': split_ratio
                        })
                    elif split_ratio > 1: #合股
                        split_data.append({
                            'ex_div_date': parsed_date,
                            'symbol': symbol,
                            'description': company_name,
                            'region': region,
                            'split_base': np.nan,
                            'split_ert': np.nan,
                            'join_base': base,
                            'join_ert': ert,
                            "base": base,
                            "ert": ert,
                            'split_ratio': split_ratio
                        })
                else:
                    logger.warning(f'get_split_data: one or more among description/symbol/split_ratio/ex_div_date is NaN for row {row}')
            else:
                logger.warning(f'get_split_data: row does not contain the right number of cells for row {row}')
        
        # Convert to pandas DataFrame
        df = pd.DataFrame(split_data).sort_values(by='description').reset_index(drop=True)
        daily_dfs.append(df)

    final_df = pd.concat(daily_dfs, axis=0).sort_values(by=['ex_div_date', 'symbol'], ascending=[False, True]).reset_index(drop=True)
    final_df['symbol'] = final_df['symbol'].astype(str)
    final_df['ex_div_date'] = pd.to_datetime(final_df["ex_div_date"])
    
    # Combine no results and error days info
    split_info_parts = []
    if len(no_results_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_no_results = "\n\n".join(no_results_days)
        split_info_parts.append(formatted_no_results)
    if len(error_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_error_days = "\n\n".join(error_days)
        split_info_parts.append(formatted_error_days)
    
    split_info = "\n\n".join(split_info_parts) if split_info_parts else "> 无"
    return final_df, split_info


def get_div_data(start_date, end_date, max_retries):
    # 经测试验证，分红API只需要x-requested-with一个header即可正常工作（可获取33条数据）
    headers = {
        "x-requested-with": "XMLHttpRequest"
    }

    url = "https://cn.investing.com/dividends-calendar/Service/getCalendarFilteredData"

    date_range = pd.date_range(start_date, end_date)
    daily_dfs = []
    no_results_days = ['以下日期无分红信息:']
    error_days = ['以下日期请求分红信息失败:']

    for date in date_range:
        if date.weekday() >= 5:
            continue # sat and sun

        date_str = date.strftime("%Y-%m-%d")
        logger.info(f'fetching dividend data for {date_str}')

        data = {
            "country[]": [
                "37",
                "36",
                "5",
                "4",
                "39"
            ],
            "dateFrom": date_str,
            "dateTo": date_str,
            "currentTab": "custom",
            "limit_from": "0"
        }

        for i in range(max_retries):
            # 经测试验证，无需cookies即可正常工作
            try:
                response = requests.post(url, headers=headers, data=data)
                response.raise_for_status()
                break
            except requests.exceptions.RequestException as e:
                logger.error(f'hard requests error fetching dividend data for {date_str}: {e}')
                time.sleep(random.normalvariate(5, 1))
                continue
        else:
            logger.error(f'max retries exceeded when fetching dividend data for {date_str}')
            error_days.append(f'> `{date_str}`')
            continue

        soup = BeautifulSoup(response.json()['data'], 'html.parser')
        if soup.find('td', class_='noResults') is not None:
            logger.info(f'server says no dividend activity on {date_str}')
            no_results_days.append(f'> `{date_str}`')
            continue
                     
        dividend_data = []
        rows = soup.find_all('tr')
        for row in rows:

            cells = row.find_all('td')

            if "theDay" in cells[0].get('class', []): # Skip divider rows that just show dates
                continue

            if len(cells) == 7: 
                # Get company information (second column)
                company_cell = cells[1]
                company_name = company_cell.find('span', class_='earnCalCompanyName')
                company_name = company_name.get_text(strip=True) if company_name else np.nan
                
                # Get region from flag class (first column)
                flag_cell = cells[0]
                flag_span = flag_cell.find('span', class_='ceFlags')
                region = ""
                if flag_span:
                    flag_classes = flag_span.get('class', [])
                    for cls in flag_classes:
                        if cls not in ['ceFlags', 'middle']:
                            region = cls.replace('_', ' ')
                            break
                
                # Get stock symbol from the link
                symbol_link = company_cell.find('a', class_='bold')
                symbol = str(symbol_link.get_text(strip=True)) if symbol_link else np.nan
                
                # Get ex-dividend date (third column)
                ex_div_date_text = cells[2].get_text(strip=True)
                ex_div_date = parse_chinese_date(ex_div_date_text)
                
                # Get dividend amount (fourth column)
                dividend_amount_text = cells[3].get_text(strip=True)
                dividend_amount = float(dividend_amount_text) if dividend_amount_text.replace('.', '').replace('-', '').isdigit() else np.nan
                
                # Get dividend type (fifth column)
                type_cell = cells[4]
                type_span = type_cell.find('span')
                dividend_type = type_span.get('class', '')[0][4:] if type_span else np.nan
                
                # Get payment date (sixth column)
                payment_date_text = cells[5].get_text(strip=True)
                payment_date = parse_chinese_date(payment_date_text)
                
                # Get yield (seventh column)
                yield_text = cells[6].get_text(strip=True)
                yield_value = round(float(yield_text.replace('%', '')) / 100, 6) if yield_text != '-' and yield_text.replace('.', '').replace('%', '').replace('-', '').isdigit() else np.nan
                
                # Only add if we have valid data
                if company_name and symbol and ex_div_date and payment_date and dividend_amount:
                    dividend_data.append({
                        'ex_div_date': ex_div_date,
                        'symbol': symbol,
                        'description': company_name,
                        'region': region,
                        'div_amount': dividend_amount,
                        'div_type': dividend_type,
                        'pmt_date': payment_date,
                        'div_yield': yield_value
                    })
                else:
                    logger.warning(f'get_div_data: one or more among description/symbol/div_amount/ex_div_date/pmt_date is NaN for row {row}')
            else:
                logger.warning(f'get_div_data: wrong cell count for non-tablesorterdivider row {row}')
        
        # Convert to pandas DataFrame
        df = pd.DataFrame(dividend_data).sort_values(by='description').reset_index(drop=True)
        daily_dfs.append(df)

    final_df = pd.concat(daily_dfs, axis = 0).sort_values(by=['ex_div_date', 'symbol'], ascending=[False, True]).reset_index(drop=True)
    final_df['symbol'] = final_df['symbol'].astype(str)
    final_df['ex_div_date'] = pd.to_datetime(final_df["ex_div_date"])
    
    # Combine no results and error days info
    div_info_parts = []
    if len(no_results_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_no_results = "\n\n".join(no_results_days)
        div_info_parts.append(formatted_no_results)
    if len(error_days) > 1:
        # Format each date on its own line with proper spacing
        formatted_error_days = "\n\n".join(error_days)
        div_info_parts.append(formatted_error_days)
    
    div_info = "\n\n".join(div_info_parts) if div_info_parts else "> 无"
    return final_df, div_info


def merge_corporate_actions(rehab_df):
    """
    Simple merge using base and ert columns
    """
    # Group by company and date
    grouped = rehab_df.groupby(['symbol', 'description', 'ex_div_date', 'region']).agg({
        # Dividends
        'div_amount': lambda x: x.sum() if x.notna().any() else np.nan,
        'div_type': lambda x: '; '.join(x.dropna().unique()) if x.notna().any() else np.nan,
        'pmt_date': lambda x: x.dropna().iloc[0] if x.notna().any() else np.nan,
        'div_yield': lambda x: x.sum() if x.notna().any() else np.nan,
        
        # Splits - use base and ert columns directly
        'base': lambda x: x.prod() if x.notna().any() else np.nan,
        'ert': lambda x: x.prod() if x.notna().any() else np.nan,
        'split_ratio': lambda x: x.prod() if x.notna().any() else np.nan,
    }).reset_index()
    
    # Add action count after aggregation
    grouped['action_count'] = rehab_df.groupby(['symbol', 'description', 'ex_div_date', 'region']).size().values
    
    # Classify as split or join based on final ratio
    def classify_action(row):
        if pd.notna(row['split_ratio']):
            if row['split_ratio'] < 1:  # Split
                return pd.Series({
                    'split_base': row['base'],
                    'split_ert': row['ert'],
                    'join_base': np.nan,
                    'join_ert': np.nan
                })
            else:  # Join
                return pd.Series({
                    'split_base': np.nan,
                    'split_ert': np.nan,
                    'join_base': row['base'],
                    'join_ert': row['ert']
                })
        else:
            return pd.Series({
                'split_base': np.nan,
                'split_ert': np.nan,
                'join_base': np.nan,
                'join_ert': np.nan
            })
    
    # Apply classification
    action_classification = grouped.apply(classify_action, axis=1)
    
    # Combine results
    final_df = pd.concat([grouped, action_classification], axis=1).sort_values(by=['ex_div_date', 'region', 'symbol'], ascending=[False, True, True]).reset_index(drop=True)
    
    return final_df


def get_today_summary_markdown(rehab_df):
    """
    Retrieve today's data from rehab_df and return a markdown table.
    More robust version that handles different data types better.
    
    Args:
        rehab_df: DataFrame containing dividend and split data
        
    Returns:
        str: Markdown formatted table of today's data
    """
    from datetime import datetime
    import pandas as pd
    
    # Get today's date
    today = datetime.now().date()
    
    # Filter data for today
    today_data = rehab_df.loc[rehab_df['ex_div_date'].dt.date == today, :]
    
    if today_data.empty:
        return f"# {today.strftime('%Y-%m-%d')} 今日公司行动数据\n\n今日无分红或拆合股数据"
    
    # Create markdown table
    markdown_table = f"# {today.strftime('%Y-%m-%d')} 今日公司行动数据\n\n"
    markdown_table += "| 代码 | 地区 | 公司名称 | 拆合股比例 | 分红金额 |\n"
    markdown_table += "|------|------|----------|------------|----------|\n"
    
    for _, row in today_data.iterrows():
        # Extract and format data
        symbol = str(row.get('symbol', '')).strip()
        region = str(row.get('region', '')).strip()
        description = str(row.get('description', '')).strip()
        
        # Handle split/join ratio
        split_ratio = 'N/A'
        if pd.notna(row.get('split_ratio')):
            ratio = row.get('split_ratio')
            if ratio < 1:
                split_ratio = f"分股: {ratio:.4f}"
            elif ratio > 1:
                split_ratio = f"合股: {ratio:.4f}"
            else:
                split_ratio = f"1:1 ({ratio:.4f})"
        
        # Handle dividend amount
        div_amount = 'N/A'
        if pd.notna(row.get('div_amount')):
            div_amount = f"{row.get('div_amount'):.4f}"
        
        # Escape pipe characters for markdown
        symbol = symbol.replace('|', '\\|')
        region = region.replace('|', '\\|')
        description = description.replace('|', '\\|')
        split_ratio = split_ratio.replace('|', '\\|')
        div_amount = div_amount.replace('|', '\\|')
        
        markdown_table += f"| {symbol} | {region} | {description} | {split_ratio} | {div_amount} |\n"
    
    return markdown_table


def main():
    logger.info("Starting Investing.com dividend and split data collection process")

    start = "2025-08-15"
    end = "2025-08-28"
    retries = 3

    wecom_key = "ee0b6801-f2c5-4811-ba1f-227b543b3459"
    need_todays_table = True

    message1 = []
    message1.append(f"# {datetime.now().strftime("%Y-%m-%d")} 英为财情汇总")
    message1.append(f"起始日期: `{start}`")
    message1.append(f"截止日期: `{end}`")

    message1.append('---')

    message1.append(f"## 分红数据特殊情况")
    logger.info(f"Collecting dividend data from {start} to {end}")
    div_df, div_info = get_div_data(start, end, retries)
    logger.info(f"Collected {len(div_df)} dividend records")
    message1.append(div_info)

    message1.append('---')
    
    message1.append(f"## 拆合股数据特殊情况")
    logger.info(f"Collecting split data from {start} to {end}")
    split_df, split_info = get_split_data(start, end, retries)
    logger.info(f"Collected {len(split_df)} split records")
    message1.append(split_info)

    message1.append('---')
    
    rehab_df = pd.concat([div_df, split_df], axis=0, join='outer', ignore_index=True).sort_values(by=['ex_div_date', 'region', 'symbol'], ascending=[False, True, True]).reset_index(drop=True)
    logger.info(f"Original dataset contains {len(rehab_df)} total records")
    
    rehab_df_merged = merge_corporate_actions(rehab_df)
    logger.info(f"Merged dataset contains {len(rehab_df_merged)} same-day same-stock corporate action records")

    assert rehab_df_merged['action_count'].sum() == len(rehab_df) # ensure all corporate actions are recorded in merged df

    logger.info("Saving data to CSV files")
    rehab_df.to_csv('output/rehab_df_original.csv', encoding='UTF-8')
    rehab_df_merged.to_csv('output/rehab_df_merged.csv', encoding='UTF-8')
    div_df.to_csv('output/div_preview.csv', encoding='UTF-8')
    split_df.to_csv('output/split_preview.csv', encoding='UTF-8')
    
    logger.info("Saving info files")
    with open('output/div_info.txt', 'w', encoding='UTF-8') as f:
        f.write(div_info)
    with open('output/split_info.txt', 'w', encoding='UTF-8') as f:
        f.write(split_info)

    logger.info("Preparing data for database insertion...")
    db_records = rehab_df.replace({np.nan: None}).to_dict("records") # np.nan was not accepted
    db_records_merged = rehab_df_merged.replace({np.nan: None}).to_dict("records") # np.nan was not accepted
    # save original table
    message1.append(f"## 数据库")
    logger.info("Starting database operations")
    try:
        if not InvestingRehabOriginal.table_exists():
                logger.info("Investing分红和拆合股数据库表(原数据)不存在, 将创建新表")
                InvestingRehabOriginal.create_table()
        with db_manager.common_db.atomic():
            for batch in chunked(db_records, 50):
                InvestingRehabOriginal.insert_many(batch).on_conflict_replace().execute()
        logger.info(f"向investing_rehab_original表插入了{len(db_records)}条数据")
        message1.append(f"investing_rehab_original表共入库{len(db_records)}条数据")

        if not InvestingRehabMerged.table_exists():
                logger.info("Investing分红和拆合股数据库表(同天公司行动合并)不存在, 将创建新表")
                InvestingRehabMerged.create_table()
        with db_manager.common_db.atomic():
            for batch in chunked(db_records_merged, 50):
                InvestingRehabMerged.insert_many(batch).on_conflict_replace().execute()
        logger.info(f"向investing_rehab_merged表插入了{len(db_records_merged)}条数据")
        message1.append(f"investing_rehab_merged表共入库{len(db_records_merged)}条数据")
        
        logger.info("Investing分红和拆合股数据批量入库成功")
    except Exception as e:
        logger.error(f"Investing分红和拆合股数据批量入库失败: {e}")
        message1.append(f"入库失败, 请检查")

    if need_todays_table:
        message2 = get_today_summary_markdown(rehab_df)

    logger.info("Process completed successfully")

    logger.warning("WeCom does not support VPN (?_?). Please disconnect from any VPN while program sleeps 10 seconds...")
    time.sleep(10)

    ok1, e1 = report_we_alert(msgtype="markdown_v2", key=wecom_key, markdown_v2_content='\n\n'.join(message1), text="")
    if ok1:
        logger.info("download summary sent to WeCom successfully")
    elif not ok1:
        logger.error(f"Error when sending download summary to WeCom: {e1}")

    ok2, e2 = report_we_alert(msgtype="markdown_v2", key=wecom_key, markdown_v2_content=message2, text="")
    if ok2:
        logger.info("summary table sent to WeCom successfully")
    elif not ok2:
        logger.error(f"Error when sending summary table to WeCom: {e2}")
    
if __name__ == '__main__':
    main()