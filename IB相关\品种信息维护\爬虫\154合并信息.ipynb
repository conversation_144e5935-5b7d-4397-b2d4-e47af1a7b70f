{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2024-03-22T01:02:38.867524Z", "start_time": "2024-03-22T01:02:08.330849Z"}}, "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n0           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n1           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n2           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n3           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n4           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n...         ...          ...     ...        ...     ...      ...      ...   \n2805   STOCK.HK       STK.HK     STK   14016386    6146    SMART      JPY   \n2806   STOCK.HK       STK.HK     STK   13905743    6857    SMART      JPY   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2808   STOCK.HK       STK.HK     STK   ********    9984    SMART      JPY   \n2809   STOCK.HK       STK.HK     STK  *********    6920    SMART      JPY   \n\n     localSymbol marketName  Product_Description  \\\n0           TSLA        NMS            TESLA INC   \n1           TSLA        NMS            TESLA INC   \n2           TSLA        NMS            TESLA INC   \n3           TSLA        NMS            TESLA INC   \n4           TSLA        NMS            TESLA INC   \n...          ...        ...                  ...   \n2805      6146.T       6146           DISCO CORP   \n2806      6857.T       6857       ADVANTEST CORP   \n2807      600519     600519                  NaN   \n2808      9984.T       9984  SOFTBANK GROUP CORP   \n2809      6920.T       6920        LASERTEC CORP   \n\n                                                   链接地址  Symbol Currency  \n0     https://contract.ibkr.info/index.php?action=De...    TSLA      MXN  \n1     https://contract.ibkr.info/index.php?action=De...    TSLA      USD  \n2     https://contract.ibkr.info/index.php?action=De...    TSLA      USD  \n3     https://contract.ibkr.info/index.php?action=De...    TSLA      USD  \n4     https://contract.ibkr.info/index.php?action=De...    TSLA      USD  \n...                                                 ...     ...      ...  \n2805  https://contract.ibkr.info/index.php?action=De...  6146.T      JPY  \n2806  https://contract.ibkr.info/index.php?action=De...  6857.T      JPY  \n2807                                                NaN     NaN      NaN  \n2808  https://contract.ibkr.info/index.php?action=De...  9984.T      JPY  \n2809  https://contract.ibkr.info/index.php?action=De...  6920.T      JPY  \n\n[2810 rows x 13 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>MXN</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2805</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>14016386</td>\n      <td>6146</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6146.T</td>\n      <td>6146</td>\n      <td>DISCO CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6146.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2806</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>13905743</td>\n      <td>6857</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6857.T</td>\n      <td>6857</td>\n      <td>ADVANTEST CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6857.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2808</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>********</td>\n      <td>9984</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>9984.T</td>\n      <td>9984</td>\n      <td>SOFTBANK GROUP CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>9984.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2809</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>6920</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6920.T</td>\n      <td>6920</td>\n      <td>LASERTEC CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6920.T</td>\n      <td>JPY</td>\n    </tr>\n  </tbody>\n</table>\n<p>2810 rows × 13 columns</p>\n</div>"}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取“154支全市场近90日均成交$6亿以上股票.xlsx”的info、Sheet1表，以info的localSymbol列对应Sheet1的Symbol列，将Sheet1的数据合并到info中\n", "import pandas as pd\n", "info = pd.read_excel('154支全市场近90日均成交$6亿以上股票.xlsx', sheet_name='info')\n", "sheet1 = pd.read_excel('283680支 IB股票 按区域-国家地区-交易所分.xlsx', sheet_name='Sheet1', index_col=[0,1,2,3,4,5,6])\n", "info = pd.merge(info, sheet1, left_on='localSymbol', right_on='Symbol', how='left')# how='left'表示以info为基准，sheet1中没有的数据用NaN填充\n", "info"]}, {"cell_type": "code", "outputs": [], "source": ["from copy import deepcopy\n", "info_copy = deepcopy(info)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:03:11.530258Z", "start_time": "2024-03-22T01:03:11.517578Z"}}, "id": "e4cce77648a86958", "execution_count": 2}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n0           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n1           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n11          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n20          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n21          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n...         ...          ...     ...        ...     ...      ...      ...   \n2805   STOCK.HK       STK.HK     STK   14016386    6146    SMART      JPY   \n2806   STOCK.HK       STK.HK     STK   13905743    6857    SMART      JPY   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2808   STOCK.HK       STK.HK     STK   ********    9984    SMART      JPY   \n2809   STOCK.HK       STK.HK     STK  *********    6920    SMART      JPY   \n\n     localSymbol marketName  Product_Description  \\\n0           TSLA        NMS            TESLA INC   \n1           TSLA        NMS            TESLA INC   \n11          TSLA        NMS      TESLA INC - CDR   \n20          TSLA        NMS            TESLA INC   \n21          TSLA        NMS           LS 1X TSLA   \n...          ...        ...                  ...   \n2805      6146.T       6146           DISCO CORP   \n2806      6857.T       6857       ADVANTEST CORP   \n2807      600519     600519                  NaN   \n2808      9984.T       9984  SOFTBANK GROUP CORP   \n2809      6920.T       6920        LASERTEC CORP   \n\n                                                   链接地址  Symbol Currency  \n0     https://contract.ibkr.info/index.php?action=De...    TSLA      MXN  \n1     https://contract.ibkr.info/index.php?action=De...    TSLA      USD  \n11    https://contract.ibkr.info/index.php?action=De...    TSLA      CAD  \n20    https://contract.ibkr.info/index.php?action=De...    TSLA      CHF  \n21    https://contract.ibkr.info/index.php?action=De...    TSLA      EUR  \n...                                                 ...     ...      ...  \n2805  https://contract.ibkr.info/index.php?action=De...  6146.T      JPY  \n2806  https://contract.ibkr.info/index.php?action=De...  6857.T      JPY  \n2807                                                NaN     NaN      NaN  \n2808  https://contract.ibkr.info/index.php?action=De...  9984.T      JPY  \n2809  https://contract.ibkr.info/index.php?action=De...  6920.T      JPY  \n\n[433 rows x 13 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>MXN</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC - CDR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>CAD</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>CHF</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>LS 1X TSLA</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>EUR</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2805</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>14016386</td>\n      <td>6146</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6146.T</td>\n      <td>6146</td>\n      <td>DISCO CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6146.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2806</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>13905743</td>\n      <td>6857</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6857.T</td>\n      <td>6857</td>\n      <td>ADVANTEST CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6857.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n    </tr>\n    <tr>\n      <th>2808</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>********</td>\n      <td>9984</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>9984.T</td>\n      <td>9984</td>\n      <td>SOFTBANK GROUP CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>9984.T</td>\n      <td>JPY</td>\n    </tr>\n    <tr>\n      <th>2809</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>6920</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6920.T</td>\n      <td>6920</td>\n      <td>LASERTEC CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6920.T</td>\n      <td>JPY</td>\n    </tr>\n  </tbody>\n</table>\n<p>433 rows × 13 columns</p>\n</div>"}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["info = deepcopy(info_copy)\n", "# 去重\n", "info = info.drop_duplicates()\n", "info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:26:51.685084Z", "start_time": "2024-03-22T01:26:51.656260Z"}}, "id": "4bc59b431e7ee56e", "execution_count": 33}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n0           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n1           STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n11          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n20          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n21          STK   STK.NASDAQ     STK   76792991    TSLA    SMART      USD   \n...         ...          ...     ...        ...     ...      ...      ...   \n2805   STOCK.HK       STK.HK     STK   14016386    6146    SMART      JPY   \n2806   STOCK.HK       STK.HK     STK   13905743    6857    SMART      JPY   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2808   STOCK.HK       STK.HK     STK   ********    9984    SMART      JPY   \n2809   STOCK.HK       STK.HK     STK  *********    6920    SMART      JPY   \n\n     localSymbol marketName  Product_Description  \\\n0           TSLA        NMS            TESLA INC   \n1           TSLA        NMS            TESLA INC   \n11          TSLA        NMS      TESLA INC - CDR   \n20          TSLA        NMS            TESLA INC   \n21          TSLA        NMS           LS 1X TSLA   \n...          ...        ...                  ...   \n2805      6146.T       6146           DISCO CORP   \n2806      6857.T       6857       ADVANTEST CORP   \n2807      600519     600519                  NaN   \n2808      9984.T       9984  SOFTBANK GROUP CORP   \n2809      6920.T       6920        LASERTEC CORP   \n\n                                                   链接地址  Symbol Currency  \\\n0     https://contract.ibkr.info/index.php?action=De...    TSLA      MXN   \n1     https://contract.ibkr.info/index.php?action=De...    TSLA      USD   \n11    https://contract.ibkr.info/index.php?action=De...    TSLA      CAD   \n20    https://contract.ibkr.info/index.php?action=De...    TSLA      CHF   \n21    https://contract.ibkr.info/index.php?action=De...    TSLA      EUR   \n...                                                 ...     ...      ...   \n2805  https://contract.ibkr.info/index.php?action=De...  6146.T      JPY   \n2806  https://contract.ibkr.info/index.php?action=De...  6857.T      JPY   \n2807                                                NaN     NaN      NaN   \n2808  https://contract.ibkr.info/index.php?action=De...  9984.T      JPY   \n2809  https://contract.ibkr.info/index.php?action=De...  6920.T      JPY   \n\n                                                  链接地址1  \n0     https://contract.ibkr.info/index.php?action=De...  \n1     https://contract.ibkr.info/index.php?action=De...  \n11    https://contract.ibkr.info/index.php?action=De...  \n20    https://contract.ibkr.info/index.php?action=De...  \n21    https://contract.ibkr.info/index.php?action=De...  \n...                                                 ...  \n2805  https://contract.ibkr.info/index.php?action=De...  \n2806  https://contract.ibkr.info/index.php?action=De...  \n2807  https://contract.ibkr.info/index.php?action=De...  \n2808  https://contract.ibkr.info/index.php?action=De...  \n2809  https://contract.ibkr.info/index.php?action=De...  \n\n[433 rows x 14 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>MXN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>11</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC - CDR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>CAD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>20</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>TESLA INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>CHF</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>21</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>76792991</td>\n      <td>TSLA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>TSLA</td>\n      <td>NMS</td>\n      <td>LS 1X TSLA</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>TSLA</td>\n      <td>EUR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2805</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>14016386</td>\n      <td>6146</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6146.T</td>\n      <td>6146</td>\n      <td>DISCO CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6146.T</td>\n      <td>JPY</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2806</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>13905743</td>\n      <td>6857</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6857.T</td>\n      <td>6857</td>\n      <td>ADVANTEST CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6857.T</td>\n      <td>JPY</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2808</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>********</td>\n      <td>9984</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>9984.T</td>\n      <td>9984</td>\n      <td>SOFTBANK GROUP CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>9984.T</td>\n      <td>JPY</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2809</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>6920</td>\n      <td>SMART</td>\n      <td>JPY</td>\n      <td>6920.T</td>\n      <td>6920</td>\n      <td>LASERTEC CORP</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>6920.T</td>\n      <td>JPY</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n<p>433 rows × 14 columns</p>\n</div>"}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将“链接地址”列赋值为\"https://contract.ibkr.info/index.php?action=Details&site=GEN&conid=\" + conId\n", "info['链接地址1'] = \"https://contract.ibkr.info/index.php?action=Details&site=GEN&conid=\" + info['conId'].astype(str)\n", "info = info.drop_duplicates(\n", "    # subset='conId'\n", "    # 去重，除了“链接地址”列，其他列都相同时，才去重\n", "    # subset=info.columns.difference(['链接地址']),\n", ")\n", "info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:26:52.007070Z", "start_time": "2024-03-22T01:26:51.972631Z"}}, "id": "468a6ed44542d5cb", "execution_count": 34}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId symbol exchange currency  \\\n462         STK   STK.NASDAQ     STK       4391    AMD    SMART      USD   \n1278        STK     STK.NYSE     STK       4721    AXP    SMART      USD   \n2301        STK     STK.NYSE     STK       4762     BA    SMART      USD   \n1372        STK     STK.NYSE     STK       4901     VZ    SMART      USD   \n2479        STK     STK.NYSE     STK       5111    BMY    SMART      USD   \n...         ...          ...     ...        ...    ...      ...      ...   \n850         STK   STK.NASDAQ     STK  617155599    LIN    SMART      USD   \n184         STK   STK.NASDAQ     STK  653400472    ARM    SMART      USD   \n1568        STK     STK.BATS     STK  676783301   FBTC    SMART      USD   \n23          STK   STK.NASDAQ     STK  677037673   IBIT    SMART      USD   \n939         STK   STK.NASDAQ     STK  692196414   ALAB    SMART      USD   \n\n     localSymbol marketName           Product_Description  \\\n462          AMD        NMS        ADVANCED MICRO DEVICES   \n1278         AXP        AXP           AMERICAN EXPRESS CO   \n2301          BA         BA                 BOEING CO/THE   \n1372          VZ         VZ    VERIZON COMMUNICATIONS INC   \n2479         BMY        BMY       BRISTOL-MYERS SQUIBB CO   \n...          ...        ...                           ...   \n850          LIN        NMS                     LINDE PLC   \n184          ARM        NMS          ARM HOLDINGS PLC-ADR   \n1568        FBTC       FBTC  FIDELITY WISE ORIGIN BITCOIN   \n23          IBIT        NMS         ISHARES BITCOIN TRUST   \n939         ALAB        NMS               ASTERA LABS INC   \n\n                                                   链接地址 Symbol Currency  \\\n462   https://contract.ibkr.info/index.php?action=De...    AMD      USD   \n1278  https://contract.ibkr.info/index.php?action=De...    AXP      USD   \n2301  https://contract.ibkr.info/index.php?action=De...     BA      USD   \n1372  https://contract.ibkr.info/index.php?action=De...     VZ      USD   \n2479  https://contract.ibkr.info/index.php?action=De...    BMY      USD   \n...                                                 ...    ...      ...   \n850   https://contract.ibkr.info/index.php?action=De...    LIN      USD   \n184   https://contract.ibkr.info/index.php?action=De...    ARM      USD   \n1568  https://contract.ibkr.info/index.php?action=De...   FBTC      USD   \n23    https://contract.ibkr.info/index.php?action=De...   IBIT      USD   \n939   https://contract.ibkr.info/index.php?action=De...   ALAB      USD   \n\n                                                  链接地址1  \n462   https://contract.ibkr.info/index.php?action=De...  \n1278  https://contract.ibkr.info/index.php?action=De...  \n2301  https://contract.ibkr.info/index.php?action=De...  \n1372  https://contract.ibkr.info/index.php?action=De...  \n2479  https://contract.ibkr.info/index.php?action=De...  \n...                                                 ...  \n850   https://contract.ibkr.info/index.php?action=De...  \n184   https://contract.ibkr.info/index.php?action=De...  \n1568  https://contract.ibkr.info/index.php?action=De...  \n23    https://contract.ibkr.info/index.php?action=De...  \n939   https://contract.ibkr.info/index.php?action=De...  \n\n[154 rows x 14 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>462</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>4391</td>\n      <td>AMD</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AMD</td>\n      <td>NMS</td>\n      <td>ADVANCED MICRO DEVICES</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AMD</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1278</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4721</td>\n      <td>AXP</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AXP</td>\n      <td>AXP</td>\n      <td>AMERICAN EXPRESS CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AXP</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2301</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4762</td>\n      <td>BA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BA</td>\n      <td>BA</td>\n      <td>BOEING CO/THE</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BA</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1372</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4901</td>\n      <td>VZ</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>VZ</td>\n      <td>VZ</td>\n      <td>VERIZON COMMUNICATIONS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>VZ</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2479</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>5111</td>\n      <td>BMY</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BMY</td>\n      <td>BMY</td>\n      <td>BRISTOL-MYERS SQUIBB CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BMY</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>850</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>617155599</td>\n      <td>LIN</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>LIN</td>\n      <td>NMS</td>\n      <td>LINDE PLC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>LIN</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>184</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>653400472</td>\n      <td>ARM</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ARM</td>\n      <td>NMS</td>\n      <td>ARM HOLDINGS PLC-ADR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ARM</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1568</th>\n      <td>STK</td>\n      <td>STK.BATS</td>\n      <td>STK</td>\n      <td>676783301</td>\n      <td>FBTC</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>FBTC</td>\n      <td>FBTC</td>\n      <td>FIDELITY WISE ORIGIN BITCOIN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>FBTC</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>677037673</td>\n      <td>IBIT</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>IBIT</td>\n      <td>NMS</td>\n      <td>ISHARES BITCOIN TRUST</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>IBIT</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>939</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>692196414</td>\n      <td>ALAB</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ALAB</td>\n      <td>NMS</td>\n      <td>ASTERA LABS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ALAB</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n<p>154 rows × 14 columns</p>\n</div>"}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# 保留链接地址以conid结尾的行\n", "# def row_conid(row):\n", "#     conid_in_link = str(row['链接地址']).split('=')[-1]\n", "#     print(conid_in_link, str(row['conId']))\n", "#     return conid_in_link == 'nan' or int(conid_in_link) == row['conId']\n", "# info[info.apply(row_conid, axis=1)]\n", "\n", "# 以conid分组，如果组内有链接地址为nan的都保留，如果组内有链接地址以conid结尾的，只保留链接地址以conid结尾的行，否则都保留\n", "def f(df):\n", "    def row_conid(row):\n", "        conid_in_link = str(row['链接地址']).split('=')[-1]\n", "        return conid_in_link == 'nan' or int(conid_in_link) == row['conId']\n", "    return df[df.apply(row_conid, axis=1)]\n", "info = info.groupby('conId', group_keys=False).apply(f)#.reset_index(drop=True)\n", "info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:26:52.441566Z", "start_time": "2024-03-22T01:26:52.334562Z"}}, "id": "b837ee63b6728a00", "execution_count": 35}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n2803   STOCK.HK       STK.HK     STK    6244735    2800     SEHK      HKD   \n2797   STOCK.HK       STK.HK     STK   37928709    2330     TWSE      TWD   \n2798   STOCK.HK       STK.HK     STK  152791428     700     SEHK      HKD   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2800   STOCK.HK       STK.HK     STK  257765507  601127  SEHKNTL      CNH   \n2802   STOCK.HK       STK.HK     STK  345370375  601138  SEHKNTL      CNH   \n2795   STOCK.HK       STK.HK     STK  571433003  510300  SEHKNTL      CNH   \n\n     localSymbol marketName Product_Description 链接地址 Symbol Currency  \\\n2803        2800       2800                 NaN  NaN    NaN      NaN   \n2797        2330       2330                 NaN  NaN    NaN      NaN   \n2798         700        700                 NaN  NaN    NaN      NaN   \n2807      600519     600519                 NaN  NaN    NaN      NaN   \n2800      601127     601127                 NaN  NaN    NaN      NaN   \n2802      601138     601138                 NaN  NaN    NaN      NaN   \n2795      510300     510300                 NaN  NaN    NaN      NaN   \n\n                                                  链接地址1  \n2803  https://contract.ibkr.info/index.php?action=De...  \n2797  https://contract.ibkr.info/index.php?action=De...  \n2798  https://contract.ibkr.info/index.php?action=De...  \n2807  https://contract.ibkr.info/index.php?action=De...  \n2800  https://contract.ibkr.info/index.php?action=De...  \n2802  https://contract.ibkr.info/index.php?action=De...  \n2795  https://contract.ibkr.info/index.php?action=De...  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2803</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>6244735</td>\n      <td>2800</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>2800</td>\n      <td>2800</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2797</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>37928709</td>\n      <td>2330</td>\n      <td>TWSE</td>\n      <td>TWD</td>\n      <td>2330</td>\n      <td>2330</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2798</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>152791428</td>\n      <td>700</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>700</td>\n      <td>700</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2800</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>257765507</td>\n      <td>601127</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601127</td>\n      <td>601127</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2802</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>345370375</td>\n      <td>601138</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601138</td>\n      <td>601138</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2795</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>571433003</td>\n      <td>510300</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>510300</td>\n      <td>510300</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# 打印“链接地址”为NaN的行\n", "info[info['链接地址'].isna()]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:26:57.323324Z", "start_time": "2024-03-22T01:26:57.309324Z"}}, "id": "3107786ae086db21", "execution_count": 36}, {"cell_type": "code", "outputs": [], "source": ["# info['链接地址2'] = \"https://contract.ibkr.info/v3.10/index.php?filter=9aBfpN&csaction=Search+Form&action=Details&ib_entity=&conid=\" + info['conId'].astype(str)\n", "# info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:25:05.545984Z", "start_time": "2024-03-22T01:25:05.530907Z"}}, "id": "d4f0de4f2c8f42fd", "execution_count": 30}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId symbol exchange currency  \\\n462         STK   STK.NASDAQ     STK       4391    AMD    SMART      USD   \n1278        STK     STK.NYSE     STK       4721    AXP    SMART      USD   \n2301        STK     STK.NYSE     STK       4762     BA    SMART      USD   \n1372        STK     STK.NYSE     STK       4901     VZ    SMART      USD   \n2479        STK     STK.NYSE     STK       5111    BMY    SMART      USD   \n...         ...          ...     ...        ...    ...      ...      ...   \n850         STK   STK.NASDAQ     STK  617155599    LIN    SMART      USD   \n184         STK   STK.NASDAQ     STK  653400472    ARM    SMART      USD   \n1568        STK     STK.BATS     STK  676783301   FBTC    SMART      USD   \n23          STK   STK.NASDAQ     STK  677037673   IBIT    SMART      USD   \n939         STK   STK.NASDAQ     STK  692196414   ALAB    SMART      USD   \n\n     localSymbol marketName           Product_Description  \\\n462          AMD        NMS        ADVANCED MICRO DEVICES   \n1278         AXP        AXP           AMERICAN EXPRESS CO   \n2301          BA         BA                 BOEING CO/THE   \n1372          VZ         VZ    VERIZON COMMUNICATIONS INC   \n2479         BMY        BMY       BRISTOL-MYERS SQUIBB CO   \n...          ...        ...                           ...   \n850          LIN        NMS                     LINDE PLC   \n184          ARM        NMS          ARM HOLDINGS PLC-ADR   \n1568        FBTC       FBTC  FIDELITY WISE ORIGIN BITCOIN   \n23          IBIT        NMS         ISHARES BITCOIN TRUST   \n939         ALAB        NMS               ASTERA LABS INC   \n\n                                                   链接地址 Symbol Currency  \\\n462   https://contract.ibkr.info/index.php?action=De...    AMD      USD   \n1278  https://contract.ibkr.info/index.php?action=De...    AXP      USD   \n2301  https://contract.ibkr.info/index.php?action=De...     BA      USD   \n1372  https://contract.ibkr.info/index.php?action=De...     VZ      USD   \n2479  https://contract.ibkr.info/index.php?action=De...    BMY      USD   \n...                                                 ...    ...      ...   \n850   https://contract.ibkr.info/index.php?action=De...    LIN      USD   \n184   https://contract.ibkr.info/index.php?action=De...    ARM      USD   \n1568  https://contract.ibkr.info/index.php?action=De...   FBTC      USD   \n23    https://contract.ibkr.info/index.php?action=De...   IBIT      USD   \n939   https://contract.ibkr.info/index.php?action=De...   ALAB      USD   \n\n                                                  链接地址1  \n462   https://contract.ibkr.info/index.php?action=De...  \n1278  https://contract.ibkr.info/index.php?action=De...  \n2301  https://contract.ibkr.info/index.php?action=De...  \n1372  https://contract.ibkr.info/index.php?action=De...  \n2479  https://contract.ibkr.info/index.php?action=De...  \n...                                                 ...  \n850   https://contract.ibkr.info/index.php?action=De...  \n184   https://contract.ibkr.info/index.php?action=De...  \n1568  https://contract.ibkr.info/index.php?action=De...  \n23    https://contract.ibkr.info/index.php?action=De...  \n939   https://contract.ibkr.info/index.php?action=De...  \n\n[154 rows x 14 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>462</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>4391</td>\n      <td>AMD</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AMD</td>\n      <td>NMS</td>\n      <td>ADVANCED MICRO DEVICES</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AMD</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1278</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4721</td>\n      <td>AXP</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AXP</td>\n      <td>AXP</td>\n      <td>AMERICAN EXPRESS CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AXP</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2301</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4762</td>\n      <td>BA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BA</td>\n      <td>BA</td>\n      <td>BOEING CO/THE</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BA</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1372</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4901</td>\n      <td>VZ</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>VZ</td>\n      <td>VZ</td>\n      <td>VERIZON COMMUNICATIONS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>VZ</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2479</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>5111</td>\n      <td>BMY</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BMY</td>\n      <td>BMY</td>\n      <td>BRISTOL-MYERS SQUIBB CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BMY</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>850</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>617155599</td>\n      <td>LIN</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>LIN</td>\n      <td>NMS</td>\n      <td>LINDE PLC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>LIN</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>184</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>653400472</td>\n      <td>ARM</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ARM</td>\n      <td>NMS</td>\n      <td>ARM HOLDINGS PLC-ADR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ARM</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1568</th>\n      <td>STK</td>\n      <td>STK.BATS</td>\n      <td>STK</td>\n      <td>676783301</td>\n      <td>FBTC</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>FBTC</td>\n      <td>FBTC</td>\n      <td>FIDELITY WISE ORIGIN BITCOIN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>FBTC</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>677037673</td>\n      <td>IBIT</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>IBIT</td>\n      <td>NMS</td>\n      <td>ISHARES BITCOIN TRUST</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>IBIT</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>939</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>692196414</td>\n      <td>ALAB</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ALAB</td>\n      <td>NMS</td>\n      <td>ASTERA LABS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ALAB</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n<p>154 rows × 14 columns</p>\n</div>"}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# 删除Product_Description为NaN的行\n", "# info = info.dropna(subset=['Product_Description'])\n", "# 删除'Currency'为NaN且'exchange'为\"SMART\"的行\n", "def row_nan(row):\n", "    return row['exchange'] == 'SMART' and (pd.isna(row['Currency']) or pd.isna(row['Product_Description']))\n", "info = info[~info.apply(row_nan, axis=1)]    \n", "info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:27:06.072696Z", "start_time": "2024-03-22T01:27:06.053579Z"}}, "id": "948c3b916657d8a7", "execution_count": 37}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId symbol exchange currency  \\\n462         STK   STK.NASDAQ     STK       4391    AMD    SMART      USD   \n1278        STK     STK.NYSE     STK       4721    AXP    SMART      USD   \n2301        STK     STK.NYSE     STK       4762     BA    SMART      USD   \n1372        STK     STK.NYSE     STK       4901     VZ    SMART      USD   \n2479        STK     STK.NYSE     STK       5111    BMY    SMART      USD   \n...         ...          ...     ...        ...    ...      ...      ...   \n850         STK   STK.NASDAQ     STK  617155599    LIN    SMART      USD   \n184         STK   STK.NASDAQ     STK  653400472    ARM    SMART      USD   \n1568        STK     STK.BATS     STK  676783301   FBTC    SMART      USD   \n23          STK   STK.NASDAQ     STK  677037673   IBIT    SMART      USD   \n939         STK   STK.NASDAQ     STK  692196414   ALAB    SMART      USD   \n\n     localSymbol marketName           Product_Description  \\\n462          AMD        NMS        ADVANCED MICRO DEVICES   \n1278         AXP        AXP           AMERICAN EXPRESS CO   \n2301          BA         BA                 BOEING CO/THE   \n1372          VZ         VZ    VERIZON COMMUNICATIONS INC   \n2479         BMY        BMY       BRISTOL-MYERS SQUIBB CO   \n...          ...        ...                           ...   \n850          LIN        NMS                     LINDE PLC   \n184          ARM        NMS          ARM HOLDINGS PLC-ADR   \n1568        FBTC       FBTC  FIDELITY WISE ORIGIN BITCOIN   \n23          IBIT        NMS         ISHARES BITCOIN TRUST   \n939         ALAB        NMS               ASTERA LABS INC   \n\n                                                   链接地址 Symbol Currency  \\\n462   https://contract.ibkr.info/index.php?action=De...    AMD      USD   \n1278  https://contract.ibkr.info/index.php?action=De...    AXP      USD   \n2301  https://contract.ibkr.info/index.php?action=De...     BA      USD   \n1372  https://contract.ibkr.info/index.php?action=De...     VZ      USD   \n2479  https://contract.ibkr.info/index.php?action=De...    BMY      USD   \n...                                                 ...    ...      ...   \n850   https://contract.ibkr.info/index.php?action=De...    LIN      USD   \n184   https://contract.ibkr.info/index.php?action=De...    ARM      USD   \n1568  https://contract.ibkr.info/index.php?action=De...   FBTC      USD   \n23    https://contract.ibkr.info/index.php?action=De...   IBIT      USD   \n939   https://contract.ibkr.info/index.php?action=De...   ALAB      USD   \n\n                                                  链接地址1  \n462   https://contract.ibkr.info/index.php?action=De...  \n1278  https://contract.ibkr.info/index.php?action=De...  \n2301  https://contract.ibkr.info/index.php?action=De...  \n1372  https://contract.ibkr.info/index.php?action=De...  \n2479  https://contract.ibkr.info/index.php?action=De...  \n...                                                 ...  \n850   https://contract.ibkr.info/index.php?action=De...  \n184   https://contract.ibkr.info/index.php?action=De...  \n1568  https://contract.ibkr.info/index.php?action=De...  \n23    https://contract.ibkr.info/index.php?action=De...  \n939   https://contract.ibkr.info/index.php?action=De...  \n\n[154 rows x 14 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>462</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>4391</td>\n      <td>AMD</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AMD</td>\n      <td>NMS</td>\n      <td>ADVANCED MICRO DEVICES</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AMD</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1278</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4721</td>\n      <td>AXP</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AXP</td>\n      <td>AXP</td>\n      <td>AMERICAN EXPRESS CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AXP</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2301</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4762</td>\n      <td>BA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BA</td>\n      <td>BA</td>\n      <td>BOEING CO/THE</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BA</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1372</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4901</td>\n      <td>VZ</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>VZ</td>\n      <td>VZ</td>\n      <td>VERIZON COMMUNICATIONS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>VZ</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2479</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>5111</td>\n      <td>BMY</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BMY</td>\n      <td>BMY</td>\n      <td>BRISTOL-MYERS SQUIBB CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BMY</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>850</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>617155599</td>\n      <td>LIN</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>LIN</td>\n      <td>NMS</td>\n      <td>LINDE PLC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>LIN</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>184</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>653400472</td>\n      <td>ARM</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ARM</td>\n      <td>NMS</td>\n      <td>ARM HOLDINGS PLC-ADR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ARM</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1568</th>\n      <td>STK</td>\n      <td>STK.BATS</td>\n      <td>STK</td>\n      <td>676783301</td>\n      <td>FBTC</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>FBTC</td>\n      <td>FBTC</td>\n      <td>FIDELITY WISE ORIGIN BITCOIN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>FBTC</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>677037673</td>\n      <td>IBIT</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>IBIT</td>\n      <td>NMS</td>\n      <td>ISHARES BITCOIN TRUST</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>IBIT</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>939</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>692196414</td>\n      <td>ALAB</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ALAB</td>\n      <td>NMS</td>\n      <td>ASTERA LABS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ALAB</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n<p>154 rows × 14 columns</p>\n</div>"}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# 以conId分组，如果Currency列有为\"USD\"的，只保留\"USD\"的行，否则都保留\n", "def f(df):\n", "    if 'USD' in df['Currency'].values:\n", "        return df[df['Currency'] == 'USD']\n", "    else:\n", "        return df\n", "info = info.groupby('conId', group_keys=False).apply(f)\n", "info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:27:07.666235Z", "start_time": "2024-03-22T01:27:07.584027Z"}}, "id": "57848c1eaa5f627a", "execution_count": 38}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "Empty DataFrame\nColumns: [instrument, locationCode, secType, conId, symbol, exchange, currency, localSymbol, marketName, Product_Description, 链接地址, Symbol, Currency, 链接地址1]\nIndex: []", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n  </tbody>\n</table>\n</div>"}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看仍然有重复的conId的行\n", "info[info.duplicated(subset='conId', keep=False)]# keep=False表示所有重复的行都显示；默认为first，表示只显示第一个重复的行"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:27:15.959598Z", "start_time": "2024-03-22T01:27:15.948590Z"}}, "id": "a1d32fb5e0e4e796", "execution_count": 39}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n2803   STOCK.HK       STK.HK     STK    6244735    2800     SEHK      HKD   \n2797   STOCK.HK       STK.HK     STK   37928709    2330     TWSE      TWD   \n2798   STOCK.HK       STK.HK     STK  152791428     700     SEHK      HKD   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2800   STOCK.HK       STK.HK     STK  257765507  601127  SEHKNTL      CNH   \n2802   STOCK.HK       STK.HK     STK  345370375  601138  SEHKNTL      CNH   \n2795   STOCK.HK       STK.HK     STK  571433003  510300  SEHKNTL      CNH   \n\n     localSymbol marketName Product_Description 链接地址 Symbol Currency  \\\n2803        2800       2800                 NaN  NaN    NaN      NaN   \n2797        2330       2330                 NaN  NaN    NaN      NaN   \n2798         700        700                 NaN  NaN    NaN      NaN   \n2807      600519     600519                 NaN  NaN    NaN      NaN   \n2800      601127     601127                 NaN  NaN    NaN      NaN   \n2802      601138     601138                 NaN  NaN    NaN      NaN   \n2795      510300     510300                 NaN  NaN    NaN      NaN   \n\n                                                  链接地址1  \n2803  https://contract.ibkr.info/index.php?action=De...  \n2797  https://contract.ibkr.info/index.php?action=De...  \n2798  https://contract.ibkr.info/index.php?action=De...  \n2807  https://contract.ibkr.info/index.php?action=De...  \n2800  https://contract.ibkr.info/index.php?action=De...  \n2802  https://contract.ibkr.info/index.php?action=De...  \n2795  https://contract.ibkr.info/index.php?action=De...  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2803</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>6244735</td>\n      <td>2800</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>2800</td>\n      <td>2800</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2797</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>37928709</td>\n      <td>2330</td>\n      <td>TWSE</td>\n      <td>TWD</td>\n      <td>2330</td>\n      <td>2330</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2798</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>152791428</td>\n      <td>700</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>700</td>\n      <td>700</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2800</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>257765507</td>\n      <td>601127</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601127</td>\n      <td>601127</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2802</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>345370375</td>\n      <td>601138</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601138</td>\n      <td>601138</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2795</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>571433003</td>\n      <td>510300</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>510300</td>\n      <td>510300</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示exchange列不为\"SMART\"的行\n", "info[info['exchange'] != 'SMART']"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:27:20.671017Z", "start_time": "2024-03-22T01:27:20.645985Z"}}, "id": "77a0741a08013924", "execution_count": 40}, {"cell_type": "code", "outputs": [], "source": ["import os\n", "os.environ[\"http_proxy\"] = \"http://localhost:7890\"\n", "os.environ[\"https_proxy\"] = \"http://localhost:7890\""], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T01:27:27.805055Z", "start_time": "2024-03-22T01:27:27.789455Z"}}, "id": "376d91d03073eb53", "execution_count": 41}, {"cell_type": "code", "outputs": [], "source": ["# 尝试单个链接\n", "# 导入\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service as ChromeService\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "from selenium.webdriver.chrome.options import Options\n", "chrome_options = Options()\n", "chrome_options.add_argument('--headless')# 无头浏览器\n", "chrome_options.add_argument('--disable-gpu')# 禁用GPU加速，防止出现bug\n", "from retry import retry\n", "\n", "@retry(tries=3, delay=5)\n", "def get_Product_Description_web(url):\n", "    browser = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=chrome_options)\n", "    browser.get(url)        \n", "\n", "    # from lxml import etree\n", "    # # 获取网页源代码\n", "    # html = browser.page_source\n", "    # # 关闭浏览器\n", "    # browser.quit()\n", "    # html = etree.HTML(html)\n", "    # Product_Description_web = html.xpath('//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[2]/td/text()')[0]\n", "    # Currency_web = html.xpath('//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[8]/td/text()')[0]\n", "    # Exchanges_web = html.xpath('//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[4]/td//a/text()')\n", "    # Exchange_web = ', '.join(Exchanges_web)\n", "    \n", "    # 直接用xpath获取\n", "    Product_Description_web = browser.find_element('xpath', '//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[2]/td').text\n", "    Currency_web = browser.find_element('xpath', '//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[8]/td').text\n", "    # Exchange_web = browser.find_element('xpath', '//*[@id=\"contractSpecs\"]/table[2]/tbody/tr[4]/td').text\n", "    \n", "    browser.quit()\n", "    print(f'Product_Description_web：{Product_Description_web}，Currency_web：{Currency_web}'\n", "          # f'，Exchange_web：{Exchange_web}'\n", "          )\n", "    # if Product_Description_web is None or Currency_web is None or Exchange_web is None:\n", "    if Product_Description_web is None or Currency_web is None:\n", "        raise ValueError('Product_Description_web, Currency_web, Exchange_web有None')\n", "    return Product_Description_web, Currency_web#, Exchange_web\n", "    \n", "get_Product_Description_web('https://contract.ibkr.info/index.php?action=Details&site=GEN&conid=272997')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T02:00:09.373913Z", "start_time": "2024-03-22T02:00:09.365604Z"}}, "id": "c4e0fcd84bfcb6b9", "execution_count": 53}, {"cell_type": "code", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Product_Description_web：TRACKER FUND OF HONG KONG-B，Currency_web：Hong Kong Dollar (HKD)\n", "Product_Description_web：TAIWAN SEMICONDUCTOR MANUFAC，Currency_web：Taiwanese Dollar (TWD)\n", "Product_Description_web：TENCENT HOLDINGS LTD，Currency_web：Hong Kong Dollar (HKD)\n", "Product_Description_web：K<PERSON><PERSON>CHOW MOUTAI CO LTD-A，Currency_web：Chinese Yuan (CNH)\n", "Product_Description_web：SERES GROUP CO L-A，Currency_web：Chinese Yuan (CNH)\n", "Product_Description_web：FOXCONN INDUSTRIAL INTERNE-A，Currency_web：Chinese Yuan (CNH)\n", "Product_Description_web：HUATAI-PB CSI 300 ETF，Currency_web：Chinese Yuan (CNH)\n"]}], "source": ["from time import sleep\n", "# 只访问“链接地址”为NaN的行的“链接地址1”列\n", "urls = info[info['链接地址'].isna()]['链接地址1'].tolist()\n", "# 将这些行的Product_Description, Currency列赋值为get_Product_Description_web函数的返回值\n", "for url in urls:\n", "    try:\n", "        Product_Description_web, Currency_web = get_Product_Description_web(url)\n", "        # Exchange_web = get_Product_Description_web(url)\n", "        info.loc[info['链接地址1'] == url, 'Product_Description'] = Product_Description_web\n", "        info.loc[info['链接地址1'] == url, 'Currency'] = Currency_web\n", "        # info.loc[info['链接地址1'] == url, 'Exchange'] = Exchange_web\n", "    except Exception as e:\n", "        print(e)\n", "    sleep(5)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T02:01:56.810025Z", "start_time": "2024-03-22T02:00:11.695047Z"}}, "id": "f452919716e5ea82", "execution_count": 54}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId  symbol exchange currency  \\\n2803   STOCK.HK       STK.HK     STK    6244735    2800     SEHK      HKD   \n2797   STOCK.HK       STK.HK     STK   37928709    2330     TWSE      TWD   \n2798   STOCK.HK       STK.HK     STK  152791428     700     SEHK      HKD   \n2807   STOCK.HK       STK.HK     STK  *********  600519  SEHKNTL      CNH   \n2800   STOCK.HK       STK.HK     STK  257765507  601127  SEHKNTL      CNH   \n2802   STOCK.HK       STK.HK     STK  345370375  601138  SEHKNTL      CNH   \n2795   STOCK.HK       STK.HK     STK  571433003  510300  SEHKNTL      CNH   \n\n     localSymbol marketName           Product_Description 链接地址 Symbol  \\\n2803        2800       2800   TRACKER FUND OF HONG KONG-B  NaN    NaN   \n2797        2330       2330  TAIWAN SEMICONDUCTOR MANUFAC  NaN    NaN   \n2798         700        700          TENCENT HOLDINGS LTD  NaN    NaN   \n2807      600519     600519      KWEICHOW MOUTAI CO LTD-A  NaN    NaN   \n2800      601127     601127            SERES GROUP CO L-A  NaN    NaN   \n2802      601138     601138  FOXCONN INDUSTRIAL INTERNE-A  NaN    NaN   \n2795      510300     510300         HUATAI-PB CSI 300 ETF  NaN    NaN   \n\n     Currency                                              链接地址1  \n2803      NaN  https://contract.ibkr.info/index.php?action=De...  \n2797      NaN  https://contract.ibkr.info/index.php?action=De...  \n2798      NaN  https://contract.ibkr.info/index.php?action=De...  \n2807      NaN  https://contract.ibkr.info/index.php?action=De...  \n2800      NaN  https://contract.ibkr.info/index.php?action=De...  \n2802      NaN  https://contract.ibkr.info/index.php?action=De...  \n2795      NaN  https://contract.ibkr.info/index.php?action=De...  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2803</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>6244735</td>\n      <td>2800</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>2800</td>\n      <td>2800</td>\n      <td>TRACKER FUND OF HONG KONG-B</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2797</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>37928709</td>\n      <td>2330</td>\n      <td>TWSE</td>\n      <td>TWD</td>\n      <td>2330</td>\n      <td>2330</td>\n      <td>TAIWAN SEMICONDUCTOR MANUFAC</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2798</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>152791428</td>\n      <td>700</td>\n      <td>SEHK</td>\n      <td>HKD</td>\n      <td>700</td>\n      <td>700</td>\n      <td>TENCENT HOLDINGS LTD</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2807</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>*********</td>\n      <td>600519</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>600519</td>\n      <td>600519</td>\n      <td>KWEICHOW MOUTAI CO LTD-A</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2800</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>257765507</td>\n      <td>601127</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601127</td>\n      <td>601127</td>\n      <td>SERES GROUP CO L-A</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2802</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>345370375</td>\n      <td>601138</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>601138</td>\n      <td>601138</td>\n      <td>FOXCONN INDUSTRIAL INTERNE-A</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2795</th>\n      <td>STOCK.HK</td>\n      <td>STK.HK</td>\n      <td>STK</td>\n      <td>571433003</td>\n      <td>510300</td>\n      <td>SEHKNTL</td>\n      <td>CNH</td>\n      <td>510300</td>\n      <td>510300</td>\n      <td>HUATAI-PB CSI 300 ETF</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>NaN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将“链接地址”为NaN的行的Currency的Chinese Yuan (CNH)替换为其括号内的内容CNH\n", "info.loc[info['链接地址'].isna(), 'Currency'] = info.loc[info['链接地址'].isna(), 'Currency'].str.extract(r'\\((.*)\\)')# *表示匹配0个或多个\n", "# 打印“链接地址”为NaN的行\n", "info[info['链接地址'].isna()]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T02:02:41.389753Z", "start_time": "2024-03-22T02:02:41.351280Z"}}, "id": "a4954ae5d3e8f43e", "execution_count": 55}, {"cell_type": "code", "outputs": [{"data": {"text/plain": "     instrument locationCode secType      conId symbol exchange currency  \\\n462         STK   STK.NASDAQ     STK       4391    AMD    SMART      USD   \n1278        STK     STK.NYSE     STK       4721    AXP    SMART      USD   \n2301        STK     STK.NYSE     STK       4762     BA    SMART      USD   \n1372        STK     STK.NYSE     STK       4901     VZ    SMART      USD   \n2479        STK     STK.NYSE     STK       5111    BMY    SMART      USD   \n...         ...          ...     ...        ...    ...      ...      ...   \n850         STK   STK.NASDAQ     STK  617155599    LIN    SMART      USD   \n184         STK   STK.NASDAQ     STK  653400472    ARM    SMART      USD   \n1568        STK     STK.BATS     STK  676783301   FBTC    SMART      USD   \n23          STK   STK.NASDAQ     STK  677037673   IBIT    SMART      USD   \n939         STK   STK.NASDAQ     STK  692196414   ALAB    SMART      USD   \n\n     localSymbol marketName           Product_Description  \\\n462          AMD        NMS        ADVANCED MICRO DEVICES   \n1278         AXP        AXP           AMERICAN EXPRESS CO   \n2301          BA         BA                 BOEING CO/THE   \n1372          VZ         VZ    VERIZON COMMUNICATIONS INC   \n2479         BMY        BMY       BRISTOL-MYERS SQUIBB CO   \n...          ...        ...                           ...   \n850          LIN        NMS                     LINDE PLC   \n184          ARM        NMS          ARM HOLDINGS PLC-ADR   \n1568        FBTC       FBTC  FIDELITY WISE ORIGIN BITCOIN   \n23          IBIT        NMS         ISHARES BITCOIN TRUST   \n939         ALAB        NMS               ASTERA LABS INC   \n\n                                                   链接地址 Symbol Currency  \\\n462   https://contract.ibkr.info/index.php?action=De...    AMD      USD   \n1278  https://contract.ibkr.info/index.php?action=De...    AXP      USD   \n2301  https://contract.ibkr.info/index.php?action=De...     BA      USD   \n1372  https://contract.ibkr.info/index.php?action=De...     VZ      USD   \n2479  https://contract.ibkr.info/index.php?action=De...    BMY      USD   \n...                                                 ...    ...      ...   \n850   https://contract.ibkr.info/index.php?action=De...    LIN      USD   \n184   https://contract.ibkr.info/index.php?action=De...    ARM      USD   \n1568  https://contract.ibkr.info/index.php?action=De...   FBTC      USD   \n23    https://contract.ibkr.info/index.php?action=De...   IBIT      USD   \n939   https://contract.ibkr.info/index.php?action=De...   ALAB      USD   \n\n                                                  链接地址1  \n462   https://contract.ibkr.info/index.php?action=De...  \n1278  https://contract.ibkr.info/index.php?action=De...  \n2301  https://contract.ibkr.info/index.php?action=De...  \n1372  https://contract.ibkr.info/index.php?action=De...  \n2479  https://contract.ibkr.info/index.php?action=De...  \n...                                                 ...  \n850   https://contract.ibkr.info/index.php?action=De...  \n184   https://contract.ibkr.info/index.php?action=De...  \n1568  https://contract.ibkr.info/index.php?action=De...  \n23    https://contract.ibkr.info/index.php?action=De...  \n939   https://contract.ibkr.info/index.php?action=De...  \n\n[154 rows x 14 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>instrument</th>\n      <th>locationCode</th>\n      <th>secType</th>\n      <th>conId</th>\n      <th>symbol</th>\n      <th>exchange</th>\n      <th>currency</th>\n      <th>localSymbol</th>\n      <th>marketName</th>\n      <th>Product_Description</th>\n      <th>链接地址</th>\n      <th>Symbol</th>\n      <th>Currency</th>\n      <th>链接地址1</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>462</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>4391</td>\n      <td>AMD</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AMD</td>\n      <td>NMS</td>\n      <td>ADVANCED MICRO DEVICES</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AMD</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1278</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4721</td>\n      <td>AXP</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>AXP</td>\n      <td>AXP</td>\n      <td>AMERICAN EXPRESS CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>AXP</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2301</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4762</td>\n      <td>BA</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BA</td>\n      <td>BA</td>\n      <td>BOEING CO/THE</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BA</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1372</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>4901</td>\n      <td>VZ</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>VZ</td>\n      <td>VZ</td>\n      <td>VERIZON COMMUNICATIONS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>VZ</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>2479</th>\n      <td>STK</td>\n      <td>STK.NYSE</td>\n      <td>STK</td>\n      <td>5111</td>\n      <td>BMY</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>BMY</td>\n      <td>BMY</td>\n      <td>BRISTOL-MYERS SQUIBB CO</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>BMY</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>850</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>617155599</td>\n      <td>LIN</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>LIN</td>\n      <td>NMS</td>\n      <td>LINDE PLC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>LIN</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>184</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>653400472</td>\n      <td>ARM</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ARM</td>\n      <td>NMS</td>\n      <td>ARM HOLDINGS PLC-ADR</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ARM</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>1568</th>\n      <td>STK</td>\n      <td>STK.BATS</td>\n      <td>STK</td>\n      <td>676783301</td>\n      <td>FBTC</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>FBTC</td>\n      <td>FBTC</td>\n      <td>FIDELITY WISE ORIGIN BITCOIN</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>FBTC</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>23</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>677037673</td>\n      <td>IBIT</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>IBIT</td>\n      <td>NMS</td>\n      <td>ISHARES BITCOIN TRUST</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>IBIT</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n    <tr>\n      <th>939</th>\n      <td>STK</td>\n      <td>STK.NASDAQ</td>\n      <td>STK</td>\n      <td>692196414</td>\n      <td>ALAB</td>\n      <td>SMART</td>\n      <td>USD</td>\n      <td>ALAB</td>\n      <td>NMS</td>\n      <td>ASTERA LABS INC</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n      <td>ALAB</td>\n      <td>USD</td>\n      <td>https://contract.ibkr.info/index.php?action=De...</td>\n    </tr>\n  </tbody>\n</table>\n<p>154 rows × 14 columns</p>\n</div>"}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["info"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T02:03:30.334024Z", "start_time": "2024-03-22T02:03:30.315335Z"}}, "id": "3d4e63f2ea87c7f2", "execution_count": 56}, {"cell_type": "code", "outputs": [], "source": ["# 保存\n", "info.to_excel('154支全市场近90日均成交$6亿以上股票_合并.xlsx', index=False)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2024-03-22T02:03:34.203152Z", "start_time": "2024-03-22T02:03:34.047716Z"}}, "id": "5d7faaa3166e56ec", "execution_count": 57}, {"cell_type": "code", "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "922fdcfacb5f7d29"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}