import traceback
from copy import copy
from datetime import datetime, time, timedelta
from queue import Queue
from threading import Thread
from typing import Dict, Optional, Union

import pytz
import redis
from ibapi.contract import ContractDetails
from vnpy.event import Event, EventEngine
from vnpy.trader.constant import (Exchange)
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.event import EVENT_TICK, EVENT_CONTRACT, EVENT_TIMER
from vnpy.trader.object import BarData, TickData, Interval, SubscribeRequest, ContractData
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, virtual, ZoneInfo, extract_vt_symbol

APP_NAME = "BarGenEngine"
EVENT_BAR = "eBarGen."
EVENT_BAR_RECORD = "eBarGenRec."

eastern = pytz.timezone('US/Eastern')
central = pytz.timezone('US/Central')
shanghai = pytz.timezone('Asia/Shanghai')

# Define the trading periods in Eastern Time (ET) for US stock market
# _BarGenEngineIb__US_TIME = [(time(9, 30), time(16, 0)), ]
# _BarGenEngineIbFut__US_TIME = [(time(17, 0), time.max), (time.min, time(16, 0))]
# _BarGenEngineIb__tradingPeriod = 'liquidHours'
# _BarGenEngineIbFut__tradingPeriod = 'tradingHours'
SECTYPE_TRADING_PERIOD = {'STK': 'liquidHours', 'FUT': 'tradingHours', 'OPT': 'tradingHours', 'IND': 'tradingHours'}
# TDAYS = load_json("tdays_dict.json")
# # 日期格式转换
# TDAYS = {key: [datetime.strptime(x, "%Y%m%d").date() for x in value] for key, value in TDAYS.items()}


# class MyDict(dict):
#     __setattr__ = dict.__setitem__
#     __getattr__ = dict.__getitem__
#
#
# def dict_to_object(dictObj):
#     if not isinstance(dictObj, dict):
#         return dictObj
#     inst = MyDict()
#     for k, v in dictObj.items():
#         inst[k] = dict_to_object(v)
#     return inst


class BarGenEngine(BaseEngine):
    """"""
    setting_filename = "barGen_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.queue = Queue()
        self.thread = Thread(target=self.run)
        self.active = False

        self.bar_recordings = []
        # self.bar_generators = {}

        self.bars: Dict[str, BarData] = {}
        self.last_bars: Dict[str, BarData] = {}
        self.last_ticks: Dict[str, TickData] = {}
        # self.last_dt: datetime = None
        self.last_dts: Dict[str, datetime] = {}

        self.tick_time: Dict[str, str] = {}  # cache tick time for debug info
        # create connection
        self.r = redis.Redis(host=SETTINGS["redis.host"], port=SETTINGS["redis.port"],
                             password=SETTINGS["redis.password"], decode_responses=True)

        self.load_setting()
        self.last_timer_event_minute = None
        self.register_event()
        self.start()  # need test vigar 1216 !
        self.put_event()

    def load_setting(self):
        """"""
        setting = load_json(self.setting_filename)
        self.write_log(f"[barGen load setting] {setting}")
        self.bar_recordings = setting.get("bar", [])
        self.write_log(f"[barGen load setting] bar_recordings : {self.bar_recordings}")
        for elem in self.bar_recordings:
            self.write_log(f"[barGen load setting] subscribe elem {elem}")
            self.subscribe_recording(elem)

    def load_last_bars(self):#, filename: str = "last_bars.json"):
        """"""
        # last_bars = dict_to_object(load_json(filename))
        # # 解析每个vt_symbol对应的BarData的datetime
        # for vt_symbol, bar in last_bars.items():
        #     bar.exchange = Exchange(bar.exchange)
        #     bar.datetime = datetime.strptime(bar.datetime, "%Y-%m-%d %H:%M:%S").astimezone(ZoneInfo('Asia/Shanghai'))
        #     bar.interval = Interval(bar.interval)
        # return last_bars

        # 改为从redis加载
        last_bars = {}
        for vt_symbol in self.bar_recordings:
            bar = self.get_redis(vt_symbol)
            if bar:
                last_bars[vt_symbol] = bar
        return last_bars

    def save_setting(self):
        """"""
        setting = {"bar": self.bar_recordings}
        save_json(self.setting_filename, setting)

    def run(self):
        """"""
        while self.active:
            try:
                if datetime.now().minute % 30 == 0 and datetime.now().second == 0:
                    # self.load_setting()
                    pass
            except Exception:
                msg = f"barGen run 触发异常已停止\n{traceback.format_exc()}"
                self.write_log(f"[run] barGen error: {msg}")

    def close(self):
        """"""
        self.active = False
        if self.thread.is_alive():
            self.thread.join()  # 使用save_json保存self.last_bars  # self.save_last_bars(self.last_bars)  # print(f"[close engine] success to save last_bars.json")  # log引擎可能已关闭，所以用print

    def start(self):
        """"""
        self.active = True
        self.thread.start()
        self.last_bars = self.load_last_bars()
        self.write_log(f"[start engine] success to load last_bars: {self.last_bars}")

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            contract = self.main_engine.get_contract(vt_symbol)
            if not contract:
                self.write_log(f"[subscribe_recording] 找不到合约：{vt_symbol}")
                return
            self.write_log(f"[subscribe_recording] prepare to send subscribe req：{vt_symbol}")
            self.subscribe(contract)
        except Exception:
            msg = f"[subscribe_recording] barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"[subscribe_recording] 添加K线记录成功：{vt_symbol}")

    def register_event(self):
        """"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)

    # def update_tick(self, tick: TickData):
    #     """"""
    #     if tick.vt_symbol in self.bar_recordings:
    #         bg = self.get_bar_generator(tick.vt_symbol)
    #         bg.update_tick(copy(tick))

    # 如果继承此类，需要重写此方法
    @virtual
    def tick_filter(self, tick: TickData):
        return False

    @virtual
    def bar_filter(self, bar: BarData):
        return False

    # def save_last_bars(self, last_bars: Dict[str, BarData], filename: str = "last_bars.json"):
    #     """"""
    #     # 先加载last_bars.json，再更新last_bars，再保存到last_bars.json
    #     last_bars_in_file = self.load_last_bars(filename)
    #     for vt_symbol, bar in last_bars.items():
    #         try:
    #             # 使用__dict__属性修改格式，否则not JSON serializable
    #             last_bars_in_file[vt_symbol] = {
    #                 "symbol": bar.symbol,
    #                 "exchange": bar.exchange.value,
    #                 "datetime": bar.datetime.strftime("%Y-%m-%d %H:%M:%S"),
    #                 "interval": bar.interval.value,
    #                 "volume": bar.volume,
    #                 "turnover": bar.turnover,
    #                 "open_interest": bar.open_interest,
    #                 "open_price": bar.open_price,
    #                 "high_price": bar.high_price,
    #                 "low_price": bar.low_price,
    #                 "close_price": bar.close_price,
    #                 "gateway_name": bar.gateway_name,
    #             }
    #         except Exception:
    #             self.write_log(f'[save_last_bars] error: {traceback.format_exc()}\n bar: {bar}')
    #     save_json(filename, last_bars_in_file)

    def update_redis(self, bar: BarData):
        self.r.hmset(bar.vt_symbol, {
            "symbol": bar.symbol,
            "exchange": bar.exchange.value, 
            "datetime": bar.datetime.strftime("%Y-%m-%d %H:%M:%S"),
            "interval": bar.interval.value,
            "volume": bar.volume,
            "turnover": bar.turnover,
            "open_interest": bar.open_interest,
            "open_price": bar.open_price,
            "high_price": bar.high_price,
            "low_price": bar.low_price,
            "close_price": bar.close_price,
            "gateway_name": bar.gateway_name
        })

    def get_redis(self, vt_symbol: str):
        data = self.r.hgetall(vt_symbol)
        if data:
            try:
                bar = BarData(symbol=data['symbol'], exchange=Exchange(data['exchange']),
                              datetime=datetime.strptime(data['datetime'], "%Y-%m-%d %H:%M:%S").astimezone(
                                  ZoneInfo('Asia/Shanghai')), interval=Interval(data['interval']),
                              volume=float(data['volume']), turnover=float(data['turnover']),
                              open_interest=float(data['open_interest']), open_price=float(data['open_price']),
                              high_price=float(data['high_price']), low_price=float(data['low_price']),
                              close_price=float(data['close_price']), gateway_name=data['gateway_name'])
                return bar
            except Exception:
                self.write_log(f"[get_redis] error {vt_symbol}: {traceback.format_exc()}")
        return None

    def update_tick(self, tick: TickData):
        if not tick.last_price:
            return

        # self.update_redis(tick)

        # reach time, send all bars
        last_dt = self.last_dts.get(tick.vt_symbol, None)

        # Filter tick data with older timestamp
        if last_dt and tick.datetime < last_dt:
            return

        if last_dt and last_dt.minute != tick.datetime.minute:
            if self.tick_filter(tick):
                return  # 将pass改为return，不再执行新bar的生成和记录
            else:
                bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
                if bar:
                    bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                    self.record_bar(bar)
                    self.bars[tick.vt_symbol] = None

        bar: Optional[BarData] = self.bars.get(tick.vt_symbol, None)
        if not bar:
            bar = BarData(symbol=tick.symbol, exchange=tick.exchange, interval=Interval.MINUTE, datetime=tick.datetime,
                          gateway_name=tick.gateway_name, open_price=tick.last_price, high_price=tick.last_price,
                          low_price=tick.last_price, close_price=tick.last_price, open_interest=tick.open_interest)
            self.bars[bar.vt_symbol] = bar
        else:
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.open_interest = tick.open_interest
            bar.datetime = tick.datetime

        last_tick: Optional[TickData] = self.last_ticks.get(tick.vt_symbol, None)
        if last_tick:
            bar.volume += max(tick.volume - last_tick.volume, 0)
            bar.turnover += max(tick.turnover - last_tick.turnover, 0)

        self.last_ticks[tick.vt_symbol] = tick
        self.last_dts[tick.vt_symbol] = tick.datetime

    @virtual
    def process_timer_event(self, event: Event):
        """"""
        pass

    def process_tick_event(self, event: Event):
        """"""
        # update cache
        tick = event.data
        time_str = tick.datetime.strftime('%Y%m%d%H%M%S')
        self.tick_time[tick.symbol] = time_str
        # self.write_log(f"@@@redis_engine: process_tick_event: {tick}")
        self.update_tick(tick)

    def tick_time_info(self):
        ret = "{"
        for k, v in self.tick_time.items():
            info = f"{k} {v}\n"
            ret = ret + info
        ret = ret + "}"
        return ret

    def process_contract_event(self, event: Event):
        """"""
        contract = event.data
        vt_symbol = contract.vt_symbol

        if vt_symbol in self.bar_recordings:
            self.subscribe(contract)

    def write_log(self, msg: str):
        """"""
        self.main_engine.write_log(msg)

    def put_event(self):
        """"""
        # bar_symbols = list(self.bar_recordings)
        # bar_symbols.sort()
        pass

    # 处理bar,供其它应用处理
    def record_bar(self, bar: BarData):
        """"""
        try:
            time_str = datetime.now().strftime("%Y-%m-%d-%H%M%S")
            if self.bar_filter(bar):
                pass
            else:
                self.last_bars[bar.vt_symbol] = bar
                self.update_redis(bar)
                self.write_log(f" ======1======record bar memory: {time_str}: {bar.vt_symbol} {bar.datetime} "
                               f"o:{bar.open_price} h:{bar.high_price} l:{bar.low_price} c:{bar.close_price} "
                               f"v:{bar.volume} oi:{bar.open_interest}\n"
                               f" ======2========put to event_engine: {bar}")
                event = Event(EVENT_BAR, bar)
                event2 = Event(EVENT_BAR_RECORD, copy(bar))
                self.event_engine.put(event)
                self.event_engine.put(event2)
        except Exception:
            msg = f"[record_bar] 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)

    # def get_bar_generator(self, vt_symbol: str):
    #     """"""
    #     bg = self.bar_generators.get(vt_symbol, None)

    #     if not bg:
    #         bg = BarGenerator(self.record_bar)
    #         self.bar_generators[vt_symbol] = bg
    #     return bg

    def subscribe(self, contract: ContractData):
        """"""
        req = SubscribeRequest(symbol=contract.symbol, exchange=contract.exchange)
        self.write_log(f"[subscribe] send subscribe req {contract.symbol}")
        self.main_engine.subscribe(req, contract.gateway_name)


class BarGenEngineIb(BarGenEngine):
    # tradingPeriod = __tradingPeriod
    # FAKE_TIME = __US_TIME

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super().__init__(main_engine, event_engine)
        self.resubscribed: list[str] = []

    # todo:美股（期货处理获取主连合约问题）处理未来几天的分红除权信息，有的时候除权导致价格下跌，触发止损

    # def trading_hours(self, data: Union[TickData, BarData]):
    #     # today = datetime.now(shanghai).date()
    #     # # 将US_TIME转为上海时区并删除时区信息
    #     # time_list = [(datetime.combine(today, start_time).astimezone(shanghai).replace(tzinfo=None),
    #     #                 datetime.combine(today, end_time).astimezone(shanghai).replace(tzinfo=None)) for start_time, end_time in
    #     #                  self.FAKE_TIME]
    #     # return time_list
    #     return self.FAKE_TIME
    #
    # def tick_filter(self, tick: TickData):
    #     time_list = self.trading_hours(tick)
    #     end_list = {x[1] for x in time_list}
    #     if tick.datetime.time() in end_list:
    #         return True
    #     return False
    #
    # def bar_filter(self, bar: BarData):
    #     time_list = self.trading_hours(bar)
    #     ret = True
    #     current_time = datetime.now(eastern)
    #     # bar_time从bar.datetime.time()获取，转为美东时区
    #     bar_time = bar.datetime.astimezone(eastern).time()
    #     # 如果当前时间不在time_list的每个(start,end)区间内，则ret=False，record_bar
    #     for start_time, end_time in time_list:
    #         if start_time <= bar_time <= end_time:
    #             ret = False
    #             break
    #     if ret:
    #         self.write_log(f"[ib_bar_filter] {bar.vt_symbol}不在交易时间: bar.datetime: {bar.datetime} "
    #                        f"current_time.time(): {current_time.time()}")
    #     return ret

    def load_setting(self):
        # 继承父类的load_setting
        super().load_setting()
        from time import sleep
        sleep(10)

        # api的contracts_details
        cds: dict[str, ContractDetails] = self.main_engine.get_gateway('IB').api.contracts_details

        # self.tradinghours_info = {
        #     symbol: {
        #         self.tradingPeriod: [
        #             [dt + '00' for dt in elem.split('-')]
        #                 for elem in getattr(cd[symbol], self.tradingPeriod).split(';')# cd[symbol][self.tradingPeriod]
        #                     if elem and 'CLOSED' not in elem],
        #        'timeZoneId':
        #             cd[symbol].timeZoneId# cd[symbol]['timeZoneId']
        #     } for symbol in cd.keys()}
        self.tradinghours_info = {}
        for symbol, cd in cds.items():
            trading_period = SECTYPE_TRADING_PERIOD.get(cd.contract.secType, 'tradingHours')
            self.write_log(f"[load_setting] {trading_period} {symbol} {cd.contract.symbol}-{cd.contract.lastTradeDateOrContractMonth}-{cd.contract.currency}-{cd.contract.secType}.{cd.contract.exchange} {cd.contract.localSymbol}")
            self.tradinghours_info[symbol] = {
                'tradingPeriod': [
                    [dt + '00' for dt in elem.split('-')]
                        for elem in getattr(cd, trading_period).split(';')
                            if elem and 'CLOSED' not in elem],
                'timeZoneId':
                    cd.timeZoneId # cd['timeZoneId']
            }

        self.write_log(f"[load_setting] tradinghours: {self.tradinghours_info}")

        # 时间比较版本  # closed_days, time_zones = self.get_closed_days(tradinghours)  # self.write_log(f"[load_setting] closed_days: {closed_days}")

    # def get_closed_days(self, tradinghours):
    #     closed_days = {}
    #     time_zones = {}
    #     for symbol in tradinghours.keys():
    #         td_hours = tradinghours[symbol][self.tradingPeriod].split(';')
    #         closed_day = [elem.split(':')[0] for elem in td_hours if 'CLOSED' in elem]
    #         time_zone = tradinghours[symbol]['timezone']
    #         closed_day = [datetime.strptime(elem, "%Y%m%d").date() for elem in closed_day]
    #         closed_days[symbol] = closed_day
    #         time_zones[symbol] = time_zone
    #     return closed_days, time_zones

    # 时间转时区转字符串再比较版本
    # data: Union[TickData, BarData]的datetime转时区为tradeinghours_info中的时区（使用pytz兼容夏令时），再转字符串
    def datetime2str(self, data: Union[TickData, BarData]):
        # contract = self.main_engine.get_contract(data.vt_symbol)
        # time_zone = pytz.timezone(contract.timeZoneId)
        try:
            time_zone = pytz.timezone(self.tradinghours_info[data.vt_symbol]['timeZoneId'])
        except:
            if data.vt_symbol not in self.resubscribed:
                self.resubscribed.append(data.vt_symbol)
                self.write_log(f"[datetime2str] error: {traceback.format_exc()} \n resubscribe {data.vt_symbol}")
                self.subscribe_recording(data.vt_symbol)
            return ''
        # 查看data的datetime是否有时区信息
        if data.datetime.tzinfo:
            # data的datetime转为tradinghours_info中的时区
            data_datetime = data.datetime.astimezone(time_zone)
        else:
            data_datetime = data.datetime.replace(tzinfo=pytz.timezone('Asia/Shanghai')).astimezone(time_zone)
        # data_datetime转为字符串
        data_datetime_str = data_datetime.strftime("%Y%m%d:%H%M%S")
        return data_datetime_str

    def data_filter(self, data: Union[TickData, BarData]):
        # 使用datetime2str
        data_datetime_str = self.datetime2str(data)
        # 如果当前时间在tradinghours_info中的tradingHours中，则ret=False，不过滤
        ret = True
        try:
            for start_time, end_time in self.tradinghours_info[data.vt_symbol]['tradingPeriod']:
                if start_time <= data_datetime_str <= end_time:
                    ret = False
                    break
        except:
            ret = True
        return ret

    def tick_filter(self, tick: TickData):
        return self.data_filter(tick)

    def bar_filter(self, bar: BarData):
        ret = self.data_filter(bar)
        if ret:
            self.write_log(f"[ib_bar_filter] {bar.vt_symbol}不在交易时间: bar.datetime: {bar.datetime} "
                           f"data_datetime_str: {self.datetime2str(bar)}")
        return ret

    def update_tick(self, tick: TickData):
        last_tick: Optional[TickData] = self.last_ticks.get(tick.vt_symbol, None)
        if last_tick and tick.volume - last_tick.volume == 0:
            return
        super().update_tick(tick)

    def process_timer_event(self, event: Event):
        """"""
        current = datetime.now(shanghai)  # 行情推送、bar生成、bar记录都使用上海时间
        # current_eastern = current.astimezone(eastern)
        current_time = current.time()
        if current.minute != self.last_timer_event_minute and current_time.second > 10:
            self.last_timer_event_minute = current.minute

            if not self.last_bars:
                self.write_log(f"[process_timer_event] last_bars is empty")
                return

            # in_time = False
            #
            # for start_time, end_time in self.FAKE_TIME:
            #     if start_time < current_eastern.time() and (
            #             current_eastern - timedelta(minutes=1)).time() <= end_time:
            #         in_time = True
            #         break

            # if in_time:
                # 不用last_ticks原因：last_ticks可能是当前分钟收到的tick，而不是上一分钟的tick
                # 每分钟检查last_bars的分钟是否是上一分钟，如果不是put一次fake_bars（其中每个vt_symbol对应的volume和turnover改为0，datetime改为上一分钟）以触发EVENT_BAR，保证每分钟都有bar

            self.write_log(f'[process_timer_event] init fake_bar from self.last_bars: {self.last_bars}')
            last_minute_t = (current - timedelta(minutes=1)).replace(second=0, microsecond=0)

            for vt_symbol in self.last_bars.keys():
                # if current_eastern.date() not in TDAYS.get(self.last_bars[vt_symbol].exchange.value, []):
                #     self.write_log(
                #         f"[process_timer_event] 标的{vt_symbol}当前美东日期不在TDAYS的{self.last_bars[vt_symbol].exchange.value}的日期列表中，当前美东日期: {datetime.now(eastern).date()}")
                #     # 跳过当前vt_symbol
                #     continue

                if not self.last_bars[vt_symbol]:
                    continue

                # todo: 用tradeinghours_info中的各品种的tradingPeriod，如果不在则跳过

                # 如果上一个bar的分钟不是上一分钟，则put一次fake_bars
                if self.last_bars[vt_symbol].datetime.minute != last_minute_t.minute:
                    # 如果有self.bars且当前bar的分钟是当前分钟没结束（因为当前分钟第一个update_tick时，self.bars产生），或者没有self.bars，则将上一个bar赋值给last_bar（datetime改为上一分钟），record_bar(self.last_bars[vt_symbol])
                    if not self.bars.get(vt_symbol, None) or self.bars[vt_symbol] and self.bars[vt_symbol].datetime.minute == current.minute:
                        try:
                            self.last_bars[vt_symbol].datetime = last_minute_t
                            self.last_bars[vt_symbol].open_price = self.last_bars[vt_symbol].high_price = self.last_bars[vt_symbol].low_price = self.last_bars[vt_symbol].close_price
                            self.last_bars[vt_symbol].volume = self.last_bars[vt_symbol].turnover = 0
                            self.record_bar(self.last_bars[vt_symbol])
                            self.write_log(
                                f"[process_timer_event] success {vt_symbol} fake_bar, "
                                f"{self.last_bars[vt_symbol].datetime.time()} c:{self.last_bars[vt_symbol].close_price} v:{self.last_bars[vt_symbol].volume}")
                        except Exception:
                            self.write_log(
                                f'[process_timer_event] error {vt_symbol} fake_bar '
                                f'current_time: {current_time} self.last_bars[vt_symbol]: {self.last_bars[vt_symbol]} '
                                f'{traceback.format_exc()}')
                    # 如果当前bar的分钟不是当前分钟（可能是上一分钟，也可能是上上分钟，只循环处理为上一分钟的情况，并将当前bar改为None）
                    # 当前bar改为None之前，则将当前bar赋值给last_bar（datetime改为上一分钟），record_bar(self.last_bars[vt_symbol])，再将当前bar改为None
                    else:
                        # 也可能在上次进入此处时，将self.bars[vt_symbol]改为None了，即伪造过一次：强制结束上一分钟的当前分钟bars并推送、清空过
                        self.last_bars[vt_symbol] = self.bars[vt_symbol]
                        self.last_bars[vt_symbol].datetime = last_minute_t
                        self.record_bar(self.last_bars[vt_symbol])
                        self.bars[vt_symbol] = None
                        self.write_log(
                            f"[process_timer_event] success {vt_symbol} fake_bar, {self.last_bars[vt_symbol].datetime} c:{self.last_bars[vt_symbol].close_price} v:{self.last_bars[vt_symbol].volume}")

    def subscribe_recording(self, vt_symbol: str):
        """"""
        try:
            symbol, exchange = extract_vt_symbol(vt_symbol)
            self.write_log(f"[subscribe_recording] prepare to send subscribe req：{vt_symbol}")
            req = SubscribeRequest(symbol=symbol, exchange=exchange)
            self.main_engine.subscribe(req, "IB")
        except Exception:
            msg = f"[subscribe_recording] barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"[subscribe_recording] 添加K线记录成功：{vt_symbol}")

'''
class BarGenEngineCtp(BarGenEngine):
    """"""

    def trading_hours(self, data: Union[TickData, BarData]):
        """"""
        if data.exchange in [Exchange.CFFEX, ]:
            return INDEX_TIME
        elif data.exchange in [Exchange.SHFE, Exchange.CZCE, Exchange.DCE, Exchange.INE]:
            # 根据品种判断是白盘还是夜盘到1点还是夜盘到2点半
            # 如果是白盘品种
            # if
            # 如果是夜盘品种
            # if
            # 如果是夜盘2品种
            # if
            return FUT_NIGHT_TIME2

    def tick_filter(self, tick: TickData):
        ret = False
        if tick.datetime.hour == 11 and tick.datetime.minute == 30:
            ret = True
        if tick.datetime.hour == 10 and tick.datetime.minute == 15:
            if tick.exchange in [Exchange.DCE, Exchange.CZCE, Exchange.SHFE]:
                ret = True
        if tick.datetime.hour == 15 and tick.datetime.minute == 0:
            ret = True
        return ret

    def process_timer_event(self, event: Event):
        """"""
        tick_time_str = self.tick_time_info()
        self.write_log(f"[process_timer_event] {tick_time_str}")


# CTP期权，使用future_tick_filter
class BarGenEngineCtpOpt(BarGenEngineCtp):
    def bar_filter(self, bar: BarData):
        time_list = self.trading_hours(bar)
        ret = True
        current_time = datetime.now(shanghai)
        # 如果当前时间不在time_list的每个(start,end)区间内，则ret=False，record_bar
        for start_time, end_time in time_list:
            if start_time <= bar.datetime.time() <= end_time:
                ret = False
        if ret:
            self.write_log(f"[opt_bar_filter] {bar.vt_symbol}不在交易时间: bar.datetime: {bar.datetime} current_time.time(): {current_time.time()}")
        return ret  # return False # test lance

    def process_timer_event(self, event: Event):
        """"""
        current = datetime.now(shanghai)
        current_time = current.time()
        if current_time.second == 10:
            if not self.last_bars:
                self.write_log(f"[process_timer_event] last_bars is empty")
            else:
                in_time = False
                for start_time, end_time in FUT_NIGHT_TIME2:
                    if start_time < current_time and (current - timedelta(minutes=1)).time() <= end_time:
                        in_time = True
                        break

                if in_time:
                    # 不用last_ticks原因：last_ticks可能是当前分钟收到的tick，而不是上一分钟的tick
                    # 每分钟检查last_bars的分钟是否是上一分钟，如果不是put一次fake_bars（其中每个vt_symbol对应的volume和turnover改为0，datetime改为上一分钟）以触发EVENT_BAR，保证每分钟都有bar
                    try:
                        self.write_log(f'[process_timer_event] init fake_bar from self.last_bars: {self.last_bars}')
                        for vt_symbol in self.last_bars.keys():
                            last_bar = self.last_bars[vt_symbol]
                            if last_bar.datetime.minute != (current - timedelta(minutes=1)).minute:
                                # if self.bars[vt_symbol].datetime.minute == current.minute:
                                # 如果有self.bars且当前bar的分钟是当前分钟没结束，或者没有self.bars，则将上一个bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)
                                if vt_symbol not in self.bars.keys() or self.bars[vt_symbol] and self.bars[
                                    vt_symbol].datetime.minute == current.minute:
                                    try:
                                        fake_bar = BarData(symbol=last_bar.symbol, exchange=last_bar.exchange,
                                                           interval=Interval.MINUTE,
                                                           # datetime=(current - timedelta(minutes=1)).replace(second=0, microsecond=0).astimezone(ZoneInfo('Asia/Shanghai')),
                                                           datetime=(current - timedelta(minutes=1)).replace(second=0,
                                                                                                             microsecond=0),
                                                           gateway_name=last_bar.gateway_name if last_bar.gateway_name else 'CTP',
                                                           open_price=last_bar.close_price,
                                                           high_price=last_bar.close_price,
                                                           low_price=last_bar.close_price,
                                                           close_price=last_bar.close_price,
                                                           open_interest=last_bar.open_interest, volume=0, turnover=0)
                                        self.record_bar(fake_bar)
                                        self.write_log(
                                            f"[process_timer_event] success to update {vt_symbol} fake_bars from last_bars, {last_bar.datetime} c:{last_bar.close_price}")
                                    except Exception:
                                        self.write_log(
                                            f'[process_timer_event] error to update {vt_symbol} fake_bars: {traceback.format_exc()}')
                                        # 查看
                                        self.write_log(f'[process_timer_event] error last_bar: {last_bar}')
                                        self.write_log(
                                            f"[process_timer_event] error vt_symbol: {vt_symbol} current_time: {current_time} bar_time: {last_bar.datetime.time()}")
                                else:
                                    # 也可能在上次进入此处时，将self.bars[vt_symbol]改为None了，即伪造过一次：强制结束上一分钟的当前分钟bars并推送、清空过
                                    if self.bars[vt_symbol]:
                                        # 如果当前bar的分钟不是当前分钟，则将当前bar赋值给last_bar（datetime改为上一分钟），record_bar(last_bar)，清空bars
                                        self.last_bars[vt_symbol] = self.bars[vt_symbol]
                                    self.last_bars[vt_symbol].datetime = (current - timedelta(minutes=1)).replace(
                                        second=0, microsecond=0)
                                    self.record_bar(self.last_bars[vt_symbol])
                                    self.bars[vt_symbol] = None
                                    # self.bars[vt_symbol].volume = 0
                                    # self.bars[vt_symbol].turnover = 0
                                    # self.last_ticks[vt_symbol] = None # test lance
                                    # self.last_dts[vt_symbol] = None # test lance
                                    self.write_log(
                                        f"[process_timer_event] success to update {vt_symbol} fake_bars from bars, {self.last_bars[vt_symbol].datetime} c:{self.last_bars[vt_symbol].close_price}")
                    except Exception:
                        self.write_log(f'[process_timer_event] error to update fake_bars: {traceback.format_exc()}')
                        self.write_log(f'[process_timer_event] last_bars: {self.last_bars}')
                self.write_log(f"[process_timer_event] last_bars keys: {self.last_bars.keys()}")
'''