import scrapy
from scrapy.linkextractors import LinkExtractor
from scrapy.spiders import Crawl<PERSON>pider, Rule
from IB.items import IbItem

class IbspiderSpider(CrawlSpider):
    name = 'ibspider'
    allowed_domains = ['www.interactivebrokers.com.hk']
    start_urls = ['https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=stk']

    rules = (
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=fut
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=europe_fut
        # https://www.interactivebrokers.com.hk/cn/index.php?f=5430&p=asia_fut
        # fut期货 p=多个字母（惰性匹配） 下划线可有可无 fut
        Rule(LinkExtractor(allow=r'f=5430&p=\w*[_]?fut'), callback='parse_item', follow=True),
        # https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=cme&showcategories=FUTGRP
        # https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=cme&showcategories=FUTGRP&p=&ptab=&cc=&limit=100&page=2
        # https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=cbot&showcategories=FUTGRP
        # https://www.interactivebrokers.com.hk/cn/index.php?f=2222&exch=cde&showcategories=FUTGRP
        Rule(LinkExtractor(allow=r'f=2222&exch=\w*&showcategories=FUTGRP'), callback='parse_detail', follow=True),
    )

    def parse_item(self, response):
        # item = {}
        #item['domain_id'] = response.xpath('//input[@id="sid"]/@value').get()
        #item['name'] = response.xpath('//div[@id="name"]').get()
        #item['description'] = response.xpath('//div[@id="description"]').get()
        # return item
        # print(f'parse_item: {response.url}')
        pass

    def parse_detail(self, response):
        item = IbItem()
        # Exchange - National Stock Exchange of India (NSE)
        交易所 = response.xpath('//*[@id="exchange-info"]/div/div/div/h2/text()').get()
        item['交易所'] = 交易所.replace('Exchange - ', '')
        item['时间'] = response.xpath('//*[@id="exchange-info"]/div/div/div/div[1]/table/tbody/tr/td[1]/text()').get()
        table = response.xpath('//*[@id="exchange-products"]/div/div/div[3]/div/div/div/table/tbody/tr')
        for tr in table:
            item['IB_Symbol'] = tr.xpath('td[1]/text()').get()
            item['Product_Description'] = tr.xpath('td[2]/a/text()').get()
            # "javascript:NewWindow('https://contract.ibkr.info/index.php?action=Details&site=GEN&conid=644107700 "
            #          "','Details','600','600','custom','front');"
            链接地址 = tr.xpath('td[2]/a/@href').get()
            # 提取链接地址中的url
            链接地址 = 链接地址.split("'")[1]
            item['链接地址'] = 链接地址
            item['Symbol'] = tr.xpath('td[3]/text()').get()
            item['Currency'] = tr.xpath('td[4]/text()').get()
            yield item