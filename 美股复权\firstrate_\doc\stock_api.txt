FirstRate Data
Bundles
Stocks
Options
Futures
Indices
ETFs
Crypto/FX
About
Stock   |   ETF   |   Index   |   Futures   |   Crypto   |   FX   |   Options
FirstRate Data STOCK API Documentation
The API provides customers with a lightweight method for accessing our datasets which can be integrated into custom workflows such as Python scripts.


Note - the API does not deliver raw data, only zip archives of csv files are served. You will need to uncompress the zip archive and iterate through the files in the archive.

Datasets are updated daily. The 'update' data (for past day, week or month) are available at 1am US Eastern Time for the prior day's trade data, whereas the full archives (containing the entire historical series) are available at 2am US Eastern Time.

All API functions are authenticated using your userid which is YtVEwkCXOU-CykSecfdlTQ.
Therefore all API requests need to be appended with the parameter userid=YtVEwkCXOU-CykSecfdlTQ as shown in the examples.

Details on the stock dataset (file format, timezone etc) are available on the readme file at https://firstratedata.com/_readme/stock.txt
API Functions
The API supports the below capabilities:

Historical Data Requests - (requesting the historical datafiles)

Full Updates - (returns the full history for just the adjusted tickers)

Splits / Dividends Requests - (requesting the historical stock/ETF splits and dividends)

Update Log - (requesting ticker update logs for dataset)

Delisted Ticker Data

Last Update - (provides the last update date for the data. This is helpful for checking if there is fresh data available to request)

Ticker Listing - (provides the full listing of tickers as well as start and end dates)

Historical Data Requests
This function returns historical data archives (.txt files in csv format which are grouped into zip archives)

Url EndPoint : https://firstratedata.com/api/data_file

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Data Details : Full details on the data format, timezone, as well as available tickers and date ranges can be viewed on the bundle ReadMe Page

Parameters : The below parameters are used with the Url Endpoint to use the Historical Data Requests function:

Parameter : type
Accepted Values : stock , etf, futures , crypto , index , fx , options

Description : Specifies the type of instrument that is being requested.

Example :
https://firstratedata.com/api/data_file?type=stock&period=week&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : period
Accepted Values : full , month , week , day

Description : Specifies the period to request data for. 'full' requests the entire historical archive, 'month' requests the last 30 days, 'week' requests the current trading week (starting on Monday), 'day' requests the last trading day.

To request the full historical archive you also need to specify a ticker_range parameter (see below).

Example :
https://firstratedata.com/api/data_file?type=stock&period=full&ticker_range=A&adjustment=adj_split&timeframe=1hour&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : ticker_range
Accepted Values : A-Z (one letter of the alphabet)

Description : Only to be used when requested the full historical dataset (ie 'period=full'). This parameter specifies the first letter of the ticker, for example 'ticker_range=C' will request all tickers beginning with the letter C

This parameter can only be used when requested the full historical archive (ie 'period=full')

Example :
https://firstratedata.com/api/data_file?type=stock&period=full&ticker_range=C&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : timeframe
Accepted Values : 1min , 5min , 30min , 1hour , 1day

Description : Specifies the period the timeframe of the data. '1min' will request 1-minute intraday bars, '5min' requests 5-minute bars etc.
Note : bars with zero volumes are not included

Example :
https://firstratedata.com/api/data_file?type=stock&period=week&adjustment=adj_split&timeframe=1min&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : adjustment
Accepted Values : adj_split , adj_splitdiv , UNADJUSTED

Description : Specifies the type of adjustment. 'adj_split' is data adjusted for splits only, 'adj_splitdiv' is data adjusted for both splits and dividends, 'UNADJUSTED' is raw data without any splits or dividend adjustments. UNADJUSTED data is only available in the 1min and 1day timeframes.

Example :
https://firstratedata.com/api/data_file?type=stock&period=week&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Delisted Ticker Data
This function returns data for tickers which have been delisted.
The pre-2025 historical dataset is split into four archives which need to be individually downloaded. Additionally there is an archive for 2025 (which is requested using the 'update'' parameter)
The 2025 archive and the current week archives are updated at the end of each each (on a Sunday).

Url EndPoint : https://firstratedata.com/api/delisted_data_file

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Parameters : The below parameters are used with the Url Endpoint to use the Delisted Ticker Data function:

Parameter : archive_number (optional)
Accepted Values : 1, 2, 3, 4, 5

Description : Specifies the archive number. This is used to download one of the four historical (pre-2025) archives. This parameter does not need to be used if the 'update' parameter is used.

Example :
https://firstratedata.com/api/delisted_data_file?archive_number=2&timeframe=1hour&adjustment=adj_splitdiv&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : update (optional)
Accepted Values : week , year

Description : Specifies the update period. 'year' will return all delisted data in 2025 , 'week' return only the last week's delisted data. The delisted data is updated at the end of each week by Sunday 11pm EST. This parameter does not need to be used if the 'archive' parameter is used.

Example :
https://firstratedata.com/api/delisted_data_file?update=week&timeframe=1hour&adjustment=adj_splitdiv&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : adjustment
Accepted Values : adj_split , adj_splitdiv , UNADJUSTED

Description : Specifies the type of adjustment. 'adj_split' is data adjusted for splits only, 'adj_splitdiv' is data adjusted for both splits and dividends, 'UNADJUSTED' is raw data without any splits or dividend adjustments. UNADJUSTED data is only available in the 1min timeframe.

Example :
https://firstratedata.com/api/delisted_data_file?update=week&timeframe=1hour&adjustment=adj_splitdiv&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : timeframe
Accepted Values : 1min , 5min , 30min , 1hour , 1day

Description : Specifies the period the timeframe of the data. '1min' will request 1-minute intraday bars, '5min' requests 5-minute bars etc.
Note : bars with zero volumes are not included.

Example :

Example :
https://firstratedata.com/api/delisted_data_file?update=week&timeframe=1hour&adjustment=adj_splitdiv&userid=YtVEwkCXOU-CykSecfdlTQ

Full Updates
This function returns the full dataset for stocks or ETFs which have been adjusted.
When a stock/ETF is adjusted for a split or a dividend, the full history is adjusted from the start date up to the present day. Therefore, customers maintaining a local archive of the full dataset will have an issue using day/week/month updates to keep the archive current since the update will not contain the full history of all the adjusted data.
The Full Updates function returns the full history of just the updated tickers. These can then replace the legacy files in the local archive.

Url EndPoint : https://firstratedata.com/api/full_update

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Parameters : The below parameters are used with the Url Endpoint to use the Individual Contract function:

Parameter : adjustment
Accepted Values : adj_split , adj_splitdiv

Description : Specifies the type of adjustment. 'adj_split' is data adjusted for splits only, 'adj_splitdiv' is data adjusted for both splits and dividends, 'UNADJUSTED' is raw data without any splits or dividend adjustments.

Example :
https://firstratedata.com/api/full_update?type=stock&period=week&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : timeframe
Accepted Values : 1min , 5min , 30min , 1hour , 1day

Description : Specifies the period the timeframe of the data. '1min' will request 1-minute intraday bars, '5min' requests 5-minute bars etc.
Note : bars with zero volumes are not included

Example :

Example :
https://firstratedata.com/api/full_update?type=stock&period=week&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : period
Accepted Values : week , day

Description : Specifies the period to request data for, 'week' requests the last 5 trading days, 'day' requests the last trading day.

Example :

Example :
https://firstratedata.com/api/full_update?type=stock&period=week&timeframe=1day&adjustment=adj_split&userid=YtVEwkCXOU-CykSecfdlTQ

Splits / Dividends Requests
This function returns historical splits or dividends for an instrument type (either stock or ETF).

Url EndPoint : https://firstratedata.com/api/meta_file

Data Format | Splits : {date,split-ratio} date is in format yyyy-MM-dd,the split-ratio is the ratio of new shares to old share

Data Format | Dividends : {ex-dividend date,dividend amount} the ex-dividend date is in the format yyyy-MM-dd and is the date on which the stock holder is no longer entitled to the dividend.

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Parameters : The below parameters are used with the Url Endpoint to use the Splits / Dividends Requests function:

Parameter : type
Accepted Values : stock , etf

Description : Specifies the type of instrument that is being requested.

Example :
https://firstratedata.com/api/meta_file?type=stock&metafile_type=splits&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : metafile_type
Accepted Values : splits , dividends

Description : Specifies the whether the request is for historical splits or dividends.

Example :
https://firstratedata.com/api/meta_file?type=stock&metafile_type=splits&userid=YtVEwkCXOU-CykSecfdlTQ

Update Logs
This function returns the ticker updates in the dataset.

Url EndPoint : https://firstratedata.com/api/update_log

Data Format : {ticker,date} date is in format yyyy-MM-dd

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Parameters : The below parameters are used with the Url Endpoint to use the Update Logs function:

Parameter : type
Accepted Values : stock

Description : Specifies the type of instrument that is being requested.

Example :
https://firstratedata.com/api/update_log?type=stock&log_type=delisted&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : log_type
Accepted Values : delisted , added, changed

Description : Specifies the type of log.
'delisted' returns a listing of delisted tickers (each delisted ticker is removed from the main dataset, the data is added to the delisted dataset and the ticker appended with '-DELISTED' so TWTR becomes 'TWTR-DELISTED'
'added' returns a listing of newly added tickers (typically due to IPOs or spinoffs).

Example :
https://firstratedata.com/api/update_log?type=stock&log_type=delisted&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : html
Accepted Values : true, false (false is the default value)

Description : Specifies the is the returned data is in HTML format. Set this value to true to view the data in a web browser.

Example :
https://firstratedata.com/api/update_log?type=stock&log_type=delisted&userid=YtVEwkCXOU-CykSecfdlTQ&html=true

Last Update
This function returns the last update for an instrument type. This function can be used to check whether there has been an update prior to executing data requests. Unlike data requests, this function returns raw data in the form of a date and not zip files.

Url EndPoint : https://firstratedata.com/api/last_update

Requires Authentication : YES. All requests must include the parameter userid with your userid given to you in your signup email. Also available from your Customer Download Page.

Parameters : The below parameters are used with the Url Endpoint to use the Last Update function:

Parameter : type
Accepted Values : stock , etf(only stocks or ETFs can currently be requested)

Description : Specifies the type of instrument that is being requested.

Example :
https://firstratedata.com/api/last_update?type=stock&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : is_full_update (optional)
Accepted Values : false , true (false is the default)

Description : This parameter is used when requesting the last update of the full historical dataset (otherwise the Last Update function will return the date for the update files for the past day, week or month).

Example :
https://firstratedata.com/api/last_update?type=stock&is_full_update=true&userid=YtVEwkCXOU-CykSecfdlTQ

Ticker Listing
This function returns the full listing of tickers as well as start and end dates for a specified instrument type. The data is returned in csv format so it can be copied into a text file and opened from a spreadsheet application if required.

Url EndPoint : https://firstratedata.com/api/ticker_listing

Data Format : {ticker},{name},{startDate},{endDate}

Parameters : The below parameters are used with the Url Endpoint to use the Last Update function:

Parameter : type
Accepted Values : stock , etf(only stocks or ETFs can currently be requested)

Description : Specifies the type of instrument that is being requested.

Example :
https://firstratedata.com/api/ticker_listing?type=stock&userid=YtVEwkCXOU-CykSecfdlTQ

Parameter : html
Accepted Values : true, false (false is the default value)

Description : Specifies the is the returned data is in HTML format. Set this value to true to view the data in a web browser.

Example :
https://firstratedata.com/api/ticker_listing?type=stock&userid=YtVEwkCXOU-CykSecfdlTQ&html=true

© 2025 X3Data Limited | <EMAIL>
x1.4
