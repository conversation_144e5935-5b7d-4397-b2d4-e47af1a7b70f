# 从"E:\OneDrive - lancely\芷瀚同步\qt\IB\IB全市场标的筛选\UI筛选\文华 IB 自选.xlsx"中获取"vn_symbol_fut"，合成规则："IB_Symbol"列+"-"+"Currency"列+"-FUT."+"exchange1"列
# 筛选“自选”列为1的，将"vn_symbol_fut"列值的列表保存到本地json文件中
import pandas as pd
import json
df = pd.read_excel(r"E:\OneDrive - lancely\芷瀚同步\qt\IB\IB全市场标的筛选\UI筛选\文华 IB 自选.xlsx")
df = df[df["自选"]==1]
df["vn_symbol_fut"] = df["IB_Symbol"] + "-" + df["Currency"] + "-FUT." + df["exchange1"]
vn_symbol_list = df["vn_symbol_fut"].tolist()
with open("vn_symbol_fut.json", "w") as f:
    json.dump(vn_symbol_list, f, indent=4)