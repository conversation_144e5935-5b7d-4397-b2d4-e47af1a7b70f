import json
import traceback
from datetime import datetime, timedelta
import os
import sys
from typing import Dict, Any, Optional, Set, List, Tuple
import queue
import threading
import time
import random
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import mysql.connector  # 添加MySQL连接器

import yfinance as yf
from yfinance.exceptions import YFRateLimitError
import typer
from loguru import logger
from peewee import (Model, CharField, DateTimeField, FloatField, BigIntegerField,
                    AutoField, MySQLDatabase, BooleanField)

log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0,
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00",
    filter=__name__
)

import inflection
from typing_extensions import Annotated
from vnpy.trader.database import ZoneInfo, get_database
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.object import BarData

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)
from utils.database_manager import db_manager, YahooInfo, IbProduct
from vnpy.trader.utility import load_json, save_json, get_file_path
from utils.proxy_utils import get_system_proxy


class YahooHistoryUpdater:
    def __init__(self):
        ...
        self.proxies = get_system_proxy()
        logger.info(f"系统代理设置: {self.proxies}")

        # ✅ 新增：全局设置代理（消除警告）
        if self.proxies and "http" in self.proxies:
            yf.set_config(proxy=self.proxies["http"])


# 预定义美东时区对象
TZ_AMERICA_NEWYORK = ZoneInfo('America/New_York')

app = typer.Typer()

# ======================================================
# 更新数据库连接配置
# ======================================================

# common_info 数据库配置（远程服务器）
COMMON_INFO_CONFIG = {
    'database': 'common_info',
    'host': '*************',
    'port': 3308,
    'user': 'zh',
    'password': 'zhP@55word'
}

# my_database 配置（本地服务器）
MY_DATABASE_CONFIG = {
    'database': 'my_database',
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'hzp123'
}

# ======================================================
# 创建数据库连接（用于 common_info）
# ======================================================
common_info_db = MySQLDatabase(
    COMMON_INFO_CONFIG['database'],
    user=COMMON_INFO_CONFIG['user'],
    password=COMMON_INFO_CONFIG['password'],
    host=COMMON_INFO_CONFIG['host'],
    port=COMMON_INFO_CONFIG['port'],
    connect_timeout=10  # 添加连接超时
)


# 重新定义用于 common_info 的 IbProduct 模型
class CommonInfoIbProduct(Model):
    """common_info 数据库的 IbProduct 模型"""
    id = AutoField(primary_key=True)
    isin = CharField(null=True)
    conid = BigIntegerField()
    is_latest = BooleanField(default=True)

    # 其他字段...

    class Meta:
        database = common_info_db
        table_name = 'ib_product'


# 初始化vnpy数据库接口
database = get_database()


# ======================================================
# 雅虎历史数据更新器（使用vnpy_mysql原生API）
# ======================================================
class YahooHistoryUpdater:
    """雅虎历史数据更新器"""

    def __init__(self):
        """初始化"""
        # 设置系统代理
        self.proxies = get_system_proxy()
        logger.info(f"系统代理设置: {self.proxies}")

        # 测试数据库连接
        self._test_db_connection()

        self.failed_symbols = set()
        self.latest_isin_conid_map = self._get_latest_product_isins()

        # 交易所映射
        self.EXCHANGE_MAPPING = {
            "YAHOO": Exchange.SMART,
            "NASDAQ": Exchange.NASDAQ,
            "NYSE": Exchange.NYSE,
            "NYSEARCA": Exchange.AMEX,
            "BATS": Exchange.BATS,
            "OTC": Exchange.OTC,
            "NYSEAMERICAN": Exchange.AMEX,
        }

        # 添加无效ISIN相关的属性
        self.invalid_isins_file = "invalid_isins.json"
        self.invalid_isins = set()
        self._invalid_isins_lock = threading.Lock()
        self._load_invalid_isins()

    def _test_db_connection(self):
        """测试数据库连接"""
        try:
            # 测试vnpy数据库连接
            test_symbol = "TEST.123"
            database.load_bar_data(
                symbol=test_symbol,
                exchange=Exchange.SMART,
                interval=Interval.DAILY,
                start=datetime(2020, 1, 1),
                end=datetime(2020, 1, 2)
            )
            logger.success("vnpy数据库连接测试成功")
        except Exception as e:
            logger.error(f"vnpy数据库连接测试失败: {str(e)}")
            raise

    def _load_invalid_isins(self):
        """从文件加载无效的ISIN列表"""
        try:
            full_file_path = get_file_path(self.invalid_isins_file)
            
            # 检查文件是否存在且修改时间不是今天的，如果是则删除
            if os.path.exists(full_file_path):
                try:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(full_file_path)).date()
                    today = datetime.now().date()
                    
                    if file_mtime != today:
                        os.remove(full_file_path)
                        logger.info(f"已删除过期的无效ISIN文件: {full_file_path} (修改时间: {file_mtime})")
                        self.invalid_isins = set()
                        return
                except Exception as e:
                    logger.warning(f"检查或删除过期无效ISIN文件时出错: {e}")
            
            data = load_json(self.invalid_isins_file)
            self.invalid_isins = set(data) if data else set()
            logger.info(f"已从{self.invalid_isins_file}加载{len(self.invalid_isins)}个无效ISIN")
        except Exception as e:
            logger.error(f"加载无效ISIN列表时出错: {str(e)}")
            self.invalid_isins = set()

    def _save_invalid_isins(self):
        """保存无效的ISIN列表到文件"""
        with self._invalid_isins_lock:
            try:
                save_json(self.invalid_isins_file, sorted(list(self.invalid_isins)))
                logger.info(f"已保存{len(self.invalid_isins)}个无效ISIN到{self.invalid_isins_file}")
            except Exception as e:
                logger.error(f"保存无效ISIN列表时出错: {str(e)}")

    def _add_invalid_isin(self, isin: str):
        """添加一个无效的ISIN到集合中"""
        with self._invalid_isins_lock:
            self.invalid_isins.add(isin)

    def _get_latest_product_isins(self) -> Dict[str, int]:
        """从common_info数据库的IbProduct表获取映射关系"""
        logger.info("正在从common_info数据库获取最新的产品isin和conid...")
        isin_conid_map = {}
        try:
            # 连接到 common_info 数据库
            common_info_db.connect()

            # 查询最新的产品信息
            query = (CommonInfoIbProduct
                     .select(CommonInfoIbProduct.isin, CommonInfoIbProduct.conid)
                     .where(
                (CommonInfoIbProduct.is_latest == True) &
                (CommonInfoIbProduct.isin.is_null(False))
            )
                     .tuples())

            for isin, conid in query:
                if isin and conid:
                    isin_conid_map[isin] = conid

            logger.info(f"已从 common_info 数据库获取{len(isin_conid_map)}个最新的产品isin。")
            logger.debug(f"前5个ISIN-CONID映射: {list(isin_conid_map.items())[:5]}")
        except Exception as e:
            logger.error(f"获取最新产品isin时出错: {str(e)}\n{traceback.format_exc()}")
        finally:
            if not common_info_db.is_closed():
                common_info_db.close()

        return isin_conid_map

    def _parse_vt_symbol(self, vt_symbol: str) -> Tuple[str, str]:
        """解析vt_symbol为conid和exchange"""
        parts = vt_symbol.split(".")
        if len(parts) != 2:
            raise ValueError(f"无效的vt_symbol格式: {vt_symbol}, 应为 'conid.exchange'")
        return parts[0], parts[1]

    def _get_isin_by_conid(self, conid: str) -> Optional[str]:
        """通过conid获取对应的isin"""
        # 尝试直接查找
        for isin, cid in self.latest_isin_conid_map.items():
            if str(cid) == conid:
                return isin

        # 尝试整数转换后查找
        try:
            conid_int = int(conid)
            for isin, cid in self.latest_isin_conid_map.items():
                if cid == conid_int:
                    return isin
        except ValueError:
            pass

        return None

    def _get_default_start_date(self, interval: Interval) -> datetime:
        """获取默认的开始日期"""
        if interval == Interval.DAILY:
            return datetime(2021, 1, 1, tzinfo=TZ_AMERICA_NEWYORK)
        else:  # 分钟级别
            return datetime(2024, 3, 1, tzinfo=TZ_AMERICA_NEWYORK)

    def _convert_to_bar_data(self, row: Dict, date_column: str, symbol: str, exchange: str,
                             interval: Interval) -> BarData:
        """将雅虎数据行转换为BarData对象"""
        # 确保日期列存在
        if date_column not in row:
            available_columns = list(row.keys())
            raise KeyError(f"行数据中缺少日期列 '{date_column}'，可用列: {available_columns}")

        # 获取日期值
        date_value = row[date_column]

        # 处理不同类型的日期对象
        if hasattr(date_value, 'to_pydatetime'):
            # Pandas Timestamp 对象
            datetime_obj = date_value.to_pydatetime()
        elif isinstance(date_value, datetime):
            # 已经是 datetime 对象
            datetime_obj = date_value
        else:
            # 尝试解析字符串日期
            try:
                datetime_obj = datetime.strptime(str(date_value), "%Y-%m-%d %H:%M:%S%z")
            except ValueError:
                try:
                    datetime_obj = datetime.strptime(str(date_value), "%Y-%m-%d")
                except ValueError as e:
                    raise ValueError(f"无法解析日期值: {date_value}") from e

        # 确保时区正确
        if datetime_obj.tzinfo is None:
            datetime_obj = datetime_obj.replace(tzinfo=TZ_AMERICA_NEWYORK)
        else:
            datetime_obj = datetime_obj.astimezone(TZ_AMERICA_NEWYORK)

        # 将 exchange 字符串转换为 Exchange 枚举
        exchange_enum = self.EXCHANGE_MAPPING.get(exchange.upper(), Exchange.SMART)

        # 创建BarData对象
        bar = BarData(
            symbol=symbol,
            exchange=exchange_enum,  # 使用枚举值而不是字符串
            datetime=datetime_obj,
            interval=interval,
            open_price=row["Open"],
            high_price=row["High"],
            low_price=row["Low"],
            close_price=row["Close"],
            volume=row["Volume"],
            gateway_name="YAHOO",
        )
        return bar

    def _download_history_data(self, isin: str, start: datetime, end: datetime, interval: Interval,
                               max_retries: int = 5, adjust: bool = False) -> Optional[List[BarData]]:
        """
        下载历史数据，带重试机制和分段下载功能
        :param isin: ISIN代码
        :param start: 开始日期
        :param end: 结束日期
        :param interval: 数据间隔
        :param max_retries: 最大重试次数
        :return: BarData列表或None
        """
        if start.tzinfo is None:
            start = start.replace(tzinfo=TZ_AMERICA_NEWYORK)
        if end.tzinfo is None:
            end = end.replace(tzinfo=TZ_AMERICA_NEWYORK)
        # 检查是否是已知的无效ISIN

        if isin in self.invalid_isins:
            logger.info(f"isin={isin}: 跳过已知的无效ISIN")
            return None

        try:
            conid = self.latest_isin_conid_map.get(isin)
            if not conid:
                logger.warning(f"无法找到isin={isin}对应的conid")
                return None

            logger.info(f"开始下载 {isin} (conid={conid}) 的 {interval} 数据，时间范围: {start} 到 {end}")

            # 将日期转换为字符串格式（仅日期部分）
            start_str = start.strftime("%Y-%m-%d")
            end_str = end.strftime("%Y-%m-%d")

            # 根据interval参数设置yfinance的interval
            yf_interval = "1d" if interval == Interval.DAILY else "1m"

            # 如果是分钟数据，需要分段下载（每次最多8天）
            if interval != Interval.DAILY:
                return self._download_minute_data_in_chunks(
                    isin=isin,
                    conid=conid,
                    start=start,
                    end=end,
                    max_retries=max_retries,
                    adjust=adjust
                )

            # 日线数据直接下载
            return self._download_data_segment(
                isin=isin,
                conid=conid,
                start_str=start_str,
                end_str=end_str,
                yf_interval=yf_interval,
                max_retries=max_retries,
                adjust=adjust
            )

        except Exception as e:
            if "Invalid ISIN number" in str(e):
                logger.warning(f"isin={isin}是无效的ISIN号码，将添加到无效ISIN列表。")
                self._add_invalid_isin(isin)
            logger.error(f"下载isin={isin}的历史数据时发生未处理异常: {str(e)}\n{traceback.format_exc()}")
            return None

    def _download_minute_data_in_chunks(self, isin: str, conid: int, start: datetime, end: datetime,
                                        max_retries: int = 5, adjust: bool = False) -> Optional[List[BarData]]:
        """
        分段下载分钟数据（每次最多8天）
        """
        logger.info(f"分钟数据需要分段下载（每次最多8天）")

        # 计算总时间跨度（天）
        total_days = (end - start).days
        logger.info(f"总时间跨度: {total_days}天")

        # 设置每个分段的时长（7天23小时59分钟）
        segment_duration = timedelta(days=7, hours=23, minutes=59)

        all_bars = []
        current_start = start

        # 分段下载
        segment_count = 0
        while current_start < end:
            segment_count += 1
            segment_end = min(current_start + segment_duration, end)

            # 如果分段太小（小于1分钟），跳过
            if (segment_end - current_start) < timedelta(minutes=1):
                break

            logger.info(f"下载分段 #{segment_count}: {current_start} 到 {segment_end}")

            # 转换为字符串格式
            start_str = current_start.strftime("%Y-%m-%d")
            end_str = segment_end.strftime("%Y-%m-%d")

            # 下载当前分段
            segment_bars = self._download_data_segment(
                isin=isin,
                conid=conid,
                start_str=start_str,
                end_str=end_str,
                yf_interval="1m",
                max_retries=max_retries,
                adjust=adjust
            )

            if segment_bars:
                all_bars.extend(segment_bars)
                logger.info(f"分段 #{segment_count} 下载完成，获取 {len(segment_bars)} 条数据")
            else:
                logger.warning(f"分段 #{segment_count} 未能获取数据")

            # 移动到下一个分段
            current_start = segment_end

            # 添加随机延迟避免触发限制
            if segment_end < end:
                delay = random.uniform(1.0, 3.0)
                logger.info(f"分段下载间延迟 {delay:.2f} 秒...")
                time.sleep(delay)

        logger.info(f"分钟数据分段下载完成，共 {segment_count} 个分段，总计 {len(all_bars)} 条数据")
        return all_bars

    def _download_data_segment(self, isin: str, conid: int, start_str: str, end_str: str,
                               yf_interval: str, max_retries: int = 5, adjust: bool = False) -> Optional[List[BarData]]:
        """
        下载单个数据分段
        """
        # 带重试机制的下载
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 创建 Ticker 对象
                ticker = yf.Ticker(isin)

                # 下载数据
                df = ticker.history(
                    start=start_str,
                    end=end_str,
                    interval=yf_interval,
                    auto_adjust=adjust,  # 默认不复权
                    actions=False  # 不包含分红和拆股信息
                )

                if df.empty:
                    logger.warning(f"isin={isin}未获取到历史数据 (时间段: {start_str} 到 {end_str})")
                    return None

                # 将日期索引转换为列
                df = df.reset_index()

                # 检查日期列的名称（可能是 'Date' 或 'Datetime'）
                date_column = 'Date' if 'Date' in df.columns else 'Datetime'

                # 转换为BarData列表
                bars = []
                for _, row in df.iterrows():
                    try:
                        bar = self._convert_to_bar_data(
                            row=row,
                            date_column=date_column,
                            symbol=str(conid),
                            exchange="YAHOO",  # 使用默认交易所
                            interval=Interval.MINUTE if yf_interval == "1m" else Interval.DAILY
                        )
                        bars.append(bar)
                    except Exception as e:
                        logger.error(f"转换行数据时出错: {str(e)}\n{traceback.format_exc()}")
                        continue

                logger.info(f"成功下载 {isin} (conid={conid}) 的 {len(bars)} 条数据 ({start_str} 到 {end_str})")
                return bars

            except Exception as e:  # 捕获所有异常
                if isinstance(e, YFRateLimitError):
                    retry_count += 1
                    if retry_count >= max_retries:
                        logger.error(f"下载isin={isin}的历史数据时遇到流控错误，已达最大重试次数: {str(e)}")
                        return None

                    # 指数退避策略
                    retry_delay = random.uniform(5.0, 15.0) * (2 ** retry_count)  # 更长的延迟
                    logger.warning(f"下载isin={isin}的历史数据时遇到流控错误: {str(e)}")
                    logger.info(f"第 {retry_count}/{max_retries} 次重试，等待 {retry_delay:.2f} 秒...")
                    time.sleep(retry_delay)
                else:
                    if "Invalid ISIN number" in str(e):
                        logger.warning(f"isin={isin}是无效的ISIN号码，将添加到无效ISIN列表。")
                        self._add_invalid_isin(isin)
                    logger.error(f"下载isin={isin}的历史数据时出错: {str(e)}\n{traceback.format_exc()}")
                    return None

        return None

    def _process_symbol(self, vt_symbol: str, start: datetime, end: datetime, interval: Interval,
                        data_queue: queue.Queue) -> None:
        """处理单个symbol的历史数据下载"""
        try:
            # 增加初始延迟范围 (2-5秒)
            delay = random.uniform(2.0, 5.0)
            logger.info(f"处理 {vt_symbol} 前等待 {delay:.2f} 秒...")
            time.sleep(delay)

            conid, exchange = self._parse_vt_symbol(vt_symbol)
            isin = self._get_isin_by_conid(conid)

            if not isin:
                logger.warning(f"无法找到conid={conid}对应的ISIN")
                self.failed_symbols.add(vt_symbol)
                return

            logger.info(f"开始处理 {vt_symbol} (conid={conid}, isin={isin})")

            # 尝试获取数据
            bars = self._download_history_data(isin, start, end, interval)

            if bars:
                logger.info(f"成功获取 {vt_symbol} (conid={conid}, isin={isin}) 的 {len(bars)} 条数据")
                data_queue.put((vt_symbol, bars))
            else:
                logger.warning(f"未能获取 {vt_symbol} (conid={conid}, isin={isin}) 的数据")
                self.failed_symbols.add(vt_symbol)

        except Exception as e:
            logger.error(f"处理vt_symbol={vt_symbol}时出错: {str(e)}\n{traceback.format_exc()}")
            self.failed_symbols.add(vt_symbol)

    def save_history_data(self, vt_symbols: List[str], start: Optional[datetime], end: Optional[datetime],
                          interval: Interval) -> None:
        """保存历史数据到数据库"""
        try:
            if not vt_symbols:
                logger.info("没有指定vt_symbol，跳过历史数据保存。")
                return

            # 设置默认时间范围
            end = end or datetime.now(TZ_AMERICA_NEWYORK)
            start = start or self._get_default_start_date(interval)

            logger.info(f"开始保存历史数据，时间范围: {start} 到 {end}, 周期: {interval}")

            # 重置状态
            self.failed_symbols.clear()

            # 创建数据队列和完成事件
            data_queue = queue.Queue()
            fetching_completed = threading.Event()

            # 创建并启动保存线程
            save_thread = threading.Thread(
                target=self._save_bars_to_db,
                args=(data_queue, fetching_completed)
            )
            save_thread.start()

            # 使用线程池并发获取数据
            with ThreadPoolExecutor(max_workers=5) as executor:
                # 提交所有任务
                future_to_symbol = {
                    executor.submit(self._process_symbol, vt_symbol, start, end, interval, data_queue): vt_symbol
                    for vt_symbol in vt_symbols
                }

                # 等待所有任务完成
                for future in as_completed(future_to_symbol):
                    vt_symbol = future_to_symbol[future]
                    try:
                        future.result()  # 获取结果（如果有异常会在这里抛出）
                    except Exception as e:
                        logger.error(f"处理vt_symbol={vt_symbol}时发生异常: {str(e)}\n{traceback.format_exc()}")
                        self.failed_symbols.add(vt_symbol)

            # 标记获取完成并等待保存线程结束
            fetching_completed.set()
            save_thread.join()

            # 保存无效ISIN列表
            self._save_invalid_isins()

            # 打印失败的symbol
            if self.failed_symbols:
                logger.warning(f"以下{len(self.failed_symbols)}个symbol未能成功获取数据: {sorted(self.failed_symbols)}")
            else:
                logger.success("所有symbol的数据获取成功！")

        except Exception as e:
            logger.error(f"保存历史数据时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise

    def _save_bars_to_db(self, data_queue: queue.Queue, completed_event: threading.Event) -> None:
        """使用vnpy_mysql的save_bar_data保存数据"""
        saved_count = 0
        symbol_count = 0

        while not completed_event.is_set() or not data_queue.empty():
            try:
                vt_symbol, bars = data_queue.get(timeout=1)
                symbol_count += 1
                logger.info(f"开始保存{vt_symbol}的{len(bars)}条数据到数据库...")

                # 直接使用vnpy_mysql的save_bar_data方法
                database.save_bar_data(bars)
                saved_count += len(bars)

                logger.info(f"成功保存{vt_symbol}的{len(bars)}条数据到数据库")
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"保存数据到数据库时出错: {str(e)}\n{traceback.format_exc()}")

        logger.success(f"所有数据保存完成，共保存{symbol_count}个symbol的{saved_count}条记录")

    def incremental_update(self, vt_symbols: List[str], end: Optional[datetime], interval: Interval) -> None:
        """增量更新历史数据"""
        try:
            if not vt_symbols:
                logger.info("没有指定vt_symbol，跳过增量更新。")
                return

            # 设置默认结束时间
            end = end or datetime.now(TZ_AMERICA_NEWYORK)

            logger.info(f"开始增量更新历史数据，结束时间: {end}, 周期: {interval}")

            # 重置状态
            self.failed_symbols.clear()

            # 创建数据队列和完成事件
            data_queue = queue.Queue()
            fetching_completed = threading.Event()

            # 创建并启动保存线程
            save_thread = threading.Thread(
                target=self._save_bars_to_db,
                args=(data_queue, fetching_completed)
            )
            save_thread.start()

            # 使用线程池并发获取数据
            with ThreadPoolExecutor(max_workers=5) as executor:
                # 提交所有任务
                future_to_symbol = {
                    executor.submit(self._process_incremental_symbol, vt_symbol, end, interval, data_queue): vt_symbol
                    for vt_symbol in vt_symbols
                }

                # 等待所有任务完成
                for future in as_completed(future_to_symbol):
                    vt_symbol = future_to_symbol[future]
                    try:
                        future.result()  # 获取结果（如果有异常会在这里抛出）
                    except Exception as e:
                        logger.error(f"处理vt_symbol={vt_symbol}时发生异常: {str(e)}\n{traceback.format_exc()}")
                        self.failed_symbols.add(vt_symbol)

            # 标记获取完成并等待保存线程结束
            fetching_completed.set()
            save_thread.join()

            # 保存无效ISIN列表
            self._save_invalid_isins()

            # 打印失败的symbol
            if self.failed_symbols:
                logger.warning(f"以下{len(self.failed_symbols)}个symbol未能成功获取数据: {sorted(self.failed_symbols)}")
            else:
                logger.success("所有symbol的数据增量更新成功！")

        except Exception as e:
            logger.error(f"增量更新历史数据时发生错误: {str(e)}\n{traceback.format_exc()}")
            raise

    def _process_incremental_symbol(self, vt_symbol: str, end: datetime, interval: Interval,
                                    data_queue: queue.Queue) -> None:
        """处理单个symbol的增量更新"""
        try:
            # 增加初始延迟范围 (2-5秒)
            delay = random.uniform(2.0, 5.0)
            logger.info(f"增量更新 {vt_symbol} 前等待 {delay:.2f} 秒...")
            time.sleep(delay)

            conid, exchange = self._parse_vt_symbol(vt_symbol)
            isin = self._get_isin_by_conid(conid)

            if not isin:
                logger.warning(f"无法找到conid={conid}对应的ISIN")
                self.failed_symbols.add(vt_symbol)
                return

            # 获取数据库中已有数据的最后时间
            start = self._get_last_bar_date(conid, exchange, interval)
            if not start:
                # 如果没有数据，使用默认开始日期
                start = self._get_default_start_date(interval)

            # 确保开始时间早于结束时间
            if start >= end:
                logger.info(f"{vt_symbol} 已经是最新数据，无需更新")
                return

            logger.info(f"开始增量更新 {vt_symbol} (conid={conid}, isin={isin})，时间范围: {start} 到 {end}")
            bars = self._download_history_data(isin, start, end, interval)
            if bars:
                data_queue.put((vt_symbol, bars))
            else:
                logger.warning(f"未能获取 {vt_symbol} (conid={conid}, isin={isin}) 的增量数据")
                self.failed_symbols.add(vt_symbol)

        except Exception as e:
            logger.error(f"处理vt_symbol={vt_symbol}时出错: {str(e)}\n{traceback.format_exc()}")
            self.failed_symbols.add(vt_symbol)

    def _get_last_bar_date(self, conid: str, exchange: str, interval: Interval) -> Optional[datetime]:
        """使用vnpy_mysql的load_bar_data获取最后一条记录的日期"""
        try:
            exchange_enum = self.EXCHANGE_MAPPING.get(exchange.upper(), Exchange.SMART)

            # 查询最后一条记录 - 使用更早的日期范围确保获取所有数据
            bars = database.load_bar_data(
                symbol=conid,
                exchange=exchange_enum,
                interval=interval,
                start=datetime(2000, 1, 1),  # 很早的日期确保获取所有数据
                end=datetime.now(TZ_AMERICA_NEWYORK)
            )

            if bars:
                # 手动找出最后一条记录
                last_bar = max(bars, key=lambda x: x.datetime)
                logger.info(f"找到 {conid}.{exchange} 的最后记录时间: {last_bar.datetime}")
                return last_bar.datetime
            else:
                logger.info(f"没有找到 {conid}.{exchange} 的现有记录")
                return None
        except Exception as e:
            logger.error(f"获取最后记录时出错: {str(e)}\n{traceback.format_exc()}")
            return None

    def incremental_update_all_symbol(self, interval: Interval, end: Optional[datetime]) -> None:
        """增量更新所有symbol的历史数据"""
        # 获取所有bar概览信息
        overviews = database.get_bar_overview()

        # 构建完整的vt_symbol列表: conid.exchange
        vt_symbols = []
        for overview in overviews:
            # 获取exchange的字符串表示（例如：Exchange.SMART -> 'SMART'）
            exchange_str = overview.exchange.value
            vt_symbol = f"{overview.symbol}.{exchange_str}"
            vt_symbols.append(vt_symbol)

        logger.info(f"获取数据标的{len(vt_symbols)}条")
        logger.info(f"开始增量更新全部数据")
        self.incremental_update(vt_symbols, end, interval)


@app.command(name="save-history")
def save_history_command(
        vt_symbols: Annotated[
            str, typer.Option("--vt-symbols", "-s", help="逗号分隔的vt_symbol列表，格式为conid.exchange")],
        start: Annotated[Optional[datetime], typer.Option("--start", "-s", help="开始日期")] = None,
        end: Annotated[Optional[datetime], typer.Option("--end", "-e", help="结束日期")] = None,
        interval: Annotated[Interval, typer.Option("--interval", "-i", help="数据周期，DAILY或MINUTE")] = Interval.DAILY
):
    """保存历史数据到数据库"""
    updater = YahooHistoryUpdater()
    symbol_list = [s.strip() for s in vt_symbols.split(",") if s.strip()]
    updater.save_history_data(symbol_list, start, end, interval)
    logger.info("历史数据保存完成。")


@app.command(name="incremental-update")
def incremental_update_command(
        vt_symbols: Annotated[
            str, typer.Option("--vt-symbols", "-s", help="逗号分隔的vt_symbol列表，格式为conid.exchange")],
        end: Annotated[Optional[datetime], typer.Option("--end", "-e", help="结束日期")] = None,
        interval: Annotated[Interval, typer.Option("--interval", "-i", help="数据周期，DAILY或MINUTE")] = Interval.DAILY
):
    """增量更新历史数据"""
    updater = YahooHistoryUpdater()
    symbol_list = [s.strip() for s in vt_symbols.split(",") if s.strip()]
    updater.incremental_update(symbol_list, end, interval)
    logger.info("增量更新完成。")


@app.command(name="incremental-all-update")
def incremental_update_all_symbol(
        end: Annotated[Optional[datetime], typer.Option("--end", "-e", help="结束日期")] = None,
        interval: Annotated[Interval, typer.Option("--interval", "-i", help="数据周期，DAILY或MINUTE")] = Interval.DAILY
):
    """增量更新全部symbol的历史数据"""
    updater = YahooHistoryUpdater()
    updater.incremental_update_all_symbol(interval, end)  # 参数顺序调整
    logger.info("增量更新完成。")


if __name__ == "__main__":
    app()