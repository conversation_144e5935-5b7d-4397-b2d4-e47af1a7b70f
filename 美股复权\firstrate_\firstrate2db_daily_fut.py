# zip文件由多个txt文件组成，每个txt文件的名称为：合约代码_年月代码_day.txt，如AD_F23_day.txt。
# 每个txt文件内容的列名为：TimeStamp,open,high,low,close,volume,open_interest，其中TimeStamp为"US/Eastern"，转换为上海时区。
# 先将txt文件转为csv文件，然后再用vnpy内建的导入csv文件函数将其入库
# 注意vnpy的列名为：symbol，exchange，datetime，interval，volume，turnover，open_interest，open_price，high_price，low_price，close_price
# 没有的字段以空值填充。
import time
from datetime import datetime
from io import BytesIO
from multiprocessing import Process
from zipfile import ZipFile

from glob import glob
import pandas as pd
import requests
import schedule
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.database import get_database
from vnpy.trader.utility import ZoneInfo
from vnpy.trader.object import BarData

min_archive = 'https://firstratedata.com/api/futures_contract?contract_files=archive&timeframe=1min&userID=SSt92rYFq0-jPdT6H7w01g'
day_archive = min_archive.replace("1min", "1day")
min_update = min_archive.replace("archive", "update")
day_update = day_archive.replace("archive", "update")

archive_list = [day_archive, min_archive]
update_list = [day_update, min_update]

tz_name = "US/Eastern"


def import_data_from_zip(file, database, overviews=None, tz_name="US/Eastern"):
    with (ZipFile(file) as z):
        start = datetime(2080, 1, 1, tzinfo=ZoneInfo("US/Eastern"))
        end = datetime(2000, 1, 1, tzinfo=ZoneInfo("US/Eastern"))
        count = 0
        names = ["datetime", "open_price", "high_price", "low_price", "close_price", "volume"]
        # names_d比names多一个open_interest
        names_d = names + ["open_interest"]
        # 如果'_1day'或者'_day'在csv中，则names为names_d，interval为Interval.DAILY
        if z.namelist() and ("_1day" in z.namelist()[0] or "_day" in z.namelist()[0]):
            names = names_d
            interval = Interval.DAILY
        else:
            interval = Interval.MINUTE

        for csv in z.namelist():
            bars = []
            # print(csv)
            with z.open(csv) as f:
                symbol = csv.split(".")[0]
                # 如果overviews不为None，则从overviews中找到symbol对应的overview，然后将overview的end字段的值赋给from_dt
                from_dt = datetime(2020, 1, 1, tzinfo=ZoneInfo("US/Eastern"))
                if overviews:
                    for overview in overviews:
                        if overview.symbol == symbol and overview.interval == interval:
                            from_dt = overview.end
                            from_dt = from_dt.astimezone(ZoneInfo("US/Eastern"))
                            break
                df = pd.read_csv(f, names=names, parse_dates=["datetime"], header=None)
                df["datetime"] = df["datetime"].dt.tz_localize(ZoneInfo(tz_name))
                # 只保留datetime大于等于from_dt的数据
                df = df[df["datetime"] >= from_dt]
                # 再将datetime转为上海时区
                df["datetime"] = df["datetime"].dt.tz_convert(ZoneInfo("Asia/Shanghai"))
                exchange = Exchange.LOCAL
                turnover = 0
                # 如果是分钟数据，则open_interest为0
                if interval == Interval.MINUTE:
                    df["open_interest"] = 0

                # 处理df的None值
                df = df.fillna(0)

                # 生成BarData
                for index, row in df.iterrows():
                    bar = BarData(symbol=symbol, exchange=exchange, datetime=row["datetime"], interval=interval,
                                  volume=row["volume"], open_price=row["open_price"], high_price=row["high_price"],
                                  low_price=row["low_price"], close_price=row["close_price"], turnover=turnover,
                                  open_interest=row["open_interest"], gateway_name="DB", )
                    bars.append(bar)

                    # do some statistics
                    count += 1

            try:
                start = min(min(bars, key=lambda x: x.datetime).datetime, start)
                end = max(max(bars, key=lambda x: x.datetime).datetime, end)
            except:
                pass

            # insert into database
            if bars:
                database.save_bar_data(bars)
    return start, end, count


def parse_download_zip(url_zip):
    res = requests.get(url_zip)
    if res.status_code == 200:
        fio = BytesIO(res.content)
        return fio
    else:
        return None


# archive数据入库
def archive_job(db, links):
    print(f'{datetime.now()}[archive_job]links: {links}')
    for archive_url in links:
        # 如果archive_url是网址，则从网址下载zip文件，否则从本地读取zip文件
        if archive_url.startswith("http"):
            fio = parse_download_zip(archive_url)
        else:
            fio = archive_url
        if fio:
            start, end, count = import_data_from_zip(fio, db)
            # print(f"[archive_job]start: {start}, end: {end}, count: {count}")
            print(f"{datetime.now()}[archive_job]start: {start}, end: {end}, count: {count}, url: {archive_url}")
        else:
            print(f"{datetime.now()}[archive_job]parse_download_zip failed, url: {archive_url}")

# 每天从网站（https://firstratedata.com/api/futures_contract?contract_files=update&timeframe=1day&userID=SSt92rYFq0-jPdT6H7w01g）下载zip文件
# fio = r'individual_contracts_update_1day_yhg78fjm92.zip'

# 然后将zip文件中的数据导入到数据库中。
# 网站数据集每天更新（更新文件在美国东部时间晚上 11.45 之前提供）。但本机是在上海，所以每天需要在上海时间中午13点运行一次上面的程序。
def update_job(db, links):
    print(f'{datetime.now()}[update_job]links: {links}')
    for update_url in links:
        if update_url.startswith("http"):
            fio = parse_download_zip(update_url)
        else:
            fio = update_url
        if fio:
            # 首先从数据库的dbbaroverviem中查询出交易所为LOCAL的数据的end字段的最大值，记为from_dt，然后从zip文件中读取数据，将数据的datetime字段的值大于等于from_dt的数据入库。
            overviews = db.get_bar_overview()
            start, end, count = import_data_from_zip(fio, db, overviews)
            print(f"{datetime.now()}[update_job]start: {start}, end: {end}, count: {count}, url: {update_url}")
        else:
            print(f"{datetime.now()}[update_job]parse_download_zip failed, url: {update_url}")

def get_zips():
    # 使用glob，从当前目录下获取zip文件，archive_zip的文件名中含有archive，update_zip的文件名中含有update
    archive_zips = glob("*archive*.zip")
    update_zips = glob("*update*.zip")
    return archive_zips, update_zips

if __name__ == '__main__':
    dbs = get_database()

    archive_zips, update_zips = get_zips()
    print(f"archive_zips: {archive_zips}, update_zips: {update_zips}")

    # p1 = Process(target=archive_job, args=(dbs, archive_zips))
    # p2 = Process(target=update_job, args=(dbs, update_zips))
    # p1.start()
    # p2.start()
    # p1.join()
    # p2.join()

    # 不用多进程
    update_job(dbs, update_zips)
    # archive_job(dbs, archive_zips)

    schedule.every().day.at("13:00").do(update_job, dbs, update_list)
    while True:
        schedule.run_pending()
        time.sleep(1)
