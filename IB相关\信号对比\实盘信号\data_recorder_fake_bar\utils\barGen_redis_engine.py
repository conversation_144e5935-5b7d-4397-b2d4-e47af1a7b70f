import traceback
from copy import copy
from datetime import datetime, time, timedelta
from queue import Queue
from threading import Thread
from typing import Dict, Optional, Union

import redis
from ibapi.contract import ContractDetails
from vnpy.event import Event, EventEngine
from vnpy.trader.constant import (Exchange)
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.event import EVENT_TICK, EVENT_CONTRACT, EVENT_TIMER
from vnpy.trader.object import BarData, TickData, Interval, SubscribeRequest, ContractData
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json, virtual, ZoneInfo, extract_vt_symbol
from tzlocal import get_localzone_name
from time import sleep
import sys
# from vnpy.usertools.db_status_manager import Status
from .db_status_manager import Status
from peewee import fn
APP_NAME = "BarGenEngine"
from .event import EVENT_BAR, EVENT_BAR_MINI, EVENT_BAR_RECORD, EVENT_BAR_CLOSE
localzone_name = get_localzone_name()
LOCAL_TZ = ZoneInfo(localzone_name)
EASTERN_TZ = ZoneInfo('US/Eastern')


class BarGenEngine(BaseEngine):
    """Base bar generation engine that handles tick data and generates bars"""

    setting_filename = "barGen_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        super().__init__(main_engine, event_engine, APP_NAME)

        # 添加Redis连接初始化
        self.redis_host = SETTINGS.get("redis.host", "localhost")
        self.redis_port = SETTINGS.get("redis.port", 6379)
        self.redis_db = SETTINGS.get("redis.db", 0)
        self.redis_password = SETTINGS.get("redis.password", None)
        self.init_redis()  # 初始化Redis连接

        # Initialize data structures
        self.queue = Queue()
        self.thread = Thread(target=self.run)
        self.active = False

        self.bar_recordings = set()
        # self.flag = "" # main or weekly

        self.bars: Dict[str, BarData] = {}
        self.last_bars: Dict[str, BarData] = {}
        self.last_ticks: Dict[str, TickData] = {}
        self.last_dts: Dict[str, datetime] = {}

        # Setup and start engine
        self.gateway = self.main_engine.get_gateway("quote")
        if self.gateway is None:
            self.gateway = self.main_engine.get_gateway("IB")
        # self.load_setting()
        self.register_event()
        # self.start()
        self.put_event()

    def init_redis(self):
        """初始化Redis连接"""
        try:
            self.r = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                password=self.redis_password,
                socket_timeout=5,
                decode_responses=True
            )
            # 测试连接
            if self.r.ping():
                self.write_log(f"Redis连接成功: {self.redis_host}:{self.redis_port}")
            else:
                self.write_log("Redis连接失败")
        except Exception as e:
            self.write_log(f"Redis连接异常: {str(e)}")
            self.r = None

    def subscribe_all(self):
        """"""
        self.write_log(f"subscribe_all bar_recordings : {self.bar_recordings}")
        while not self.gateway.api.data_ready:
            self.write_log("wait for IB data ready")
            sleep(1)
        self.write_log("IB data ready")
        for elem in self.bar_recordings:
            self.write_log(f"subscribe elem {elem}")
            self.subscribe_recording(elem)

    def load_last_bars(self):
        """加载上一次的bar数据"""
        if not self.r:
            self.write_log("Redis未初始化，跳过")
            return {}
        last_bars = {}
        for vt_symbol in self.bar_recordings:
            bar = self.get_redis(vt_symbol)
            if bar:
                # 优先用tradinghours_info的timeZoneId（如果有），否则用bar自己的时区，否则用本地LOCAL_TZ
                tradinghours_info = getattr(self.gateway, 'tradinghours_info', {})
                tzinfo = None
                if vt_symbol in tradinghours_info:
                    time_zone_id = tradinghours_info[vt_symbol].get('timeZoneId')
                    if time_zone_id:
                        try:
                            tzinfo = ZoneInfo(time_zone_id)
                        except Exception:
                            self.write_log(f"ZoneInfo({time_zone_id})失败，使用默认LOCAL_TZ")
                if not tzinfo:
                    tzinfo = getattr(bar.datetime, 'tzinfo', None) or LOCAL_TZ
                bar_dt = bar.datetime
                bar_date = bar_dt.astimezone(tzinfo).date() if bar_dt else None
                now_date = datetime.now(tzinfo).date()
                if bar_date == now_date:
                    last_bars[vt_symbol] = bar
                else:
                    self.write_log(f"跳过加载旧的K线数据: {bar.vt_symbol}, bar日期: {bar_date}, 当前日期: {now_date}")
        return last_bars

    def save_setting(self):
        """"""
        pass

    def run(self):
        """"""
        while self.active:
            try:
                if datetime.now().minute % 30 == 0 and datetime.now().second == 0:
                    # self.load_setting()
                    pass
                sleep(1) # 避免CPU忙等
            except Exception:
                msg = f"触发异常已停止\n{traceback.format_exc()}"
                self.write_log(msg)

    def close(self):
        """"""
        self.active = False
        if self.thread.is_alive():
            self.thread.join()

    def start(self):
        """"""
        self.update_subscriptions()
        self.last_bars = {} # self.load_last_bars()
        self.write_log(f"success to load last_bars: {self.last_bars}")
        self.subscribe_all()
        self.engine_start_time = datetime.now(LOCAL_TZ)
        self.active = True
        self.thread.start()

    def subscribe_recording(self, vt_symbol: str):
        """订阅K线记录"""
        try:
            contract = self.main_engine.get_contract(vt_symbol)
            if not contract:
                self.write_log(f"找不到合约：{vt_symbol}")
                return
            self.write_log(f"prepare to send subscribe req：{vt_symbol}")
            self.subscribe_con(contract)
        except Exception:
            msg = f"barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"添加K线记录成功：{vt_symbol}")

    def register_event(self):
        """注册事件处理函数"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)
        self.event_engine.register(EVENT_BAR_CLOSE, self.force_close_bar)

    def update_redis(self, bar: BarData):
        """更新Redis数据"""
        if not self.r:
            self.write_log("Redis未初始化，跳过更新")
            return
        self.r.hset(bar.vt_symbol, mapping={
            "symbol": bar.symbol,
            "exchange": bar.exchange.value,
            "datetime": bar.datetime.isoformat(),
            "interval": bar.interval.value,
            "volume": bar.volume,
            "turnover": bar.turnover,
            "open_interest": bar.open_interest,
            "open_price": bar.open_price,
            "high_price": bar.high_price,
            "low_price": bar.low_price,
            "close_price": bar.close_price,
            "gateway_name": bar.gateway_name
        })

    def get_redis(self, vt_symbol: str):
        """从Redis获取数据"""
        data = self.r.hgetall(vt_symbol)
        if data:
            try:
                bar = BarData(
                    symbol=data['symbol'],
                    exchange=Exchange(data['exchange']),
                    datetime=datetime.fromisoformat(data['datetime']),
                    interval=Interval(data['interval']),
                    volume=float(data['volume']),
                    turnover=float(data['turnover']),
                    open_interest=float(data['open_interest']),
                    open_price=float(data['open_price']),
                    high_price=float(data['high_price']),
                    low_price=float(data['low_price']),
                    close_price=float(data['close_price']),
                    gateway_name=data['gateway_name']
                )
                return bar
            except Exception:
                self.write_log(f"error {vt_symbol}: {traceback.format_exc()}")
        return None

    def update_tick(self, tick: TickData):
        """基于tick合成分钟线"""
        # self.update_redis(tick) # 已改为缓存bar

        # reach time, send all bars
        last_dt = self.last_dts.get(tick.vt_symbol)
        if not last_dt or last_dt.minute != tick.datetime.minute:
            bar: Optional[BarData] = self.bars.get(tick.vt_symbol)
            if bar:
                bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                self.process_bar(bar)
                self.bars[tick.vt_symbol] = None

        # 创建或更新当前Bar
        bar: Optional[BarData] = self.bars.get(tick.vt_symbol)
        if not bar:
            bar = BarData(
                symbol=tick.symbol,
                exchange=tick.exchange,
                interval=Interval.MINUTE,
                datetime=tick.datetime.replace(second=0, microsecond=0),
                gateway_name=tick.gateway_name,
                open_price=tick.last_price,
                high_price=tick.last_price,
                low_price=tick.last_price,
                close_price=tick.last_price,
                open_interest=tick.open_interest
            )
            self.bars[bar.vt_symbol] = bar
        else:
            bar.high_price = max(bar.high_price, tick.last_price)
            bar.low_price = min(bar.low_price, tick.last_price)
            bar.close_price = tick.last_price
            bar.open_interest = tick.open_interest

        # 更新成交量（需要与前一个tick比较）
        last_tick = self.last_ticks.get(tick.vt_symbol)
        if last_tick:
            bar.volume += max(tick.volume - last_tick.volume, 0)
            bar.turnover += max(tick.turnover - last_tick.turnover, 0)

        self.last_ticks[tick.vt_symbol] = tick
        self.last_dts[tick.vt_symbol] = tick.datetime

    @virtual
    def process_timer_event(self, event: Event):
        """定时器事件处理"""
        pass

    def process_tick_event(self, event: Event):
        """TICK事件处理"""
        tick = event.data
        self.update_tick(tick)

    def process_contract_event(self, event: Event):
        """"""
        contract = event.data
        vt_symbol = contract.vt_symbol

        if vt_symbol in self.bar_recordings:
            self.subscribe_con(contract)

    def write_log(self, msg: str):
        """日志输出"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        self.main_engine.write_log(formatted_msg)

    def put_event(self):
        """"""
        # bar_symbols = list(self.bar_recordings)
        # bar_symbols.sort()
        pass

    # 处理bar,供其它应用处理
    def record_bar(self, bar: BarData):
        """保存bar数据"""
        # 检查是否在交易时间内，如果不在则不记录
        if self.gateway.data_invalid(bar):
            return

        try:
            time_str = datetime.now().strftime("%Y-%m-%d-%H%M%S")
            self.last_bars[bar.vt_symbol] = bar
            self.update_redis(bar)
            self.write_log(
                f" ======1======record bar memory: {time_str}: {bar.vt_symbol} {bar.datetime} {bar.interval}"
                f"o:{bar.open_price} h:{bar.high_price} l:{bar.low_price} c:{bar.close_price} "
                f"v:{bar.volume} oi:{bar.open_interest}")
            event = Event(EVENT_BAR, bar)
            event2 = Event(EVENT_BAR_RECORD, copy(bar))
            self.event_engine.put(event)
            self.event_engine.put(event2)
        except Exception:
            msg = f"触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)

    def process_bar(self, bar: BarData) -> None:
        """处理bar,包括记录和补充缺失"""
        # 检查是否需要补充缺失的bar
        last_bar = self.last_bars.get(bar.vt_symbol)
        
        if last_bar:
            # 有last_bar的情况：补充中间缺失的bar
            minutes_diff = int((bar.datetime - last_bar.datetime).total_seconds() / 60)
            if minutes_diff > 1:
                # 生成补充用的bar
                last_bar.volume = 0
                last_bar.turnover = 0
                last_bar.open_price = last_bar.close_price
                last_bar.high_price = last_bar.close_price
                last_bar.low_price = last_bar.close_price

                # 补充缺失的bars
                for _ in range(minutes_diff - 1):
                    last_bar.datetime = last_bar.datetime + timedelta(minutes=1)
                    self.write_log(f"补充缺失的bar: {last_bar.vt_symbol}, 时间: {last_bar.datetime}, 价格: {last_bar.close_price}")
                    self.record_bar(copy(last_bar))
        else:
            # 没有last_bar的情况：从交易时间段起始时间开始补充
            self._fill_bars_from_trading_start(bar)

        # 记录当前bar
        self.record_bar(bar)

    def _fill_bars_from_trading_start(self, bar: BarData) -> None:
        """从交易时间段起始时间开始补充缺失的bar"""
        try:
            # 获取交易时间信息
            tradinghours_info = getattr(self.gateway, 'tradinghours_info', {})
            trading_info = tradinghours_info.get(bar.vt_symbol)
            
            if not trading_info:
                self.write_log(f"未找到{bar.vt_symbol}的交易时间信息，跳过补bar")
                return
                
            trading_periods = trading_info.get('tradingPeriod', [])
            if not trading_periods:
                self.write_log(f"{bar.vt_symbol}没有交易时间段信息，跳过补bar")
                return
            
            # 找到当前bar所属的交易时间段
            current_period_start = None
            bar_dt = bar.datetime
                
            for start_time, end_time in trading_periods:
                if start_time <= bar_dt < end_time:
                    current_period_start = start_time
                    break
                    
            if not current_period_start:
                self.write_log(f"{bar.vt_symbol}的bar时间{bar_dt}不在任何交易时间段内，跳过补bar")
                return
                
            # 检查是否是盘中启动，如果是则不补充bar
            # 其他位置的代码会从数据库读取开盘到启动之间的bar
            engine_start_minute = self.engine_start_time.astimezone(bar_dt.tzinfo).replace(second=0, microsecond=0)
            period_start_minute = current_period_start.astimezone(bar_dt.tzinfo).replace(second=0, microsecond=0)
            
            # 如果engine启动时间晚于交易开始时间，说明是盘中启动，不补充
            if engine_start_minute > period_start_minute:
                self.write_log(f"{bar.vt_symbol}检测到盘中启动，跳过补充开盘缺失bar")
                return
            
            # 计算需要补充的分钟数（从交易开始时间到当前bar的前一分钟）
            bar_minute = bar_dt.replace(second=0, microsecond=0)
            minutes_diff = int((bar_minute - period_start_minute).total_seconds() / 60)
            
            if minutes_diff > 0:
                self.write_log(f"开始补充{bar.vt_symbol}从交易开始时间的缺失bar，需要补充{minutes_diff}个bar")
                
                # 直接copy原bar作为模板
                fill_bar = copy(bar)
                # 修改为补充bar的属性
                fill_bar.high_price = bar.open_price
                fill_bar.low_price = bar.open_price
                fill_bar.close_price = bar.open_price
                fill_bar.volume = 0  # 补充的bar成交量为0
                fill_bar.turnover = 0
                
                # 补充缺失的bars：从period_start_minute开始，逐个补充到bar的前一分钟
                for i in range(minutes_diff):
                    fill_bar.datetime = period_start_minute + timedelta(minutes=i)
                    self.write_log(f"补充缺失的bar: {fill_bar.vt_symbol}, 时间: {fill_bar.datetime}, 价格: {fill_bar.close_price}")
                    self.record_bar(copy(fill_bar))
                    
        except Exception as e:
            self.write_log(f"补充{bar.vt_symbol}开盘缺失bar时出错: {str(e)}")

    def update_subscriptions(self):
        """从status表获取vt_symbol，更新bar_recordings集合"""
        statuses = Status.select(fn.SUBSTRING_INDEX(Status.content, '_', 1).distinct().alias('vt_symbol'))
        for status in statuses:
            if status.vt_symbol not in self.bar_recordings:
                self.bar_recordings.add(status.vt_symbol)

    def subscribe_con(self, contract: ContractData):
        """订阅合约"""
        req = SubscribeRequest(symbol=contract.symbol, exchange=contract.exchange)
        self.write_log(f"send subscribe req {contract.symbol}")
        self.main_engine.subscribe(req, contract.gateway_name)

    def force_close_bar(self, event: Event) -> None:
        """强制收盘当前bar"""
        vt_symbol = event.data
        bar: Optional[BarData] = self.bars.get(vt_symbol)

        if bar:
            bar.datetime = bar.datetime.replace(second=0, microsecond=0)
            self.process_bar(bar)
            self.bars[vt_symbol] = None
            self.write_log(f"Force closed bar for {vt_symbol} at {bar.datetime}")

class BarGenEngineIb(BarGenEngine):
    """IB specific bar generation engine that handles trading hours"""

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        self.last_5s_bars: Dict[str, BarData] = {}  # 存储上一个5s bar
        self.last_5s_dts: Dict[str, datetime] = {}  # 存储上一个5s bar的时间
        super().__init__(main_engine, event_engine)

    def subscribe_recording(self, vt_symbol: str):
        """Subscribe to market data for given symbol"""
        try:
            symbol, exchange = extract_vt_symbol(vt_symbol)
            self.write_log(f"prepare to send subscribe req：{vt_symbol}")
            req = SubscribeRequest(symbol=symbol, exchange=exchange)
            self.gateway.subscribe(req)
        except Exception:
            msg = f"barGen error 触发异常已停止\n{traceback.format_exc()}"
            self.write_log(msg)
            return
        self.write_log(f"添加K线记录成功：{vt_symbol}")

    def start(self):
        """"""
        super().start()
        self.setup_minute_bar_mode()

    def setup_minute_bar_mode(self):
        """根据gateway配置决定使用哪种方式合成分钟线，支持动态切换"""
        # 先注销所有相关事件，避免重复注册
        self.event_engine.unregister(EVENT_BAR_MINI, self.process_bar_mini_event)
        self.event_engine.unregister(EVENT_TICK, self.process_tick_event)
        # 根据当前配置注册
        if self.gateway.use_5s_bar:
            self.event_engine.register(EVENT_BAR_MINI, self.process_bar_mini_event)
        else:
            self.event_engine.register(EVENT_TICK, self.process_tick_event)

    def process_bar_mini_event(self, event: Event):
        """处理5s bar事件"""
        bar_5s = event.data
        self.update_bar_mini(bar_5s)

    def update_bar_mini(self, bar_5s: BarData):
        """基于5s bar合成分钟线"""
        # 检查是否是新的一分钟，如果是则先处理上一分钟的bar
        last_dt = self.last_5s_dts.get(bar_5s.vt_symbol)
        if not last_dt or last_dt.minute != bar_5s.datetime.minute:
            bar: Optional[BarData] = self.bars.get(bar_5s.vt_symbol)
            if bar:
                bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                self.process_bar(bar)
                self.bars[bar_5s.vt_symbol] = None

        # 更新或创建新的分钟bar
        bar: Optional[BarData] = self.bars.get(bar_5s.vt_symbol)
        if not bar:
            # 创建新的分钟bar
            bar = BarData(
                symbol=bar_5s.symbol,
                exchange=bar_5s.exchange,
                interval=Interval.MINUTE,
                datetime=bar_5s.datetime.replace(second=0),
                gateway_name=bar_5s.gateway_name,
                open_price=bar_5s.open_price,
                high_price=bar_5s.high_price,
                low_price=bar_5s.low_price,
                close_price=bar_5s.close_price,
                volume=bar_5s.volume,
                turnover=bar_5s.turnover,
                open_interest=bar_5s.open_interest
            )
            self.bars[bar.vt_symbol] = bar
        else:
            # 更新现有分钟bar的数据
            if bar.volume and bar_5s.volume:
                # 或者bar的vol不为0，bar_5s的vol不为0。正常更新
                bar.high_price = max(bar.high_price, bar_5s.high_price)
                bar.low_price = min(bar.low_price, bar_5s.low_price)
                bar.close_price = bar_5s.close_price
                bar.volume += bar_5s.volume
                bar.turnover += bar_5s.turnover
                bar.open_interest = bar_5s.open_interest
            elif not bar.volume:
                # 如果bar的vol为0，覆盖bar的ohlc turnover oi
                bar.open_price = bar_5s.open_price
                bar.high_price = bar_5s.high_price
                bar.low_price = bar_5s.low_price
                bar.close_price = bar_5s.close_price
                bar.volume = bar_5s.volume
                bar.turnover = bar_5s.turnover
                bar.open_interest = bar_5s.open_interest

        # 当收到55秒的bar_5s时，记录并清空当前分钟的bar
        if bar_5s.datetime.second == 55:            
            bar: Optional[BarData] = self.bars.get(bar_5s.vt_symbol)
            if bar:
                bar.datetime = bar.datetime.replace(second=0, microsecond=0)
                self.process_bar(bar)
                self.bars[bar_5s.vt_symbol] = None

        # 更新last_5s_bars和last_5s_dts
        self.last_5s_bars[bar_5s.vt_symbol] = bar_5s
        self.last_5s_dts[bar_5s.vt_symbol] = bar_5s.datetime

    def update_tick(self, tick: TickData):
        """重写update_tick方法，只更新last_ticks，不再合成分钟bar"""
        if self.gateway.use_5s_bar:
            # self.update_redis(tick) # 已改为缓存bar
            self.last_ticks[tick.vt_symbol] = tick
            self.last_dts[tick.vt_symbol] = tick.datetime
        else:
            # 否则调用父类方法处理tick
            super().update_tick(tick)