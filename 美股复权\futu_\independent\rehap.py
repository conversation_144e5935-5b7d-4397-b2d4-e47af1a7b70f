from futu import *
import pandas as pd

# 设置 pandas 显示选项，不限制列数和行数
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11211)

ret, data = quote_ctx.get_rehab("US.EDU")
if ret == RET_OK:
    print(data)
    # print(data['ex_div_date'][0])    # 取第一条的除权除息日
    # print(data['ex_div_date'].values.tolist())   # 转为 list
else:
    print('error:', data)
quote_ctx.close() # 结束后记得关闭当条连接，防止连接条数用尽
