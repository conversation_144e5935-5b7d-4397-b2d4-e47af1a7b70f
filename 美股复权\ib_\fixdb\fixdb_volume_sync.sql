-- 将 vnpy_stk_us_frt_d_2206_250814.dbbardata 的 volume 字段覆盖到 vnpy_stk_us_ib_d_2206_250814__.dbbardata
-- 只更新目标表中已存在的记录，不插入新记录

UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata AS target
INNER JOIN vnpy_stk_us_frt_d_2206_250814.dbbardata AS source
ON target.symbol = source.symbol 
   AND target.exchange = source.exchange 
   AND target.datetime = source.datetime 
   AND target.`interval` = source.`interval`
SET target.volume = source.volume;

-- 查询更新影响的记录数
SELECT ROW_COUNT() AS updated_rows;