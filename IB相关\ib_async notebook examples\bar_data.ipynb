{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2025-08-27T14:37:41.659Z", "start_time": "2025-08-27T14:37:40.777879Z"}}, "cell_type": "code", "source": ["from ib_async import *\n", "util.startLoop()\n", "\n", "ib = IB()\n", "# ib.connect('47.242.117.184', 4012, clientId=465)\n", "ib.connect('192.168.1.54', 4011, clientId=465)"], "outputs": [{"data": {"text/plain": ["<IB connected to 192.168.1.54:4011 clientId=465>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"cell_type": "markdown", "metadata": {}, "source": ["## Historical data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To get the earliest date of available bar data the \"head timestamp\" can be requested:"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-06-17T05:52:42.920567Z", "start_time": "2025-06-17T05:52:42.442245Z"}}, "source": ["contract = Stock('IBM', 'SMART', 'USD')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True) # datetime.datetime(1980, 12, 12, 14, 30)"], "outputs": [{"data": {"text/plain": ["datetime.datetime(1980, 3, 17, 14, 30)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["contract = Stock('AAPL', 'SMART', 'USD')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True) # datetime.datetime(1980, 12, 12, 14, 30)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["#         \"HHI.HK-20240429-HKD-FUT.HKFE\",\n", "contract = Future('HHI.HK', '202404', 'HKFE')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "ib.reqContractDetails(contract)"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["ib.qualifyContracts(contract)\n", "contract"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To request hourly data of the last 60 trading days:"]}, {"metadata": {"ExecuteTime": {"end_time": "2025-08-27T14:40:45.154777Z", "start_time": "2025-08-27T14:40:34.538897Z"}}, "cell_type": "code", "source": ["from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "contract = Stock('GIBO', 'SMART', 'USD')\n", "ib.qualifyContracts(contract)\n", "US_Eastern = ZoneInfo('America/New_York')\n", "bars = ib.reqHistoricalData(\n", "        contract,\n", "        # endDateTime='20250614 04:00:00',\n", "        endDateTime=datetime(2025, 8, 14, 16, 0, tzinfo=US_Eastern),\n", "        durationStr='20 D',\n", "        barSizeSetting='1 day',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)\n", "\n", "df = util.df(bars)\n", "df"], "outputs": [{"data": {"text/plain": ["          date   open   high    low  close       volume  average  barCount\n", "0   2025-07-16   8.00   8.32   7.64   7.96   187424.600    7.984      7709\n", "1   2025-07-17   7.98   7.98   7.00   7.50   250639.165    7.494      8309\n", "2   2025-07-18   6.78   7.44   6.72   7.04   229227.705    6.956      6315\n", "3   2025-07-21   7.18   7.18   6.20   6.46   218257.870    6.528      9497\n", "4   2025-07-22   7.00   8.72   6.00   8.66   866052.430    7.760     21686\n", "5   2025-07-23   9.42  10.30   8.14   8.48   844494.625    9.326     27762\n", "6   2025-07-24   8.38  10.16   8.00   9.20   831692.820    9.198     18434\n", "7   2025-07-25   9.72  15.26   9.22  13.96  2935228.890   12.028     72907\n", "8   2025-07-28  17.80  26.40  12.66  13.44  5202580.360   20.198    217335\n", "9   2025-07-29  16.38  20.88  13.60  19.00  2800417.270   17.688    107955\n", "10  2025-07-30  16.34  18.20  13.40  14.90   695897.460   15.588     29299\n", "11  2025-07-31  14.00  14.18  11.30  12.04   431855.615   12.478     15885\n", "12  2025-08-01  10.60  13.86  10.00  11.10   560297.325   11.850     18657\n", "13  2025-08-04  11.52  12.20  10.80  10.96   192674.105   11.484      7618\n", "14  2025-08-05  10.96  13.42  10.00  10.40   576863.480   11.708     21700\n", "15  2025-08-06  10.32  10.32   7.22   8.00   617227.085    8.324     22334\n", "16  2025-08-07   8.00   8.48   7.60   7.82   231912.715    7.924      7088\n", "17  2025-08-08   7.64   7.78   7.30   7.46   161369.525    7.536      5069\n", "18  2025-08-11   8.56   9.80   7.72   8.48   586140.165    8.812     21401\n", "19  2025-08-12   8.10   8.30   7.60   7.92   247079.260    7.936      8641\n", "20  2025-08-13   8.02   8.76   7.70   8.00   276710.410    8.208      7044\n", "21  2025-08-14   8.00   8.14   7.30   7.72   194486.205    7.750      6273"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-07-16</td>\n", "      <td>8.00</td>\n", "      <td>8.32</td>\n", "      <td>7.64</td>\n", "      <td>7.96</td>\n", "      <td>187424.600</td>\n", "      <td>7.984</td>\n", "      <td>7709</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-07-17</td>\n", "      <td>7.98</td>\n", "      <td>7.98</td>\n", "      <td>7.00</td>\n", "      <td>7.50</td>\n", "      <td>250639.165</td>\n", "      <td>7.494</td>\n", "      <td>8309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-07-18</td>\n", "      <td>6.78</td>\n", "      <td>7.44</td>\n", "      <td>6.72</td>\n", "      <td>7.04</td>\n", "      <td>229227.705</td>\n", "      <td>6.956</td>\n", "      <td>6315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-07-21</td>\n", "      <td>7.18</td>\n", "      <td>7.18</td>\n", "      <td>6.20</td>\n", "      <td>6.46</td>\n", "      <td>218257.870</td>\n", "      <td>6.528</td>\n", "      <td>9497</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-07-22</td>\n", "      <td>7.00</td>\n", "      <td>8.72</td>\n", "      <td>6.00</td>\n", "      <td>8.66</td>\n", "      <td>866052.430</td>\n", "      <td>7.760</td>\n", "      <td>21686</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2025-07-23</td>\n", "      <td>9.42</td>\n", "      <td>10.30</td>\n", "      <td>8.14</td>\n", "      <td>8.48</td>\n", "      <td>844494.625</td>\n", "      <td>9.326</td>\n", "      <td>27762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2025-07-24</td>\n", "      <td>8.38</td>\n", "      <td>10.16</td>\n", "      <td>8.00</td>\n", "      <td>9.20</td>\n", "      <td>831692.820</td>\n", "      <td>9.198</td>\n", "      <td>18434</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2025-07-25</td>\n", "      <td>9.72</td>\n", "      <td>15.26</td>\n", "      <td>9.22</td>\n", "      <td>13.96</td>\n", "      <td>2935228.890</td>\n", "      <td>12.028</td>\n", "      <td>72907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2025-07-28</td>\n", "      <td>17.80</td>\n", "      <td>26.40</td>\n", "      <td>12.66</td>\n", "      <td>13.44</td>\n", "      <td>5202580.360</td>\n", "      <td>20.198</td>\n", "      <td>217335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2025-07-29</td>\n", "      <td>16.38</td>\n", "      <td>20.88</td>\n", "      <td>13.60</td>\n", "      <td>19.00</td>\n", "      <td>2800417.270</td>\n", "      <td>17.688</td>\n", "      <td>107955</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2025-07-30</td>\n", "      <td>16.34</td>\n", "      <td>18.20</td>\n", "      <td>13.40</td>\n", "      <td>14.90</td>\n", "      <td>695897.460</td>\n", "      <td>15.588</td>\n", "      <td>29299</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2025-07-31</td>\n", "      <td>14.00</td>\n", "      <td>14.18</td>\n", "      <td>11.30</td>\n", "      <td>12.04</td>\n", "      <td>431855.615</td>\n", "      <td>12.478</td>\n", "      <td>15885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2025-08-01</td>\n", "      <td>10.60</td>\n", "      <td>13.86</td>\n", "      <td>10.00</td>\n", "      <td>11.10</td>\n", "      <td>560297.325</td>\n", "      <td>11.850</td>\n", "      <td>18657</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2025-08-04</td>\n", "      <td>11.52</td>\n", "      <td>12.20</td>\n", "      <td>10.80</td>\n", "      <td>10.96</td>\n", "      <td>192674.105</td>\n", "      <td>11.484</td>\n", "      <td>7618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2025-08-05</td>\n", "      <td>10.96</td>\n", "      <td>13.42</td>\n", "      <td>10.00</td>\n", "      <td>10.40</td>\n", "      <td>576863.480</td>\n", "      <td>11.708</td>\n", "      <td>21700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2025-08-06</td>\n", "      <td>10.32</td>\n", "      <td>10.32</td>\n", "      <td>7.22</td>\n", "      <td>8.00</td>\n", "      <td>617227.085</td>\n", "      <td>8.324</td>\n", "      <td>22334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2025-08-07</td>\n", "      <td>8.00</td>\n", "      <td>8.48</td>\n", "      <td>7.60</td>\n", "      <td>7.82</td>\n", "      <td>231912.715</td>\n", "      <td>7.924</td>\n", "      <td>7088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2025-08-08</td>\n", "      <td>7.64</td>\n", "      <td>7.78</td>\n", "      <td>7.30</td>\n", "      <td>7.46</td>\n", "      <td>161369.525</td>\n", "      <td>7.536</td>\n", "      <td>5069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2025-08-11</td>\n", "      <td>8.56</td>\n", "      <td>9.80</td>\n", "      <td>7.72</td>\n", "      <td>8.48</td>\n", "      <td>586140.165</td>\n", "      <td>8.812</td>\n", "      <td>21401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2025-08-12</td>\n", "      <td>8.10</td>\n", "      <td>8.30</td>\n", "      <td>7.60</td>\n", "      <td>7.92</td>\n", "      <td>247079.260</td>\n", "      <td>7.936</td>\n", "      <td>8641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2025-08-13</td>\n", "      <td>8.02</td>\n", "      <td>8.76</td>\n", "      <td>7.70</td>\n", "      <td>8.00</td>\n", "      <td>276710.410</td>\n", "      <td>8.208</td>\n", "      <td>7044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2025-08-14</td>\n", "      <td>8.00</td>\n", "      <td>8.14</td>\n", "      <td>7.30</td>\n", "      <td>7.72</td>\n", "      <td>194486.205</td>\n", "      <td>7.750</td>\n", "      <td>6273</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-08-27T14:41:36.573330Z", "start_time": "2025-08-27T14:41:18.838773Z"}}, "cell_type": "code", "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        # endDateTime='20250614 04:00:00',\n", "        endDateTime=datetime(2025, 8, 14, 16, 0, tzinfo=US_Eastern),\n", "        durationStr='1 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)\n", "\n", "df = util.df(bars)\n", "df"], "outputs": [{"data": {"text/plain": ["                         date  open  high   low  close    volume  average  \\\n", "0   2025-08-14 09:30:00-04:00  8.00  8.00  7.72   7.72  2250.260    7.868   \n", "1   2025-08-14 09:31:00-04:00  7.80  7.80  7.78   7.78   282.215    7.792   \n", "2   2025-08-14 09:32:00-04:00  7.78  7.78  7.70   7.70  2179.970    7.724   \n", "3   2025-08-14 09:33:00-04:00  7.70  7.80  7.70   7.78   266.645    7.760   \n", "4   2025-08-14 09:34:00-04:00  7.76  7.78  7.64   7.68  2201.325    7.694   \n", "..                        ...   ...   ...   ...    ...       ...      ...   \n", "385 2025-08-14 15:55:00-04:00  7.64  7.66  7.62   7.66   425.875    7.646   \n", "386 2025-08-14 15:56:00-04:00  7.66  7.66  7.60   7.60   607.545    7.606   \n", "387 2025-08-14 15:57:00-04:00  7.62  7.64  7.60   7.64   115.355    7.604   \n", "388 2025-08-14 15:58:00-04:00  7.62  7.70  7.62   7.70   505.305    7.640   \n", "389 2025-08-14 15:59:00-04:00  7.72  7.80  7.70   7.78  1050.085    7.776   \n", "\n", "     barCount  \n", "0          28  \n", "1          12  \n", "2          42  \n", "3          23  \n", "4         109  \n", "..        ...  \n", "385        23  \n", "386        21  \n", "387         9  \n", "388        23  \n", "389        59  \n", "\n", "[390 rows x 8 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-08-14 09:30:00-04:00</td>\n", "      <td>8.00</td>\n", "      <td>8.00</td>\n", "      <td>7.72</td>\n", "      <td>7.72</td>\n", "      <td>2250.260</td>\n", "      <td>7.868</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-08-14 09:31:00-04:00</td>\n", "      <td>7.80</td>\n", "      <td>7.80</td>\n", "      <td>7.78</td>\n", "      <td>7.78</td>\n", "      <td>282.215</td>\n", "      <td>7.792</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-08-14 09:32:00-04:00</td>\n", "      <td>7.78</td>\n", "      <td>7.78</td>\n", "      <td>7.70</td>\n", "      <td>7.70</td>\n", "      <td>2179.970</td>\n", "      <td>7.724</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-08-14 09:33:00-04:00</td>\n", "      <td>7.70</td>\n", "      <td>7.80</td>\n", "      <td>7.70</td>\n", "      <td>7.78</td>\n", "      <td>266.645</td>\n", "      <td>7.760</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-08-14 09:34:00-04:00</td>\n", "      <td>7.76</td>\n", "      <td>7.78</td>\n", "      <td>7.64</td>\n", "      <td>7.68</td>\n", "      <td>2201.325</td>\n", "      <td>7.694</td>\n", "      <td>109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>385</th>\n", "      <td>2025-08-14 15:55:00-04:00</td>\n", "      <td>7.64</td>\n", "      <td>7.66</td>\n", "      <td>7.62</td>\n", "      <td>7.66</td>\n", "      <td>425.875</td>\n", "      <td>7.646</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>386</th>\n", "      <td>2025-08-14 15:56:00-04:00</td>\n", "      <td>7.66</td>\n", "      <td>7.66</td>\n", "      <td>7.60</td>\n", "      <td>7.60</td>\n", "      <td>607.545</td>\n", "      <td>7.606</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>387</th>\n", "      <td>2025-08-14 15:57:00-04:00</td>\n", "      <td>7.62</td>\n", "      <td>7.64</td>\n", "      <td>7.60</td>\n", "      <td>7.64</td>\n", "      <td>115.355</td>\n", "      <td>7.604</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>388</th>\n", "      <td>2025-08-14 15:58:00-04:00</td>\n", "      <td>7.62</td>\n", "      <td>7.70</td>\n", "      <td>7.62</td>\n", "      <td>7.70</td>\n", "      <td>505.305</td>\n", "      <td>7.640</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>389</th>\n", "      <td>2025-08-14 15:59:00-04:00</td>\n", "      <td>7.72</td>\n", "      <td>7.80</td>\n", "      <td>7.70</td>\n", "      <td>7.78</td>\n", "      <td>1050.085</td>\n", "      <td>7.776</td>\n", "      <td>59</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>390 rows × 8 columns</p>\n", "</div>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='20240420 17:00:00',\n", "        durationStr='10 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=False,\n", "        formatDate=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert the list of bars to a data frame and print the first and last rows:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2024-04-22T10:51:16.575594Z", "start_time": "2024-04-22T10:51:16.150247Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-04-08 17:15:00+08:00</td>\n", "      <td>5881.0</td>\n", "      <td>5884.0</td>\n", "      <td>5880.0</td>\n", "      <td>5881.0</td>\n", "      <td>109.0</td>\n", "      <td>5882.1</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-04-08 17:16:00+08:00</td>\n", "      <td>5880.0</td>\n", "      <td>5880.0</td>\n", "      <td>5877.0</td>\n", "      <td>5879.0</td>\n", "      <td>89.0</td>\n", "      <td>5878.9</td>\n", "      <td>57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-04-08 17:17:00+08:00</td>\n", "      <td>5879.0</td>\n", "      <td>5881.0</td>\n", "      <td>5879.0</td>\n", "      <td>5881.0</td>\n", "      <td>37.0</td>\n", "      <td>5879.8</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-04-08 17:18:00+08:00</td>\n", "      <td>5882.0</td>\n", "      <td>5882.0</td>\n", "      <td>5881.0</td>\n", "      <td>5881.0</td>\n", "      <td>13.0</td>\n", "      <td>5881.2</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-04-08 17:19:00+08:00</td>\n", "      <td>5881.0</td>\n", "      <td>5883.0</td>\n", "      <td>5881.0</td>\n", "      <td>5883.0</td>\n", "      <td>17.0</td>\n", "      <td>5882.2</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9220</th>\n", "      <td>2024-04-20 02:55:00+08:00</td>\n", "      <td>5782.0</td>\n", "      <td>5784.0</td>\n", "      <td>5782.0</td>\n", "      <td>5783.0</td>\n", "      <td>56.0</td>\n", "      <td>5782.9</td>\n", "      <td>36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9221</th>\n", "      <td>2024-04-20 02:56:00+08:00</td>\n", "      <td>5784.0</td>\n", "      <td>5784.0</td>\n", "      <td>5783.0</td>\n", "      <td>5783.0</td>\n", "      <td>3.0</td>\n", "      <td>5783.7</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9222</th>\n", "      <td>2024-04-20 02:57:00+08:00</td>\n", "      <td>5783.0</td>\n", "      <td>5785.0</td>\n", "      <td>5781.0</td>\n", "      <td>5785.0</td>\n", "      <td>66.0</td>\n", "      <td>5782.5</td>\n", "      <td>52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9223</th>\n", "      <td>2024-04-20 02:58:00+08:00</td>\n", "      <td>5784.0</td>\n", "      <td>5786.0</td>\n", "      <td>5784.0</td>\n", "      <td>5786.0</td>\n", "      <td>10.0</td>\n", "      <td>5785.0</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9224</th>\n", "      <td>2024-04-20 02:59:00+08:00</td>\n", "      <td>5786.0</td>\n", "      <td>5786.0</td>\n", "      <td>5784.0</td>\n", "      <td>5785.0</td>\n", "      <td>26.0</td>\n", "      <td>5785.1</td>\n", "      <td>24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9225 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                          date    open    high     low   close  volume  \\\n", "0    2024-04-08 17:15:00+08:00  5881.0  5884.0  5880.0  5881.0   109.0   \n", "1    2024-04-08 17:16:00+08:00  5880.0  5880.0  5877.0  5879.0    89.0   \n", "2    2024-04-08 17:17:00+08:00  5879.0  5881.0  5879.0  5881.0    37.0   \n", "3    2024-04-08 17:18:00+08:00  5882.0  5882.0  5881.0  5881.0    13.0   \n", "4    2024-04-08 17:19:00+08:00  5881.0  5883.0  5881.0  5883.0    17.0   \n", "...                        ...     ...     ...     ...     ...     ...   \n", "9220 2024-04-20 02:55:00+08:00  5782.0  5784.0  5782.0  5783.0    56.0   \n", "9221 2024-04-20 02:56:00+08:00  5784.0  5784.0  5783.0  5783.0     3.0   \n", "9222 2024-04-20 02:57:00+08:00  5783.0  5785.0  5781.0  5785.0    66.0   \n", "9223 2024-04-20 02:58:00+08:00  5784.0  5786.0  5784.0  5786.0    10.0   \n", "9224 2024-04-20 02:59:00+08:00  5786.0  5786.0  5784.0  5785.0    26.0   \n", "\n", "      average  barCount  \n", "0      5882.1        80  \n", "1      5878.9        57  \n", "2      5879.8        21  \n", "3      5881.2        10  \n", "4      5882.2        12  \n", "...       ...       ...  \n", "9220   5782.9        36  \n", "9221   5783.7         3  \n", "9222   5782.5        52  \n", "9223   5785.0         7  \n", "9224   5785.1        24  \n", "\n", "[9225 rows x 8 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df = util.df(bars)\n", "df"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["        # \"HHI.HK-20241230-HKD-FUT.HKFE\",\n", "# contract = Future('HHI.HK', '202405', 'HKFE')\n", "# contract = Stock(conId=8314)\n", "contract = Stock('GIBO', 'SMART', 'USD')\n", "ib.qualifyContracts(contract)\n", "# HeadTimeStamp = ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)\n", "# HeadTimeStamp"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='5 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["df = util.df(bars)\n", "df"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["contract = Future('ES', '202406', 'CME')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='20240403 17:00:00',\n", "        durationStr='10 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:43:08.246536Z", "start_time": "2024-04-10T07:43:08.196534Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-03-19 08:30:00-05:00</td>\n", "      <td>5205.75</td>\n", "      <td>5206.00</td>\n", "      <td>5201.25</td>\n", "      <td>5202.00</td>\n", "      <td>14422.0</td>\n", "      <td>5204.075</td>\n", "      <td>3519</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-03-19 08:31:00-05:00</td>\n", "      <td>5201.75</td>\n", "      <td>5202.50</td>\n", "      <td>5197.25</td>\n", "      <td>5199.25</td>\n", "      <td>11501.0</td>\n", "      <td>5199.500</td>\n", "      <td>2983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-03-19 08:32:00-05:00</td>\n", "      <td>5199.00</td>\n", "      <td>5202.00</td>\n", "      <td>5198.50</td>\n", "      <td>5201.00</td>\n", "      <td>8110.0</td>\n", "      <td>5200.850</td>\n", "      <td>2029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-03-19 08:33:00-05:00</td>\n", "      <td>5201.00</td>\n", "      <td>5203.00</td>\n", "      <td>5200.25</td>\n", "      <td>5202.00</td>\n", "      <td>6491.0</td>\n", "      <td>5201.325</td>\n", "      <td>1555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-03-19 08:34:00-05:00</td>\n", "      <td>5202.00</td>\n", "      <td>5204.25</td>\n", "      <td>5200.75</td>\n", "      <td>5202.00</td>\n", "      <td>6428.0</td>\n", "      <td>5202.600</td>\n", "      <td>1627</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4495</th>\n", "      <td>2024-04-02 15:55:00-05:00</td>\n", "      <td>5262.50</td>\n", "      <td>5262.50</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>319.0</td>\n", "      <td>5262.250</td>\n", "      <td>103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4496</th>\n", "      <td>2024-04-02 15:56:00-05:00</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>233.0</td>\n", "      <td>5262.225</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4497</th>\n", "      <td>2024-04-02 15:57:00-05:00</td>\n", "      <td>5262.25</td>\n", "      <td>5262.25</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>164.0</td>\n", "      <td>5262.250</td>\n", "      <td>53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4498</th>\n", "      <td>2024-04-02 15:58:00-05:00</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>5262.00</td>\n", "      <td>5262.25</td>\n", "      <td>365.0</td>\n", "      <td>5262.250</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4499</th>\n", "      <td>2024-04-02 15:59:00-05:00</td>\n", "      <td>5262.00</td>\n", "      <td>5262.50</td>\n", "      <td>5262.00</td>\n", "      <td>5262.00</td>\n", "      <td>372.0</td>\n", "      <td>5262.250</td>\n", "      <td>93</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4500 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                          date     open     high      low    close   volume  \\\n", "0    2024-03-19 08:30:00-05:00  5205.75  5206.00  5201.25  5202.00  14422.0   \n", "1    2024-03-19 08:31:00-05:00  5201.75  5202.50  5197.25  5199.25  11501.0   \n", "2    2024-03-19 08:32:00-05:00  5199.00  5202.00  5198.50  5201.00   8110.0   \n", "3    2024-03-19 08:33:00-05:00  5201.00  5203.00  5200.25  5202.00   6491.0   \n", "4    2024-03-19 08:34:00-05:00  5202.00  5204.25  5200.75  5202.00   6428.0   \n", "...                        ...      ...      ...      ...      ...      ...   \n", "4495 2024-04-02 15:55:00-05:00  5262.50  5262.50  5262.00  5262.25    319.0   \n", "4496 2024-04-02 15:56:00-05:00  5262.00  5262.25  5262.00  5262.25    233.0   \n", "4497 2024-04-02 15:57:00-05:00  5262.25  5262.25  5262.00  5262.25    164.0   \n", "4498 2024-04-02 15:58:00-05:00  5262.00  5262.25  5262.00  5262.25    365.0   \n", "4499 2024-04-02 15:59:00-05:00  5262.00  5262.50  5262.00  5262.00    372.0   \n", "\n", "       average  barCount  \n", "0     5204.075      3519  \n", "1     5199.500      2983  \n", "2     5200.850      2029  \n", "3     5201.325      1555  \n", "4     5202.600      1627  \n", "...        ...       ...  \n", "4495  5262.250       103  \n", "4496  5262.225        48  \n", "4497  5262.250        53  \n", "4498  5262.250       105  \n", "4499  5262.250        93  \n", "\n", "[4500 rows x 8 columns]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["df = util.df(bars)\n", "df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T08:07:00.629479Z", "start_time": "2024-04-10T08:07:00.230612Z"}}, "outputs": [{"data": {"text/plain": ["datetime.datetime(2021, 6, 6, 22, 0)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["contract = Future('ES', '202412', 'CME')\n", "\n", "ib.reqHeadTimeStamp(contract, whatToShow='TRADES', useRTH=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T08:07:05.068425Z", "start_time": "2024-04-10T08:07:04.234599Z"}}, "outputs": [], "source": ["bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='10 D',\n", "        barSizeSetting='1 min',\n", "        whatToShow='TRADES',\n", "        useRTH=True,\n", "        formatDate=1)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T08:07:05.405818Z", "start_time": "2024-04-10T08:07:05.070422Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>average</th>\n", "      <th>barCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-03-26 08:30:00-05:00</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>0.0</td>\n", "      <td>5401.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-03-26 08:31:00-05:00</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>0.0</td>\n", "      <td>5401.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-03-26 08:32:00-05:00</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>0.0</td>\n", "      <td>5401.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-03-26 08:33:00-05:00</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>0.0</td>\n", "      <td>5401.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-03-26 08:34:00-05:00</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>5401.25</td>\n", "      <td>0.0</td>\n", "      <td>5401.25</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4432</th>\n", "      <td>2024-04-09 15:55:00-05:00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>0.0</td>\n", "      <td>5372.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4433</th>\n", "      <td>2024-04-09 15:56:00-05:00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>0.0</td>\n", "      <td>5372.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4434</th>\n", "      <td>2024-04-09 15:57:00-05:00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>0.0</td>\n", "      <td>5372.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4435</th>\n", "      <td>2024-04-09 15:58:00-05:00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>0.0</td>\n", "      <td>5372.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4436</th>\n", "      <td>2024-04-09 15:59:00-05:00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>5372.00</td>\n", "      <td>0.0</td>\n", "      <td>5372.00</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4437 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                          date     open     high      low    close  volume  \\\n", "0    2024-03-26 08:30:00-05:00  5401.25  5401.25  5401.25  5401.25     0.0   \n", "1    2024-03-26 08:31:00-05:00  5401.25  5401.25  5401.25  5401.25     0.0   \n", "2    2024-03-26 08:32:00-05:00  5401.25  5401.25  5401.25  5401.25     0.0   \n", "3    2024-03-26 08:33:00-05:00  5401.25  5401.25  5401.25  5401.25     0.0   \n", "4    2024-03-26 08:34:00-05:00  5401.25  5401.25  5401.25  5401.25     0.0   \n", "...                        ...      ...      ...      ...      ...     ...   \n", "4432 2024-04-09 15:55:00-05:00  5372.00  5372.00  5372.00  5372.00     0.0   \n", "4433 2024-04-09 15:56:00-05:00  5372.00  5372.00  5372.00  5372.00     0.0   \n", "4434 2024-04-09 15:57:00-05:00  5372.00  5372.00  5372.00  5372.00     0.0   \n", "4435 2024-04-09 15:58:00-05:00  5372.00  5372.00  5372.00  5372.00     0.0   \n", "4436 2024-04-09 15:59:00-05:00  5372.00  5372.00  5372.00  5372.00     0.0   \n", "\n", "      average  barCount  \n", "0     5401.25         0  \n", "1     5401.25         0  \n", "2     5401.25         0  \n", "3     5401.25         0  \n", "4     5401.25         0  \n", "...       ...       ...  \n", "4432  5372.00         0  \n", "4433  5372.00         0  \n", "4434  5372.00         0  \n", "4435  5372.00         0  \n", "4436  5372.00         0  \n", "\n", "[4437 rows x 8 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df = util.df(bars)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Instruct the notebook to draw plot graphics inline:"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:06:28.466777Z", "start_time": "2024-04-10T07:06:28.455577Z"}}, "outputs": [], "source": ["%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Plot the close data"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:06:29.472574Z", "start_time": "2024-04-10T07:06:29.364183Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXsAAAD6CAYAAABApefCAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAAsTAAALEwEAmpwYAABPK0lEQVR4nO2dd5gb1dX/P0fa6rJuu+4dYxuwMW5gegymE4rzhl5CICTkpSTAS6i/VBJCCgECJAQIhAAJpoSAgVADphhwxQVj3Lu97l6vt+r8/pgZaaRVGe2q7/08jx9Ld65mrmZHZ86ce+73iKpiMBgMhsLGl+0BGAwGgyH9GGNvMBgM7QBj7A0Gg6EdYIy9wWAwtAOMsTcYDIZ2QFG2BxCLyspKHTx4cLaHYTAYDHnF7Nmzt6pqVWR7zhr7wYMHM2vWrGwPw2AwGPIKEVkdrd2EcQwGg6EdYIy9wWAwtAOMsTcYDIZ2QM7G7A0GgyEZGhsbWbduHXV1ddkeSkYoKyujf//+FBcXe+pvjL3BYCgI1q1bR+fOnRk8eDAiku3hpBVVZdu2baxbt44hQ4Z4+owJ4xgMhoKgrq6OHj16FLyhBxARevTokdRTjCdjLyKLRGSZ/e8xEakUkQ9E5CsRmS8i4+x+fhH5o4isEpEFIjLetY+7RGRdZLvBYDCkivZg6B2S/a5ePftSVR1m//s2UAucrqr7Aw8Bt9r9TgV6A0OAS4A/24M6DjgKGAxcDzya1CgN7Yp/z9/ArtrGbA/DYCgovBr7MNF7Va1V1Z0i4gcGAPPtTeuBMqy5gArASe6fCjyuqk2q+iZQJSK9Iw8iIleKyCwRmVVdXd2Kr2PIdxau38W1z8zl2n/MzfZQDIY2s2rVKoYNG5btYQAejL2IdAR6icgKEXlXRCba7fcCO4Djgfvt7nOxvP5q4HXgZ3b7AEKGH6ybQp/IY6nqw6o6QVUnVFW1WO1rKFAe+2AlU37/Hvsamjn9/g8AWLO9Nsujyn/WbKulrrE528Mw5AgJs3FUdS+Wl46IfBN4ARigqteJyA+Bq4FpwAnAtVjGvgcwDnhWRA4GSoCAa7cBwFyF7ZxvP/4ZG3buY8mmPQBs3h2abFq5dW+2hlUQqCrH/OZdjh1exRPfPjTbwzHkAEll46jqNKBcRLra7wNYMftJdpeTgKdVtVlVPwN2AwcBG4F+rl31Bda1beiGfOedJVuChh7g67ZX7/Dx8m2ZHlLB8NQnawB4b6kJh2aaV155hYMPPpghQ4Zw++23B9sDgQC33XYbI0aMYOjQofz4xz8GrCyib37zmwwcOJADDjgAgH379nHppZcycuRIJk2axJIlS9o8roSevYh0AYpVdauInAJsA/qLiKrqLuBsYLbdfQnWJO0bIrI/MBRYBUwHvi8iTwHHAUtVdXubR28oKPbUN4W9v+Olhbx1/bFZGk1+c/u/FmZ7CFnlpy8vYvGG3Snd54F9K/jx1w+K22fVqlVcddVV/Pe//2W//fbjs88+Y+bMmQA88cQTzJkzhwULFtDU1MSUKVOYOHEijY2N1NTUsGbNGrZvt8zir371KyZNmsQTTzzB66+/zo9+9CNeeumlNo3fy6Kq7sBbdprPJuAcrBj8yyLSDKwELrf73gY8bquu1QPfVdXtIvIicCywAutmcUGbRm1oFyzbUpPtIRQMqsq2vQ1UdirN9lAKmtdff51vfOMb7LfffgC45x5fffVVrrrqKkpKSigpKeHSSy/lnXfe4fvf/z4LFizg7rvv5qqrrgr23blzJ/feey+BQICuXbu2eWxeYvYrgf0imudjpVdG9t0HnBulPYAVz7+2dcM0FBqqGrW9Z+dStuypD75vag5Q5Ddr/9pCU3OAVz7fyA/+OY9/X30kB/fvmu0hpZ1EHni6qKuro6goulltamrC5wu/lv1+P8OGDWPOnDn89re/ZcyYMcyZM4empiZefPFFRo8enbKxmV+RISvMWbMzavuZh/TlV1NDF/iXm/dE7WeAl+dv4IU562hqDsTtV9vYzH8WbQJMllO6mTx5Ms899xwbNmwACP4PcMIJJ/DQQw/R0NDAvn37ePLJJzn55JNZu3YtnTt35u6776Zbt26sWrWKo446ioceegiAmpoaZs+eHfV4yWCMvSEr1MdICSwvKeL8QwcG35923wdR+7VXHv9wJe8traa2oYlrnpnL9c/O55YXFsT9zKMzVrK3wTrf82LcZA2pYcyYMdxxxx0cc8wxDBs2jA8//DC47corr2T48OGMHDmSsWPHMnXqVI4//ni+/PJLRowYwfDhwzniiCMYM2YMP/nJT1izZg0DBw7ksMMOC8by24LEepzONhMmTFBTqapwmbliG+c9PLNF+4ybJjOgewfO+fPHfLrSusBX/PJUfL72sww+HoNvng5AkU9oCli/3S7lxcz/8YlR+wEcOawHPhFmfLUVgFV3nZah0WaWL774IpjN0l6I9p1FZLaqTojsazx7Q1bwxzDencuseOez3z082La3oSlq3/aGO1zjGHqA0qLoP+MSe66jS3kxG3e1D9lfQ2yMsTdkBbet/+MFY5kwqBsAZcX+Fn1nrdoBQF1jc8L4dCHifO8f/3tR1O1b9tQzb+3O4Hvnaf17X7PyKnpXlLOvwaxhbO8YY2/ICh1LQxkLlZ1KeeyyiTx/1RFhxv6v35oIhDz7kXe8zkWPfpLZgeYAI+94nWG3vRZcKBWNX07/Ivi62fb6i3xCRVkRAVXGDOgS3L5+5770DdaQs5jiJYasUOxKp+xYUkRFWTHjbe/eoaLcujyvfnouXcqtajwzV5i1eNHYtS+kEupITRT5BZ9PUFV6V5QHt2/ZXUe/ruUt9lEIqGq7kTlOdr7VGHtDVnBfpx1KW4ZuIPyG0F6zSJoD0X/QF08axP9OHkaxX/jhs/N5f2k1u+samfTLt6m1QzaVnUrxiRBQIBAKf5XEiPHnO2VlZWzbtq1dFDBxKlWVlZV5/owx9oas07k0+mVYHGMxVSCg7SY7Z1+UFNWJg7vx87NGBd8PrezI+0urefLj1UFDf8FhA5k6th93v76EgCrPzV4f7J+jCXhtpn///qxbt472Io/u1KD1ijH2hiwRsjixlvC7nTP3YqBbX1xARXkxt55a+Gl2jU2WR37UsEo+WGalTk773hFhfc4e24/HP1rFb/7zZbDtF2eOwucTxPbs3TeNWE8L+cKyLXsoK/bTv1uHsPbi4mLP9VjbI4X5PGfIeRzv8vxDB8b00nt0DN0Eps0OiaT+47O1PPz+CrbsLvx0wkY7+2hYz04x+4wZ0LVFm3NOq/fU88yn4RO7TYH8zmia8vv3OerX72Z7GHmHMfaGrHLksB4xt1V1ji/adeerX8TdXgg02l74gX0q4vZzS0zcd/7YuH2bmvPbs3e4582l/Om95Sat1CPG2Buygldz88mtx8fctre+8H/kThinuCj+HMVZh4TKRZwxpm/cvvkexnG49+2vuOu1JcxcYeoeeMEYe0NWcMI4Qnwj1qsidrbBiN6xQxuFghPGcSarO5ZEz1wqL/Ez544TmHHT5MT7LBBj/+q1RwNQ31T4N/1UYCZoDVlBbd++LRlyibJKvv/UbHwi/PGCca0/SJZpcBn7WbdPiZs22b1jCd07liTc56ZdhbGoqthvXTyNBRKWSjfGszdklbYkUMb7iTcHlFcXbOKVzze24QjZxyngUuL3UdmplIqy4lbtZ3S/Lkw5oCcAP3o+vkpmPnDJ4YOCdQ7yfcI5U3gy9iKySESW2f8eE5FKEflARL4SkfkiMs7V90AR+VhEVovINLutSEQeF5H1IjJTREx+VDsnFbne73yxhboYUslz1uwIvs7n4uU3Pfc5AKu3te07vHzNUXw9QSw/12gOKNf/cx7//XJLWHtJkY8OJUUU+YxnnwxePftSVR1m//s2UAucrqr7YxUcvxUsow48D9yhqoOAS+zPXwKUAf2BR4F7UvgdDHlIMGbvwbW/+38Ojtr+5eY9nHbfjKjbyl0aOz9/ZTGBgLK7rjHv4rsXTxoEwHEje7Xq869cc1Qwjh8pR5HrbN/bwAtz1/ODf84LtlXvqaehKcDy6ppgSKuxHYrjtQavMfuwW6eq1gK1IuLHqkc73950JjBLVd+y+znBwanAH1RV7aLjxtgbbBJb+3MmDGDcwK50LC3i8F+9E7ZteXV0j7fIH9rvO0u2sHTLHi78yyeMGdCVx2yBtXzA0aB3tIGSZVS/kABa/24dOHZ4FTtrG1IytnTjzOvsrA3p/jjqnlv21Ac9+3U7CmMOIt0k9OxFpCPQS0RWiMi7IjLRbr8X2AEcD9xvdz8E6ybwoYgsFpEL7fYBwGoIu1G0cDNE5EoRmSUis9rLkuf2inpOvrQY1rMzfbp4F+9auH43ABceZlW9qqlrYtveBt5ZsiXex3IOpyyjL0WzayVFPhryJewRZZjOLfxHJ40IKqfOXr2jZUdDCxJeQqq6V1UrVHUo8CDwgt1+HdAVeBqYZnfvCQwCpgCnAfeISBVQAriftQJAi+dpVX1YVSeo6gR3VXZD4ZFMGCeSRLI42/c2cOM062HTyU657cWFyR8oh4hV7CVZSvw+GvIklOW29fe8uRSA1+1auj1skTcgWNHMEJ+k/AVVnQaUi0hX+30AK2Y/ye6yBXhDVfep6kpgCTAY2Aj0AxCRcsCvqrtT8QUM+U2yJuxPF43j7Ru+FtYWuUjod2+ENGLG9O8K5H/h8liicMkiYoW+Yk1su1lRXcPGLKZpuifx7337K3bXNfKcLZtRWuQLhnEM3vASxukiIpX261OAbUB/EXGCgWcDTunz14AzRaRERAZgGfovgenAZXafi4CXUvYNDO2Kk0f1YUhlx7C2DRHFOJwiHw9eOI4Jg/NrUjIWqTL2b32xGYC/fbwqYd/jfvdeizmSTBIZ6rv79SXB1906lNhCb9DDw9oCgzfPvjvwiYgsB24HzsGKwc8TkWXAd4DLAVT1I+ANYBHwH+Aq24N/ACgTkbVYxv72VH8RQ34RCuO03TubtTr6Y/zA7h3o2qGE208rfHVMr9Q1WtHUfExH/fvMNQzoXs4hA7rSpYM1YX3q6D507dC6yev2RsJsHDscs19E83wgaq68qt4J3BnRVgec38oxGgqYVDyI//Cf81m8YTd9upRz8eFWquLQyo7BTJSOMfTy84FR/SpYvCH1Ec9nPl3LL88endNFPqKtxVi7fR9DK0MyGaV+n8mz90j+/goMeU2y2TiRFPsl7Ef+lxkrAVhlLz4aWhUyCKma3MwGXctLOCSKhHFr8fskOMfRFNCg5EA8slUsJtYV0tAUyvUo9vtMnr1HjFyCISs4xrm1KYWxvLm/fbwasAp6OHgxaLlKY3MgKAuQCtw3Pq9GcvHG7ORSxKqxeurBfYKvi4uErTX1mRpSXmOMvSHjLNuyh5fnbwBg4uDurdpHtwRx2k5loYdWv+uOkk0nX1WZs2ZHUoWivXrfXjnbJYW8Yec+Plq2laufnsNfP7RuvjNXbGPwzdN5a/HmYL8/vPVVyo6fDM5puuWUkUx0TbS7//brduyjsVlpMt59QoyxN2ScKb9/H4A7zx5F51YKe3162xRuPHF4zO3utDz361RltTQ1B5IOH7w0bwNTH/yIf9s3Ok/HCShFqVpRhXXO+3W1FqdN+f37/GXGCl75fCN/fGcZAOc9PBOAK/42K/iZNdv3ZtWYdu9YElaK8VCXg/DfL63Fl07+vSE2xtgbssaFhw1q9WeL/T6uPm7/mNvdGRpuYx+ZttlaLvjLJ3z9/g+S+owznxBL4iEaTc2BlOaTF/l9XH3csOD7mvom6zhxNO6Xbq7hssc/S9kYksWZRH7r+mO4//yx9IxS42BPXVOmh5V3GGNvyGscOQQg6LFCuAe/22UIUqG2qap8umo7SzYlt1DLbxutQBLFQ5qaNUznJxXsrQ+dD2eys6EpwC6XBk0k63dmfnFV5N9qWM/OMZU7a01pwoQYY2/ICscOT40cRs/OIS/vgQtDRUrc3vCofhVUlBVR2amE3XWxDZoXVJW/frgq+H5fQzOzV+/wtF8no2VDEqtSGwOpnaAFwgqc7LEN/77GZsb87I0Wfb85vj+nje6TkhTZZAkWuInT51tHDAagd5yKZgYLY+wNGadDiZ/hvVJTUvA7x4SWe7hTFN2e/cjeFcz/8YmM6teFjbvq2nS8OWt28rNXFgff//r1JXzjoY84788zE37W0XJ5Yc56z8fbsbch5bIAZS755xUJQkqDKzuCeK8ZnEq86CdNHWdNOJfGqeBlsDBnyJBRGpsD1DY0p8xb7VBiZd10jlg4FRn6EBH6dCmnyCdJZcNEsi0izW+HLRe8eONufvryItZsq4352U6l0evHxqOmvomaFMejI21n5I33+hOGM+17hzOyd2dOPzg7Xj2EbjDxjL2jad9gsnESYoy9IaPs3meFO1JpQB66cByPf9vSqHd036NlsHQq9dMUUM584MNWH6uuKdyorN1eG8xd/+uHq7jib7EnMt1PG15vOCV+X9QJybYQmZd+59mjw96fN3EAEwd35/UfHMOgHvaEdhZce+ccxStKX1pk3UDnuiqTGaJjjL0hozhZH/26edemT8Qpo/swfpCVjve7b45hygG9oubhnzyqNwCfr9vFF61cKBSpFrl6Wy0jenUOvq/eE3uBj8/lokYqdcaiKaBUlKV2ofuwnp3D3keGQCJvLiKSlTBO6Pixt/XpYo3V0fwxxMYYe0NGcYxhqvLdI5lyYC8euXRC1DDR+EHduf/8sQDcOf2LhPvaVdvIsi01LNtSE8wzr4/w7Hfta6SkyEfHEn/wfUNTdMPTs6I0+DpeqqObxuZAMFSRKnp3CTfm7r9FtLmUbIdx4lFW7KeyUwmBVKRZFTjG2BsyxofLtnK6nZueLQmDr4/py8jenRMah+aAcsjP32DK799jyu/f4xf2zaHZNvp/usjK/GkKKCV+H3+8wHofULhh2vyo+5QkPPutNfU0NgcIaOpvjJElDt1/izd+eGzUz7RlnqO1eD2kiJBENmu7xRh7Q8a46bnPg69TuSo0WTqXFQUNSVNzgMUbdvPsrLU8bxfGAFizvRZVOLh/F3pVlFJtx7kdSZ4RvSuo7FTKgO7lXHrEYCaP7Bn87MvzN7BlTx37Gpr5aPlWvrTz8d0GM55nv2HnPib84i3+8JZVnSnVnn33jiVMv/ao4PtENxPJUjaOc9REypw+yc7NKN8wqpeGjOFemJOuMI4XnAm/v89czZuLN/Pe0lC942+M74+q8tOXFwHwwynDueu1JcEwjrMgqrJTCbNunxK232e/ezjn/PljAO5/exlPzrRE2XwCc+44IaxvfVMzEF0qwqkO9cC7y4H0nKuD+oYKkQ/o1gGIXdQ8a2EcJ/UyQT+fiAnjeMB49oas0BTI7oTajtoGbv/XwjBD71BdUx/UXBnZpzNFfuG9pdVsramn2TYq0WSTDx3SncU/OwkgaOjBCu3sbWgO847/PS+2Po6TYeKQzpBXv67l+HzCvecdwpOXHxqzXzZsqZfUS7CMvcm8TIwnz15EFgHO7NL7wE3Av4BeQC1wmarOcfXvBCwHblDVv4tIEfAIcAKwFjjfLopiaKdUdSpN3CldCDGlDp6fvY7KztbY7j3vEPp0KWdnbSN1jQFunDY/qNLpi2GBOpQU8emtx7NlTz3PfLqGt7/YwqbddTQ3a1gsJN7y/sjVuLGO1VZW3XVa8PWZLjXMSKxsnOx5zvFSL8GSyTZhnMR4DeOUqmpQPUlEOgCnq+pOEfkecCvwP67+twHuq/kSoAzoD1wB3AOc1YZxG/KcVEsApAr35KqTY26FXCz533EDLandeKtae1aU0bOijDvPHs2hQ9Zz3T/mtXiSGRxHkG1fxI0g28VXsh3GSYQJ43jD6y8u7Eyqaq1t6P1Y9WiDvxARGQOMxqpF6zAVeESt2+9TQHiw09DuSLUEQDJ4PbIjv+DMpVrhgthhnGg4/ZoDGuYdx9NyWbB+V/g+cqB0YHbCOM4Ebfx+PpON44mExl5EOgK9RGSFiLwrIhPt9nuBHcDxwP12WxnwEHBdxG4GAKvBulEAtSLSLaIPInKliMwSkVnV1S1jqYbCIdVKjskQzXhMv/YoLpoUXUGztsGSK/DbHqSI90Lpzk2tKaBhBjNe6mVTRBWubJQEDEOyZOw9TtCKEJxLMcQmobFX1b2qWqGqQ4EHgRfs9uuArsDTwDS7+0PAA6q6PGI3JYD7OTZAeJjHOdbDqjpBVSdUVaVGFdGQm2QzGyeaoT2wTwW/OCskGzCoR4fga6fAioj12WQ8badKVuQxo4UdmpoDfLR8K0s3h88nZDvilShmngzXPzuPwTdPZ+322BpCDl6E0MC6CZuYfWKSuoxUdRpQLiJd7fcBLAM/SUT6AacDd4jIEuBs4G47pr8R6AcgIuWAX1WzU9jSkBNkM4zz2aqWOiqOp371ZGtq6vChPYLbbjv1AMCKszc0BZLytJ3v2dgcCPOOI/PsZ63azj8+W8sFf/mEN1wlASF9E7SZZm99U1Dx8+i7303YPxT2SpRnL2Q5uSsvSDhBKyJdgGJV3SoipwDbgP4ioqq6C8uoz1bV9UCV63OPA2/Z2TgdgcuwMnkuAl5K/Vcx5DK56nl999ihQU10gIsmDeLdL7dwzsQBwbazxvbjB/+cx+frdvH5ul1R9hIbvyuM4+axD1YGNf1r6pv4nz99HNz20IXjKPb7gqUBsz5Bm6JFS84ahGg0NMWWhUh0rxOJ/qRkCMdLNk534C3b89kEnIMVg39ZRJqBlcDlCfbxAPBXEVkLrADObfWIDXlJ5G8xF36c/3fSCP538rCwtt5dyph+7dFxPzeqX4XnYzhzE03N4cmL7vz+SHG10f27hL3P9uSjkJoVtIs2hD/MOwb+iY9W8Yvpi5l1+wlhC7uSy8ZJwQALnITG3s6H3y+ieT4wJEp39+e+5XpdB5zfivEZCoTICbTBPVJTC7YttKbgxf49O/Gcq/h1Ipy5ibe/2MyhQ7qHbVNVS9clwlIV+330qijj+JE9eXvJlpjCapkiFVGkaGUNN+7ahyD8+N/WauWtNfVRV/EmXEHryw3nIdfJzWRnQ8Hx3Sdnh73PhTz71kwSf21EVVilp0Q4MftHPmi5hvD+d5YBLUM8zmeq7MVdjTmwPLSttvTIu94Jvr79NGsO5M7pX3DMb0Kx+8j1BaEJ2vjm3m/y7D2R/V+coV3wzpIt2R5CC5IRGHvsWxN4+OLx3HbagUkdo9GVRhlpjn7/piV0Fpmp44jEVdqrjLOdZy+0fQXtgX1Coa9e9hqDyInon728OCyk5aUGLRjVS68YITRDu6Ws2LuxP25kr1Ydw8nRj0cLY2/H+b915GACqpw8unerjp0qUnGvWWwXi7nymKExJ5w/XbWd+Wt3cpidCeU19dKoXnrDePaGjHP0/pXZHgJAUOcmnTgZN1PH9osaCrnlhQXs2heuheOElyo7lXLTySOpKIuuRplJUmVLbz31gBbG/vfnjOG57x0ORK8l600IzRj7RBjP3pBxnrz8sKwef8nPT2bXvsZgOCGdiAj9upbbceeQPPLWGqtQ+TOfrqGyUwkA3z1mKCN6d065fn1bSYWe/X5VHYOL0yLDUlPH9Wf+2p1AuECc12MabRxv5NZVZTBkgLJif0YMvUNJkY/G5gDb9jYEjx+23fbkxw7sytRx/TM2Lu+0PY5TU9/EwO7WqmS/SyrDCaU5TzPuiXwvBcfBiu3X1CcOl7mpb2oO1ihINZ+s2MZP7AyjXMIYe0NGGeySIWgvFPuFDTv3cduLCwE4d8KAsO2/sydqc3mlbFsc5+XVNWzeXc+/51sa/tEmnEuKQm1O9lHwkAlOy2erdrBwfXIL8kfc/jqn3jcjqc945dyHZ/L4R6tyLrRkjL0ho/zwhOHZHkLGKfb7mLXakmj47jFDufq4Ycy+fQq//eaYYJ8inzB+UAttwJzAss2tN1xfbAw3xJFF2wFK/KGnnU276nhy5mpWVO+1jt/qI8dn6eaaNO3Z4pEZKzz3ralv4qwHPuTj5dvSNh5j7A0ZxataZKHyo5NHIiL06FRK366hUNLvzhlDj2wWdIlDW/9ikV73bteEtPPE0L9bOfv37ATAaws3cse/FnKjXVsgl5944vHul97TjVdU1zBv7U5++8aXaRuPMfaGjNDTXiB09LDcyMTJJI5MwMH9u4SJqB02pAfXHr8/T3/nMM4Y0zdbw/NEW8I4X24KN/ZHDqtkaETxFp9PeO4qa2XyAtfNYdzArjn7xJOIZM6ZMy8RKZ2RSoyxN2SEg/t35cA+FXTrWJLtoWSNqWPDS//5fcL1JwzniP0qc/qJp63ZOMcfEL5GoXeXMl6+5qgW/bqUF9O7ooxVW63wTb+u5Tz73cPpWJrdpMH6puZW5fEnE7N3/vzpTCoyxt6QEdQu+tEeGWaHJ5zatvlGW/Xso4VhYl0LRX4JVuq67vj9PclqXHL4IM86R1t213H9P+d56gsQCCgTf/EWP315sefPODQmYewdyYx0ppAaY2/ICAHVvI29thUnZJFNDf+20pYVqtH+7LFuIG69ouIib+erxO/zLAP9q9eW8MLc9Z76AkybvZbddU08/tEqz59xaE5CZN/pu2TTnhYaQanCGHtDRlCsZe3tkVDd2vz8ubU1jNOhxMq0cdcNiHXfdxvtmnpvRs/v876C1q3R44UfPb8g+HpbTX1Sn40sLxkPt4bSs7PWJnUcr+Tn1WfIOwLafjNxnEf0bNbdbQttHbVjwC88bGCCnuFPPyUez5fP530F7fbaBk/9ovHqwk2e+o2xC9VPGOx9Ytl9Y/jxvxexZU9dUmPzgjH2hrSiqjzw7jI27tzXbmP2jteZ32Gc1n/WcbrdN/sSv4+hVR3D1hpAuGdfWuRNStqfhDbOQ/+NLI/tnR17vd0oJtrZQ907ep+jabTDOCce2IvKTqXs9fhUkwzG2BvSyvLqGn7zny/5aktNu43Zb9xlFe5IZeHuTCJtLOjtfNZ9r/P5hHdu+Bpfj0g5dV8jXuPwlmeffuXL52avi7nt9YWbGHzz9LBQT2RRmng4nv01x+3PrNunMKQy9cV9PBl7EVkkIsvsf4+JSKWIfCAiX4nIfBEZZ/frJyLTRWSJiHwmIgfZ7UUi8riIrBeRmSISt8qVoXBw//7y2LFtE46H2rVD9tUrM80f3/mKG571vjjKfY0UewzjOPIL0RQz4zGge3nCPt1cf7N4ktiPfmCtll22pSY4vxFZnS0ezpNJOqd1vO66VFWH2f++DdQCp6vq/sBDwK12v2HATao6EngQuMduvwQoA/oDj7raDQWO+9E9Xz3btnLf+WO5eNIgDkhycjCXaI3P/Pzsdfz2jaXBOQsvxt59vYzu39XTcT5avhWAe978Kqnxrd2+j9mrt8ftM7SqE0MrO3LGmL7B8pDNAW0xWRttXiYZz955KklncXmvxj5s1Kpaq6o7RcSPVXx8vt3+nqo6cm+zgCr79VTgEbW+0VPAlGgHEZErRWSWiMyqrq6O1sWQx7TTKA7Denbi52eNSusPOZ241Jk9M3/tTm6w5Q7C9pMA5xxdPXkY/bom9rwBttux9DXb9yY3SOAbD30cd/vs1TtYsXUvJUW+oLH/6cuLGP+Lt6jeU8/n63ayt74pLOPKceiTWVTldE1nqDOhsReRjkAvEVkhIu+KyES7/V5gB3A8cH+Uj94AvGi/HgCsButGAdSKSIupalV9WFUnqOqEqqqqyM2GPKe9xuzzndY8kTnxbXdM3ufhZud08dLXwbmsEqW1O552a56wSop8wTDR3z5eDcDEO9/ijD9+yHefnB2Mubsn4ZMK40SZ10g1CY29qu5V1QpVHYoVmnnBbr8O6Ao8DUxz+otIJxGZBpQCv7SbSwD3nyIApE8EwpCTfLwifYp+hvSSbBjnyZmWQXTHvL0YMufGkozRc5yIRHVynYyX0w/uk8S+4aSDelHi90VV6wT4YNnWYNlF9zhaE8bJqmfvRlWnAeUi0tV+H8CK2U8CEJFy4E3gA1U9X1WdigIbgX6uPn5VTU6A2mAwZAVJssarO3wxvFfn4OtkDFlrjF4i27pjr6W2mYwRrrD1epwCNInHENr3Ex+vDlbgSkRzEvMarcVLGKeLiFTar08BtgH9RaSL3eVswCkvcx3wqqreG7Gb6cBl9uuLgJfaOnBD/vHN8blYhcmQiGTNz4fLtgZf+8Im6BPjeN9bk1it6kzqJrof/WK6pW/zn8WbGDuwa4vt7yzZzOCbp/ODf8xlRXUNK6praGwKBPV56hoDrKiOr4H/4tz1LNm4J/j+zAc+9PQdnPtPOud1vMjJdQfesk/oJuAcrBj8yyLSDKwELrf7HgIcKyKXuj4/EXgA+KuIrAVWAOemZPSGvCJfJygNyYVxGlzhDvef3MsK6rlrdgJWXPxnZ47ydLzQMeKPcptd97esyM+UA3sFj9UcUPw+4duPzwLgX/M28K95VlWtEr+PYr+PabaEwW/+E19v/q8frvI05kicJ4J0TmslNPaquhLYL6J5PtAiV15Vz4uzq/OTG5qh0Givcgn5jhXG8dZXVXn60zXB927PPpl7/eVHeV+KE5ygTTDGPfVWGKdjaVHY92lsDuD3RV+t29AcoMQvwfrBtR5Eyk4Z1ZvXPEorOARyIYxjMKQKD2q1hhwkmZv0fxZt5p0lVoWmX00dHeapJmPIRvfrkriTTQ9bliCRHEVNnTWFWFFeHBRnA1i1LX7KpltmebZdXjIe3zlmaMI+blSVNdtrgdzIszcYWknIhXrJfjQ25B+JMl0c6ptCnu+5EwaEGfgOpYm1bpzc+mRy1K8+bhgAxwyPn6595iFW8ZhfnDkqTJTt5D/MYG99U6yPUeLSyq+J08+hS3lyK6VfnLueB23NnnQ+/Bpjb0gr7sflPXWJfyiG3EPwHsYpKw4ZdJ9PwoyjF2GzCttQ9uvmbUEVQO8Kq5ZvSYICJs6Np1NZUYuiKH9+P3ZxcKc2rpv//OCYmP0Hde/AL88eHXcsbj5ftyv42m/COIZ8Jb3SVIaMkIT96RxRQtAx9skKe3VKohRh0D4muNicAiHRIiVr7TBKNKLdRIZWWd/n3AkD6FRaFKazU+T3Maqf94VbFeXutQjG2BvylHSWWTNkDq9/xchVo054JLL+bixaY+q8LqpqCijFfgnOQfzh3EOC2xpiLJgC6NahpEXZw2K/j8U/O4lfTR3Nhzcfx5s/PDZs+6i+3ucc9jWEnn6MsTfkLUlUZjPkKELsUlXrd+7j4kc/YeOufagqV/19Tth2ZyFSohCLw6VHDAKgfxJhHK/ZOE12iqXD0ftXBl9PX7Ax5uc6lhbxi7NapoF2KCnC5xO6lBeHha/ACmGdN3EAvSqSqzucC6qXBkOr8DqxZ8hdinxCU4y79tOfrGbGV1t5Zf5Grn5mbosJTKfcXrHHVKxzJw5k1V2n0bVDiefx+TwuqmpqVopd1rTIo2UtLfIFJ3eTQUQS3oAgYp7DePaGfMVEcfKfIr9ltKLJDDw/2yre7fMJ0z9v6R074mHFHj371uCYx0Qhw6ZAAL9LgtjvUS+/yCeen0zc+DzKTLSmYEtrMMbekFbc13qJSbTPSxyvfOitr4alVgJs2m3VSp2zJnr+eUWZNfnYNcl0xKSw7WMis9oU0LBcfC+TwBccNpCedrZPIl78/hH85ZIJwfc+j56928BndQWtwdAW3N5W7y7efjSG3MJtILfvbeDNxZvp2bmMk0f1DrZv2GmVXnz00gm8v7Q6GJr4wZT96de1jNNGe1eaTJagZ5zAi25uVk+hmzd+eAxXPDGLNdtrufTwwcH2z39yIgf/5I2Ynxs7MFy13SfeEhTcxj6dqZfG2BtSQm1DEx1KWl5O7kvdqCXkJ+6c9IDC/3vJqk+06q7Tgu2OzsyA7h34qUvTpqzYz8Uug5kOQmGc2H0CAaUxEPAUJhneq3PUEoTOU4rncYl4Uths7SrjZDHP1YY28/rCjRz4//7DrFUtS7yZ1Mv8x10L1hEEA9hd19gi28QtaZwpQqqXsa+1obe+ygtz1nuua+vQlgQDn4inOSu3N29W0Bpymg9sSVt3AQcHY+vzH3fo4w9vheq8Lly3i827vUsRpwvHWf/LjJUJ+8YqQOLw/FWHA3DlMZb2Y9+I0og9OpZwtsc1A8mGcTqU+NMqFmjCOIY2E69sXTJFLwy5SVEMbzhSYqBHR+/pkqnEuf7W2/MG8di4qy7u9vGDugPwP+P78z9R6i/MvuMEz+Py+bxN0DoG/qObj/O879ZgPHtDyvh/Ly1i8M3TWbk1pCKYhJ6VIUeJNWn43tLq4OtrjhvGC98/IlNDCieBM5xMZapUIh49+2BJwjTXezDGPkk27arjd298aTzWOCzbEqrmY85T/rNg/a6423/3zTHccOIIBvVITv8mVSSykW4Jh64d0pgCGoHXmL3TJ52Ts+DR2IvIIhFZZv97TEQqReQDEflKROaLyDhX37tEZJ2ILBCR8XZbkYg8LiLrRWSmiHivTJAB1m6v5bUFGxMapo+Wb2XSr97m/neW8dmqxLrW7ZV9jc3MWbODU+6dQV2CGKkh90nknXYuy240OFGc2y2XfNFhg9I9nCBeY/bBKlXpHo/HfqWqOsz+922gFjhdVffHKjh+K4CIHAccBQwGrgcetT9/CVAG9Lfb7knZN2gjzQHliidmcdVTc1i3Yx+79jXG7OsuSfboByuS0twuJN5avJlLHvs0+Hgc+Vt7ae56pj74EV9s3M2/5q4Ptrs1xA35Q7R0RXcN1/GDurXYnkkSefZNrt+pV9mGVGAtqvIQxnH1T+t4PPYLG7Gq1qrqThHxY9WjnW9vmgo8rqpNqvomUCUive32R9RynZ8CpqRm+G3nwkdm8uVmq0Dw3z9ZzZifvsGXm/a06Ncc0GAuMVgVea544rNMDTOnuOJvs3h/aTV32ze/yEvUrVvv3BD/eeWkYIaDIb+49rj9W7S5J2N7dEpO7CvVxEsQAGsxlUMsjZ90EKmNs62mngsfmcmWPeGTxJmoPwsejL2IdAR6icgKEXlXRCba7fcCO4Djgfvt7gOA1a6Prwf6uNtVtRaoFZEW7oCIXCkis0RkVnV1deTmlLNy615mrgjlhr8y39L2WL+zpbb1vsaWtSff/TL9Y8xl/vTecs5/eGYLWdvGQIAK+9HeUT1M9+STIX10i5Jlc/6hAzn5oN78/pwxWRhROImMZHVNyLg6Wj2ZwLnknfDwM5+u4cNl2/j6/R+wtSaUsur8fNJt7L0UHN8LVFiDkW8CLwADVPU6EfkhcDUwDTgBKAHcZzMANMdpjzzWw8DDABMmTEh7jOSnLy8Ke++kbkXKlQLs81BouL0wuEcHVm2zbogfr9jGxyu2Bbcd2Kci7Alos62dYkx9YTF2YDeOP6BXtocBJDaSry0IFf+OlVn07o1fo2fn1D6hOGGZgIJfYGetFSLevLueCb94K7gCOZiNkyNhHABUdRpQLiJd7fcBrJj9JLvLRsC94qAvsM7dLiLlgF9VW67AyTCx4nfRHgvdd+JYLN6wmy274+fxFgLxhKHcFXuAYBpmOheLGNLPeRMHhL3v6KGebKZIFMZxe42x5BKGVHakYxLVsbzgHMoJ05RGkWCwtlv/Z32CVkS6iEil/foUYBvQX0ScUixnA7Pt19OBS0XELyInAEtVdbvdfpnd5yLgpRR+h1ajqhzYp4Jr7YLFlx9lJQlFm3jdVtOQcH+n3jeDy5+YldpBJkFTcyAjk8ZOaOagvuGl14b36tSitqdzQzW2Pr+5JELfJpcUTBNdW251y0UbWvqYfdIk0CdBz976Tc74amvYdsejz6XUy+7AJyKyHLgdOAcrBj9PRJYB3wEut/u+CCwCVgC/xgrxADwAlInIWixjf3vKvkEr2bWvkbe+2EJ9UzNXH7c/9553CKeOtlT8mlVpDmhYKmajPbHzwAXjmP//TuQb46zVdUs2hV88iXKS08kp987g0sc+TftxmpqV40b25N7zxoa1P/atiS0ek7fsyf5yekPbKSkK/7vm0pNaIiM5un+oRGDk73PJz0/mvf+bnNZxqVrzW+7C4hDKEsqZCVpVXamq+9n/jlTV+ar6mqoOsVMxT1DV5XbfgKpeq6qDVHWcqi6x2+tU9XxVHaCqx6rqpvhHTT+PzLCWei+v3kuJXYnG+eM0NAXY79ZXOfgnb/C7N6yMk0Y7X3xQjw506VDMwfYF9PQna7Iw+uh8taUmqFOTCuav3cn/Pj2nxdNCY3OAIp+E1eU8qG8F/bt1YFCPDlH31b+r9zJzhtzDrY/j6MfkColspPv6vfb48MyismJ/qwqTeMEdxnkkim6PM5/ljC7dN9DceRbLMJujxNadeN6OvVbIZk99E/e/swwIlVdzLoxzJlgxTKdQcVMGZ/kTsTeiNFxr+f5Tc5j++UY27grXHGlsDlBc5AvLsDn5IOup6PoThvP8VS2XzafrB2XIDO5KU45+TK7gtpHRpBHcue7FGcwKc0/Q1ja0/E0e9et3aWwOoKoZCXO2219gU3PLi8Ix9jc9/3lY+9kPfsirdkFiJwZdXmJNUJUW+QgElPe/ym4apvsi/9pv/5vSfX++bhe7akOLzRqblWKf0LdLGaeM6k2fLmVccfRQwPJOxg3sylNXHMbEwaHs2kwuZjGkHidTxbmp5xJuj7gxSh69uykyTTidiMuzr42RzffLV79ANf3xeihw1ct/fLqGHp1KOeHAlili0fJtY623mLtmJ3PZCYR7qL0qSqlrDDBzxTa+/Xj2JmYBalyeQ3WK4+Tff2oOECpW0dgcoNjvQ0R46KLxLfqLCEcOq+TIYZUMvnk6EFs50ZAfFPt9zLhpMlUpTk9MNe8u2cLJo8KrYrk9+0wueg/G7OM89P/1w1VMHlGVkcSKgjb2N7+wALBijJGPntE8+3hSCRMHd2PKAb3o65q5Ly3yU9PQxK9dMgrZorY+9esAIiVjB988nRMO7EVjs7bIuklEsYdycIbcZkD36PMxuUS033CYN59Bz/7dL7cA4Yu6zj90IM98mp15vnbxC/zGQx+3aIvm2Q+ujH0xP/2dSXz32P3CHhl9AtM/38j8tTuDj7mj+lXE2kVaiSwE3VZiicK9uXgzW2vqqYuyojgeZgWtIRNEuyG5Q5yZ9OydVMuF60MZeyce1IsD+lTQoSS0TqGhORAzsSGVtAtjDy3v+O8s2dKiT/9u0U/4X781MWrMeVS/UErXqaP7cNiQ7nSMUoc1EzgrWoGkS69FozHKk4+bF10CZ/G49vj9jQCaIWNE81F2uuabslEm0x2imTyiJ69ddzS3nHpAsK2+MZDWQuMOBWvsIz3TF+esa/W+IkuTObiryXcuK/Kscpdq6hqbw/LrExlqLyTSEIlWxSca158wnDvPHt3m8RgMXogW+75h2vzg6yOHVWZsLN1s7fyKcuv/3q6V582u31d9U8Bk47SFpog/unvl3CpXJSUvlEfRyoFwadVOpUX4PZYhSzUj73i9RVuy3zGS2gTpm4cOzq30O4MB4nvu958/NqMF0X965igglNRx3qEhyYk/vRcq6bhg/a6YMg6ppGCNfWOEZzpt9rpgTvznSaxyHdm7Mz0romcguP88I/tUIBLds8gk3zpiMGCtXJ2zZkfwOyfL3gTCbwMzEGM0GJIlmrF35JgHZniCucw28s5TtztUs6cuPKy8vLptzpkXCtjYt/yje5E3/fTW43nkkgnB96//4JioKpgAizeGnhbGDuxqe/bZNfZnHNIXgMc/WsnUBz/iiY9WtWo/iRZmZbs6kcEQjWg/8YsmWdWpDnbJJmSCSG/dnaTQK0JMMBNOYgEb+5Z/dWeVa01dbEPWs6KMKQf24viRPRMew6110aHYn7WYvRsnLviqLev60HvLg9tUNep5ica2vS2F3/53cqj4SIcsTUQbDNH47rHWor6wnPqAMvjm6dz79lf4JPN6PpEZaG7jf/wBln35+ZkHZW48GTtShnHPwDs4cfzP1+1M+Pk/XzyeJT8/OW6f7q6iDkV+Hz6RqJ5FunFfRJGTydtdRvvGaZ9z+n0feNrn3a8vCb4+Yr8eLP7ZSdx44ohg26A8yLk2tB/OOsRSVnenWW52VYTKRnQ1clWsO4xz40kjeOCCcRmtCVCw7lm0PPDmYM3U0EmPlRZY5PdRlECy2zGyjriS3xc7Pz2d+CRKJRgXL8/fAMDzdkbS7rpGKsqK4+7TmdB++OLxfG1EzxbaNiZv3pBLOL9Ft1Fv7XxVqohMp3T/ZkqL/Jx2cJ8WJQrTScEa+xo75jyyd2eW2DVlnRBGXWMzA7qXc885h3Bw/66tPoZz03CKL1uefeaNfaJUy2uemRv2fvXW2jDZ12hcevggnvh4NUftXxlm6DuU+GPqfBgM2cKxo+7Vstk29l4WjWciv96hYMM4Vz9tGbjvHRuKMzuGeMPOfZQV+ZkwuHub1BiDfyan+ECWJmgnDGpRzjcuP5++mFmrtsft4xSRLo14vJl+7dG8+cNjkhugwZBmQtrxod9ffY559kOirNAvyqCMSMEae6c83mFDQ/ngO+w4/icrt6dkEUPkPnySnTz7SJGxl68+Km7/T1du5/Z/LYzbp7E5gE9aZhQMqezI/hnMVTYYvOAY++v+MS/Ylsni4tGIDHVGy+rLpGSUp0OJyCIRWWb/e8wuVfiMiCwWkYUicozdzy8ifxSRVSKyQETGu/Zxl4isi2xPFyN7V1DVuZQ+Xco5Y4yVjvjCnHXcYoujDezeMWXHUtu194uVDZCNuL2b0f27cPjQHnH7bI+SbePm9YWbsnLjMhhaQ7RFSfWNWTb2Ed5gtAw2p8/gDKxb8RqzL1XVYc4bERkNPKiqM0RkMvAIMBw4FegNDAEOAf4CTBCR44CjgMHAZOBRe3vaaA5Y1ZQA7jt/LC9/voG/fbw6uP2yIwe3+RjOn9JdQ3L1tlqG3PIqF08axM/PGtXmY3gh2r3lycsPZdPuOo769btRP7NlT71dNCH6I85XW2pSOUSDIa1Eu4yz7dlH3oBG9m75RNyxtIhHLpkQnPdLJ14fIsLMiaouUNUZ9ttZQJX9ej1QhnUTqQAc6zoVeFxVm1T1TaBKRNJaBaEpoGEn++yx/cK2905BkWEJxgmt9+7HttcWbmzz/pOhU2kRM24K1dIs8vvo2yV+KcBnPl0bc9v4Qd0YM6BrqoZnMKSVaJ59tidoizyEcQCmHNgrOEeWThIaexHpCPQSkRUi8q6ITIzociNWoXGAuUAtUA28DvzMbh9AyPCDdVMIrzBgHetKEZklIrOqq9tW+ak5oGEne1JEWGNoZdvDOEHP3v7f/bfdWtPA0s172nwMLyhWDdhIeddE6ZGbopRmdAioUmFWyRryhGiVnlZuze7TaSzjni28FBzfq6oVqjoUeBB4AUBEikTkPuBo4Dq7+7VYxr4HcAzwrIiUAiWA+zYbIEpquKo+rKoTVHVCVVVV5OakiPTs3bK/F08alJLVdM4unBh9pHfxm0wVNdHWVaZvjlWai1A1KoMhH4hm7H/56pIoPTOH2xycOjr75RyT+jWr6jSgXES6YRn9vcCJquq4sCcBT6tqs6p+BuwGDgI2Au44Sl+g9ZrDHmhqDoSlNbkNV6rWA131tWF0KLFSOKHlcuxoxY8jeWneeuau2dHmsQiJv1SnUstTP9ROOY1UBnXz1eaajNTFNBhSQS6u8XP/fhx9nmziJYzTRUQq7denANuwjHq1qt6iqm6hmSVYk7SIyP7AUGAVMB241M7WOQFYqqrxE73bSHOEZ+82/KnSyBg/qBuLf3ZyUDYhMq/27SgFUiK57h/zOPvBj9o0DsVb2owTlikr8VPkE5rjLMbq0bGEmvrYZRoNhlwiWsz+63YWXrZwG/s4D9EZw4tn3x34RESWA7cD52Bl0pzhSsdcJiKjgNuAPiKyGsvAf9c26i8Ci4AVwK+Bq1P/VcJpCmhY/rk7jJMujzXaBffeUm9zD9/52yy+akOMP9ZX6uWSZ3ZMe4lf8PskvDZnBE0BZXCP1KWnGgzpJJoD1zPLxdHdQ2rKAWvvJWa/UlX3s/8dqarzVfVmVa1S1WGufwtVdZ+qnquqg1R1uKo+Z+8joKrX2u3jVDXtwbR9Dc1hgY0u5SEtmHRFJ6Lt111BCqyi3YNvns5Hy7eGtb+5eDMfLd/WquPGS+ufcdNx/PQMS1nPOQfFfp/l2ccJ4zQF1MTsDXmD29HabWvFZzt92BcjspAtsj+CNPHJyu3Md0kQu1Ob0hXfS0bnIl7aY2uIdeiSIh/fnNCf358zhnvOPQSAcyYOwO+LH7NvbAq0WJlrMOQq7oWMz35m/bbe9/hUnS7cdubIYfEXOWaCgjX2EH6yJaw9PUYsVqrjzBWWx+72pF+ev6FFEfTWkihi36GkiKnj+nNAnwpW3XUak0f0pMgnPP3JGgbfPJ2fvbw42LeusZn73v6KPfVNxrM35A25eK26kyYyraUfjdw7Qymif7fyoMZ1JOk68bFuIgvsJ4xI2eUdtmSBs1S6tTILquopG8eN+7F3xlchD+jWFxfw+zeXAuHzHAZDLuPOaY/2xHrWIZmfrPWaOJEpCnbVjGq4p+22w+kK48Tab6M9ORNZ6s+RYT7t4D488O7yFp9LhmTvX7F0tF+Ysz74Ol6Yx2DIVUoivPz3/28ylZ1LYvROH1mWyGpBwXr2zQGNaXzT9UQVq0K8k+K4bue+sHZHjGxvveXxt/baaM3n3Br47s9fftSQ4OtD2qD1bzBki8jfw8AeHbJSRjPHbH3hevbNGp5n7w5zpCtmHys89J/Fm5i3dmeL5dOOZz8oA4p38XCHj9ylFiPlFwyGXOaXZ4/m1hcXMDBHrttsq99GUrDGXlVjGvV0xeyf/mRN1PaF63ezkN0tZEydot7OTaC110Zbryn3x51J5Ll3nEC3jpl/9DUYWsvofvGrr2WabDxNxKPAwziZjdlvralvcSw3x40MLy68M2js2/5naNMNzGXtnUpbXTvEr1FrMOQazpN8PM2nTNI9x5ylgjX2AY0dQ083sTxtxx4fbNd/dfS2y+zSf5mM2Vd2Cl2I7s8HAopIbqSKGQzJ4KwLybKMfQs6l+aGh58bo0gDjtGKRlOCAt1tpXdFGYMrOzBzRbj8T5N9FZ43cSCfr1vA+h3WhG1RW3OEVZNMvIQ/XzyeeWt3MX/tTj5ftzPYHtD0zWkYDOnEuW5zQZrAYdVdp2V7CEEK1rNvVg1b0epOI1y5bW9ajnnDCcMBOHr/SsZEyWRxxuDkrzvec4cSJ2bf+ptQsvZ5/KDuXH7UEERCnv17S6v547vL4sooGAy5ilO/IpBjE6O5QsEa+0g9+/qm0IKmiYO6peWYvSqs6lcKHLV/Zcsx2U8UJUXWaQ+GcdpY5KAtl7ZPJBh2evSDlW0ah8GQTZzfu/vJPVopwPZKQRr7Vz7fQENTgHU7QnntTomyQT06cOkRg9M+hqP3r+LLX5zM1ZOHUWob93/OsjQ7nKXd9faK2sjyZa2htXsQLE9ox96GrGuJGAxtwTH2q+wn9y7lxRw2pHs2h5RTFKSxd4zW3obQitUB3ay0x+tPGJ6xycfSIj83njSCn58ZXnjcMfaNtmff1onkNj21ivX57bUNwab/nbxfm8ZjMGQD53fkrEZXVZNo4KLgJmi/2Lg7aNhvPHFEsL1bx5L0T5bEuK4iFyc5MfuGCGPfFqPd2ovaWWzmVNW6//yxWS/6YDC0hsgnZG1luc5CpeCM/bXPzA3qWLs17DNJpNE+fL9weVNHu6OxKXrt2qSP14aovYjlATXHqKNrMOQLncpC5mxPXSOKt3Kd7YWCC+NUZMnAQ/y4+aShodhhsR3Dr7YXYQU9+1YabdW2xeyV0MpZY+wN+UppUSjR4e8z19ir6LM4oBzDk7EXkUWu8oOP2XVpnxGRxSKyUESOcfU9UEQ+FpHVIjLNbisSkcdFZL2IzBSRIbGP1jbcs+/ZeoSLZrQ3764Pbbc3r9xqTSSlZIK2lbsQgY276rjm6blAcgVYDIZcZeOufQRMGCcMr2GcUlUd5rwRkdHAg6o6Q0QmA48Aw0WkCHgeuEZV3xKRcvsjlwBlQH/gCuAe4KwUfYcw3AuCnAycTOEcO9qjo2PYoWU+fVtj9m2J9T9vSxqvsMdnPHtDIfC3j1dTVuwzE7QuvBr7MHOiqgtcb2cBVfbrM4FZqvqW3c/JfZwK/EFVVUSewjL2LRCRK4ErAQYOHOhxaOG4bVWmtSlOH9OHWat3cOOJw+P2269np7D3qTGwrdtH5AKqWNW2DIZ8w0zQhpMwjCMiHYFeIrJCRN4VkYkRXW4EXrRfHwLUisiHdojnQrt9ALAaQFVr7T4tVjap6sOqOkFVJ1RVVUVu9oRzJ+/RsYSuHTJr7EuL/Pxq6uiwerfRqOxUSm97ARa4Y/atI5XrBVMRUjIYcgFrLstczw4Jjb2q7lXVClUdCjwIvADBOPx9wNHAdXb3nsAgYApwGnCPiFQBJYA7phIAwmv0pYhgKCXHbumnjOod9r64KHWV56184jbtIojRxTEUCg3NAePZu0jKyqjqNKDc9spfAPYCJ6rqHrvLFuANVd2nqiuBJcBgYCPQD8CO4/tVdXdqvkI4jmOaa/WHbzp5JABVnS2v3106LTJmP3/tzqR1clJ1TZuYvaGQMJdzCC9hnC4iUmm/PgXYBpwEVKvqLarqLqz6GnCmiJSIyAAsQ/8lMB24zO5zEfBS6r5COE7MOdeySsoj9G+KXca+yJV6+d8vt3DmAx/y9xiFUNKNI8pmMBQCJowTwov/2x34RESWA7cD52DF5s9wpWMuE5FRqvoR8AawCPgPcJXtwT8AlInIWixjf3savguQu2GcyOE4ejkQ7k3PXbMTgKWb9pAMrf26N508Iuz9CCMcZSggcswMZJWE2Th2OCZSLGU+cHOM/ncCd0a01QHnt3KMSREK4+TWX9kZjRO+cZQv3Rejamiy1SnE4IW2pF5u3lUX9r441+JfBkMbyC0rkF0KTi7B8exzzdhXdS7l6snDOGtsPyBkVIt8Embwi33hGvdeae3jage7is7/nTSC/508LEFvgyG/yLUn/GxScG6cY+NzzNYjItx40giG2Tn2G3ZaSxDc2S9Pf7Im6PHXN3pfENYWbRxnxbHR/TYUIqUpqO9cKBScZ+/cyXM9hXDVtloA6psCQa98/c597G2wMlKd4uVeaMvikTPG9OWgvhUM62mMvaHw+J/x/bM9hJyh4G57oQnaLA/EI5GLmO57+ysA/jVvQ1L7ab02jhhDbyhYenYuS9ypnVCAxt76vzHNRcVTxYkH9Qoz1EMqOya9j/z4pgaDIZsUnrH35Ydn7+jnRBY26du1dZ6IySc2GOD9/5vM3759aLaHkZMUXMzeCeMMreyUoGd28TsSCRFueVMrnkiSXW1rMBQqA3t0CK5SN4RTgMbe+j/XPXv3+NxDjVSh9L7DNg3HYCgYSot87FfVkSuOHprtoeQUBWjs88PqdbLz2zuVFlHkWsjU5DL2DU2BYCpmPIxfbzCE8PmEt2/4WraHkXMUnLHPE1vPeRMHUNfYzMWHDwprn7d2Z/D1yq17vckXtKEsocFgaB8U3gRtnlj7Ir+PK44eGlY3M5KmQMuFVV9t3sOtLy5g8M3T+fkri4PtZqWgwWCIRwEae+v/QjB9UWw9P3tlMU/bipiPfrASMGEcg8GQmIIz9m8v2QLAkiRVI3OR7z89u0XbtpqGqH0L4eZmMBjSR8EZ+0UbrJooa7bXZnkkbWft9n0t2qIVtTKplwaDIREFZ+xvPsWqCDWsZ27n2Udy5iF9PfWLNSdhQvYGgyEeBWfsHbmBQJ55u326lHvqF2nTr392XlBUzWAwGGLhydiLyCJXRarH7FKFz4jIYhFZKCLHRPTvJCKbReQi+32RiDwuIutFZKaIDEnHl4GQTnyrFydliYam8NnYCw4bSGWnkpYdI1z4F+asB3JPv99gMOQWXvPsS1U1WNlCREYDD6rqDBGZDDwCDHf1vw1odr2/BCgD+gNXAPcAZ7Vh3DFxKkG1RnYgm/zjs1DN2e8cPYTGZmVrTQOqGpZWubO25QTtD6cM55TRvTMyToPBkJ94DeOEWU5VXaCqM+y3s4AqZ5uIjAFGY9WidZgKPKLWTOJTwJRWjzgBJUWWYcy3MI57uMcf0IuOpVb+/daI7JvIwuVHDuvBdVP2Z3gvI1NsMBhik9DYi0hHoJeIrBCRd0VkYkSXG4EX7b5lwEPAdRF9BgCrAVS1FqgVkW5RjnWliMwSkVnV1dXJfxtCYZx8kTh2qGsKPQhNGtqDZjuq4+TUA/x7/gZG9+sS9rkPl23LyPgMBkN+k9DYq+peVa1Q1aHAg8ALEIzD3wccTci4PwQ8oKrLI3ZTAriD0gHCwzzOsR5W1QmqOqGqqipysydCMXvvZf1ygeKInMrNu61C4Pe8tRSAbTX1XPvMXKbNXpfxsRkMhvwnqWwcVZ0GlNte+QvAXuBEVd0jIv2A04E7RGQJcDZwt4h8D9gI9AMQkXLAr6q7U/g9gjjCYU15NkF7wWEDw94ff0DPsPfNeRaWMhgMuYWXME4XEam0X58CbANOAqpV9RZVbQJQ1fWqWqWqI1V1JFZo5yZV/RMwHbjM3uVFwEtp+C5AyLMP5Jmxv+XUkWHvTz+4L8ePtAz+hp37OPTOt8O2v3LNURkbm8FgyH+8ZON0B96yM0I2AecA5wNniMgyV7+zVHVhjH08APxVRNYCK4BzWz/k+JTanv2koT3SdYi0EK3S1KAe1pqBI+56p8W2URGxe4PBYIhHQmOvqiuB/SKa5wM3J/jct1yv67BuEGmnrNjPny4az7iBXTNxuJQRbQVssd/kzhsMhtRQcHr2ACePyr+c82hm3SyUMhgMqaIgjX0+Ek2P3l3BKhp9upTl5Y3NYDBkHmPsc4RoPvzcNTvC3v/xgrE8N3sd4wdaSxQ+vuX4DIzMYDAUAsbY5wjRYvafrdoe9t4vwuOXHZqhERkMhkKi4FQv85VoYZyD+oZn3LhX2RoMBkMyGGOfw0Rm4xw7vGeMngaDwRAfY+xzmAsOGxR8LQLdO0aRPDYYDAYPmJh9DvH2DcfSoSSkannGmL50LPFz+ROzMGoJBoOhLRhjn0PsV9WylKKj9WMwGAxtwViSHKckQa69wWAweMFYkhynNKJYicFgMLQGY+xzHOPZGwyGVGAsSY7TqdRMqxgMhrZjjH2OM6B7ebaHYDAYCgDjNuY4IsL954+lS3lxtodiMBjyGGPs84Cvj+mb7SEYDIY8x1MYR0QWicgy+99jdqnCZ0RksYgsFJFj7H79RGS6iCwRkc9E5CC7vUhEHheR9SIyU0SGpPNLGQwGgyEcrzH7UlUdZv/7NjAQeFBVDwSuAR6x+w3Dqjs7EngQuMduvwQoA/oDj7raDQaDwZABvBr7sMX6qrpAVWfYb2cBVXb7e6q6KLIdmAo8oqoKPAVMadOoDQaDwZAUCY29iHQEeonIChF5V0QmRnS5EXgxykdvcLUPAFYDqGotUCsi3aIc60oRmSUis6qrq5P5HgaDwWCIQ0Jjr6p7VbVCVYdihWZegGAc/j7gaOA6p7+IdBKRaUAp8Eu7uQQIuHYbAFqIs6vqw6o6QVUnVFVVRW42GAwGQytJKs9eVacB5bZX/gKwFzhRVfcAiEg58Cbwgaqer6pN9kc3Av1cffyqujtF38FgMBgMCfASxukiIpX261OAbcBJQLWq3uIy6GB5+K+q6r0Ru5kOXGa/vgh4qc0jNxgMBoNnvOTZdwfessvmbQLOAc4HzhCRZa5+ZwGHAMeKyKWu9onAA8BfRWQtsAI4t80jNxgMBoNnRHO0KoaIVGNP6raCSmBrCoeT75jzEcKci3DM+QinEM7HIFVtMemZs8a+LYjILFWdkO1x5ArmfIQw5yIccz7CKeTzYYTQDAaDoR1gjL3BYDC0AwrV2D+c7QHkGOZ8hDDnIhxzPsIp2PNRkDF7g8FgMIRTqJ69wWAwGFwYY28wGAztAGPsDQaDoR1QcMZeRM4RkZV2oZVvZ3s86UBESkTkQbtwzFci8g27/ToRWSMiX9rSFk7/u0RknYgsEJHxdltBFpQRkVdF5BH7dbs9H7bMyT/s77Pcvmba5fkQkctt1d4VIvItuy2qnUjmHOUdqlow/4DOwFos0bXeWPIOVdkeVxq+Z2/gG/br4cBOYASw1D4HBwIbgGLgOOADLGmME4B59ue+DfwDEOA7wL+y/b1ScF5Osr/3I8B+7fl8AH8Dbre/T1l7PR9AVyyJlk5ABbASS3K9hZ1I9hzl279C8+xPAt5T1fWqugl4Bzg+y2NKOaq6SVWft18vBZqA84BnVXWPqi4GVgHjsQrHPK6qTar6JlAlIr0psIIydt2FnwJ3201n007Ph/19jgB+qRZ1tN/zsQ/YBXQAyoEdWOcmmp1I9hzlFYVm7INFUmzWAX2yNJaMICKXAZ9jCdZF++6R52R9ZLvGKSiTR9wL/B7rKQdiXwvt4XwchOXBPm+HI35LOz0fqlqP9aS3yv73F6Av3s5FonOUVxSasfdUJKVQEJFbgGuBC4n93ZNtzztslVVV1Wddze32fAA9scIQ1wDjgCOBM2iH50NExgGXY9W/Hgh8Hyuc0+7OhReJ43xiI/A11/v+wCfZGUp6EZEHgI7AkapaKyLBAjE2/bHikpHtfbE8Fqd9eQEUlLkG6CoiS4AuWI/rFVjf0aE9nY8twGxVXQcgIm9iGaf2eH1MAV5X1e0AIvI6Vk3tyHPxCVZ8P5lzlFcUmmf/BnCSiPR0xS3fyPKYUo6ITAJGqOq37EdssArEnCciHUTkQKywzjy7/VIR8YvICcBS+8IvmIIyapWyHKaqI4FbgOew6ii0y/MBzAQOFJG+IlKKZfBqaJ/nYwkwWUTKRKQTVmx+OdHtRLK/obyioDx7Vd0kIrcBH9tNN6jq3myOKU0cAkyQ8OIxVwN/BxYBdcAVqqoi8iJwLFZGwjbgArt/QReUUdXZItIuz4eq7hWRa7BKhJZiTS7+zjb87ep8qOq/RWQ08KXd9KSqPiUixbS0E8leM3mF0cYxGAyGdkChhXEMBoPBEAVj7A0Gg6EdYIy9wWAwtAOMsTcYDIZ2gDH2BoPB0A4wxt5gMBjaAcbYGwwGQzvAGHuDwWBoB/x/f8cuclfA47sAAAAASUVORK5CYII=", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df.plot(y='close');"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is also a utility function to plot bars as a candlestick plot. It can accept either a DataFrame or a list of bars. Here it will print the last 100 bars:"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:06:32.382542Z", "start_time": "2024-04-10T07:06:31.994051Z"}, "scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["util.barplot(bars[-100:], title=contract.symbol);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Historical data with realtime updates\n", "\n", "A new feature of the API is to get live updates for historical bars. This is done by setting `endDateTime` to an empty string and the `keepUpToDate` parameter to `True`.\n", "\n", "Let's get some bars with an keepUpToDate subscription:"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:07:52.549074Z", "start_time": "2024-04-10T07:07:52.107168Z"}}, "outputs": [], "source": ["# contract = Forex('EURUSD')\n", "# contract = Stock('TSLA', 'SMART', 'USD')\n", "contract = Future('ES', '202409', 'CME')\n", "bars = ib.reqHistoricalData(\n", "        contract,\n", "        endDateTime='',\n", "        durationStr='900 S',\n", "        barSizeSetting='10 secs',\n", "        whatToShow='MIDPOINT',\n", "        useRTH=True,\n", "        formatDate=1,\n", "        keepUpToDate=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Replot for every change of the last bar:"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:03.969650Z", "start_time": "2024-04-10T07:07:53.937492Z"}, "scrolled": true}, "outputs": [], "source": ["from IPython.display import display, clear_output\n", "import matplotlib.pyplot as plt\n", "\n", "def onBarUpdate(bars, hasNewBar):\n", "    plt.close()\n", "    plot = util.barplot(bars)\n", "    clear_output(wait=True)\n", "    display(plot)\n", "\n", "bars.updateEvent += onBarUpdate\n", "\n", "ib.sleep(10)\n", "ib.cancelHistoricalData(bars)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Realtime bars\n", "------------------\n", "\n", "With ``reqRealTimeBars`` a subscription is started that sends a new bar every 5 seconds.\n", "\n", "First we'll set up a event handler for bar updates:"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:07.076916Z", "start_time": "2024-04-10T07:08:07.058915Z"}}, "outputs": [], "source": ["def onBarUpdate(bars, hasNewBar):\n", "    print(bars[-1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then do the real request and connect the event handler,"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["bars = ib.reqRealTimeBars(contract, 5, 'MIDPOINT', False)\n", "bars.updateEvent += onBarUpdate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["let it run for half a minute and then cancel the realtime bars."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error 162, reqId 5: Historical Market Data Service error message:API historical data query cancelled: 5\n"]}, {"name": "stdout", "output_type": "stream", "text": ["RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 5, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.122925, high=1.122975, low=1.122925, close=1.122975, volume=-1, wap=-1.0, count=-1)\n", "RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 10, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.122975, high=1.122975, low=1.122975, close=1.122975, volume=-1, wap=-1.0, count=-1)\n", "RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 15, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.122975, high=1.122975, low=1.122975, close=1.122975, volume=-1, wap=-1.0, count=-1)\n", "RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 20, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.122975, high=1.123025, low=1.122975, close=1.123025, volume=-1, wap=-1.0, count=-1)\n", "RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 25, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.123025, high=1.123025, low=1.122975, close=1.122975, volume=-1, wap=-1.0, count=-1)\n", "RealTimeBar(time=datetime.datetime(2019, 12, 31, 12, 27, 30, tzinfo=datetime.timezone.utc), endTime=-1, open_=1.122975, high=1.122975, low=1.122975, close=1.122975, volume=-1, wap=-1.0, count=-1)\n"]}], "source": ["ib.sleep(30)\n", "ib.cancelRealTimeBars(bars)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The advantage of reqRealTimeBars is that it behaves more robust when the connection to the IB server farms is interrupted. After the connection is restored, the bars from during the network outage will be backfilled and the live bars will resume.\n", "\n", "reqHistoricalData + keepUpToDate will, at the moment of writing, leave the whole API inoperable after a network interruption."]}, {"cell_type": "code", "execution_count": 43, "metadata": {"ExecuteTime": {"end_time": "2024-04-10T07:08:12.038481Z", "start_time": "2024-04-10T07:08:12.029973Z"}}, "outputs": [], "source": ["ib.disconnect()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}