{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:25:30.413220700Z", "start_time": "2023-08-25T03:25:30.341190200Z"}}, "source": ["from datetime import datetime\n", "from time import sleep\n", "\n", "import pandas as pd\n", "from vnpy.trader.utility import load_json\n", "from vnpy_ib import IbGateway\n", "from vnpy_scripttrader import init_cli_trading\n", "from vnpy_scripttrader.cli import process_log_event\n", "\n", "setting = load_json(\"connect_ib.json\")\n", "engine = init_cli_trading([IbGateway])  #返回Script_engine 示例，并且给main_engine注册了gateway\n", "engine.connect_gateway(setting, \"IB\")  #链接"], "outputs": []}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:25:42.588501200Z", "start_time": "2023-08-25T03:25:32.570166500Z"}}, "source": ["# 查询资金 - 自动\n", "sleep(10)\n", "print(\"***查询资金和持仓***\")\n", "print(engine.get_all_accounts(use_df=True))"], "outputs": []}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:25:48.270342300Z", "start_time": "2023-08-25T03:25:48.242078700Z"}}, "source": ["# 查询持仓\n", "print(engine.get_all_positions(use_df=True))"], "outputs": []}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:25:50.103796700Z", "start_time": "2023-08-25T03:25:50.074796900Z"}}, "source": ["# 订阅行情\n", "from vnpy.trader.constant import Exchange\n", "from vnpy.trader.object import SubscribeRequest\n", "\n", "# 从我测试直接用Script_engine有问题，IB的品种太多，get_all_contracts命令不行,需要指定具体后才可以，这里使用main_engine订阅\n", "req1 = SubscribeRequest(\"F-USD-STK\", Exchange.SMART)  #创建行情订阅\n", "engine.main_engine.subscribe(req1, \"IB\")"], "outputs": []}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2023-08-25T03:28:04.709218900Z", "start_time": "2023-08-25T03:28:04.697212400Z"}}, "source": ["# 使用script_engine订阅历史数据是从rqdata获取，vnpy v2.07已经提供历史数据获取，这里创建HistoryRequest来获取,\n", "# 查询如果没有endtime，默认当前。返回历史数据输出到数据库和csv文件\n", "# 关于api更多信息可以参见 https://interactivebrokers.github.io/tws-api/historical_bars.html\n", "print(\"***从IB读取历史数据, 返回历史数据输出到数据库和csv文件***\")\n", "from vnpy.trader.object import HistoryRequest\n", "from vnpy.trader.object import Interval\n", "import zoneinfo\n", "\n", "start = datetime(2023, 5, 10, 0, 43, tzinfo=zoneinfo.ZoneInfo(key='UTC'))\n", "end = datetime(2023, 5, 11, 11, 11, tzinfo=zoneinfo.ZoneInfo(key='UTC'))\n", "start"], "outputs": []}, {"cell_type": "code", "execution_count": 12, "source": ["\n", "historyreq = HistoryRequest(\n", "    symbol=\"F-USD-STK\",\n", "    exchange=Exchange.SMART,\n", "    start=start,\n", "    end=end,\n", "    interval=Interval.MINUTE\n", ")\n", "bardatalist = engine.main_engine.query_history(historyreq, \"IB\")\n", "print(bardatalist)"], "metadata": {"collapsed": false, "ExecuteTime": {"start_time": "2023-08-25T03:25:52.934566100Z"}}, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 把历史数据BarData输出到csv\n", "pd.DataFrame(bardatalist).to_csv(str(historyreq.symbol) + \".csv\", index=True, header=True)\n", "print(\"History data export to CSV\")"], "outputs": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "source": ["# # 读取历史数据，并把历史数据BarData放入数据库\n", "from vnpy.trader.database import get_database\n", "\n", "database_manager = get_database()\n", "database_manager.save_bar_data(bardatalist)"], "outputs": []}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "source": ["\n", "# # 参考backtesting.ipynb, 使用自带的双均线策略回测，10日上穿60日做多，否则反之\n", "print(\"***从数据库读取历史数据, 进行回测***\")\n", "from vnpy_ctastrategy.backtesting import BacktestingEngine\n", "from vnpy_ctastrategy.strategies.double_ma_strategy import (\n", "    DoubleMaStrategy,\n", ")\n", "\n", "btengine = BacktestingEngine()  #新建回测引擎\n", "btengine.set_parameters(\n", "    vt_symbol=\"EUR-USD-CASH.IDEALPRO\",\n", "    interval=\"1m\",\n", "    start=datetime(2023, 1, 1),\n", "    end=datetime(2024, 1, 1),\n", "    rate=0,\n", "    slippage=0.00005,\n", "    size=1000,\n", "    pricetick=0.00005,\n", "    capital=1_000_000,\n", ")\n", "btengine.add_strategy(DoubleMaStrategy, {\"fast_window\": 10, \"slow_window\": 60})\n", "\n", "btengine.load_data()\n", "btengine.run_backtesting()\n", "df = btengine.calculate_result()\n", "btengine.calculate_statistics()\n", "btengine.show_chart()"], "outputs": []}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "source": ["# 给script_engine载入双均线策略，实盘运行\n", "print(\"***从数据库读取准备数据, 实盘运行***\")\n", "# 使用cta交易引擎\n", "from vnpy_ctastrategy import CtaStrategyApp\n", "from vnpy_ctastrategy.base import EVENT_CTA_LOG\n", "\n", "engine.event_engine.register(EVENT_CTA_LOG, process_log_event)\n", "cta_engine = engine.main_engine.add_app(CtaStrategyApp)  #加入app\n", "cta_engine.init_engine()\n", "cta_engine.add_strategy(\"DoubleMaStrategy\", \"DoubleMaStrategy_IB_12087792_v1\", \"EUR-USD-CASH.IDEALPRO\",\n", "                        {\"fast_window\": 10, \"slow_window\": 50})\n", "sleep(10)\n", "cta_engine.init_strategy(\"DoubleMaStrategy_IB_12087792_v1\")\n", "sleep(10)\n", "cta_engine.start_strategy(\"DoubleMaStrategy_IB_12087792_v1\")"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "1b43cb0bd93d5abbadd54afed8252f711d4681fe6223ad6b67ffaee289648f85"}}}, "nbformat": 4, "nbformat_minor": 2}