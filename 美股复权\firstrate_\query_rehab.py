import os
import sys
import csv
import re
from datetime import datetime
from loguru import logger
from peewee import fn
from vnpy.trader.utility import ZoneInfo

# 添加项目根目录到Python路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(file_path)

from 美股复权.utils.database_manager import FutuRehab, db_manager

# 常量定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时区
START_DATE = datetime(2022, 6, 1, tzinfo=ET_TZ)
SCANNER_FILE = 'scanner_unique_stk_us_all.csv'
from vnpy.trader.utility import get_file_path
SCANNER_FILE = get_file_path(SCANNER_FILE)
OUTPUT_FILE = get_file_path("rehab_records.csv")

def ib_to_futu_symbol(ib_symbol: str) -> str:
    """将IB符号转换为富途符号格式"""
    # return ib_symbol.replace(' ', '.')
    return f'US.{ib_symbol.replace(' ', '.')}'

def load_ib_symbols():
    """从CSV加载IB符号并转换为富途符号"""
    symbol_map = {}
    if not os.path.exists(SCANNER_FILE):
        logger.error(f"扫描器文件不存在: {SCANNER_FILE}")
        return symbol_map
    
    with open(SCANNER_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            conid = row['conId']
            ib_symbol = row['symbol']
            futu_symbol = ib_to_futu_symbol(ib_symbol)
            symbol_map[conid] = futu_symbol
    
    logger.info(f"已加载 {len(symbol_map)} 个标的映射")
    return symbol_map

def query_rehab_records(symbol_map: dict):
    """查询符合条件的复权记录"""
    # 获取所有富途符号
    futu_symbols = list(set(symbol_map.values()))
    
    # 构建查询条件
    rehab_query = (
        FutuRehab.select(
            FutuRehab.code,
            FutuRehab.ex_div_date,
            FutuRehab.split_ratio,
            FutuRehab.per_share_div_ratio,
            FutuRehab.per_cash_div,
            FutuRehab.special_dividend
        )
        .where(
            (FutuRehab.code.in_(futu_symbols)) &
            (FutuRehab.ex_div_date >= START_DATE) &
            (
                (FutuRehab.split_ratio.is_null(False)) | 
                (FutuRehab.per_share_div_ratio.is_null(False))
            ) &
            (
                (FutuRehab.per_cash_div.is_null(False)) | 
                (FutuRehab.special_dividend.is_null(False))
            )
        )
        .order_by(FutuRehab.code, FutuRehab.ex_div_date)
    )
    
    # 添加conid映射
    records = []
    for rehab in rehab_query:
        # 查找对应的conid
        conids = [cid for cid, fsym in symbol_map.items() if fsym == rehab.code]
        for conid in conids:
            records.append({
                'conid': conid,
                'futu_symbol': rehab.code,
                'ex_div_date': rehab.ex_div_date,
                'split_ratio': rehab.split_ratio,
                'per_share_div_ratio': rehab.per_share_div_ratio,
                'per_cash_div': rehab.per_cash_div,
                'special_dividend': rehab.special_dividend
            })
    
    logger.info(f"找到 {len(records)} 条符合条件的复权记录")
    return records

def save_to_csv(records: list):
    """保存结果到CSV文件"""
    if not records:
        logger.warning("没有记录可保存")
        return
    
    with open(OUTPUT_FILE, 'w', encoding='utf-8', newline='') as f:
        fieldnames = records[0].keys()
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(records)
    
    logger.success(f"结果已保存至: {OUTPUT_FILE}")

if __name__ == "__main__":
    # 确保数据库连接
    db_manager.common_db.connect()
    
    try:
        # 1. 加载IB符号映射
        symbol_map = load_ib_symbols()
        
        # 2. 查询复权记录
        records = query_rehab_records(symbol_map)
        
        # 3. 保存结果
        save_to_csv(records)
    except Exception as e:
        logger.error(f"执行出错: {e}")
    finally:
        db_manager.common_db.close()