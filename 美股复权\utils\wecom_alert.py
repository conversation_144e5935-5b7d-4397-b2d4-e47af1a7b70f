#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
企业微信机器人告警推送工具

使用方法:
from wecom_alert import report_we_alert, WecomAlertManager

# 基础文本消息（向后兼容）
success, msg = report_we_alert(
    text="测试消息",
    key="your-wecom-key-here"
)

# Markdown_v2消息（支持更丰富的格式）
success, msg = report_we_alert(
    text="",  # 可以为空
    key="your-wecom-key-here",
    markdown_v2_content="# 标题\n**加粗文本**\n- 列表项\n> 引用内容"
)

# 使用消息管理器（文本模式）
manager = WecomAlertManager()
manager.start()  # 启动消息处理线程
manager.add_message("测试消息")  # 添加消息到队列
manager.stop()  # 停止消息处理线程

# 使用消息管理器（markdown_v2模式）
manager = WecomAlertManager(use_markdown_v2=True)
manager.start()
manager.add_message("# 标题\n**重要通知**")
manager.stop()
"""

import traceback
import requests
import queue
import threading
import time
from typing import Optional, List
from collections import deque
import os
from loguru import logger
log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
logger.add(
    f"logs/{log_file_name}",
    level=0, # TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
    format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="00:00", # rotation="10 MB"
    filter=__name__
)
# retention="30 days" # 保留30天的日志


import urllib.request, os

def get_system_proxy() -> dict:
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}

    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            os.environ[f"{protocol}_proxy"] = proxy

    return proxies

proxies = get_system_proxy()

def report_we_alert(
    text: str,
    key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459",
    msgtype: Optional[str] = None,
    markdown_v2_content: Optional[str] = None
) -> tuple[bool, str]:
    """
    发送消息到企业微信机器人，发送的消息不能超过20条/分钟。

    参数:
        text (str): 要发送的文本消息（向后兼容）
        key (str): 企业微信机器人的webhook key
        msgtype (str, optional): 消息类型，支持 "text" 或 "markdown_v2"
        markdown_v2_content (str, optional): markdown_v2内容，最长4096字节

    返回:
        tuple[bool, str]: (是否成功, 成功/错误消息)
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"

    # 确定消息类型，向后兼容
    if msgtype is None:
        msgtype = "markdown_v2" if markdown_v2_content is not None else "text"

    # 构建payload
    if msgtype == "markdown_v2":
        content = markdown_v2_content if markdown_v2_content is not None else text
        payload = {
            "msgtype": "markdown_v2",
            "markdown_v2": {
                "content": content  # markdown_v2内容，最长不超过4096个字节，必须是utf8编码
            }
        }
    else:  # 默认text类型
        payload = {
            "msgtype": "text",
            "text": {
                "content": text  # 文本内容，最长不超过2048个字节，必须是utf8编码
            }
        }
    
    try:
        response = requests.post(url, json=payload, proxies=proxies)
        
        if response.status_code == 200:
            response_json = response.json()
            
            if response_json.get("errcode") == 0:
                return True, response_json.get("errmsg", "操作成功")
            else:
                return False, response_json.get("errmsg", "未知错误")
        else:
            return False, f"HTTP错误: {response.status_code}，{response.text}"
            
    except requests.RequestException as e:
        return False, f"请求异常: {str(e)}"
    except Exception as e:
        return False, f"未知错误: {str(e)}\n{traceback.format_exc()}"

class WecomAlertManager:
    """企业微信消息管理器，处理消息队列和限流"""

    def __init__(self, key: str = "ee0b6801-f2c5-4811-ba1f-227b543b3459", interval: int = 10, use_markdown_v2: bool = False):
        """初始化消息管理器

        Args:
            key: 企业微信机器人的webhook key
            interval: 消息处理间隔（秒），默认10秒
            use_markdown_v2: 是否使用markdown_v2格式发送消息
        """
        self.message_queue = deque()
        self.queue_lock = threading.Lock()  # deque 需要锁保护
        self.is_stopping = False
        self.message_thread = None
        self.key = key
        self.interval = interval  # 消息处理间隔
        self.use_markdown_v2 = use_markdown_v2
        self.max_bytes = 4096 if use_markdown_v2 else 2048  # 消息大小限制
        
    def start(self):
        """启动消息处理线程"""
        if self.message_thread is None or not self.message_thread.is_alive():
            self.is_stopping = False
            self.message_thread = threading.Thread(target=self._process_messages)
            self.message_thread.daemon = True
            self.message_thread.start()
            
    def stop(self):
        """停止消息处理线程，等待所有消息处理完成"""
        self.is_stopping = True
        if self.message_thread:
            self.message_thread.join()
            
    def add_message(self, message: str):
        """添加消息到队列，自动处理超长消息"""
        logger.debug(f"add_message 开始处理消息，长度: {len(message)} 字符")

        if not message.strip():
            logger.debug("add_message 消息为空，跳过")
            return

        # 处理超长消息，无需后缀标记
        logger.debug("add_message 开始分割消息")
        message_parts = self._split_message(message)
        logger.debug(f"add_message 消息分割完成，共 {len(message_parts)} 部分")

        logger.debug("add_message 等待队列锁")
        with self.queue_lock:
            logger.debug("add_message 获得队列锁，开始添加消息")
            for i, part in enumerate(message_parts):
                self.message_queue.append(part)
                logger.debug(f"add_message 添加第 {i+1}/{len(message_parts)} 部分到队列")
            logger.debug(f"add_message 完成，队列当前大小: {len(self.message_queue)}")
        logger.debug("add_message 释放队列锁")

    def _split_message(self, text: str) -> List[str]:
        """智能分割消息，优先按行分割，必要时按字符分割"""
        text_bytes = len(text.encode('utf-8'))
        logger.debug(f"_split_message 开始，文本字节数: {text_bytes}, 限制: {self.max_bytes}")

        if text_bytes <= self.max_bytes:
            logger.debug("_split_message 文本未超限，直接返回")
            return [text]

        # 检测并提取表头信息
        table_header = self._extract_table_header(text)
        logger.debug(f"_split_message 提取表头: {len(table_header)} 字节" if table_header else "_split_message 未检测到表格")

        parts = []
        remaining = text
        part_count = 0

        while remaining:
            part_count += 1
            remaining_bytes = len(remaining.encode('utf-8'))
            logger.debug(f"_split_message 处理第 {part_count} 部分，剩余字节: {remaining_bytes}")

            if remaining_bytes <= self.max_bytes:
                # 为最后一部分添加表头（如果需要）
                final_part = self._add_table_header_if_needed(remaining, table_header, part_count > 1)
                parts.append(final_part)
                logger.debug(f"_split_message 剩余部分未超限，完成分割")
                break

            # 截断当前部分（为续段预留表头空间）
            logger.debug(f"_split_message 开始智能截断第 {part_count} 部分")
            truncated, new_remaining = self._truncate_smart(remaining, table_header)
            logger.debug(f"_split_message 截断完成，截断部分字节: {len(truncated.encode('utf-8'))}")

            # 防止无限循环：如果截断结果为空，强制按字符截断
            if not truncated:
                logger.warning(f"表格保护导致截断为空，强制按字符截断")
                truncated, new_remaining = self._truncate_by_chars(remaining)
                logger.debug(f"_split_message 字符截断完成，截断部分字节: {len(truncated.encode('utf-8'))}")

            # 再次检查：如果还是为空，说明有严重问题
            if not truncated:
                logger.error(f"字符截断也失败，剩余字节: {len(remaining.encode('utf-8'))}")
                raise ValueError("无法截断消息，可能存在单个字符超过字节限制的情况")

            # 为截断部分添加表头（如果需要）
            final_truncated = self._add_table_header_if_needed(truncated, table_header, part_count > 1)
            parts.append(final_truncated)
            remaining = new_remaining

        logger.debug(f"_split_message 完成，共分割为 {len(parts)} 部分")
        return parts

    def _truncate_smart(self, text: str, table_header: str = "") -> tuple[str, str]:
        """智能截断：优先按行，保护表格，为续段预留表头空间"""
        lines = text.split('\n')
        logger.debug(f"_truncate_smart 开始，共 {len(lines)} 行")

        # 计算实际可用空间（为续段预留表头空间）
        header_bytes = len(table_header.encode('utf-8')) + 1 if table_header else 0  # +1 for newline
        effective_max_bytes = self.max_bytes - header_bytes

        if effective_max_bytes <= 0:
            logger.warning("表头过大，无法进行智能截断")
            return self._truncate_by_chars(text)

        if header_bytes > 0:
            logger.debug(f"_truncate_smart 为表头预留 {header_bytes} 字节，实际限制: {effective_max_bytes}")

        truncated_lines = []
        current_bytes = 0

        # 尝试按行收集
        for i, line in enumerate(lines):
            line_with_newline = line + '\n' if i < len(lines) - 1 else line
            line_bytes = len(line_with_newline.encode('utf-8'))

            if current_bytes + line_bytes <= effective_max_bytes:
                truncated_lines.append(line)
                current_bytes += line_bytes
            else:
                # 当前行放不下
                logger.debug(f"_truncate_smart 第 {i+1} 行放不下，已收集 {len(truncated_lines)} 行")
                if truncated_lines:
                    # 有已收集的行，检查表格保护
                    logger.debug("_truncate_smart 开始表格保护检查")
                    final_lines, remaining_lines = self._protect_table_at_break(
                        truncated_lines, lines[i:]
                    )
                    logger.debug("_truncate_smart 表格保护完成，按行截断")
                    return '\n'.join(final_lines), '\n'.join(remaining_lines)
                else:
                    # 第一行就超长，按字符强制截断
                    logger.debug("_truncate_smart 第一行就超长，使用字符截断")
                    return self._truncate_by_chars(text)

        # 所有行都能放下
        logger.debug("_truncate_smart 所有行都能放下")
        return text, ""

    def _protect_table_at_break(self, truncated_lines: List[str], remaining_lines: List[str]) -> tuple[List[str], List[str]]:
        """在截断点保护表格完整性"""
        if not truncated_lines or not remaining_lines:
            return truncated_lines, remaining_lines

        last_line = truncated_lines[-1].strip()
        next_line = remaining_lines[0].strip()

        # 检查是否在表头和分隔行之间截断
        if (next_line.startswith('|') and '---' in next_line and
            last_line.startswith('|') and last_line.endswith('|')):
            # 将表头+分隔行都移到下一部分，确保表格完整性
            if len(truncated_lines) > 1:
                # 表头+分隔行的完整内容
                header_separator = last_line + '\n' + next_line
                header_separator_bytes = len(header_separator.encode('utf-8'))

                # 如果表头+分隔行不超限，则移动
                if header_separator_bytes <= self.max_bytes:
                    logger.debug("表格保护：将表头+分隔行移到下一部分")
                    return truncated_lines[:-1], [last_line] + remaining_lines

        # 检查是否在表格数据中截断，需要保护完整的表头结构
        if (last_line.startswith('|') and last_line.endswith('|') and
            next_line.startswith('|') and next_line.endswith('|') and
            '---' not in last_line and '---' not in next_line):

            # 向前查找完整的表头结构（表头+分隔行）
            separator_idx = -1
            header_idx = -1

            for i in range(len(truncated_lines) - 1, -1, -1):
                line = truncated_lines[i].strip()
                if line.startswith('|') and '---' in line:
                    separator_idx = i
                    # 检查分隔行前面是否有表头
                    if i > 0:
                        prev_line = truncated_lines[i-1].strip()
                        if prev_line.startswith('|') and prev_line.endswith('|') and '---' not in prev_line:
                            header_idx = i - 1
                    break

            # 如果找到完整的表头结构，将其移到下一部分
            if header_idx >= 0 and separator_idx >= 0 and header_idx < len(truncated_lines) - 1:
                table_structure = truncated_lines[header_idx:]
                table_content = '\n'.join(table_structure)
                table_bytes = len(table_content.encode('utf-8'))

                # 确保表头结构不超限，且移动后不会导致空的截断部分
                if table_bytes <= self.max_bytes and header_idx > 0:
                    logger.debug(f"表格保护：将表头结构（{len(table_structure)}行）移到下一部分")
                    return truncated_lines[:header_idx], table_structure + remaining_lines

        return truncated_lines, remaining_lines

    def _extract_table_header(self, text: str) -> str:
        """从文本中提取表格表头（表头行+分隔行），如果有多张表则取最后一个"""
        lines = text.split('\n')
        last_header = ""

        for i, line in enumerate(lines):
            line = line.strip()
            # 查找分隔行
            if line.startswith('|') and '---' in line and i > 0:
                # 检查前一行是否是表头
                prev_line = lines[i-1].strip()
                if prev_line.startswith('|') and prev_line.endswith('|') and '---' not in prev_line:
                    # 找到完整的表头结构，保存为最后一个
                    last_header = prev_line + '\n' + line
                    logger.debug(f"发现表头: {len(last_header)} 字节")

        if last_header:
            logger.debug(f"提取最后一个表头: {len(last_header)} 字节")
        else:
            logger.debug("未发现表格表头")

        return last_header

    def _add_table_header_if_needed(self, content: str, table_header: str, is_continuation: bool) -> str:
        """为内容添加表头（如果需要且是续段）"""
        if not table_header or not is_continuation:
            return content

        lines = content.split('\n')
        # 检查内容是否包含表格数据（但没有表头）
        has_table_data = False
        has_header = False

        for line in lines:
            line_stripped = line.strip()
            if line_stripped.startswith('|') and line_stripped.endswith('|'):
                if '---' in line_stripped:
                    has_header = True  # 已有分隔行，说明有表头
                    break
                else:
                    has_table_data = True  # 有表格数据行

        # 如果有表格数据但没有表头，则添加表头
        if has_table_data and not has_header:
            new_content = table_header + '\n' + content
            new_content_bytes = len(new_content.encode('utf-8'))

            # 由于截断时已经预留了表头空间，这里应该不会超限
            if new_content_bytes <= self.max_bytes:
                logger.debug("为续段添加表头")
                return new_content
            else:
                logger.error(f"添加表头后超限: {new_content_bytes} > {self.max_bytes}，预留空间计算有误")
                return content

        return content

    def _truncate_by_chars(self, text: str) -> tuple[str, str]:
        """按字符强制截断，确保UTF-8安全"""
        if len(text.encode('utf-8')) <= self.max_bytes:
            return text, ""

        # 二分查找最大安全长度
        left, right = 0, len(text)
        while left < right:
            mid = (left + right + 1) // 2
            if len(text[:mid].encode('utf-8')) <= self.max_bytes:
                left = mid
            else:
                right = mid - 1

        if left == 0:
            raise ValueError(f"单个字符超过字节限制: {self.max_bytes}")

        return text[:left], text[left:]



    def _collect_messages(self) -> List[str]:
        """从队列中收集消息，确保拼接后不超过大小限制"""
        messages = []
        logger.debug("_collect_messages 开始收集消息")

        logger.debug("_collect_messages 等待队列锁")
        with self.queue_lock:
            logger.debug(f"_collect_messages 获得队列锁，队列大小: {len(self.message_queue)}")

            while self.message_queue:
                message = self.message_queue.popleft()
                logger.debug(f"_collect_messages 取出消息，长度: {len(message)} 字符")

                # 模拟添加这条消息后的完整内容
                test_messages = messages + [message]
                test_content = "\n".join(test_messages)
                test_bytes = len(test_content.encode('utf-8'))

                if test_bytes > self.max_bytes:
                    # 拼接后会超限，放回队列前面并结束收集
                    logger.debug(f"_collect_messages 拼接后超限 ({test_bytes} > {self.max_bytes})，放回消息")
                    self.message_queue.appendleft(message)
                    break

                # 可以安全添加
                messages.append(message)
                logger.debug(f"_collect_messages 添加消息，当前批次: {len(messages)} 条")

            logger.debug(f"_collect_messages 收集完成，共 {len(messages)} 条消息，队列剩余: {len(self.message_queue)}")

        logger.debug("_collect_messages 释放队列锁")
        return messages

    def _send_messages(self, messages: List[str]) -> bool:
        """发送消息列表"""
        if not messages:
            return True

        content = "\n".join(messages)

        try:
            if self.use_markdown_v2:
                success, error_msg = report_we_alert("", self.key, markdown_v2_content=content)
            else:
                success, error_msg = report_we_alert(content, self.key)

            if not success:
                logger.warning(f"发送消息失败: {error_msg}")

            return success

        except Exception as e:
            logger.error(f"发送消息异常: {str(e)}")
            return False

    def _process_messages(self):
        """处理消息队列"""
        retry_messages = None  # 存储需要重试的消息批次
        logger.info("消息处理线程启动")

        while True:
            logger.debug(f"消息处理循环开始，等待 {self.interval} 秒")
            time.sleep(self.interval)

            # 优先处理重试消息
            if retry_messages:
                logger.info(f"处理重试消息，共 {len(retry_messages)} 条")
                success = self._send_messages(retry_messages)
                if success:
                    logger.info("重试消息发送成功")
                    retry_messages = None  # 重试成功，清空
                else:
                    logger.warning("重试消息发送失败，继续重试")
                    # 重试仍然失败，继续下次重试（不改变消息内容和顺序）
                    continue
            else:
                # 收集新消息
                logger.debug("开始收集新消息")
                messages = self._collect_messages()
                if messages:
                    logger.info(f"收集到 {len(messages)} 条新消息，开始发送")
                    success = self._send_messages(messages)
                    if not success:
                        # 发送失败，保存这批消息用于重试
                        logger.warning(f"新消息发送失败，加入重试队列")
                        retry_messages = messages
                else:
                    logger.debug("没有新消息需要处理")
            
            # 如果正在停止，检查是否还有待处理的消息
            if self.is_stopping:
                with self.queue_lock:
                    queue_empty = not self.message_queue
                    queue_size = len(self.message_queue)

                if queue_empty and not retry_messages:
                    logger.info("消息处理线程正常退出，所有消息已处理完成")
                    break
                else:
                    # 仍有消息未发出，记录日志但继续处理
                    retry_count = len(retry_messages) if retry_messages else 0
                    logger.warning(f"收到停止信号但仍有消息未发出 - 队列中消息数: {queue_size}, 重试消息数: {retry_count}")
                    # 不break，继续处理剩余消息

if __name__ == "__main__":
    # 测试示例 - 向后兼容性测试
    print("=== 向后兼容性测试 ===")
    success, msg = report_we_alert("这是一条测试消息")
    print(f"基础文本消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试markdown_v2功能
    print("\n=== Markdown_v2功能测试 ===")
    markdown_content = """# 测试标题
## 二级标题
**加粗文本** 和 *斜体文本*

### 列表示例
- 无序列表 1
- 无序列表 2
  - 子列表 2.1
  - 子列表 2.2

1. 有序列表 1
2. 有序列表 2

### 引用和代码
> 这是一个引用
>> 二级引用

`行内代码`

```
代码块示例
def hello():
    print("Hello World")
```

### 链接和分割线
[企业微信官网](https://work.weixin.qq.com)

---

### 表格
| 姓名 | 部门 | 状态 |
| :--- | :---: | ---: |
| 张三 | 开发 | 在线 |
| 李四 | 测试 | 离线 |
"""

    success, msg = report_we_alert(
        text="",
        markdown_v2_content=markdown_content
    )
    print(f"Markdown_v2消息 - 发送结果: {'成功' if success else '失败'}")
    print(f"返回信息: {msg}")

    # 测试WecomAlertManager的markdown_v2模式
    print("\n=== WecomAlertManager Markdown_v2测试 ===")
    manager = WecomAlertManager(use_markdown_v2=True)
    manager.start()

    # 添加几条markdown格式的消息
    manager.add_message("# 第一条消息\n**重要通知**：系统维护开始")
    manager.add_message("# 第二条消息\n- 维护项目1\n- 维护项目2")
    manager.add_message("# 第三条消息\n> 预计维护时间：2小时")

    manager.stop()
    print("WecomAlertManager测试完成")
