import json

# 读取conids.txt
with open('conids.txt', 'r', encoding='utf-8') as f:
    conids = f.read().strip().split(',')

# 构造bar字典
bar = {
    f"{conid}.SMART": {
        "symbol": int(conid),
        "exchange": "SMART",
        "gateway_name": "IB"
    }
    for conid in conids
}

# 构造最终结构
result = {
    "tick": {},
    "bar": bar
}

# 写入data_recorder_setting_ib.json
with open('data_recorder_setting_ib.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=4)

print("已生成新的 data_recorder_setting_ib.json")