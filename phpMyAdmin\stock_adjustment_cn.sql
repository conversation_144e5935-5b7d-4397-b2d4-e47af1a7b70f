SET GLOBAL time_zone = 'Asia/Shanghai';

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `stock_adjustment_cn`;
CREATE TABLE `stock_adjustment_cn` (
  `id` int NOT NULL AUTO_INCREMENT,
  `old_symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `new_symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `exchange` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,  -- 交易所字段
  `old_price` DOUBLE NOT NULL,
  `new_price` DOUBLE NOT NULL,
  `price_diff` DOUBLE AS (`new_price` - `old_price`) STORED,  -- 价差字段（生成列）
  `ex_date` date NOT NULL,  -- 除权日，未来的日期
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
