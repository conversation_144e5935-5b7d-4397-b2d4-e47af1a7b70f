import configparser
from datetime import datetime

from vnpy.trader.constant import Interval
from vnpy.trader.database import get_database, DB_TZ
from vnpy.trader.datafeed import get_datafeed
from vnpy.trader.object import HistoryRequest
from vnpy.trader.utility import extract_vt_symbol

# Initialize the configparser
config = configparser.ConfigParser()

# Read the INI file
config.read('download.ini')

# Initialize the database
db = get_database()
dfeed = get_datafeed()

# Iterate through each contract section
for contract_section in config.sections():
    contract_symbol = extract_vt_symbol(contract_section)[0]
    sub_contracts = config.get(contract_section, 'sub_contracts').split(', ')
    history_data = []
    for sub_contract in sub_contracts:
        start_key = f"{sub_contract}.start"
        end_key = f"{sub_contract}.end"
        start = config.get(contract_section, start_key)
        end = config.get(contract_section, end_key)

        # 解析为datetime格式
        start = datetime.strptime(start, '%Y-%m-%d').replace(hour=17, tzinfo=DB_TZ)
        if end == 'now':
            end = datetime.now().replace(tzinfo=DB_TZ)
        else:
            end = datetime.strptime(end, '%Y-%m-%d').replace(hour=17, tzinfo=DB_TZ)

        symbol, exchange = extract_vt_symbol(sub_contract.strip())
        print(f"downloading {symbol} {exchange} {start} {end}")
        req = HistoryRequest(symbol=symbol, exchange=exchange, interval=Interval.MINUTE, start=start, end=end, )
        bars = dfeed.query_bar_history(req)
        for bar in bars:
            bar.symbol = contract_symbol

        history_data.extend(bars)

    if history_data:
        db.save_bar_data(history_data)
