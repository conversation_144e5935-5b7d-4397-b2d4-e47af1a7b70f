import unicodedata
from openpyxl.utils import get_column_letter
from openpyxl.styles import numbers, Alignment
from openpyxl.worksheet.worksheet import Worksheet
from typing import Optional, Dict, Any

def auto_adjust_worksheet(worksheet: Worksheet, 
                          adjust_width: bool = True,
                          center_align: bool = True,
                          date_format: str = numbers.FORMAT_DATE_DATETIME,
                          date_width: int = 20,
                          extra_width: int = 2,
                          max_width: Optional[int] = None,
                          custom_column_widths: Optional[Dict[str, int]] = None,
                          column_alignments: Optional[Dict[str, str]] = None):
    """
    自动调整Excel工作表的列宽和对齐方式。
    
    Args:
        worksheet: 工作表对象
        adjust_width: 是否自动调整列宽
        center_align: 是否居中对齐（对于未指定特殊对齐方式的列）
        date_format: 日期格式
        date_width: 日期列宽度
        extra_width: 额外宽度
        max_width: 最大宽度
        custom_column_widths: 自定义列宽度 {列字母: 宽度}
        column_alignments: 自定义列对齐方式 {列字母: 'left'|'center'|'right'}
    """
    for col_idx, column in enumerate(worksheet.columns, 1):
        column_letter = get_column_letter(col_idx)
        
        if adjust_width:
            _adjust_column_width(worksheet, column, column_letter, date_format, date_width, extra_width, max_width, custom_column_widths)

        # 根据column_alignments设置对齐方式
        if column_alignments and column_letter in column_alignments:
            align = column_alignments[column_letter]
            _align_column(column, align)
        elif center_align:
            _align_column(column, 'center')

def _adjust_column_width(worksheet: Worksheet, column, column_letter: str, date_format: str, date_width: int, extra_width: int, max_width: Optional[int], custom_column_widths: Optional[Dict[str, int]]):
    if custom_column_widths and column_letter in custom_column_widths:
        worksheet.column_dimensions[column_letter].width = custom_column_widths[column_letter]
        return

    header_cell = column[0]
    is_date_column = 'date' in str(header_cell.value).lower()
    header_length = _get_column_width(str(header_cell.value))

    if is_date_column:
        _format_date_column(column, date_format)
        adjusted_width = max(date_width, header_length)
    else:
        max_length = header_length
        for cell in column[1:]:
            try:
                cell_length = _get_column_width(str(cell.value))
                if cell_length > max_length:
                    max_length = cell_length
            except:
                pass
        adjusted_width = max_length + extra_width

    if max_width:
        adjusted_width = min(adjusted_width, max_width)

    worksheet.column_dimensions[column_letter].width = adjusted_width

def _align_column(column, align: str):
    """设置列的对齐方式"""
    for cell in column:
        cell.alignment = Alignment(horizontal=align, vertical='center')

def _format_date_column(column, date_format: str):
    for cell in column:
        cell.number_format = date_format

def _get_column_width(s: str) -> int:
    """计算字符串的显示宽度，考虑中文字符和大写字母"""
    return sum(2 if (unicodedata.east_asian_width(c) in ['F', 'W'] or c.isupper()) else 1 for c in s)

def format_number(x: Any, n: int = 3) -> Any:
    """格式化数字，使用普通数字格式并保留指定有效数字"""
    if isinstance(x, (int, float)):
        negative = False
        if float(x) < 0:
            negative = True
            x = -float(x)
        # 先转换为指定有效数字
        formatted = f"{float(f'{x:.{n}g}')}"
        # 如果结果中包含科学计数法（e），则转换为完整表示的字符串
        if 'e' in formatted.lower():
            # 将科学计数法转换为完整的字符串表示
            mantissa, exponent = formatted.lower().split('e')
            exponent = int(exponent)
            if exponent > 0:
                # 正指数：向右移动小数点
                parts = mantissa.split('.')
                if len(parts) == 1:
                    result = mantissa + '0' * exponent
                else:
                    integer, decimal = parts
                    decimal = decimal + '0' * (exponent - len(decimal))
                    result = integer + decimal
            else:
                # 负指数：向左移动小数点
                parts = mantissa.split('.')
                digits = ''.join(parts)
                result = '0.' + '0' * (-exponent - 1) + digits
            if negative:
                result = '-' + result
            return result
        else:
            if negative:
                return '-' + formatted
            return formatted
    return x