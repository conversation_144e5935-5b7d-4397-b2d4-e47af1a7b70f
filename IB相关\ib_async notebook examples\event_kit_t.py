# Create an event and connect two listeners
# 创建一个事件并连接两个监听器
import eventkit as ev

def f(a, b):
    print(a * b)

def g(a, b):
    print(a / b)

event = ev.Event()
event += f
event += g
event.emit(10, 5)


# Create a simple pipeline
# 创建一个简单的管道
import eventkit as ev

event = (
    ev.Sequence('abcde')
    .map(str.upper)
    .enumerate()
)

print(event.run())  # in Jupyter: await event.list()


# Create a pipeline to get a running average and standard deviation
# 创建管道以获取运行平均值和标准差

import random
import eventkit as ev

source = ev.Range(1000).map(lambda i: random.gauss(0, 1))

event = source.array(500)[ev.ArrayMean, ev.ArrayStd].zip()

print(event.last().run())  # in Jupyter: await event.last()

# Combine async iterators together
# 将异步迭代器组合在一起

import asyncio
import eventkit as ev

async def ait(r):
    for i in r:
        await asyncio.sleep(0.1)
        yield i

async def main():
    async for t in ev.Zip(ait('XYZ'), ait('123')):
        print(t)

asyncio.get_event_loop().run_until_complete(main())  # in Jupyter: await main()

# Real-time video analysis pipeline
# 实时视频分析管道

# self.video = VideoStream(conf.CAM_ID)
# scene = self.video | FaceTracker | SceneAnalyzer
# lastScene = scene.aiter(skip_to_last=True)
# async for frame, persons in lastScene:
#     ...



# Distributed computing  分布式计算
# The distex library provides a poolmap extension method to put multiple cores or machines to use:
# distex 库提供了 poolmap 扩展方法可以使用多个核心或机器：
from distex import Pool
import eventkit as ev
import bz2

pool = Pool()
# await pool  # un-comment in Jupyter
data = [b'A' * 1000000] * 1000

pipe = ev.Sequence(data).poolmap(pool, bz2.compress).map(len).mean().last()

print(pipe.run())  # in Jupyter: print(await pipe)
pool.shutdown()