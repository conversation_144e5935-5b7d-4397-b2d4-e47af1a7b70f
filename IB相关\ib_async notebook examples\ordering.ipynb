{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ordering\n", "\n", "\n", "## Warning: This notebook will place live orders\n", "\n", "Use a paper trading account (during market hours).\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "source": ["from ib_async import *\n", "util.startLoop()\n", "\n", "ib = IB()\n", "ib.connect('127.0.0.1', 7497, clientId=13)\n", "# util.logToConsole()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a contract and a market order:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "source": ["contract = Forex('EURUSD')\n", "ib.qualifyContracts(contract)\n", "\n", "order = LimitOrder('SELL', 20000, 1.11)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["placeOrder will place the order order and return a ``Trade`` object right away (non-blocking):"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "source": ["trade = ib.placeOrder(contract, order)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["``trade`` contains the order and everything related to it, such as order status, fills and a log.\n", "It will be live updated with every status change or fill of the order."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "source": ["ib.sleep(1)\n", "trade.log"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["``trade`` will also available from ``ib.trades()``:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "source": ["assert trade in ib.trades()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Likewise for ``order``:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "source": ["assert order in ib.orders()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's create a limit order with an unrealistic limit:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "source": ["limitOrder = LimitOrder('BUY', 20000, 0.05)\n", "limitTrade = ib.placeOrder(contract, limitOrder)\n", "\n", "limitTrade"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["``status`` will change from \"PendingSubmit\" to \"Submitted\":"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "source": ["ib.sleep(1)\n", "assert limitTrade.orderStatus.status == 'Submitted'"], "outputs": []}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "source": ["assert limitTrade in ib.openTrades()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's modify the limit price and resubmit:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "source": ["limitOrder.lmtPrice = 0.10\n", "\n", "ib.placeOrder(contract, limitOrder)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["And now cancel it:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "source": ["ib.cancelOrder(limitOrder)"], "outputs": []}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "source": ["limitTrade.log"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["placeOrder is not blocking and will not wait on what happens with the order.\n", "To make the order placement blocking, that is to wait until the order is either\n", "filled or canceled, consider the following:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "source": ["%%time\n", "order = MarketOrder('BUY', 100)\n", "\n", "trade = ib.placeOrder(contract, order)\n", "while not trade.isDone():\n", "    ib.waitOnUpdate()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["What are our positions?"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": true}, "source": ["ib.positions()"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["What's the total of commissions paid today?"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "source": ["sum(fill.commissionReport.commission for fill in ib.fills())"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["whatIfOrder can be used to see the commission and the margin impact of an order without actually sending the order:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "source": ["order = MarketOrder('SELL', 20000)\n", "ib.whatIfOrder(contract, order)"], "outputs": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "source": ["ib.disconnect()"], "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.5"}}, "nbformat": 4, "nbformat_minor": 4}