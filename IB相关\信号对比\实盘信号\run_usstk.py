import multiprocessing
import signal
import platform
import sys,os,json
import shutil
from pathlib import Path
from time import sleep
from datetime import time, datetime, timedelta
from logging import INFO
from vnpy.event import EventEngine,Event
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import SubscribeRequest
from vnpy.trader.setting import SETTINGS
from vnpy.trader.engine import MainEngine as BaseMainEngine
from vnpy.trader.event import EVENT_LOG
from vnpy.trader.utility import save_json,load_json,extract_vt_symbol, ZoneInfo
try:
    from vnpy.usertools.db_status_manager import Status
except:
    from data_recorder_fake_bar.utils.db_status_manager import Status
from urllib.parse import quote_plus as urlquote
from sqlalchemy import create_engine,text
from sqlalchemy.orm import sessionmaker
import traceback
import redis
from data_recorder_fake_bar.utils.barGen_redis_engine import BarGenEngineIb
from data_recorder_fake_bar.utils.recorder_engine import RecorderEngine
from data_recorder_fake_bar.utils.ib_gateway import IbGateway
multiprocessing.set_start_method('spawn', force=True)
from data_recorder_fake_bar.utils.event import EVENT_BAR

SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True

# 交易时段配置（美东时间）
START_TIME = time(9, 25)
US_DAY_START = time(9, 30)
US_DAY_END = time(16, 0)  # 收盘后半小时再关闭
CLOSE_TIME = time(16, 30)

# 盘后定时段生成：每半小时启动10分钟，从16:35-16:45, 17:05-17:15, ..., 23:35-23:45, 00:05-00:15, ..., 08:35-08:45, 09:05-09:15
def generate_preclose_periods():
    periods = []
    base_date = datetime.today()
    # 第一段：16:35-16:45, 17:05-17:15, ..., 23:35-23:45, 23:05-23:15
    start_dt = datetime.combine(base_date, time(16, 35))
    end_dt = datetime.combine(base_date, time(23, 45))
    while start_dt <= end_dt:
        periods.append((start_dt.time(), (start_dt + timedelta(minutes=10)).time()))
        start_dt += timedelta(minutes=30)
    # 00:05-00:15, 00:35-00:45, ..., 08:05-08:15, 08:35-08:45, 09:05-09:15
    start_dt = datetime.combine(base_date, time(0, 5))
    end_dt = datetime.combine(base_date, time(9, 15))
    while start_dt <= end_dt:
        periods.append((start_dt.time(), (start_dt + timedelta(minutes=10)).time()))
        start_dt += timedelta(minutes=30)
    return periods

PRE_CLOSE_PERIODS = generate_preclose_periods()

# 配置文件
connect_trade_filename = 'connect_ib.json'
connect_quote_filename = 'connect_ib_quote.json'

userName = SETTINGS['database.user']
password =  SETTINGS['database.password']
dbHost =  SETTINGS['database.host']
dbPort = SETTINGS['database.port']
DB_CONNECT = f'mysql+pymysql://{userName}:{urlquote(password)}@{dbHost}:{dbPort}/scout?charset=utf8'
# SQLAlchemy setup
engine = create_engine(
    DB_CONNECT,
    max_overflow=50,  # 超过连接池大小外最多创建的连接
    pool_size=50,  # 连接池大小
    pool_timeout=5,  # 池中没有线程最多等待的时间，否则报错
    pool_recycle=3600,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
    echo=False
)
Session = sessionmaker(bind=engine)

def process_log_event(event: Event):
    """"""
    log = event.data
    msg = f"{log.time}\t{log.msg}"
    print(msg)

def check_trading_period(is_us_market: bool = True, extended: bool = True):
    """
    检查当前时间是否在交易时段内
    :param is_us_market: 是否美股市场，保留参数以兼容
    :param extended: 是否使用扩展交易时段（默认True，使用CLOSE_TIME，否则用US_DAY_START/US_DAY_END）
    """
    eastern = ZoneInfo('US/Eastern')
    current_time = datetime.now(eastern).time()
    if extended:
        in_main = START_TIME <= current_time <= CLOSE_TIME
        in_preclose = any(start <= current_time <= end for start, end in PRE_CLOSE_PERIODS)
        return in_main or in_preclose
    else:
        return US_DAY_START <= current_time <= US_DAY_END

class MainEngine(BaseMainEngine):
    """
    扩展MainEngine，确保所有行情订阅都通过quote网关进行
    """
    def subscribe(self, req: SubscribeRequest, gateway_name: str = "") -> None:
        """始终使用quote网关订阅行情"""
        super().subscribe(req, "quote")

    def unsubscribe(self, req: SubscribeRequest, gateway_name: str = "") -> None:
        """始终使用quote网关取消订阅行情"""
        gateway: BaseGateway = self.get_gateway("quote")
        if gateway:
            gateway.unsubscribe(req)

def run_child(stop_event=None):
    """
    Running in the child process.
    """

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    def _signal_handler(signum, frame):
        """
        子进程内的信号处理函数。
        当子进程收到 SIGINT (Ctrl+C) 或 SIGTERM 信号时调用。
        """
        main_engine.write_log(f"子进程收到信号: {signum}，准备关闭应用...")
        stop_event.set()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, _signal_handler)
    signal.signal(signal.SIGTERM, _signal_handler)
    if platform.system() == "Windows":
        signal.signal(signal.SIGBREAK, _signal_handler)
    main_engine.write_log("子进程信号处理器已注册。")
    try:
        # 添加数据记录引擎
        recorder_engine = main_engine.add_engine(RecorderEngine)
        recorder_engine.start()
        main_engine.write_log("添加数据记录引擎")

        # 添加行情网关
        quote_gateway = main_engine.add_gateway(IbGateway, "quote")
        main_engine.write_log("行情接口添加成功")
        setting = load_json(connect_quote_filename)
        quote_gateway.quote_only = 1  # 设置为仅行情模式
        # quote_gateway.use_5s_bar = False # 默认True
        main_engine.write_log(f"行情网关配置: 地址={setting['TWS地址']}, 端口={setting['TWS端口']}, 客户号={setting['客户号']}")
        quote_gateway.connect(setting)
        main_engine.write_log("行情网关连接成功 [仅行情模式]")

        # 添加交易网关
        trade_gateway = main_engine.add_gateway(IbGateway)
        main_engine.write_log("交易接口添加成功")
        setting = load_json(connect_trade_filename)
        trade_gateway.quote_only = -1  # 设置为仅交易模式
        main_engine.write_log(f"交易网关配置: 地址={setting['TWS地址']}, 端口={setting['TWS端口']}, 客户号={setting['客户号']}")
        trade_gateway.connect(setting)
        main_engine.write_log("交易网关连接成功 [仅交易模式]")

        # 注册CTA日志事件
        # log_engine = main_engine.get_engine("log")
        # event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
        # main_engine.write_log("注册CTA日志事件监听")

        # 添加barGen Engine
        main_engine.write_log("添加barGen Engine")
        bargen_engine = main_engine.add_engine(BarGenEngineIb)
        bargen_engine.start()
        main_engine.write_log("Bargen for redis 启动")

        main_engine.write_log("delete not maintained pnl list")
        main_engine.write_log("backup pnl list")
        sleep(10)
        
        # 初始化执行状态，将所有running_status转为0
        # cta_engine = main_engine.add_app(CtaStrategyApp)
        # cta_engine.init_engine()
        main_engine.write_log("CTA策略引擎初始化完成")
        sleep(10)
    except Exception as e:
        # 处理可能发生的异常
        msg = f"main_running: An error occurred: {e} {traceback.format_exc()}"
        main_engine.write_log(msg)

    while not stop_event.is_set():
        sleep(1)
        try:
            # 各策略模块的进出调度，与当前测试无关，移除
            # -1 0 未开始 1刚开始跑,load_bar中 2正常运行  3初始化进行中  4已初始化完成待启动  5异常  6. 生命周期结束,主动被杀.
            pass
        except Exception as e:
            # 处理可能发生的异常
            msg = f"main_running: An error occurred: {e} {traceback.format_exc()}"
            main_engine.write_log(msg)

    # 优雅关闭逻辑
    main_engine.write_log("开始关闭应用...")
    try:
        # 关闭交易网关
        if trade_gateway:
            main_engine.write_log("开始撤销所有活动委托...")
            trade_gateway.cancel_all()
            main_engine.write_log("所有活动委托已撤销")
            trade_gateway.close()

        # 关闭行情网关
        if quote_gateway:
            main_engine.write_log("正在关闭行情网关连接...")
            quote_gateway.close()
            main_engine.write_log("行情网关已关闭")

        sleep(10)  # 等待撤单操作完成
        main_engine.close()
    except Exception as e:
        pass
    sys.exit(0)  # 确保子进程彻底退出

def run_parent():
    """
    Running in the parent process.
    """
    print("启动server守护父进程")

    child_process = None
    stop_event = multiprocessing.Event()

    def parent_signal_handler(signum, frame):
        """
        父进程信号处理函数
        """
        print(f"父进程收到信号: {signum}，准备关闭子进程...")
        stop_event.set()

        if child_process and child_process.is_alive():
            print("等待子进程优雅关闭(最长20秒)...")
            child_process.join(timeout=20)

        if child_process and child_process.is_alive():
            print("子进程未能优雅关闭，强制终止")
            child_process.terminate()
            child_process.join()
        print("子进程已处理完毕，父进程退出")
        sys.exit(0)

    # 在父进程中注册信号处理器
    signal.signal(signal.SIGINT, parent_signal_handler)
    signal.signal(signal.SIGTERM, parent_signal_handler)
    # Windows特有的Ctrl+Break信号
    if platform.system() == "Windows":
        signal.signal(signal.SIGBREAK, parent_signal_handler)
    print("父进程信号处理器已注册。")

    while True:
        trading = check_trading_period()

        # 在交易时段启动子进程
        if trading and child_process is None:
            print("启动子进程...")
            stop_event.clear()  # 启动前清除停止事件，确保子进程知道要运行
            child_process = multiprocessing.Process(target=run_child, args=(stop_event,))
            child_process.start()
            print(f"子进程 {child_process.pid} 启动成功。")

        # 非交易时间且子进程仍在运行，则通知子进程退出
        if not trading and child_process is not None and child_process.is_alive():
            print("非交易时间，通知子进程关闭...")
            stop_event.set()  # 通知子进程停止
            child_process.join(timeout=20)  # 等待子进程优雅关闭

            # 如果超时后子进程仍在运行，强制终止它
            if child_process.is_alive():
                print("子进程未能在20秒内关闭，强制终止...")
                child_process.kill()  # 使用kill()而不是terminate()，确保彻底终止
                child_process.join(timeout=5)  # 再等待5秒确保资源释放
                if child_process.is_alive():
                    print("警告：子进程仍然无法终止，可能需要手动处理")

        # 如果子进程已经退出，重置 child_process 为 None，以便下次交易时间重新启动
        if child_process is not None and not child_process.is_alive():
            child_process = None
            print("子进程关闭成功。")

        sleep(5)  # 每5秒检查一次状态

if __name__ == "__main__":
    run_parent()
