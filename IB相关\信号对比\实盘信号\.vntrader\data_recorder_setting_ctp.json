{"tick": {}, "bar": {"CF309.CZCE": {"symbol": "CF309", "exchange": "CZCE", "gateway_name": "CTP"}, "FG309.CZCE": {"symbol": "FG309", "exchange": "CZCE", "gateway_name": "CTP"}, "IF2309.CFFEX": {"symbol": "IF2309", "exchange": "CFFEX", "gateway_name": "CTP"}, "MA2309.CZCE": {"symbol": "MA2309", "exchange": "CZCE", "gateway_name": "CTP"}, "OI309.CZCE": {"symbol": "OI309", "exchange": "CZCE", "gateway_name": "CTP"}, "RM309.CZCE": {"symbol": "RM309", "exchange": "CZCE", "gateway_name": "CTP"}, "SA309.CZCE": {"symbol": "SA309", "exchange": "CZCE", "gateway_name": "CTP"}, "SF309.CZCE": {"symbol": "SF309", "exchange": "CZCE", "gateway_name": "CTP"}, "SM309.CZCE": {"symbol": "SM309", "exchange": "CZCE", "gateway_name": "CTP"}, "SR309.CZCE": {"symbol": "SR309", "exchange": "CZCE", "gateway_name": "CTP"}, "TA309.CZCE": {"symbol": "TA309", "exchange": "CZCE", "gateway_name": "CTP"}, "UR309.CZCE": {"symbol": "UR309", "exchange": "CZCE", "gateway_name": "CTP"}, "a2309.DCE": {"symbol": "a2309", "exchange": "DCE", "gateway_name": "CTP"}, "al2309.SHFE": {"symbol": "al2309", "exchange": "SHFE", "gateway_name": "CTP"}, "bu2309.SHFE": {"symbol": "bu2309", "exchange": "SHFE", "gateway_name": "CTP"}, "c2309.DCE": {"symbol": "c2309", "exchange": "DCE", "gateway_name": "CTP"}, "cs2309.DCE": {"symbol": "cs2309", "exchange": "DCE", "gateway_name": "CTP"}, "cu2309.SHFE": {"symbol": "cu2309", "exchange": "SHFE", "gateway_name": "CTP"}, "eg2309.DCE": {"symbol": "eg2309", "exchange": "DCE", "gateway_name": "CTP"}, "fu2309.SHFE": {"symbol": "fu2309", "exchange": "SHFE", "gateway_name": "CTP"}}}