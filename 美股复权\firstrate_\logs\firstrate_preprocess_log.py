import os
import sys
import warnings
from datetime import datetime
from dateutil.relativedelta import relativedelta
from functools import lru_cache
from typing import Optional, Tuple, List
from collections import defaultdict
from copy import copy
import pandas as pd
import bisect, numpy as np
import pandas_market_calendars as mcal
import typer
from time import sleep
from loguru import logger

warnings.filterwarnings("ignore")

import queue
import threading
import traceback
import concurrent.futures
import signal

# Global flag to indicate if the program is being terminated
is_terminating = False

# Signal handler function
def handle_termination_signal(signum, frame):
    """Handle termination signals (SIGTERM, SIGINT)"""
    global is_terminating
    logger.warning(f"收到终止信号 {signum}，正在优雅关闭...")
    is_terminating = True
    # 不在这里调用 stop，让主线程处理清理工作

# Register signal handlers
signal.signal(signal.SIGTERM, handle_termination_signal)
signal.signal(signal.SIGINT, handle_termination_signal)

# 引入vnpy和数据库
from vnpy.trader.utility import get_file_path, ZoneInfo
from vnpy.trader.setting import SETTINGS
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData
from vnpy.trader.database import DB_TZ
from vnpy.trader.database import BaseDatabase
from peewee import Model

# 添加项目根目录到 Python 路径
file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(file_path)

# 引入富途复权因子和FirstRate复权因子
from utils.database_manager import FutuRehab, db_manager, FirstrateRehab, IbProduct

from utils.mysql_database import create_mysql_database

# 导入get_latest_conid中的NA_VALUES
from utils.mixin import NA_VALUES

# 时区定义
ET_TZ = ZoneInfo("America/New_York")  # 美东时间

class DatabaseSaver:
    """数据库保存管理器，使用多线程处理数据保存"""

    def __init__(self, database: BaseDatabase, num_threads=10):
        """初始化"""
        self.queue = queue.Queue()  # 使用单个队列处理所有线程的任务
        self.mysql_database = database
        self.active = False
        self.threads = [
            threading.Thread(target=self._save_worker, args=(i,), daemon=True)
            for i in range(num_threads)
        ]
        self._processed_count = 0
        self._total_count = 0
        self._lock = threading.Lock()  # 用于保护计数器

    def start(self):
        """启动保存线程"""
        if not self.active:
            self.active = True
            for thread in self.threads:
                thread.start()

    def stop(self):
        """停止保存线程"""
        if self.active:
            # 发送停止信号
            for _ in self.threads:
                self.queue.put(None)

            # 等待队列中的所有任务处理完成
            logger.info("等待数据保存完成...")
            self.queue.join()
            logger.info("所有数据已处理完成")

            self.active = False

            # 等待所有线程结束
            for thread in self.threads:
                if thread.is_alive():
                    thread.join()

            logger.info(f"数据保存已完成，共处理 {self._processed_count} 条数据")

    def put_bars(self, bars: List[BarData], symbol: str = None):
        """添加K线数据到保存队列"""
        if not self.active:
            self.start()

        with self._lock:
            self._total_count += len(bars)

        # 如果队列中的任务超过一定数量，则阻塞等待
        while self.queue.qsize() >= 100:
            logger.warning(f"队列中任务数量已达限制，等待处理... 当前队列大小: {self.queue.qsize()}")
            sleep(1)  # 等待1秒

        self.queue.put((bars, symbol))

    def _save_worker(self, worker_id: int):
        """保存工作线程"""
        while self.active:
            try:
                data = self.queue.get(timeout=1)

                if data is None:
                    self.queue.task_done()
                    break

                bars, symbol = data

                # 添加重试逻辑，不限制重试次数
                retry_count = 0
                while True:
                    try:
                        self.mysql_database.save_bar_data(bars)
                        break  # 成功则跳出重试循环
                    except Exception as e:
                        # 检查是否是死锁错误或其他需要重试的错误
                        # if "Deadlock found when trying to get lock" in error_str or "Lock wait timeout exceeded" in error_str:
                        if 'lock' in str(e).lower():
                            retry_count += 1
                            wait_time = min(0.5 * (2 ** retry_count), 30)  # 指数退避策略，最大等待30秒
                            logger.warning(f"线程 {worker_id} 遇到数据库锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                            sleep(wait_time)

                            # 重新创建数据对象
                            for bar in bars:
                                bar.exchange = Exchange(bar.exchange)
                                bar.interval = Interval(bar.interval)
                                bar.gateway_name = None
                                bar.vt_symbol = None
                        else:
                            # 非死锁错误直接抛出
                            raise

                with self._lock:
                    self._processed_count += len(bars)

                # 打印进度
                if symbol:
                    logger.info(f"线程 {worker_id} 已保存 {symbol} 的 {len(bars)} 条数据，总进度: {self._processed_count}/{self._total_count}")

                self.queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"保存数据时出错: {str(e)}\n{traceback.format_exc()}")


class FirstratePreprocessor:
    """FirstRate数据预处理器，负责数据筛选、时间对齐、复权等操作"""

    def __init__(self, data_dir="S:\\firstrate\\stock", data_path="full_1min", start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None, db_threads=10, process_threads=10, ignore_overview=False, use_database=False,
                 interval: Interval = Interval.MINUTE, database_name: Optional[str] = None,
                 use_contract_style: bool = True, use_detailed_adjustment: bool = False):
        """初始化

        Args:
            data_dir: FirstRate数据所在的基础目录
            data_path: FirstRate数据文件夹名称，默认为"full_1min"，可选"month_1min"等
            start_date: 开始日期，只处理该日期之后的数据
            end_date: 结束日期，只处理该日期及之前的数据
            db_threads: 数据库保存线程数，默认为10
            process_threads: 标的处理线程数，默认为10
            ignore_overview: 是否忽略数据库中的overview信息，强制全量覆盖
            use_database: 是否使用数据库模式
            interval: 时间间隔，支持分钟线(MINUTE)和日线(DAILY)
            database_name: 数据库名称，如果为None则根据interval自动选择
            use_contract_style: 是否使用合约式复权（以下一个复权日前一个交易日为后缀添加到symbol）
            use_detailed_adjustment: 是否使用细分项复权（区分股数变动和分红）
        """
        # 文件路径定义
        self.base_data_dir = data_dir
        self.data_path = data_path
        self.firstrate_dir = os.path.join(self.base_data_dir, self.data_path)
        self.start_date = start_date
        self.end_date = end_date
        self.process_threads = process_threads
        self.ignore_overview = ignore_overview
        self.use_database = use_database
        self.interval = interval
        self.use_contract_style = use_contract_style
        self.use_detailed_adjustment = use_detailed_adjustment

        # 创建数据库连接
        settings = SETTINGS.copy()
        if database_name:
            settings['database.database'] = database_name
        elif interval == Interval.DAILY:
            settings['database.database'] = 'vnpy_stk_us_d'
        logger.info(f"使用数据库: {settings['database.database']}")
        
        # 创建数据库实例和获取Model类
        self.database, self.DbBarData, self.DbTickData, self.DbBarOverview, self.DbTickOverview = create_mysql_database(settings)

        # 创建原始数据库连接
        raw_settings = settings.copy()
        raw_settings['database.database'] = 'vnpy_stk_us_ib'
        self.raw_database, self.RawDbBarData, self.RawDbTickData, self.RawDbBarOverview, self.RawDbTickOverview = create_mysql_database(raw_settings)

        self.nyse = mcal.get_calendar('NYSE')

        # 匹配文件
        self.matched_file = f'matched_symbols_{data_path}.csv'

        # 加载匹配的标的
        self.matched_df = self._load_matched_symbols()

        # 加载 FirstrateRehab 数据，用于检查复权因子缺失
        self.firstrate_rehab_symbols = self._load_firstrate_rehab_symbols()

        # 加载复权因子
        self.rehab_factors = self._load_all_rehab_factors()
        logger.debug(f'测试获取AAPL复权因子：')
        logger.debug(self.get_futu_rehab_factors(265598, "AAPL"))

        # 显示时区信息
        logger.info(f"数据库时区: {DB_TZ}")
        logger.info(f"使用的美东时区: {ET_TZ}")
        logger.info(f"数据间隔: {interval.value}")
        if start_date:
            logger.info(f"只处理 {start_date.strftime('%Y-%m-%d')} 之后的数据")
        if end_date:
            logger.info(f"只处理 {end_date.strftime('%Y-%m-%d')} 及之前的数据")

        # 创建数据保存管理器并启动
        self.database_saver = DatabaseSaver(self.database, db_threads)
        self.database_saver.start()
        # 根据是否使用合约式复权和其他条件决定是否加载overviews
        self.overviews = [] if ignore_overview or not use_contract_style else self.database_saver.mysql_database.get_bar_overview()
        
        # 用于收集缺失复权因子的记录
        self.missing_rehab_records = []
        self.missing_lock = threading.Lock()  # 线程安全锁

    def _load_matched_symbols(self) -> pd.DataFrame:
        """加载匹配后的标的列表"""
        if self.use_database:
            # 从数据库直接获取数据，只选择需要的字段
            query = IbProduct.select(
                IbProduct.conid,
                IbProduct.symbol,
            ).where(IbProduct.is_latest == True)
            rows = [(row.conid, row.symbol, row.symbol.replace(' ', '.')) for row in query]
            matched_df = pd.DataFrame(rows, columns=['conid', 'ib_symbol', 'firstrate_symbol'])
            logger.info(f"从数据库加载了 {len(matched_df)} 条匹配记录")
            return matched_df
        else:
            # 从CSV文件加载数据
            matched_file_path = get_file_path(self.matched_file)

            if not os.path.exists(matched_file_path):
                logger.error(f"匹配文件 {matched_file_path} 不存在，请先运行 get_latest_conid.py")
                return pd.DataFrame()

            matched_df = pd.read_csv(
                matched_file_path,
                na_values=NA_VALUES,
                keep_default_na=False
            )

            logger.info(f"从文件加载了 {len(matched_df)} 条匹配记录")
            return matched_df

    @lru_cache(maxsize=999)
    def get_previous_trading_day(self, current_date):
        """获取给定日期的上一个美股交易日"""
        trading_days = self.nyse.schedule(start_date=current_date - pd.Timedelta(days=10), end_date=current_date)
        if len(trading_days) < 2:  # 确保至少有两个交易日
            trading_days = self.nyse.schedule(start_date=current_date - pd.Timedelta(days=20), end_date=current_date)
        return trading_days.index[-2].date()  # 返回倒数第二个交易日

    @lru_cache(maxsize=999)
    def generate_trading_minutes(self, start_date_str: str, end_date_str: str) -> List[pd.Timestamp]:
        """生成指定日期范围内的交易分钟时间戳"""
        # 美股交易日历
        schedule = self.nyse.schedule(start_date=start_date_str, end_date=end_date_str)
        if schedule.empty:
            return []

        minutes = mcal.date_range(schedule, frequency='1Min', closed='left', force_close=False)
        return [ts.astimezone(ET_TZ) for ts in minutes]

    def load_firstrate_data(self, symbol: str, query_start_date: Optional[datetime] = None) -> pd.DataFrame:
        """加载FirstRate数据

        Args:
            symbol: FirstRate标的代码
            query_start_date: 数据查询（美东时区）开始日期，如果指定了，则只加载该日期之后的数据
        """
        file_path = os.path.join(self.firstrate_dir, f"{symbol}_{self.data_path}_UNADJUSTED.txt")

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {file_path}")
            return pd.DataFrame()

        # 读取数据，格式：DateTime,Open,High,Low,Close,Volume
        df = pd.read_csv(
            file_path,
            header=None,
            names=["datetime", "open", "high", "low", "close", "volume"],
            parse_dates=["datetime"],
            na_values=NA_VALUES,
            keep_default_na=False
        )

        # 规范化日期时间格式，FirstRate数据默认是美东时间
        df["datetime"] = pd.to_datetime(df["datetime"]).dt.tz_localize(ET_TZ)

        # 确定最终的过滤日期（取两者中较晚的日期）
        effective_date = None
        if self.start_date and query_start_date:
            effective_date = max(self.start_date, query_start_date)
        elif self.start_date:
            effective_date = self.start_date
        elif query_start_date:
            effective_date = query_start_date

        # 应用日期过滤条件
        if effective_date:
            df = df[df["datetime"] > effective_date]
            if df.empty:
                logger.warning(f"在 {effective_date} 之后无数据: {symbol}")
                return df

        # 应用结束日期过滤条件
        end_date = self.end_date if self.end_date else datetime.now(ET_TZ)
        df = df[df["datetime"] <= end_date]
        if df.empty:
            logger.warning(f"在 {end_date} 之前无数据: {symbol}")
            return df

        # 在设置索引前处理重复时间戳，保留第一条记录
        df = df.drop_duplicates(subset=["datetime"], keep="first")

        # 设置索引
        df.set_index("datetime", inplace=True)

        return df

    def load_raw_database_data(self, symbol: str, query_start_date: Optional[datetime] = None, exchange: Exchange = Exchange.SMART) -> pd.DataFrame:
        """从数据库加载数据

        Args:
            symbol: 数据库中的symbol
            query_start_date: 数据查询（无时区信息）开始日期，如果指定了，则只加载该日期之后的数据
            exchange: 交易所
        """
        # 确定查询的开始和结束日期
        end_date = self.end_date.astimezone(DB_TZ) if self.end_date else datetime.now(DB_TZ)
        if query_start_date is None:
            # 如果是data_path是full_1min就是从start_date开始，month_1min就是-1个月
            if self.data_path == "full_1min":
                query_start_date = self.start_date.astimezone(DB_TZ)
            elif self.data_path == "month_1min":
                query_start_date = end_date - relativedelta(months=1)
            else:
                # 其他情况
                query_start_date = end_date - relativedelta(days=int(self.data_path))

        # 从数据库加载数据
        bars = self.raw_database.load_bar_data(
            symbol,
            exchange,
            self.interval,
            query_start_date.replace(tzinfo=None),  # 数据库查询需要无时区信息的时间
            end_date.replace(tzinfo=None)
        )
        
        if not bars:
            logger.warning(f"数据库中无数据: {symbol}")
            return pd.DataFrame()
            
        # 检查第一条数据的时间是否等于query_start_date，如果是则删除
        first_bar = bars[0]
        first_bar_time = first_bar.datetime.replace(tzinfo=None)
        if first_bar_time == query_start_date:
            bars = bars[1:]
            if not bars:  # 如果删除后没有数据了
                logger.warning(f"删除起始时间重复数据后无数据: {symbol}")
                return pd.DataFrame()
            
        # 将数据转换为DataFrame
        data = {
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }
        timestamps = []
        
        for bar in bars:
            # 数据库中时间是上海时间(无时区信息)，需要转换为美东时间
            # 先将数据库时间视为上海时间并添加时区信息，然后转换为美东时间
            dt = bar.datetime.replace(tzinfo=DB_TZ).astimezone(ET_TZ)
            timestamps.append(dt)
            data['open'].append(bar.open_price)
            data['high'].append(bar.high_price)
            data['low'].append(bar.low_price)
            data['close'].append(bar.close_price)
            data['volume'].append(bar.volume)
        
        # 创建DataFrame
        df = pd.DataFrame(data, index=timestamps)
        
        # 如果设置了开始日期，先过滤数据
        if self.start_date:
            df = df[df.index >= self.start_date]
            if df.empty:
                logger.warning(f"该时间段内无数据: {symbol}")
                return df
                
        # 处理重复时间戳，保留第一条记录
        df = df[~df.index.duplicated(keep='first')]
        
        # 按时间排序
        df.sort_index(inplace=True)
        
        return df

    def _load_firstrate_rehab_symbols(self) -> set:
        """加载 FirstrateRehab 数据中的所有标的代码，筛选规则与futu一致"""
        query = (FirstrateRehab
                .select(FirstrateRehab.symbol, FirstrateRehab.ex_div_date)
                .where(FirstrateRehab.ex_div_date >= self.start_date.replace(tzinfo=None) if self.start_date else True)
                .distinct())
        return {(record.symbol, record.ex_div_date) for record in query}

    def _load_all_rehab_factors(self) -> pd.DataFrame:
        """一次性加载所有复权因子"""
        # 获取所有需要的标的代码和对应的conid
        symbol_conid_map = {}  # futu_code -> conid的映射
        firstrate_symbol_map = {}  # futu_code -> firstrate_symbol的映射
        for _, row in self.matched_df.iterrows():
            futu_code = f"US.{row['ib_symbol'].replace(' ', '.')}"
            symbol_conid_map[futu_code] = row['conid']
            firstrate_symbol_map[futu_code] = row['firstrate_symbol']

        # 批量查询复权因子
        if not symbol_conid_map:
            return pd.DataFrame()

        # 根据复权模式选择查询字段
        if self.use_detailed_adjustment:
            # 分红合约式复权模式：查询股数变动相关字段和forward复权因子
            query = (FutuRehab
                    .select(FutuRehab.code, FutuRehab.ex_div_date, 
                           FutuRehab.split_ratio, FutuRehab.per_share_div_ratio,
                           FutuRehab.per_cash_div, FutuRehab.special_dividend,

                           # (自算)
                           # FutuRehab.prev_close)
                           # (futu)
                           FutuRehab.forward_adj_factorA, FutuRehab.forward_adj_factorB)
            
                    .where(
                        (FutuRehab.code.in_(list(symbol_conid_map.keys()))) &
                        (FutuRehab.ex_div_date >= self.start_date.replace(tzinfo=None) if self.start_date else True)
                    ))
        else:
            # 统一复权模式：只查询forward_adj_factorA和forward_adj_factorB
            query = (FutuRehab
                    .select(FutuRehab.code, FutuRehab.ex_div_date, 
                           FutuRehab.forward_adj_factorA, FutuRehab.forward_adj_factorB)
                    .where(
                        (FutuRehab.code.in_(list(symbol_conid_map.keys()))) &
                        (FutuRehab.ex_div_date >= self.start_date.replace(tzinfo=None) if self.start_date else True)
                    ))

        # 转换为DataFrame
        records = []
        for record in query:
            record_dict = {
                'conid': symbol_conid_map[record.code],
                'ex_div_date': record.ex_div_date,
            }

            # 根据复权模式添加不同字段
            # (自算)
            # if self.use_detailed_adjustment:
            #     record_dict.update({
            #         'split_ratio': record.split_ratio,
            #         'per_share_div_ratio': record.per_share_div_ratio,
            #         'prev_close': record.prev_close
            #     })
            # else:
            #     record_dict.update({
            #         'forward_adj_factorA': record.forward_adj_factorA,
            #         'forward_adj_factorB': record.forward_adj_factorB
            #     })
            # (futu)
            if self.use_detailed_adjustment:
                record_dict.update({
                    'split_ratio': record.split_ratio,
                    'per_share_div_ratio': record.per_share_div_ratio,
                })
            # 所有模式都需要forward复权因子
            record_dict.update({
                'per_cash_div': record.per_cash_div,
                'special_dividend': record.special_dividend,
                'forward_adj_factorA': record.forward_adj_factorA,
                'forward_adj_factorB': record.forward_adj_factorB
            })
            
            records.append(record_dict)

        df = pd.DataFrame(records)
        if not df.empty:
            df.set_index(['conid', 'ex_div_date'], inplace=True)
            df.sort_index(inplace=True)

        # 检查哪些标的在firstrate有复权数据但在futu中没有
        futu_symbols_with_rehab = set(df.index.get_level_values(0)) if not df.empty else set()
        firstrate_symbols_with_rehab = self.firstrate_rehab_symbols

        missing_rehab_symbols = []
        for futu_code, firstrate_symbol in firstrate_symbol_map.items():
            if firstrate_symbol in firstrate_symbols_with_rehab:
                conid = symbol_conid_map[futu_code]
                if conid not in futu_symbols_with_rehab:
                    missing_rehab_symbols.append((futu_code, conid, firstrate_symbol))

        logger.info(f"已加载 {len(df)} 条复权因子记录，{len(df.index.unique(level=0))} 个标的")
        logger.debug(f"复权因子数据: {df.head()}")
        if missing_rehab_symbols:
            logger.warning(f"\n警告: 发现 {len(missing_rehab_symbols)} 个标的在FirstRate有复权数据但在富途中缺失:")
            for futu_code, conid, firstrate_symbol in missing_rehab_symbols:
                logger.warning(f"  - {futu_code} (conid: {conid}, firstrate: {firstrate_symbol})")

        return df

    def get_futu_rehab_factors(
        self,
        conid: int,
        firstrate_symbol: str,
        overview_end: Optional[datetime] = None,
        data_end_date: Optional[datetime] = None
    ) -> Tuple[pd.DataFrame, bool]:
        """获取复权因子
        Args:
            conid: 合约ID
            firstrate_symbol: FirstRate标的代码
            overview_end: 数据库中已有数据的结束日期，只返回该日期之后的复权因子
            data_end_date: 数据的最新日期，不返回该日期之后的下一个交易日的复权因子
        Returns:
            Tuple[pd.DataFrame, bool]: (复权因子DataFrame, 是否缺失复权数据)
        """
        factors = pd.DataFrame()
        missing = False

        if not self.rehab_factors.empty and conid in self.rehab_factors.index.get_level_values(0):
            factors = self.rehab_factors.loc[conid]
            # 如果指定了overview_end，只保留复权因子日期前一个交易日大于overview_end的因子
            if overview_end:
                # 修改这里：确保日期类型一致
                overview_end_date = pd.Timestamp(overview_end.date())
                factors = factors[factors.index.map(lambda x: pd.Timestamp(self.get_previous_trading_day(x.date()))) > overview_end_date]
            # 如果指定了data_end_date，只保留复权因子日期的前一个交易日不大于data_end_date的因子
            if data_end_date:
                # 修改这里：确保日期类型一致
                end_date = pd.Timestamp(data_end_date.date())
                factors = factors[factors.index.map(lambda x: pd.Timestamp(self.get_previous_trading_day(x.date()))) <= end_date]
        else:
            # 检查firstrate中是否有对应时间范围的复权数据
            firstrate_rehab_dates = {date for symbol, date in self.firstrate_rehab_symbols if symbol == firstrate_symbol}
            if firstrate_rehab_dates:
                # 如果指定了overview_end，只保留复权因子日期前一个交易日大于overview_end的因子
                if overview_end:
                    # 修改这里：确保日期类型一致
                    overview_end_date = pd.Timestamp(overview_end.date())
                    firstrate_rehab_dates = {date for date in firstrate_rehab_dates
                                          if pd.Timestamp(self.get_previous_trading_day(date.date())) > overview_end_date}
                # 如果指定了data_end_date，只保留复权因子日期的前一个交易日不大于data_end_date的因子
                if data_end_date:
                    # 修改这里：确保日期类型一致
                    end_date = pd.Timestamp(data_end_date.date())
                    firstrate_rehab_dates = {date for date in firstrate_rehab_dates
                                          if pd.Timestamp(self.get_previous_trading_day(date.date())) <= end_date}
                if firstrate_rehab_dates:
                    missing = True

        return factors, missing

    def classify_adj_type(self, factors_tuple) -> str:
        """分类复权类型
        
        Args:
            factors_tuple: 复权因子行数据（namedtuple from itertuples）
            
        Returns:
            str: 'stock_change' (股数变动) 或 'dividend' (分红) 或 'both' (两者都有)
        """
        has_stock_change = False
        has_dividend = False
        
        # 检查股数变动（拆股、合股、送股）
        split_ratio = getattr(factors_tuple, 'split_ratio', None)
        per_share_div_ratio = getattr(factors_tuple, 'per_share_div_ratio', None)
        
        if (pd.notna(split_ratio) and split_ratio != 1.0) or \
           (pd.notna(per_share_div_ratio) and per_share_div_ratio > 0.0):
            has_stock_change = True
            
        # 检查分红
        per_cash_div = getattr(factors_tuple, 'per_cash_div', None)
        special_dividend = getattr(factors_tuple, 'special_dividend', None)
        
        if (pd.notna(per_cash_div) and per_cash_div != 0.0) or \
           (pd.notna(special_dividend) and special_dividend != 0.0):
            has_dividend = True
            
        if has_stock_change and has_dividend:
            return 'both'
        elif has_stock_change:
            return 'stock_change'
        elif has_dividend:
            return 'dividend'
        else:
            return 'none'

    def cal_share_factor(self, factors_tuple) -> float:
        """计算股数变动的复权因子
        
        Args:
            factors_tuple: 复权因子行数据（namedtuple from itertuples）
            
        Returns:
            float: 股数变动复权因子
        """
        factor = 1.0
        
        # 拆合股因子
        # split_ratio > 1 表示合股（如20表示20合1），价格应该乘以split_ratio
        # split_ratio < 1 表示拆股（如0.1表示1拆10），价格应该乘以split_ratio
        split_ratio = getattr(factors_tuple, 'split_ratio', None)
        if pd.notna(split_ratio) and split_ratio != 1.0:
            factor *= split_ratio
            
        # 送股因子
        # per_share_div_ratio表示送股比例，如20表示20送1，即每20股送1股
        # 送股后总股数变为原来的(1 + 1/per_share_div_ratio)倍，价格应该除以这个倍数
        per_share_div_ratio = getattr(factors_tuple, 'per_share_div_ratio', None)
        if pd.notna(per_share_div_ratio) and per_share_div_ratio != 0.0:
            # 送股比例：每per_share_div_ratio股送1股
            # 股数变化倍数 = (per_share_div_ratio + 1) / per_share_div_ratio
            # 价格复权因子 = per_share_div_ratio / (per_share_div_ratio + 1)
            factor *= per_share_div_ratio / (per_share_div_ratio + 1.0)
            
        return factor

    def cal_div_factor(self, factors_tuple, close_price: float) -> float:
        """计算分红的复权因子

        Args:
            factors_tuple: 复权因子行数据（namedtuple from itertuples）
            close_price: 复权前收盘价

        Returns:
            float: 分红复权因子
        """
        if close_price <= 0:
            return 1.0

        dividend_amount = 0.0

        # 普通分红
        per_cash_div = getattr(factors_tuple, 'per_cash_div', None)
        if pd.notna(per_cash_div):
            dividend_amount += per_cash_div

        # 特别分红
        special_dividend = getattr(factors_tuple, 'special_dividend', None)
        if pd.notna(special_dividend):
            dividend_amount += special_dividend

        if dividend_amount == 0.0:
            return 1.0

        # 分红复权因子 = (复权前收盘价 - 分红金额) / 复权前收盘价
        return (close_price - dividend_amount) / close_price

    def cal_combined_factor(self, factors_tuple, close_price: float) -> Tuple[float, float]:
        """计算综合复权因子（同时有股数变动和分红时）
        
        Args:
            factors_tuple: 复权因子行数据（namedtuple from itertuples）
            close_price: 复权前收盘价
            
        Returns:
            Tuple[float, float]: (价格复权因子, 成交量复权因子)
        """
        # 先计算分红影响后的价格
        dividend_amount = 0.0
        per_cash_div = getattr(factors_tuple, 'per_cash_div', None)
        if pd.notna(per_cash_div) and per_cash_div > 0:
            dividend_amount += per_cash_div
        special_dividend = getattr(factors_tuple, 'special_dividend', None)
        if pd.notna(special_dividend) and special_dividend > 0:
            dividend_amount += special_dividend
            
        # 除息后价格
        ex_div_price = close_price - dividend_amount if dividend_amount > 0 else close_price
        
        # 再计算股数变动因子
        share_factor = self.cal_share_factor(factors_tuple)
        
        # 最终价格复权因子：先除息，再应用股数变动
        price_factor = (ex_div_price / close_price) * share_factor
            
        # 成交量复权因子（与股数变动相反）
        # 拆股时股数增加，成交量需要除以拆股比例
        # 合股时股数减少，成交量需要乘以合股比例
        volume_factor = 1.0 / share_factor if share_factor != 0 else 1.0
        
        return price_factor, volume_factor

    def process_symbol(self, row: pd.Series):
        """处理单个标的数据"""
        conid = row['conid']
        ib_symbol = row['ib_symbol']
        firstrate_symbol = row['firstrate_symbol']
        
        logger.info(f"\n处理标的: {ib_symbol} (conid: {conid}), FirstRate标的: {firstrate_symbol}")

        assert type(conid) == int

        overview = next((o for o in self.overviews if o.symbol == str(conid) and o.exchange == Exchange.SMART and o.interval == self.interval), None)
        overview_end_et = overview_end_et_none = None
        if overview:
            overview_end_et = overview.end.replace(tzinfo=DB_TZ).astimezone(ET_TZ)
            overview_end_et_none = overview_end_et.replace(tzinfo=None)  # 去除时区信息
        
        # 根据模式选择加载数据的方法
        if self.use_database:
            df = self.load_raw_database_data(str(conid), query_start_date=overview.end if overview else None)
        else:
            df = self.load_firstrate_data(firstrate_symbol, query_start_date=overview_end_et)
            
        if df.empty:
            logger.warning(f"无加载数据: {firstrate_symbol}")
            return False

        # 4. 根据时间间隔处理数据
        if self.interval == Interval.MINUTE:
            # 分钟线数据处理
            # 2. 获取数据的日期范围并生成交易分钟
            trading_minutes = self.generate_trading_minutes(
                df.index.min().strftime("%Y-%m-%d"),
                df.index.max().strftime("%Y-%m-%d")
            )

            if not trading_minutes:
                logger.warning(f"该时间段内无交易日: {firstrate_symbol}")
                return False

            # 3. 使用完整时间索引重新索引数据
            full_df = df.reindex(trading_minutes)

            # 4. 按日期分组填充收盘价
            full_df['close'] = full_df.groupby(full_df.index.date)['close'].ffill().bfill()
        else:
            full_df = df

        # 5.删除开盘初期没有数据的行（收盘价为NaN的行）
        full_df = full_df.dropna(subset=['close'])

        if full_df.empty:
            logger.warning(f"处理后无有效数据: {firstrate_symbol}")
            return False

        # 6. 用收盘价填充其他价格字段
        for col in ['open', 'high', 'low']:
            full_df[col] = full_df[col].fillna(full_df['close'])

        # 7. 成交量用0填充
        full_df['volume'] = full_df['volume'].fillna(0)

        # 获取数据的最新日期
        data_end_date = full_df.index.max()

        # 保存不复权数据到数据库
        # # 8. 转换为BarData对象
        # all_bars = []
        # for idx, row_data in full_df.iterrows():
        #     bar = BarData(
        #         symbol=str(conid),
        #         exchange=Exchange.SMART,
        #         datetime=idx.to_pydatetime(),
        #         interval=Interval.MINUTE,
        #         volume=float(row_data['volume']),
        #         open_price=float(row_data['open']),
        #         high_price=float(row_data['high']),
        #         low_price=float(row_data['low']),
        #         close_price=float(row_data['close']),
        #         turnover=0.0,
        #         open_interest=0.0,
        #         gateway_name="FIRSTRATE"
        #     )
        #     all_bars.append(bar)

            # # 9. 保存到数据库
        # if all_bars:
        #     self.database_saver.put_bars(all_bars, ib_symbol)
        #     print(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
        #     return True
        # else:
        #     print(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
        #     return False

        # 保存合约式多symbol复权数据到数据库
        '''
        合约式美股复权并入库的步骤：
        
        0. get_bar_overview获取数据库overview
        1. 如果无factors，symbol无需改名直接入库
            # （统一复权模式）情况14
            # （分红合约式复权模式）情况31
        2. 否则，根据复权模式进行处理：
        
        2.1 统一复权模式（use_detailed_adjustment=False）：使用forward_adj_factorA和forward_adj_factorB
        
            数据情况图解（14种情况）：
            
            时间轴：       ←─────────────────────────────────────────────────────→
                            过去                    现在
            除权日：          *  |              |               |
                           new Ex1            Ex2             Ex3
            情况分布（有事件）：
                        1─2─*─3─|              |               |              
                          4─*─5─|───────8──────|               |              
                              6─|───────9──────|───────11──────|              
                              7─|───────10─────|───────12──────|──────13──────
            情况分布（无事件）：
                           *─14─
            
            说明：
            ---------------------------
            情况1（无交叉）、2（交叉多次后续复权所需窗口）：数据库中已有数据，改名为first_symbol
            3：数据库无，再往历史无复权因子：不复权、copy、改名first_symbol、保存
            4：数据库有，改名之后读出来：复权、copy、改名为target_symbol(后缀)、保存
            5、6、9：数据库无：复权、copy、改名target_symbol(后缀)、保存
            7、10、12：数据库无：复权、copy、改名target_symbol(conid)、保存
            8、11：历史复权因子后的第一段复权窗口：保存、不复权、copy、改名target_symbol
            13、14：最新数据：保存、不复权、不copy、不改名
            ---------------------------
            8、11、13上方：数据date在下一个复权事件日期及之后，即最后一个除权日前的所属下一个除权日后的数据：跳过不保存
            1、4下方：过于久远的数据（cutoff_date之前）- 跳过不保存
            ---------------------------
            
            2.1.1. 对于新第1个复权事件的first_symbol名
                处理数据库中最新日期认为的最新数据线（symbol为conid）的库中数据
                2.1.1.1. 库中数据
                    # 情况1、2 
                    - 计算first_symbol = "conid_{first_ex_date前一个交易日%y%m%d}"
                    - 如果有overview，更新数据库中symbol为conid的改为first_symbol
            2.1.2. 对新第2个及以后复权事件的target_symbol名：复权并生成新后缀数据线
                2.1.2.1. 初始化存储不同时间段数据的字典bars_dict
                2.1.2.2. 如果有overview，load_bar_data加载并拼接first_symbol原始数据（4个月/4年前开始）
                2.1.2.3. 预先计算所有的cutoff_dates以提高性能
                2.1.2.4. 遍历每条数据，对每个复权因子应用统一复权逻辑：
                    遍历复权factors的每行，根据数据date与所需最早日期、当前复权事件日期判断是否需要：复权base_bar/copy(base_bar)/改名symbol(first_symbol或target_symbol)/保存
                    2.1.2.4.0 根据下一个除权日确定next_ex_date_(可能无）、target_symbol
                    2.1.2.4.1. 过于久远的数据（date < cutoff_date），跳过不保存
                        # 情况1、4下方
                    2.1.2.4.2. 所需最早日期 <= date < 当前复权事件日期current_ex_date_：
                        2.1.2.4.2.1. 特殊处理：数据库first_symbol（conid改名而来）数据续接部分
                            再往历史无复权因子，且在overview_end_et（如果有）至新的first_ex_date之间：不复权、copy、改名first_symbol、保存
                            # 情况3
                        2.1.2.4.2.2. 同时：复权base_bar、copy、改名target_symbol、保存
                            # 情况4
                            # 情况5、6、9
                            # 情况7、10、12
                    2.1.2.4.3. 当前复权事件日期 <= date：
                        2.1.2.4.3.1. 还有下一个复权事件
                            2.1.2.4.3.1.1. 历史复权因子后的第一段复权窗口：不复权、copy、改名target_symbol、保存
                                # 情况8、11
                            2.1.2.4.3.1.2. 数据date在下一个复权事件日期及之后，即最后一个除权日前的所属下一个除权日后的数据：跳过不保存
                                # 情况8、11、13上方
                        2.1.2.4.3.2. 没有下一个复权事件，即最后一个除权日后的无所属下一个除权日最新数据：不复权、不copy、不改名、保存
                            # 情况13
                2.1.2.5. 保存所有时间段的数据
        
        2.2 分红合约式复权模式（use_detailed_adjustment=True：用classify_adj_type、cal_share_factor等工具函数
        
            分红合约式复权数据情况图解（31种情况）：
            
            时间轴：     ←─────────────────────────────────────────────────────→
                          过去                    现在                    未来
            
            除权事件：      split     split     div      split     div     split     split     div     split
                       *   |        |         +         |        +        |         |         +        |
                      new Ex1      Ex2      Ex3       Ex4      Ex5      Ex6       Ex7       Ex8      Ex9
            情况分布（有分红事件）： 
              ──0──+          
                   +─1─*─3─|───5────|────9────+         |        +        |         |         +        |
                           |        |         +         |        +        |         |         +        |
                   + 2─*─4─|───6────|────10───+────13───|───16───+        |         |         +        |
                           |        |         +         |        +        |         |         +        |
                           |        |         +         |        +        |         |         +        |
                            ───7────|────11───+────14───|───17───+───19───|───21────|───23────+        |
                            ───8────|────12───+────15───|───18───+───20───|───22────|───24────+───25───|───26────
            情况分布（无分红有股数事件）：
              ──27─+
                   +─28─*─29─|───30────
            情况分布（无分红无股数事件）：
                        *─31─
            
            说明：
            ---------------------------
            0、27：数据库中已有以"conid_"开头的量价数据update，用第一个"后续股数总因子"复权
            1：数据库有，改名为first_symbol之前读出来，改名、后续总股数复权、update
            3：数据库无，再往历史无复权因子：copy、副本上应用"后续股数总因子"、改名first_symbol、保存
            2：数据库有，改名之后读出来，复权、copy、改名为target_symbol(后缀)、保存
            4、6、10：数据库无，复权、copy、改名target_symbol(后缀)、保存
            7、8、11、12、14、15、17、18、20、22、24：数据库无，复权、copy、改名target_symbol(conid)、保存
            5、9、13、16、19、21、23：历史复权因子后的第一段复权窗口：copy、副本上应用下次的"后续股数总因子"、改名target_symbol、保存
            25：有下一个复权事件没有下一个分红复权事件：copy、副本上应用下次的"后续股数总因子"、不改名（为conid）、保存
            28：新数据事件无分红有股数，将symbol为conid的应用第一个"后续股数总因子"列update
            29：无新分红的后续有拆合股的部分的数据：copy、副本上应用"后续股数总因子"、不改名、保存
            30：无新分红的后续也无拆合股的部分的数据：不copy、不复权、不改名、保存
            26、31：最终数据，直接保存为conid
            ---------------------------
            1、3、5、9、13、16、19、21、23、25、26上方：数据date在下一个分红复权事件日期及之后，即最后一个分红除权日前的所属下一个分红除权日后的数据：跳过不保存
            0、2、4下方：过于久远的数据（cutoff_date之前）- 跳过不保存
            各有线行之间的无线行：跳过不保存
            ---------------------------            
            关键差异：
            - 只有分红事件（div +）才产生新的symbol数据线
            - 拆股事件（split |）只影响"后续股数总因子"计算
            - 迭代传递使用forward_adj_factorA和forward_adj_factorB（与统一复权模式一致）
            - 副本应用"后续股数总因子"进行最终调整
            ---------------------------
        
            2.2.1. 首先判断factors中是否有dividend或both事件，维护成div_ex_dates，其中第一个赋值给first_div_ex_date和first_symbol（默认为None）

            # (自算)
            # 2.2.2. 根据interval区别处理计算factors每个ex_div_date的div_factor、share_factor：
            #     - 分钟线复权：使用数据库中的prev_close字段（不复权日线收盘价）作为参考收盘价
            #     - 日线复权：用full_df的原始close计算（二分查找除权日前最接近的收盘价）（两者乘积为combined_factor）。再给factors添加一列“后续股数总因子”（share_forward_cumprod）：倒序排列后的share_factor列的expanding累乘（包括ex_div_date自身所在行）
            # (futu)
            2.2.2. 计算factors每个ex_div_date的股数变动因子：
                - 只计算股数变动相关的share_factor（拆股、合股、送股）
                - 给factors添加一列“后续股数总因子”（share_forward_cumprod）：倒序排列后的share_factor列的expanding累乘（包括ex_div_date自身所在行）
            
            2.2.3. 核心处理分为两部分：
                2.2.3.1. 数据库中有的数据，update调整
                    2.2.3.1.1. 将symbol以"conid_"开头的量价数据update，用第一个“后续股数总因子”（由于不再以stock_change拆成不同symbol数据线）update
                        # 情况0、27
                    2.2.3.1.2. 如果有overview，
                        2.2.3.1.2.1. 计算start_date（start_date = max(overview_end_et, first_div_ex_date_et)前4个月/4年前开始
                        2.2.3.1.2.2. 判断first_symbol：
                            2.2.3.1.2.2.1. 为None：将symbol为conid的应用第一个“后续股数总因子”列update
                                # 情况28
                            2.2.3.1.2.2.2. 非None
                                2.2.3.1.2.2.2.1. （start_date < overview.end时才）load_bar_data加载并拼接symbol（conid）的start_date后的数据作为full_df
                                2.2.3.1.2.2.2.2. 将symbol为conid：symbol update为first_symbol，应用第一个“后续股数总因子”列update
                                    # 情况1
                
                2.2.3.2. 数据库中没有的数据，调整到位再入
                    2.2.3.2.1. 开始遍历full_df和factors每条数据，调整数据，判断first_div_ex_date：
                        2.2.3.2.1.1. 为None：对于overview_end_et（如果有）之后的每个date的数据分情况处理
                            2.2.3.2.1.1.1. 有后续拆合股事件：copy、副本上应用"后续股数总因子"、不改名、保存
                                # 情况29
                            2.2.3.2.1.1.2. 无后续拆合股事件：不copy、不复权、不改名、保存
                                # 情况30
                        2.2.3.2.1.2. 非None
                            根据数据date与所需最早日期、当前复权事件日期判断是否需要：复权base_bar/copy(base_bar)/改名symbol(first_symbol或target_symbol)/保存
                            2.2.3.2.1.2.0. 根据div_ex_dates的next_div_ex_date(可能无）、target_symbol("conid_{next_div_ex_date_前一个交易日%y%m%d}")
                            2.2.3.2.1.2.1. 过于久远的数据（date < cutoff_date）：跳过不保存
                                # 情况0、2、4下方
                            2.2.3.2.1.2.2. 所需最早日期 <= date < 当前复权事件日期current_ex_date_：
                                2.2.3.2.1.2.2.1. 特殊处理：数据库first_symbol（conid改名而来）数据续接部分
                                    再往历史无复权因子，且在overview_end_et（如果有）至新的first_div_ex_date之间：copy、副本上应用当次的“后续股数总因子”、改名first_symbol、保存
                                    # 情况3                                    
                                2.2.3.2.1.2.2.2. 同时：复权base_bar、copy、改名target_symbol、保存
                                    # 情况2
                                    # 情况4、6、10
                                    # 情况7、8、11、12、14、15、17、18、20、22、24
                                2.2.3.2.1.2.2.3. # 各有线行之间的无线行：跳过不保存
                                
                            2.2.3.2.1.2.3. 当前复权事件日期 <= date:时间区间以因子数量n拆为n+1段，遍历时少一段，此处处理第n+1段
                                2.2.3.2.1.2.3.1. 还有下一个分红复权事件
                                    2.2.3.2.1.2.3.1.1. 数据date正好（防重复）在下一个分红复权事件日期之前，即最后一个分红除权日前的最新数据：copy、副本上应用下次的"后续股数总因子"、改名target_symbol、保存 
                                        # 情况5、9、13、16、19、21、23 
                                    2.2.3.2.1.2.3.1.2. 数据date在下一个分红复权事件日期及之后，即最后一个分红除权日前的所属下一个分红除权日后的数据：跳过不保存
                                        # 情况1、3、5、9、13、16、19、21、23、25、26上方
                                2.2.3.2.1.2.3.2. 没有下一个分红复权事件，即最后一个分红除权日后的无所属下一个分红除权日最新数据
                                    2.2.3.2.1.2.3.2.1. 有下一个复权事件：copy、副本上应用下次的"后续股数总因子"、不改名（为conid）、保存
                                        # 情况25
                                    2.2.3.2.1.2.3.2.2. 没有下一个复权事件：不copy、副本上应用下次的"后续股数总因子（赋值为1）"、不改名（为conid）、保存
                                        # 情况26
            
            2.2.4. 总体思路。数据库中的数据始终是这样：只有含分红的复权事件才产生新数据线；带_%y%m%d后缀的和不带后缀的各条线之间比例差距只有各次的div_factor的差距
                             * 根据下一个分红除权日确定target_symbol
                             * 创建副本并设置正确的symbol存储

        3. 前复权覆盖模式（use_contract_style=False）
            3.1. 清空数据库中对应interval的所有数据（在process_all方法中执行）
            3.2. 获取复权因子数据
            3.3. 如果没有复权因子，直接用原始symbol入库
            3.4. 如果有复权因子，应用前复权逻辑：
                3.4.1. 遍历每条数据，创建基础BarData对象
                3.4.2. 对每条数据遍历所有复权因子：
                    3.4.2.1. 如果数据日期 < 除权日期，应用复权因子（forward_adj_factorA和forward_adj_factorB）
                    3.4.2.2. 复权公式：
                        - volume = volume / forward_adj_factorA
                        - price = price * forward_adj_factorA + forward_adj_factorB
                3.4.3. 所有数据使用同一个symbol（conid）保存到数据库
        '''

        # 8. 获取数据库中的数据概览
        if overview:
            # 时区处理：
            # 1. 对于load_bar_data，使用原始的overview.end（带DB_TZ时区）
            # 2. 对于get_futu_rehab_factors，将overview.end转换为ET_TZ后去除时区信息
            factors, missing = self.get_futu_rehab_factors(conid, firstrate_symbol, overview_end_et_none, data_end_date)
        else:
            # 如果没有overview，获取所有复权因子（但仍然需要限制在数据结束日期之前）
            factors, missing = self.get_futu_rehab_factors(conid, firstrate_symbol, None, data_end_date)

        if missing:
            # 收集缺失复权因子的记录
            with self.missing_lock:
                self.missing_rehab_records.append({
                    'conid': conid,
                    'ib_symbol': ib_symbol,
                    'firstrate_symbol': firstrate_symbol
                })
            # return False
        
        logger.info(f"复权因子信息 - {firstrate_symbol} (conid: {conid}): \n{factors}")

        # 8.5. 计算调整后的forward_adj_factorA（基于复权日前一日收盘价）
        if not factors.empty:
            # 初始化调整后的factorA数组，默认使用原始factorA
            adjusted_factorA = factors['forward_adj_factorA'].values.copy()

            # 只处理forward_adj_factorB不等于0的复权日
            factorB_nonzero_mask = factors['forward_adj_factorB'] != 0.0

            if factorB_nonzero_mask.any():
                logger.info(f"发现 {factorB_nonzero_mask.sum()} 个复权日的forward_adj_factorB不为0，需要调整")

                # 获取需要调整的复权日索引
                factors_to_adjust_indices = np.where(factorB_nonzero_mask)[0]
                factors_to_adjust_dates = factors.index[factorB_nonzero_mask]

                # 使用searchsorted进行向量化查找
                full_df_index_values = full_df.index.values
                factors_index_values = factors_to_adjust_dates.values

                # 使用searchsorted找到每个复权日在full_df中的位置
                positions = np.searchsorted(full_df_index_values, factors_index_values, side='left')

                # 计算前一日位置（position - 1）
                prev_positions = positions - 1

                # 创建mask，标识哪些复权日有有效的前一日数据
                valid_mask = prev_positions >= 0

                if valid_mask.any():
                    # 向量化获取前一日收盘价
                    valid_prev_positions = prev_positions[valid_mask]
                    prev_closes = full_df.iloc[valid_prev_positions]['close'].values

                    # 获取需要调整且有前一日数据的复权因子
                    valid_factors_indices = factors_to_adjust_indices[valid_mask]
                    valid_original_factorA = factors.iloc[valid_factors_indices]['forward_adj_factorA'].values
                    valid_original_factorB = factors.iloc[valid_factors_indices]['forward_adj_factorB'].values

                    # 向量化计算调整后的factorA
                    # 调整后forward_adj_factorA = (前一日收盘价 * forward_adj_factorA + forward_adj_factorB) / 前一日收盘价
                    valid_adjusted_factorA = (prev_closes * valid_original_factorA + valid_original_factorB) / prev_closes

                    # 更新对应位置的调整后factorA
                    adjusted_factorA[valid_factors_indices] = valid_adjusted_factorA

                    # 记录调整信息
                    for i, (ex_date, original_idx) in enumerate(zip(factors_to_adjust_dates[valid_mask], valid_factors_indices)):
                        prev_close = prev_closes[i]
                        original_factorA = valid_original_factorA[i]
                        original_factorB = valid_original_factorB[i]
                        adj_factorA = valid_adjusted_factorA[i]
                        logger.info(f"复权日 {ex_date.date()}: 前一日收盘价={prev_close:.4f}, "
                                  f"原始factorA={original_factorA:.6f}, factorB={original_factorB:.6f}, "
                                  f"调整后factorA={adj_factorA:.6f}")

                # 记录没有前一日数据的复权日
                invalid_mask = ~valid_mask
                if invalid_mask.any():
                    for ex_date in factors_to_adjust_dates[invalid_mask]:
                        logger.warning(f"[conid: {conid}, ib_symbol: {ib_symbol}, firstrate_symbol: {firstrate_symbol}] 复权日 {ex_date.date()}: 找不到前一日数据，使用原始factorA")
            else:
                logger.info("所有复权日的forward_adj_factorB都为0，无需调整")

            # 添加调整后的forward_adj_factorA列到factors
            factors['adjusted_forward_adj_factorA'] = adjusted_factorA
            # logger.info(f"调整后复权因子: \n{factors[['forward_adj_factorA', 'forward_adj_factorB', 'adjusted_forward_adj_factorA']]}")

        # 9. 如果没有复权因子，直接用原始symbol入库
        if factors.empty: # 统一复权模式情况14、分红合约式复权模式情况31
            # 将数据转换为BarData对象并保存
            all_bars = []
            for row in full_df.itertuples():
                bar = BarData(
                    symbol=str(conid),
                    exchange=Exchange.SMART,
                    datetime=row.Index.to_pydatetime(),
                    interval=self.interval,
                    volume=float(row.volume),
                    open_price=float(row.open),
                    high_price=float(row.high),
                    low_price=float(row.low),
                    close_price=float(row.close),
                    turnover=0.0,
                    open_interest=0.0,
                    gateway_name="FIRSTRATE"
                )
                all_bars.append(bar)

            # 保存到数据库
            if all_bars:
                self.database_saver.put_bars(all_bars, ib_symbol)
                logger.info(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
                return True
            else:
                logger.info(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
                return False

        if self.use_contract_style:
            if not self.use_detailed_adjustment:
                # 2.1 统一复权模式（合约式复权处理逻辑，兼容分钟线和日线）
                # 2.1.1.1. 如果有overview，更新原始数据的symbol
                # 获取第一个除权日前一个交易日
                first_ex_date = factors.index[0]
                first_ex_date_et = first_ex_date.replace(tzinfo=ET_TZ)
                prev_trading_day = self.get_previous_trading_day(first_ex_date)
                first_symbol = f"{conid}_{prev_trading_day.strftime('%y%m%d')}"
                if overview: 
                    # 情况1（无交叉）、2（交叉多次后续复权所需窗口）：数据库中已有数据，改名为first_symbol
                    # 更新数据库中的symbol
                    # 添加重试逻辑，不限制重试次数
                    retry_count = 0
                    update_count = 0

                    while True:
                        try:
                            with self.database_saver.mysql_database.db.atomic():
                                update_count = (self.DbBarData
                                .update(symbol=first_symbol)
                                .where(
                                    (self.DbBarData.symbol == str(conid)) &
                                    (self.DbBarData.exchange == Exchange.SMART.value) &
                                    (self.DbBarData.interval == self.interval.value))
                                .execute())

                                # 更新视图表
                                (self.DbBarOverview
                                .update(symbol=first_symbol)
                                .where(
                                    (self.DbBarOverview.symbol == str(conid)) &
                                    (self.DbBarOverview.exchange == Exchange.SMART.value) &
                                    (self.DbBarOverview.interval == self.interval.value))
                                .execute())
                            break  # 成功则跳出重试循环
                        except Exception as e:
                            # 检查是否是锁错误
                            if 'lock' in str(e).lower():
                                retry_count += 1
                                wait_time = min(0.5 * (2 ** retry_count), 30)  # 指数退避策略，最大等待30秒
                                logger.warning(f"更新symbol时遇到数据库锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                                sleep(wait_time)
                            else:
                                # 非锁错误直接抛出
                                raise
                    logger.info(f"更新完成: {conid} -> {first_symbol}, 更新记录数: {update_count}")

                # 2.1.2.1. 初始化存储不同时间段数据的字典
                bars_dict = defaultdict(list)

                # 2.1.2.2. 如果有overview，加载并拼接原始数据
                if overview:
                    # 确定加载的时间
                    # full_df_min_dbtz = full_df.index.min().astimezone(DB_TZ).replace(tzinfo=None) # 先ET_TZ转换时区为DB_TZ，再去除时区信息
                    # end_date = min(overview.end, full_df_min_dbtz)
                    # start_date = max(end_date.replace(tzinfo=DB_TZ).astimezone(ET_TZ), first_ex_date_et)
                    start_date = max(overview_end_et, first_ex_date_et)
                    if self.interval == Interval.MINUTE:
                        start_date = (start_date - relativedelta(months=4)).replace(day=1, hour=0, minute=0, second=0, microsecond=0).astimezone(DB_TZ).replace(tzinfo=None)
                    else:
                        start_date = (start_date - relativedelta(years=4)).replace(day=1, hour=0, minute=0, second=0, microsecond=0).astimezone(DB_TZ).replace(tzinfo=None)

                    # 只有当start_date小于end_date时才加载数据
                    if start_date < overview.end:
                        # 加载原始数据，使用first_symbol
                        original_bars = self.database_saver.mysql_database.load_bar_data(
                            first_symbol,
                            Exchange.SMART,
                            self.interval,
                            start_date,
                            overview.end
                        )

                        # 将原始数据转换为DataFrame，并将时区从DB_TZ转换为ET_TZ
                        if original_bars:
                            logger.info(f"加载到原始数据{start_date} -> {overview.end}: {len(original_bars)} 条")
                            original_df = pd.DataFrame({
                                'open': [bar.open_price for bar in original_bars],
                                'high': [bar.high_price for bar in original_bars],
                                'low': [bar.low_price for bar in original_bars],
                                'close': [bar.close_price for bar in original_bars],
                                'volume': [bar.volume for bar in original_bars]
                            }, index=[bar.datetime.astimezone(ET_TZ) for bar in original_bars])

                            # 拼接数据
                            full_df = pd.concat([original_df, full_df])
                            # 如果有重复数据，日志警告
                            if full_df.index.duplicated().any():
                                logger.warning(f"加载数据后发现重复时间戳: {first_symbol}, 重复数量: {full_df.index.duplicated().sum()}")
                                # 去除重复的时间戳
                                full_df = full_df[~full_df.index.duplicated(keep='first')]  # 去除重复的时间戳
                            full_df.sort_index(inplace=True)  # 按时间排序

                total_rows = len(full_df)
                logger.info(f"开始处理 {total_rows} 条数据的复权...")

                # 2.1.2.3. 预先计算所有的cutoff_dates以提高性能
                if self.interval == Interval.DAILY:
                    # 日线数据使用4年
                    cutoff_dates = {ex_date: (ex_date - relativedelta(years=4)).replace(day=1).date() for ex_date in factors.index}
                else:
                    # 分钟线数据使用4个月
                    cutoff_dates = {ex_date: (ex_date - relativedelta(months=4)).replace(day=1).date() for ex_date in factors.index}
                # print(factors)

                # 2.1.2.4. 遍历数据并应用复权因子
                for row in full_df.itertuples():
                    idx = row.Index
                    date = idx.date()

                    # 创建基础BarData对象
                    base_bar = BarData(
                        symbol=str(conid),  # 临时使用，后面会修改
                        exchange=Exchange.SMART,
                        datetime=idx.to_pydatetime(),
                        interval=self.interval,
                        volume=float(row.volume),
                        open_price=float(row.open),
                        high_price=float(row.high),
                        low_price=float(row.low),
                        close_price=float(row.close),
                        turnover=0.0,
                        open_interest=0.0,
                        gateway_name="FIRSTRATE"
                    )

                    # 遍历复权factors的每行，根据数据date与所需最早日期、当前复权事件日期判断是否需要：复权base_bar/copy(base_bar)/改名symbol(first_symbol或target_symbol)/保存
                    for i, (current_ex_date, current_factors) in enumerate(factors.iterrows()):
                        cutoff_date = cutoff_dates[current_ex_date]
                        current_ex_date_ = current_ex_date.date()
                        current_factor_A = current_factors['adjusted_forward_adj_factorA']
                        # current_factor_B = current_factors['forward_adj_factorB']

                        # 2.1.2.4.0 根据下一个除权日确定next_ex_date_(可能无）、target_symbol
                        next_ex_date_ = factors.index[i + 1].date() if i < len(factors) - 1 else None
                        target_symbol = f"{conid}_{self.get_previous_trading_day(next_ex_date_).strftime('%y%m%d')}" if next_ex_date_ is not None else str(conid)
                        no_copy = False

                        # 判断日期区间和是否需要复权
                        # 2.1.2.4.1. 过于久远的数据（date < cutoff_date），跳过不保存
                        if date < cutoff_date:
                            # 情况1、4下方：过于久远的数据，跳过不保存
                            # logger.info(f"因子{i}: 过于久远的数据，跳过 (date={date} < cutoff={cutoff_date})")
                            continue
                        
                        # 2.1.2.4.2. 所需最早日期 <= date < 当前复权事件日期：
                        elif date < current_ex_date_:
                            # logger.info(f"因子{i}: 需要复权的数据 (cutoff={cutoff_date} <= date={date} < ex_date={current_ex_date_})")
                            # 2.1.2.4.2.1. 特殊处理：数据库first_symbol（conid改名而来）数据续接部分
                            # 再往历史无复权因子，且数据晚于overview结束时间（或无overview）：不复权、copy、改名first_symbol、保存
                            if i == 0 and (overview_end_et is None or idx > overview_end_et):
                                # 只在是区间所属复权因子（且第一个）时处理期间数据
                                # 情况3：数据库无，新无需复权数据，改名为first_symbol
                                # logger.info(f"因子{i}: 情况3 - 保存到first_symbol={first_symbol}, 不复权")
                                unadj_bar = copy(base_bar)
                                unadj_bar.symbol = first_symbol
                                bars_dict[first_symbol].append(unadj_bar)
                            
                            # 2.1.2.4.2.2. 同时：复权base_bar、copy、改名target_symbol、保存
                            # 情况4：数据库有，改名之后读出来，复权、copy、改名为target_symbol(后缀)、保存
                            # 情况5、6、9：数据库无，复权、copy、改名target_symbol(后缀)、保存
                            # 情况7、10、12：数据库无，复权、copy、改名target_symbol(conid)、保存
                            # original_close = base_bar.close_price
                            base_bar.volume = base_bar.volume / current_factor_A
                            base_bar.open_price = base_bar.open_price * current_factor_A #+ current_factor_B
                            base_bar.high_price = base_bar.high_price * current_factor_A #+ current_factor_B
                            base_bar.low_price = base_bar.low_price * current_factor_A #+ current_factor_B
                            base_bar.close_price = base_bar.close_price * current_factor_A #+ current_factor_B
                            # logger.info(f"因子{i}: 应用调整后复权因子，收盘价: {original_close:.4f} -> {base_bar.close_price:.4f} (调整后factorA={current_factor_A:.6f})")

                        # 2.1.2.4.3. 当前复权事件日期 <= date：
                        else:  # current_ex_date_ <= date
                            # logger.info(f"因子{i}: 除权日后数据 (ex_date={current_ex_date_} <= date={date})")
                            # 2.1.2.4.3.1. 还有下一个复权事件
                            if next_ex_date_ is not None:
                                if date < next_ex_date_:
                                    # 2.1.2.4.3.1.1. 历史复权因子后的第一段复权窗口：保存、不复权、copy、改名target_symbol
                                    # 情况8、11
                                    pass
                                    # logger.info(f"因子{i}: 情况8、11 - 不复权，保存到target_symbol={target_symbol}")
                                else:
                                    # 2.1.2.4.3.1.2. 数据date在下一个复权事件日期及之后，即最后一个除权日前的所属下一个除权日后的数据：跳过不保存
                                    # 情况8、11、13上方：跳过不保存
                                    # logger.info(f"因子{i}: 跳过保存 (date={date} >= next_ex_date={next_ex_date_})")
                                    continue
                            else:
                                # 2.1.2.4.3.2. 没有下一个复权事件，即最后一个除权日后的无所属下一个除权日最新数据：保存、不复权、不copy、不改名
                                # 情况13：最新数据，保存、不复权、不copy、不改名
                                # logger.info(f"因子{i}: 情况13 - 最新数据，不copy，保存到conid={conid}")
                                no_copy = True

                        # 创建bar副本并设置正确的symbol
                        if target_symbol == str(conid):
                            if no_copy:
                                # 情况13：最新数据，保存、不复权、不copy、不改名
                                # logger.info(f"因子{i}: 直接保存到conid={conid} (no_copy)")
                                bars_dict[target_symbol].append(base_bar)
                            else:
                                # 情况7、10、12：没有下一个复权事件，即最后一个除权日后的无所属下一个除权日最新数据：保存、不复权、copy、改名target_symbol
                                # logger.info(f"因子{i}: copy后保存到conid={conid}")
                                bars_dict[target_symbol].append(copy(base_bar))
                        else:
                            # 情况3、4、5、6、8、9、11：使用调整后的symbol，创建副本
                            # logger.info(f"因子{i}: copy后保存到target_symbol={target_symbol}")
                            adjusted_bar = copy(base_bar)
                            adjusted_bar.symbol = target_symbol
                            bars_dict[target_symbol].append(adjusted_bar)

                logger.info(f"复权处理完成，共处理 {total_rows} 条数据")

                # 2.1.2.5. 保存所有时间段的数据
                for symbol, bars in bars_dict.items():
                    if bars:
                        self.database_saver.put_bars(bars, f"{ib_symbol}_{symbol}")
                        logger.info(f"已将数据加入保存队列: {ib_symbol}_{symbol}, 数据量: {len(bars)}")

                return True

            else:
                # 分红合约式复权模式实现
                logger.info("使用分红合约式复权模式")
                
                # 2.2.1. 首先判断factors中是否有dividend或both事件，维护成div_ex_dates
                div_ex_dates = []
                both_ex_dates = []
                first_div_ex_date = None
                first_div_ex_date_et = None
                first_symbol = None
                
                for factors_tuple in factors.itertuples():
                    ex_date = factors_tuple.Index
                    adjustment_type = self.classify_adj_type(factors_tuple)
                    if adjustment_type in ['dividend', 'both']:
                        div_ex_dates.append(ex_date)
                        if first_div_ex_date is None:
                            first_div_ex_date = ex_date
                            first_div_ex_date_et = ex_date.replace(tzinfo=ET_TZ)
                            prev_trading_day = self.get_previous_trading_day(ex_date)
                            first_symbol = f"{conid}_{prev_trading_day.strftime('%y%m%d')}"
                        if adjustment_type == 'both':
                            both_ex_dates.append(ex_date)
                
                logger.info(f"分红事件日期: {div_ex_dates}")
                logger.info(f"第一个分红事件日期: {first_div_ex_date}")
                logger.info(f"第一个symbol: {first_symbol}")

                # (自算)
                # # 2.2.2. 根据interval区别处理计算factors每个ex_div_date的各种复权因子
                # # - 分钟线复权：使用数据库中的prev_close字段（不复权日线收盘价）作为参考收盘价
                # # - 日线复权：用full_df的原始close计算（二分查找除权日前最接近的收盘价）
                # factors_with_cumulative = factors.copy()
                # # 为每个复权日计算复权因子，需要使用full_df中<ex_date且离ex_date最近的收盘价
                # share_factors = []
                # div_factors = []
                # combined_factors = []
                # # 预先排序full_df的索引以提高性能
                # full_df_sorted_index = None
                # if self.interval == Interval.DAILY:
                #     full_df_sorted_index = full_df.index.sort_values()
                # for factors_tuple in factors.itertuples():
                #     ex_date = factors_tuple.Index
                #     ex_date_et = ex_date.replace(tzinfo=ET_TZ)
                #     # 根据时间间隔类型决定如何获取参考收盘价
                #     if self.interval == Interval.DAILY:
                #         # 日线复权：使用二分查找找到小于ex_date的最后一个索引位置
                #         insert_pos = bisect.bisect_left(full_df_sorted_index, ex_date_et)
                #         if insert_pos > 0:
                #             # 找到了小于ex_date的数据，取最接近的一个
                #             closest_date = full_df_sorted_index[insert_pos - 1]
                #             reference_close_price = full_df.loc[closest_date, 'close']
                #             logger.debug(f"除权日 {ex_date.date()} 使用参考收盘价: {reference_close_price} (来自 {closest_date.date()})")
                #             # 计算各种复权因子
                #             share_factor = self.cal_share_factor(factors_tuple)
                #             div_factor = self.cal_div_factor(factors_tuple, reference_close_price)
                #             combined_factor = share_factor * div_factor
                #         else:
                #             # 没找到数据，所有因子都赋值为1
                #             logger.warning(f"除权日 {ex_date.date()} 之前无数据，所有复权因子设为1")
                #             share_factor = 1.0
                #             div_factor = 1.0
                #             combined_factor = 1.0
                #     else:
                #         # 分钟线复权：使用数据库中的prev_close字段（不复权日线收盘价）
                #         reference_close_price = factors_tuple.prev_close
                #         if not reference_close_price:
                #             logger.warning(f"除权日 {ex_date.date()} 缺少prev_close字段，所有复权因子设为1")
                #             share_factor = 1.0
                #             div_factor = 1.0
                #             combined_factor = 1.0
                #         else:
                #             logger.debug(f"除权日 {ex_date.date()} 使用数据库prev_close: {reference_close_price}")
                #             # 计算各种复权因子
                #             share_factor = self.cal_share_factor(factors_tuple)
                #             div_factor = self.cal_div_factor(factors_tuple, reference_close_price)
                #             combined_factor = share_factor * div_factor
                #     share_factors.append(share_factor)
                #     div_factors.append(div_factor)
                #     combined_factors.append(combined_factor)
                # factors_with_cumulative['share_factor'] = share_factors
                # factors_with_cumulative['div_factor'] = div_factors
                # factors_with_cumulative['combined_factor'] = combined_factors
                # # 计算"后续股数总因子"share_forward_cumprod：倒序排列后的share_factor列的expanding累乘
                # reversed_share_factors = np.array(share_factors[::-1])
                # cumulative_share_factors = np.cumprod(reversed_share_factors)
                # factors_with_cumulative['share_forward_cumprod'] = cumulative_share_factors[::-1]
                # logger.info(f"复权因子计算完成:\n{factors_with_cumulative[['share_factor', 'div_factor', 'combined_factor', 'share_forward_cumprod']]}")
                # (futu)
                # 2.2.2. 计算factors每个ex_div_date的股数变动因子
                # 迭代传递使用forward_adj_factorA和forward_adj_factorB（与统一复权模式一致）
                factors_with_cumulative = factors.copy()
                # 为每个复权日计算股数变动因子
                share_factors = []
                for factors_tuple in factors.itertuples():
                    # 计算股数变动因子
                    share_factor = self.cal_share_factor(factors_tuple)
                    share_factors.append(share_factor)
                factors_with_cumulative['share_factor'] = share_factors
                # 计算"后续股数总因子"share_forward_cumprod：倒序排列后的share_factor列的expanding累乘
                reversed_share_factors = np.array(share_factors[::-1])
                cumulative_share_factors = np.cumprod(reversed_share_factors)
                factors_with_cumulative['share_forward_cumprod'] = cumulative_share_factors[::-1]
                logger.info(f"复权因子计算完成:\n{factors_with_cumulative[['share_factor', 'share_forward_cumprod']]}")
                
                # 2.2.3. 核心处理分为两部分：数据库中有的数据和数据库中没有的数据
                
                # 2.2.3.1. 数据库中有的数据，update调整
                if overview:
                    logger.info("处理数据库中已有的数据")
                    
                    # 2.2.3.1.1. 数据库中已有以"conid_"开头的量价数据update，用第一个"后续股数总因子"复权（情况0、27）
                    share_forward_cumprod = factors_with_cumulative['share_forward_cumprod'].iloc[0]
                    logger.info(f"应用第一个后续股数总因子: {share_forward_cumprod}")
                    
                    # 更新以"conid_"开头的数据
                    retry_count = 0
                    while True:
                        try:
                            with self.database_saver.mysql_database.db.atomic():
                                # 更新价格和成交量字段
                                update_count = (self.DbBarData
                                    .update(
                                        open_price=self.DbBarData.open_price * share_forward_cumprod,
                                        high_price=self.DbBarData.high_price * share_forward_cumprod,
                                        low_price=self.DbBarData.low_price * share_forward_cumprod,
                                        close_price=self.DbBarData.close_price * share_forward_cumprod,
                                        volume=self.DbBarData.volume / share_forward_cumprod
                                    )
                                    .where(
                                        (self.DbBarData.symbol.startswith(f"{conid}_")) &
                                        (self.DbBarData.exchange == Exchange.SMART.value) &
                                        (self.DbBarData.interval == self.interval.value)
                                    )
                                    .execute())
                                logger.info(f"更新以conid_开头的数据: {update_count} 条记录")
                            break
                        except Exception as e:
                            if 'lock' in str(e).lower():
                                retry_count += 1
                                wait_time = min(0.5 * (2 ** retry_count), 30)
                                logger.warning(f"更新数据时遇到锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                                sleep(wait_time)
                            else:
                                raise
                    
                    # 2.2.3.1.2. 如果有overview，处理symbol为conid的数据
                    # 2.2.3.1.2.1. 计算start_date
                    start_date = overview_end_et
                    if first_div_ex_date is not None:
                        start_date = max(start_date, first_div_ex_date_et)
                    
                    if self.interval == Interval.MINUTE:
                        start_date = (start_date - relativedelta(months=4)).replace(day=1, hour=0, minute=0, second=0, microsecond=0).astimezone(DB_TZ).replace(tzinfo=None)
                    else:
                        start_date = (start_date - relativedelta(years=4)).replace(day=1, hour=0, minute=0, second=0, microsecond=0).astimezone(DB_TZ).replace(tzinfo=None)
                    
                    # 2.2.3.1.2.2. 判断first_symbol
                    if first_symbol is None:
                        # 2.2.3.1.2.2.1. 为None：将symbol为conid的应用第一个"后续股数总因子"列update
                        # 情况28：新数据事件无分红有股数，将symbol为conid的应用第一个"后续股数总因子"列update
                        retry_count = 0
                        while True:
                            try:
                                with self.database_saver.mysql_database.db.atomic():
                                    update_count = (self.DbBarData
                                        .update(
                                            open_price=self.DbBarData.open_price * share_forward_cumprod,
                                            high_price=self.DbBarData.high_price * share_forward_cumprod,
                                            low_price=self.DbBarData.low_price * share_forward_cumprod,
                                            close_price=self.DbBarData.close_price * share_forward_cumprod,
                                            volume=self.DbBarData.volume / share_forward_cumprod
                                        )
                                        .where(
                                            (self.DbBarData.symbol == str(conid)) &
                                            (self.DbBarData.exchange == Exchange.SMART.value) &
                                            (self.DbBarData.interval == self.interval.value)
                                        )
                                        .execute())
                                    logger.info(f"更新symbol为conid的数据: {update_count} 条记录")
                                break
                            except Exception as e:
                                if 'lock' in str(e).lower():
                                    retry_count += 1
                                    wait_time = min(0.5 * (2 ** retry_count), 30)
                                    logger.warning(f"更新数据时遇到锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                                    sleep(wait_time)
                                else:
                                    raise
                    else:
                        # 2.2.3.1.2.2.2. 非None
                        # 2.2.3.1.2.2.2.1. （start_date < overview.end时才）load_bar_data加载并拼接symbol（conid）的start_date后的数据作为full_df
                        if start_date < overview.end:
                            # 加载原始数据，使用conid，改名为first_symbol之前读出来
                            original_bars = self.database_saver.mysql_database.load_bar_data(
                                str(conid),
                                Exchange.SMART,
                                self.interval,
                                start_date,
                                overview.end
                            )
                            
                            if original_bars:
                                logger.info(f"加载到原始数据{start_date} -> {overview.end}: {len(original_bars)} 条")
                                original_df = pd.DataFrame({
                                    'open': [bar.open_price for bar in original_bars],
                                    'high': [bar.high_price for bar in original_bars],
                                    'low': [bar.low_price for bar in original_bars],
                                    'close': [bar.close_price for bar in original_bars],
                                    'volume': [bar.volume for bar in original_bars]
                                }, index=[bar.datetime.astimezone(ET_TZ) for bar in original_bars])
                                
                                # 拼接数据
                                full_df = pd.concat([original_df, full_df])
                                if full_df.index.duplicated().any():
                                    logger.warning(f"加载数据后发现重复时间戳: {first_symbol}, 重复数量: {full_df.index.duplicated().sum()}")
                                    # 去除重复的时间戳
                                    full_df = full_df[~full_df.index.duplicated(keep='first')]
                                full_df.sort_index(inplace=True)
                        
                        # 2.2.3.1.2.2.2.2. 将symbol为conid：symbol update为first_symbol，应用第一个"后续股数总因子"列update
                        # 情况1：数据库有，改名为first_symbol之前读出来，改名、后续总股数复权、update
                        retry_count = 0
                        while True:
                            try:
                                with self.database_saver.mysql_database.db.atomic():
                                    # 同时更新symbol和应用第一个"后续股数总因子"
                                    update_count = (self.DbBarData
                                        .update(
                                            symbol=first_symbol,
                                            open_price=self.DbBarData.open_price * share_forward_cumprod,
                                            high_price=self.DbBarData.high_price * share_forward_cumprod,
                                            low_price=self.DbBarData.low_price * share_forward_cumprod,
                                            close_price=self.DbBarData.close_price * share_forward_cumprod,
                                            volume=self.DbBarData.volume / share_forward_cumprod
                                        )
                                        .where(
                                            (self.DbBarData.symbol == str(conid)) &
                                            (self.DbBarData.exchange == Exchange.SMART.value) &
                                            (self.DbBarData.interval == self.interval.value)
                                        )
                                        .execute())
                                    
                                    # 更新视图表
                                    (self.DbBarOverview
                                        .update(symbol=first_symbol)
                                        .where(
                                            (self.DbBarOverview.symbol == str(conid)) &
                                            (self.DbBarOverview.exchange == Exchange.SMART.value) &
                                            (self.DbBarOverview.interval == self.interval.value)
                                        )
                                        .execute())
                                    
                                    logger.info(f"更新symbol: {conid} -> {first_symbol}, 更新记录数: {update_count}")
                                    logger.info(f"应用后续股数总因子到first_symbol的数据")
                                break
                            except Exception as e:
                                if 'lock' in str(e).lower():
                                    retry_count += 1
                                    wait_time = min(0.5 * (2 ** retry_count), 30)
                                    logger.warning(f"更新数据时遇到锁错误，等待 {wait_time:.2f} 秒后第 {retry_count} 次重试...")
                                    sleep(wait_time)
                                else:
                                    raise
                
                # 2.2.3.2. 数据库中没有的数据，调整到位再入
                logger.info("处理数据库中没有的新数据")
                
                # 初始化存储不同时间段数据的字典
                bars_dict = defaultdict(list)
                
                # 预先计算所有的cutoff_dates
                if self.interval == Interval.DAILY:
                    cutoff_dates = {ex_date: (ex_date - relativedelta(years=4)).replace(day=1).date() for ex_date in factors.index}
                else:
                    cutoff_dates = {ex_date: (ex_date - relativedelta(months=4)).replace(day=1).date() for ex_date in factors.index}
                
                total_rows = len(full_df)
                logger.info(f"开始处理 {total_rows} 条数据的细分项复权...")
                
                # 2.2.3.2.1. 开始遍历full_df和factors每条数据，调整数据，判断first_div_ex_date
                for row in full_df.itertuples():
                    idx = row.Index
                    date = idx.date()
                    
                    # 创建基础BarData对象
                    base_bar = BarData(
                        symbol=str(conid),
                        exchange=Exchange.SMART,
                        datetime=idx.to_pydatetime(),
                        interval=self.interval,
                        volume=float(row.volume),
                        open_price=float(row.open),
                        high_price=float(row.high),
                        low_price=float(row.low),
                        close_price=float(row.close),
                        turnover=0.0,
                        open_interest=0.0,
                        gateway_name="FIRSTRATE"
                    )
                    
                    # 2.2.3.2.1.1. 为None：对于overview_end_et（如果有）之后的每个date的数据分情况处理
                    if first_div_ex_date is None:
                        # 检查是否在overview_end_et之后
                        if overview_end_et is None or idx > overview_end_et:
                            # 查找是否有后续的拆合股事件需要应用
                            found_factor = False
                            for factors_tuple in factors_with_cumulative.itertuples():
                                ex_date = factors_tuple.Index
                                if date < ex_date.date():
                                    # 2.2.3.2.1.1.1. 有后续拆合股事件：copy、副本上应用"后续股数总因子"、不改名、保存
                                    # 情况29：无新分红的后续有拆合股的部分的数据
                                    share_forward_cumprod = factors_tuple.share_forward_cumprod
                                    adjusted_bar = copy(base_bar)
                                    adjusted_bar.volume = adjusted_bar.volume / share_forward_cumprod
                                    adjusted_bar.open_price = adjusted_bar.open_price * share_forward_cumprod
                                    adjusted_bar.high_price = adjusted_bar.high_price * share_forward_cumprod
                                    adjusted_bar.low_price = adjusted_bar.low_price * share_forward_cumprod
                                    adjusted_bar.close_price = adjusted_bar.close_price * share_forward_cumprod
                                    # 不改名copy后add到bars_dict
                                    bars_dict[str(conid)].append(adjusted_bar)
                                    found_factor = True
                                    break  # 找到第一个符合条件的就退出
                            
                            if not found_factor:
                                # 2.2.3.2.1.1.2. 无后续拆合股事件：不copy、不复权、不改名、保存
                                # 情况30：无新分红的后续也无拆合股的部分的数据
                                logger.info(f"情况30: 直接保存到conid={conid} (无股数变动)")
                                bars_dict[str(conid)].append(base_bar)

                    else:
                        # 2.2.3.2.1.2. 非None：根据数据date与所需最早日期、当前复权事件日期判断是否需要：复权base_bar/copy(base_bar)/改名symbol(first_symbol或target_symbol)/保存
                        period_saved = False

                        # 遍历每个复权因子，按时间顺序应用
                        for i, (current_ex_date, current_factors) in enumerate(factors_with_cumulative.iterrows()):
                            cutoff_date = cutoff_dates[current_ex_date]
                            current_ex_date_ = current_ex_date.date()

                            # (自算)
                            # combined_factor = current_factors['combined_factor']
                            # (futu)
                            current_factor_A = current_factors['adjusted_forward_adj_factorA']
                            # current_factor_B = current_factors['forward_adj_factorB']

                            share_forward_cumprod = current_factors['share_forward_cumprod']

                            # 2.2.3.2.1.2.0. 根据div_ex_dates的next_div_ex_date(可能无）、target_symbol("conid_{next_div_ex_date_前一个交易日%y%m%d}")

                            # 根据下一个除权日确定next_ex_date_(可能无）
                            next_ex_date_ = factors_with_cumulative.index[i + 1].date() if i < len(factors) - 1 else None
                            # i+1行的share_forward_cumprod
                            next_share_forward_cumprod = factors_with_cumulative['share_forward_cumprod'].iloc[i + 1] if i < len(factors) - 1 else 1

                            # 找到相对于当前复权事件日期的下一个分红除权日
                            next_div_ex_date = None
                            for div_date in div_ex_dates:
                                if div_date.date() > current_ex_date_:
                                    next_div_ex_date = div_date
                                    break
                            next_div_ex_date_ = next_div_ex_date.date() if next_div_ex_date is not None else None
                            target_symbol = f"{conid}_{self.get_previous_trading_day(next_div_ex_date_).strftime('%y%m%d')}" if next_div_ex_date_ is not None else str(conid)
                            no_copy = False
                            
                            logger.info(f"因子{i}: ex_date={current_ex_date_}, cutoff={cutoff_date}")
                            logger.info(f"因子{i}: next_div_ex_date={next_div_ex_date_}, target_symbol={target_symbol}")

                            # 判断日期区间和是否需要复权
                            # 2.2.3.2.1.2.1. 过于久远的数据（date < cutoff_date），跳过不保存
                            if date < cutoff_date:
                                # 情况0、2、4下方：过于久远的数据，跳过不保存
                                logger.info(f"因子{i}: 过于久远的数据，跳过 (date={date} < cutoff={cutoff_date})")
                                continue
                            
                            # 2.2.3.2.1.2.2. 所需最早日期 <= date < 当前复权事件日期current_ex_date_：
                            elif date < current_ex_date_:
                                if i == 0 and (overview_end_et is None or idx > overview_end_et):
                                    # 2.2.3.2.1.2.2.1. 特殊处理：数据库first_symbol（conid改名而来）数据续接部分如果还没存，且在overview_end_et（如果有）至新的first_div_ex_date之间
                                    # 情况3：数据库无，再往历史无复权因子：copy、副本上应用"后续股数总因子"、改名first_symbol、保存
                                    logger.info(f"因子{i}: 情况3 - 保存到first_symbol={first_symbol}, 应用股数因子={share_forward_cumprod}")
                                    unadj_bar = copy(base_bar)
                                    unadj_bar.symbol = first_symbol

                                    # 应用current_ex_date当次的"后续股数总因子"
                                    unadj_bar.volume = unadj_bar.volume / share_forward_cumprod
                                    unadj_bar.open_price = unadj_bar.open_price * share_forward_cumprod
                                    unadj_bar.high_price = unadj_bar.high_price * share_forward_cumprod
                                    unadj_bar.low_price = unadj_bar.low_price * share_forward_cumprod
                                    unadj_bar.close_price = unadj_bar.close_price * share_forward_cumprod

                                    bars_dict[first_symbol].append(unadj_bar)

                                # 2.2.3.2.1.2.2.2. 同时：复权base_bar、copy、改名target_symbol、保存
                                # 情况2：数据库有，改名之后读出来，复权、copy、改名为target_symbol(后缀)、保存
                                # 情况4、6、10：数据库无，复权、copy、改名target_symbol(后缀)、保存
                                # 情况7、8、11、12、14、15、17、18、20、22、24：数据库无，复权、copy、改名target_symbol(conid)、保存

                                # (自算)
                                # base_bar.volume = base_bar.volume / combined_factor
                                # base_bar.open_price = base_bar.open_price * combined_factor
                                # base_bar.high_price = base_bar.high_price * combined_factor
                                # base_bar.low_price = base_bar.low_price * combined_factor
                                # base_bar.close_price = base_bar.close_price * combined_factor
                                # (futu)
                                original_close = base_bar.close_price
                                base_bar.volume = base_bar.volume / current_factor_A
                                base_bar.open_price = base_bar.open_price * current_factor_A #+ current_factor_B
                                base_bar.high_price = base_bar.high_price * current_factor_A #+ current_factor_B
                                base_bar.low_price = base_bar.low_price * current_factor_A #+ current_factor_B
                                base_bar.close_price = base_bar.close_price * current_factor_A #+ current_factor_B
                                logger.info(f"因子{i}: 应用调整后复权因子，收盘价: {original_close:.4f} -> {base_bar.close_price:.4f} (调整后factorA={current_factor_A:.6f})")

                                if current_ex_date not in div_ex_dates:
                                    # 2.2.3.2.1.2.2.3. # 各有线行之间的无线行：跳过不保存
                                    logger.info(f"因子{i}: 非分红事件，跳过保存")
                                    continue

                                if current_ex_date in both_ex_dates:
                                    # 既分红又股数变动，不重复应用当次“后续股数总因子”
                                    share_forward_cumprod = next_share_forward_cumprod

                            # 2.2.3.2.1.2.3. 当前复权事件日期 <= date:时间区间以因子数量n拆为n+1段，遍历时少一段，此处处理第n+1段
                            else:  # current_ex_date_ <= date
                                logger.info(f"因子{i}: 除权日后数据 (ex_date={current_ex_date_} <= date={date})")
                                if next_div_ex_date_ is not None:
                                    # 2.2.3.2.1.2.3.1. 还有下一个分红复权事件
                                    if date < next_div_ex_date_ and next_ex_date_ == next_div_ex_date_:
                                        # 2.2.3.2.1.2.3.1.1. 数据date正好（防重复）在下一个分红复权事件日期之前，即最后一个分红除权日前的最新数据
                                        # 情况5、9、13、16、19、21、23：历史复权因子后的第一段复权窗口：copy、副本上应用下次的"后续股数总因子"、改名target_symbol、保存
                                        pass
                                    else:
                                        # 2.2.3.2.1.2.3.1.2. 数据date在下一个分红复权事件日期及之后，即最后一个分红除权日前的所属下一个分红除权日后的数据：跳过不保存
                                        # 情况1、3、5、9、13、16、19、21、23、25、26上方：跳过不保存
                                        logger.info(f"因子{i}: 跳过保存 (date={date} >= next_div_ex_date={next_div_ex_date_})")
                                        continue
                                else:
                                    # 2.2.3.2.1.2.3.2. 没有下一个分红复权事件，即最后一个分红除权日后的无所属下一个分红除权日最新数据
                                    if i < len(factors) - 1:
                                        # 2.2.3.2.1.2.3.2.1. 有下一个复权事件没有下一个分红复权事件：copy、副本上应用下次的"后续股数总因子"、不改名（为conid）、保存
                                        # 情况25：历史复权因子后的第一段复权窗口
                                        if period_saved:
                                            continue
                                        period_saved = True
                                        logger.info(f"因子{i}: 情况25 - 保存到conid={conid}")
                                    else:
                                        # 2.2.3.2.1.2.3.2.2. 没有下一个复权事件：不copy、副本上应用下次的"后续股数总因子（赋值为1）"、不改名（为conid）、保存
                                        # 情况26：最终数据，直接保存为conid
                                        logger.info(f"因子{i}: 情况26 - 最新数据，不copy，保存到conid={conid}")
                                        no_copy = True
                                share_forward_cumprod = next_share_forward_cumprod

                            # 创建bar副本并设置正确的symbol
                            if no_copy and target_symbol == str(conid):
                                # 使用原始symbol，直接添加base_bar，应用后续股数总因子
                                logger.info(f"因子{i}: 直接保存到conid={conid} (no_copy)")
                                adjusted_bar = base_bar
                            else:
                                # copy后应用后续股数总因子
                                logger.info(f"因子{i}: copy后保存到target_symbol={target_symbol}")
                                adjusted_bar = copy(base_bar)
                            if target_symbol != str(conid):
                                adjusted_bar.symbol = target_symbol
                            
                            # 应用后续股数总因子
                            original_close_final = adjusted_bar.close_price
                            adjusted_bar.volume = adjusted_bar.volume / share_forward_cumprod
                            adjusted_bar.open_price = adjusted_bar.open_price * share_forward_cumprod
                            adjusted_bar.high_price = adjusted_bar.high_price * share_forward_cumprod
                            adjusted_bar.low_price = adjusted_bar.low_price * share_forward_cumprod
                            adjusted_bar.close_price = adjusted_bar.close_price * share_forward_cumprod
                            logger.info(f"因子{i}: 应用股数因子，收盘价: {original_close_final:.4f} -> {adjusted_bar.close_price:.4f}")

                            bars_dict[target_symbol].append(adjusted_bar)
                
                logger.info(f"细分项复权处理完成，共处理 {total_rows} 条数据")
                
                # 保存所有时间段的数据
                for symbol, bars in bars_dict.items():
                    if bars:
                        self.database_saver.put_bars(bars, f"{ib_symbol}_{symbol}")
                        logger.info(f"已将数据加入保存队列: {ib_symbol}_{symbol}, 数据量: {len(bars)}")
                
                return True

        else:
            # 3.4. 前复权覆盖模式实现（兼容分钟线和日线）
            total_rows = len(full_df)
            logger.info(f"开始处理 {total_rows} 条数据的前复权...")

            # 3.4.1. 初始化存储复权数据的列表
            all_bars = []

            # 3.4.2. 遍历每条数据并应用前复权逻辑
            for row in full_df.itertuples():
                idx = row.Index
                date = idx.date()

                # 3.4.2.1. 创建基础BarData对象（使用conid作为统一symbol）
                base_bar = BarData(
                    symbol=str(conid),  # 前复权模式直接使用conid作为symbol
                    exchange=Exchange.SMART,
                    datetime=idx.to_pydatetime(),
                    interval=self.interval,
                    volume=float(row.volume),
                    open_price=float(row.open),
                    high_price=float(row.high),
                    low_price=float(row.low),
                    close_price=float(row.close),
                    turnover=0.0,
                    open_interest=0.0,
                    gateway_name="FIRSTRATE"
                )

                # 3.4.2.2. 遍历每个复权因子，按时间顺序应用前复权
                for i, (current_ex_date, current_factors) in enumerate(factors.iterrows()):
                    current_ex_date_ = current_ex_date.date()
                    current_factor_A = current_factors['adjusted_forward_adj_factorA']
                    # current_factor_B = current_factors['forward_adj_factorB']

                    # 3.4.2.3. 如果数据日期 < 除权日期，应用前复权因子
                    if date < current_ex_date_:
                        # 3.4.2.4. 应用前复权公式
                        base_bar.volume = base_bar.volume / current_factor_A  # 成交量除以复权因子
                        base_bar.open_price = base_bar.open_price * current_factor_A #+ current_factor_B  # 价格复权
                        base_bar.high_price = base_bar.high_price * current_factor_A #+ current_factor_B
                        base_bar.low_price = base_bar.low_price * current_factor_A #+ current_factor_B
                        base_bar.close_price = base_bar.close_price * current_factor_A #+ current_factor_B
                        # logger.info(f"标的 {conid} 在 {date} 应用除权日 {current_ex_date_} 的复权因子 ({current_factor_A}, {current_factor_B})")

                # 3.4.2.5. 添加复权后的数据到结果列表
                all_bars.append(base_bar)

            # 3.4.3. 保存所有复权后的数据到数据库
            if all_bars:
                self.database_saver.put_bars(all_bars, ib_symbol)
                logger.info(f"已将数据加入保存队列: {ib_symbol} (conid: {conid}), 数据量: {len(all_bars)}")
                return True
            else:
                logger.info(f"没有数据需要保存: {ib_symbol} (conid: {conid})")
                return False

    def process_all(self, conid_list: Optional[List[int]] = None, start_conid: Optional[int] = None,
                   n: Optional[int] = None) -> Tuple[int, int, List[int], List[int]]:
        """处理所有匹配的标的

        Args:
            conid_list: 要处理的conid列表，如果为None则处理所有匹配的标的
            start_conid: 从指定的conid开始处理，用于断点恢复
            n: 处理第1到n个conid（不包含第n个）

        Returns:
            Tuple[int, int, List[int], List[int]]: (成功数量, 失败数量, 成功的conid列表, 失败的conid列表)
        """
        if self.matched_df.empty:
            logger.warning("没有找到匹配的标的")
            return 0, 0, [], []

        # 如果不使用合约式复权，先清空对应interval的数据库数据
        if not self.use_contract_style:
            logger.info(f"清空数据库中的{self.interval.value}数据...")
            # 使用ORM删除对应interval的数据
            with self.database_saver.mysql_database.db.atomic():
                # 删除DbBarData表中的数据
                self.DbBarData.delete().where(self.DbBarData.interval == self.interval.value).execute()
                # 删除DbBarOverview表中的数据
                self.DbBarOverview.delete().where(self.DbBarOverview.interval == self.interval.value).execute()
            logger.info(f"{self.interval.value}数据清空完成")

        # 如果指定了conid列表，只处理这些conid
        if conid_list:
            self.matched_df = self.matched_df[self.matched_df['conid'].isin(conid_list)]
            if self.matched_df.empty:
                logger.warning(f"未找到指定的conid: {conid_list}")
                return 0, 0, [], []
            logger.info(f"将处理指定的 {len(self.matched_df)} 个标的")
        # 如果指定了start_conid，从该conid开始处理
        elif start_conid:
            start_idx = self.matched_df[self.matched_df['conid'] == start_conid].index
            if len(start_idx) > 0:
                self.matched_df = self.matched_df.loc[start_idx[0]:]
                logger.info(f"从conid {start_conid} 开始处理，剩余 {len(self.matched_df)} 个标的")
            else:
                logger.warning(f"未找到conid {start_conid}，将从头开始处理")
        # 如果指定了n，只处理前n个conid（不包含第n个）
        elif n is not None:
            # 保存第n个conid，用于后续输出
            next_conid = self.matched_df.iloc[n]['conid']
            self.matched_df = self.matched_df.iloc[:n]
            logger.info(f"将处理前 {n} 个标的，共 {len(self.matched_df)} 个")
            # 如果有下一个conid，输出用于继续处理
            logger.info("继续处理后续conid的命令:")
            logger.info(f"-c {next_conid}")

        success_count = 0
        fail_count = 0
        successful_conids = []  # 记录成功的conid
        failed_conids = []  # 记录失败的conid和symbol

        # 是否使用串行处理模式
        use_serial_mode = False
        if len(self.matched_df) <= 3:  # 如果标的数量很少，使用串行模式
            use_serial_mode = True
            logger.info("标的数量较少，使用串行处理模式")

        # 串行处理每个标的
        if use_serial_mode:
            for _, row in self.matched_df.iterrows():
                # 检查是否收到终止信号
                if is_terminating:
                    logger.warning("检测到终止信号，停止提交新任务...")
                    break
                    
                try:
                    if self.process_symbol(row):
                        success_count += 1
                        successful_conids.append(row['conid'])
                    else:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))
                except Exception as e:
                    logger.error(f"处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    fail_count += 1
                    failed_conids.append((row['conid'], row['ib_symbol']))
        else:
            # 并行处理模式
            lock = threading.Lock()

            def process_worker(row):
                nonlocal success_count, fail_count
                try:
                    if self.process_symbol(row):
                        with lock:
                            success_count += 1
                            successful_conids.append(row['conid'])
                    else:
                        with lock:
                            fail_count += 1
                            failed_conids.append((row['conid'], row['ib_symbol']))
                except Exception as e:
                    logger.error(f"处理标的时出错: {str(e)}\n{traceback.format_exc()}")
                    with lock:
                        fail_count += 1
                        failed_conids.append((row['conid'], row['ib_symbol']))

            # 创建线程池
            num_threads = min(self.process_threads, len(self.matched_df))  # 最多self.process_threads个线程
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
                # 提交所有任务
                futures = []
                for _, row in self.matched_df.iterrows():
                    # 检查是否收到终止信号
                    if is_terminating:
                        logger.warning("检测到终止信号，停止提交新任务...")
                        break
                    futures.append(executor.submit(process_worker, row))
                
                # 等待所有已提交任务完成
                for future in concurrent.futures.as_completed(futures):
                    # 只是等待任务完成，不提交新任务
                    pass

        # 等待所有数据保存完成
        self.database_saver.stop()

        logger.info(f"处理完成. 成功: {success_count}, 失败: {fail_count}")

        # 统一输出缺失复权因子的记录
        if self.missing_rehab_records:
            logger.warning(f"\n发现 {len(self.missing_rehab_records)} 个标的在数据区间内富途没有复权因子但FirstRate有复权因子:")
            for record in self.missing_rehab_records:
                logger.warning(f"  - {record['ib_symbol']} (conid: {record['conid']}, firstrate: {record['firstrate_symbol']})")
            
            # 保存缺失记录到CSV文件
            missing_df = pd.DataFrame(self.missing_rehab_records)
            missing_folder = "missing_rehab_factors"
            os.makedirs(missing_folder, exist_ok=True)
            csv_filename = os.path.join(missing_folder, f"missing_rehab_factors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            missing_df.to_csv(csv_filename, index=False)
            logger.warning(f"缺失复权因子的记录已保存到: {csv_filename}")

        # 如果指定了n或者收到了终止信号，输出成功的conid列表
        if n is not None or is_terminating:
            logger.info("成功的conid列表:")
            logger.info(f"  {', '.join(str(conid) for conid in successful_conids)}")

            # 合并成功和失败的conid
            all_conids = successful_conids + [conid for conid, _ in failed_conids]
            if all_conids:
                sql_content = self.generate_cleanup_sql(all_conids)
                sql_file = f"cleanup_to_{n if n is not None else 'interrupted'}.sql"
                with open(sql_file, "w") as f:
                    f.write(sql_content)
                logger.info(f"已生成SQL文件: {sql_file}")

        if failed_conids:
            logger.warning("失败的标的列表:")
            for conid, symbol in failed_conids:
                logger.warning(f"  - {symbol} (conid: {conid})")
            logger.warning("失败的conid列表（用于重试）:")
            logger.warning(f"--conid-list \"{','.join(str(conid) for conid, _ in failed_conids)}\"")

        return success_count, fail_count, successful_conids, [conid for conid, _ in failed_conids]

    def generate_cleanup_sql(self, conids: List[int]) -> str:
        """生成用于清理数据库的SQL语句

        Args:
            conids: 需要清理的conid列表

        Returns:
            str: SQL语句
        """
        if not conids:
            return ""

        # 生成SQL语句
        sql = []
        sql.append("-- 删除DbBarData表中的数据")
        sql.append("DELETE FROM dbbardata ")
        sql.append("WHERE (symbol IN (" + ",".join(f"'{conid}'" for conid in conids) + ")")

        # 添加LIKE条件
        like_conditions = [f"OR symbol LIKE '{conid}_%' " for conid in conids]
        sql.extend(like_conditions)
        sql.append(");")
        sql.append("")

        sql.append("-- 删除DbBarOverview表中的数据")
        sql.append("DELETE FROM dbbaroverview ")
        sql.append("WHERE (symbol IN (" + ",".join(f"'{conid}'" for conid in conids) + ")")

        # 添加LIKE条件
        sql.extend(like_conditions)
        sql.append(");")
        sql.append("")

        # 添加Python命令注释
        sql.append("'''")
        sql.append(f"python firstrate_preprocess.py -l \"{','.join(str(conid) for conid in conids)}\"")
        sql.append("'''")

        return "\n".join(sql)


app = typer.Typer()

@app.command()
def process(
    data_path: str = typer.Option(
        "full_1min",
        "--data-path",
        "-p",
        help="数据路径，可选 full_1min 或 month_1min"
    ),
    data_dir: str = typer.Option(
        "S:\\firstrate\\stock",
        "--data-dir",
        "-d",
        help="FirstRate数据目录"
    ),
    start_date: datetime = typer.Option(
        datetime(2024, 3, 1),
        "--start-date",
        "-s",
        help="开始日期，格式：YYYY-MM-DD，只处理该日期之后的数据",
        formats=["%Y-%m-%d"]
    ),
    end_date: datetime = typer.Option(
        datetime.now(),
        "--end-date",
        "-e",
        help="结束日期，格式：YYYY-MM-DD，只处理该日期及之前的数据",
        formats=["%Y-%m-%d"]
    ),
    start_conid: Optional[int] = typer.Option(
        None,
        "--start-conid",
        "-c",
        help="从指定的conid开始处理，用于断点恢复"
    ),
    n: Optional[int] = typer.Option(
        None,
        "--n",
        "-n",
        help="处理第1到n个conid（不包含第n个）"
    ),
    conid_list: Optional[str] = typer.Option(
        None,
        "--conid-list",
        "-l",
        help="要处理的conid列表，用逗号分隔，例如：95514904,548309229,162225735,705140936,662521562,150304986,252184470"
    ),
    ignore_overview: bool = typer.Option(
        False,
        "--ignore-overview",
        "-f",
        help="忽略数据库中的overview信息，强制全量覆盖"
    ),
    db_threads: int = typer.Option(
        10,
        "--db-threads",
        "-dt",
        help="数据库保存线程数，默认为10"
    ),
    process_threads: int = typer.Option(
        10,
        "--process-threads",
        "-pt",
        help="标的处理线程数，默认为10"
    ),
    use_database: bool = typer.Option(
        False,
        "--use-database",
        "-db",
        help="使用数据库模式加载数据，而不是本地文件"
    ),
    interval: str = typer.Option(
        "1m",
        "--interval",
        "-i",
        help="时间间隔，可选 1m（分钟线）或 d（日线）"
    ),
    database_name: Optional[str] = typer.Option(
        None,
        "--database",
        "-db-name",
        help="数据库名称，如果不指定则根据interval自动选择"
    ),
    use_contract_style: bool = typer.Option(
        True,
        "--use-contract-style",
        "-cs",
        help="是否使用合约式复权（以下一个复权日前一个交易日为后缀添加到symbol）"
    ),
    use_detailed_adjustment: bool = typer.Option(
        False,
        "--use-detailed-adjustment",
        "-da",
        help="是否使用细分项复权（区分股数变动和分红，只对分红进行合约式复权）"
    )
):
    """FirstRate数据预处理和复权工具"""
    log_file_name = os.path.basename(__file__).replace(".py", "_{time:YYYYMMDD}.log")
    logger.add(
        f"logs/{log_file_name}",
        level="INFO" if n is not None or conid_list is not None else 30,# TRACE 0, DEBUG 10, INFO 20, SUCCESS 25, WARNING 30, ERROR 40, CRITICAL 50
        format="{time} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="00:00",  # 每天零点切换新文件
    )
    try:
        # 获取最新的conid和firstrate_symbol匹配关系
        # 检查当前是否为交易日
        nyse = mcal.get_calendar('NYSE')
        now = datetime.now(ET_TZ)
        today = now.date()
        schedule = nyse.schedule(start_date=today, end_date=today)
        if schedule.empty:
            logger.warning(f"当前日期 {today} 不是交易日，程序退出")
            return
            
        # 直接使用Interval构造
        try:
            interval_enum = Interval(interval)
        except ValueError:
            logger.error(f"不支持的时间间隔: {interval}，可选值: 1m（分钟线）或 d（日线）")
            return

        # 创建处理器
        processor = FirstratePreprocessor(
            data_dir=data_dir,
            data_path=data_path,
            start_date=start_date.replace(tzinfo=ET_TZ),
            end_date=datetime.combine(end_date.date(), datetime.max.time()).replace(tzinfo=ET_TZ),
            db_threads=db_threads,
            process_threads=process_threads,
            ignore_overview=ignore_overview,
            use_database=use_database,
            interval=interval_enum,
            database_name=database_name,
            use_contract_style=use_contract_style,
            use_detailed_adjustment=use_detailed_adjustment
        )

        # 处理conid列表
        conid_list_to_process = None

        # 如果指定了conid_list，解析并处理
        if conid_list:
            try:
                conid_list_to_process = [int(conid.strip()) for conid in conid_list.split(',')]
                logger.info(f"将处理指定的conid列表: {conid_list_to_process}")
            except ValueError as e:
                logger.error(f"conid列表格式错误: {e}")
                return

        # 处理所有标的
        success, fail, successful_conids, failed_conids = processor.process_all(conid_list_to_process, start_conid, n)
        logger.info(f"处理完成. 成功: {success}, 失败: {fail}")

    except KeyboardInterrupt:
        logger.warning("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}\n{traceback.format_exc()}")
    finally:
        # 确保数据保存线程正确关闭
        if 'processor' in locals() and not is_terminating:
            # 只有在未收到终止信号时才调用stop，避免重复调用
            processor.database_saver.stop()

if __name__ == "__main__":
    app()
