-- frt无标的删除波动日及之前数据
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '793649350' AND datetime < '2025-08-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-05-16 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384917' AND datetime < '2024-11-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384917' AND datetime < '2022-10-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-05-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384917' AND datetime < '2022-10-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2023-05-27 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '380252552' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-07-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '739040161' AND datetime < '2025-07-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384922' AND datetime < '2024-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384917' AND datetime < '2023-01-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '443363787' AND datetime < '2023-07-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '75216051' AND datetime < '2024-08-20 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '381762240' AND datetime < '2024-05-22 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-12-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-07-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '68384917' AND datetime < '2024-01-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2023-06-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-06-21 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '626106921' AND datetime < '2024-09-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '766482473' AND datetime < '2025-03-11 12:00:00';

-- CTM 589635261
SET @multiplier = 20;
UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata
SET open_price = open_price * @multiplier,
    high_price = high_price * @multiplier,
    low_price = low_price * @multiplier,
    close_price = close_price * @multiplier,
    volume = volume / @multiplier
WHERE symbol = '589635261'
AND datetime < '2022-10-14 12:00:00';

-- IXHL	668803560
SET @multiplier = 4;
UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata
SET open_price = open_price * @multiplier,
    high_price = high_price * @multiplier,
    low_price = low_price * @multiplier,
    close_price = close_price * @multiplier,
    volume = volume / @multiplier
WHERE symbol = '668803560'
AND datetime < '2023-11-30 13:00:00';

-- PROP 	659764497 	无需处理
-- BMNR		785071865 	无需处理
-- BURU		717996868 	无需处理
-- BTX		479088068 	无需处理
-- GE		498843743 	无需处理
-- SYTA		751043413 	无需处理

-- FMST 758099200 ib重复应用了合股2次
SET @multiplier = 50;
UPDATE vnpy_stk_us_ib_d_2206_250814__.dbbardata
SET open_price = open_price / @multiplier,
    high_price = high_price / @multiplier,
    low_price = low_price / @multiplier,
    close_price = close_price / @multiplier,
    volume = volume * @multiplier
WHERE symbol = '758099200'
AND datetime < '2023-07-05 12:00:00';

-- 数据整合无前收数据，且异常：删除波动日及之前
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '640550564' AND datetime < '2023-07-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '797031797' AND datetime < '2023-09-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '598714460' AND datetime < '2023-03-21 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '598714460' AND datetime < '2023-02-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '598714460' AND datetime < '2024-01-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '598714460' AND datetime < '2024-10-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '599374254' AND datetime < '2022-10-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '348573506' AND datetime < '2022-10-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '348573506' AND datetime < '2022-10-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '569570290' AND datetime < '2022-09-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-06-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-04-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-03-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-02-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-12-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-08-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-09-20 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-02-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-08-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-09-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-03-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-11-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-12-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-06-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-02-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-07-26 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-07-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-11-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-01-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-09-17 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-03-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-02-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-01-18 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-10-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-03-21 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2023-02-16 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625545608' AND datetime < '2022-08-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '744396821' AND datetime < '2024-12-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '663892041' AND datetime < '2023-11-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '589635261' AND datetime < '2022-10-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '793105127' AND datetime < '2024-07-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '641104407' AND datetime < '2022-12-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '685258007' AND datetime < '2024-03-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '696511327' AND datetime < '2022-07-16 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '772196027' AND datetime < '2023-02-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '772196027' AND datetime < '2023-03-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '772196027' AND datetime < '2023-03-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-20 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-05 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-01-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2023-09-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-02-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '762274563' AND datetime < '2025-01-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '578561396' AND datetime < '2022-10-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '755606536' AND datetime < '2025-02-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '660293657' AND datetime < '2023-11-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '570106768' AND datetime < '2022-06-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '704415151' AND datetime < '2024-06-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '704415151' AND datetime < '2024-05-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-01-27 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-03-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-03-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-04-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-03-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-04-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-04-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-10-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-05-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-06-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-01-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-10-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-11-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-12-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-08-04 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-10-26 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-10-06 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-11-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2024-03-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2022-11-29 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2024-04-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2024-02-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-10-20 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625797633' AND datetime < '2023-05-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '631538926' AND datetime < '2023-06-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '631538926' AND datetime < '2023-06-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-05-17 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-04-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-05-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-05-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2022-06-09 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-04-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-04-26 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-04-27 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '552352795' AND datetime < '2023-07-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-04-22 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-04-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-07-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-05-17 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-06-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-06-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-04-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '799151668' AND datetime < '2025-06-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-06-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-05-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-04-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2024-10-10 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2023-04-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-05-22 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2024-03-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-05-21 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2023-03-08 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '794639097' AND datetime < '2025-04-29 12:00:00';
-- 数据整合有前收数据，均不一致：波动日及之前删除
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '708058620' AND datetime < '2025-08-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '325796753' AND datetime < '2025-07-17 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '775821309' AND datetime < '2025-03-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '775821309' AND datetime < '2025-02-19 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '775821309' AND datetime < '2025-01-30 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '647316948' AND datetime < '2023-08-12 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '647316948' AND datetime < '2023-06-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '625287723' AND datetime < '2023-06-02 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '773659680' AND datetime < '2023-03-16 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '773659680' AND datetime < '2023-03-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '493464130' AND datetime < '2023-01-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '10828168' AND datetime < '2023-01-25 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '589316218' AND datetime < '2022-08-13 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '795432422' AND datetime < '2022-08-09 12:00:00';
-- 数据迁移：从vnpy_stk_us_frt_d_2206_250814.dbbardata读取数据写入vnpy_stk_us_ib_d_2206_250814__.dbbardata
-- 先删除目标表中的旧数据
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '722312982' AND datetime < '2024-08-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '598077906' AND datetime < '2022-11-22 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '561196594' AND datetime < '2022-11-03 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '783906270' AND datetime < '2025-05-15 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '652827247' AND datetime < '2024-04-11 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '754087637' AND datetime < '2024-12-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '605523658' AND datetime < '2024-11-01 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '648081824' AND datetime < '2023-08-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '754087637' AND datetime < '2024-12-24 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '777704938' AND datetime < '2022-09-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '672767548' AND datetime < '2023-12-23 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '689982377' AND datetime < '2024-03-14 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '798837403' AND datetime < '2025-07-31 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '781675063' AND datetime < '2023-04-28 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '758099200' AND datetime < '2024-08-07 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '777704909' AND datetime < '2024-12-17 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '754087637' AND datetime < '2024-12-27 12:00:00';
DELETE FROM vnpy_stk_us_ib_d_2206_250814__.dbbardata WHERE symbol = '798837403' AND datetime < '2025-08-01 12:00:00';

-- 从源表读取数据插入到目标表，索引冲突时覆盖
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '722312982' AND datetime < '2024-08-15 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '598077906' AND datetime < '2022-11-22 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '561196594' AND datetime < '2022-11-03 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '783906270' AND datetime < '2025-05-15 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '652827247' AND datetime < '2024-04-11 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '754087637' AND datetime < '2024-12-31 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '605523658' AND datetime < '2024-11-01 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '648081824' AND datetime < '2023-08-24 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '754087637' AND datetime < '2024-12-24 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '777704938' AND datetime < '2022-09-14 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '672767548' AND datetime < '2023-12-23 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '689982377' AND datetime < '2024-03-14 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '798837403' AND datetime < '2025-07-31 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '781675063' AND datetime < '2023-04-28 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '758099200' AND datetime < '2024-08-07 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '777704909' AND datetime < '2024-12-17 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '754087637' AND datetime < '2024-12-27 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbardata 
(symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price)
SELECT symbol, exchange, datetime, `interval`, volume, turnover, open_interest, open_price, high_price, low_price, close_price
FROM vnpy_stk_us_frt_d_2206_250814.dbbardata
WHERE symbol = '798837403' AND datetime < '2025-08-01 12:00:00'
ON DUPLICATE KEY UPDATE
    exchange = VALUES(exchange),
    datetime = VALUES(datetime),
    `interval` = VALUES(`interval`),
    volume = VALUES(volume),
    turnover = VALUES(turnover),
    open_interest = VALUES(open_interest),
    open_price = VALUES(open_price),
    high_price = VALUES(high_price),
    low_price = VALUES(low_price),
    close_price = VALUES(close_price);

-- 重置overview
INSERT INTO vnpy_stk_us_ib_d_2206_250814__.dbbaroverview (symbol, exchange, `interval`, `count`, `start`, `end`)
            SELECT
                symbol,
                exchange,
                `interval`,
                COUNT(id) AS `count`,
                MIN(datetime) AS `start`,
                MAX(datetime) AS `end`
            FROM
                vnpy_stk_us_ib_d_2206_250814__.dbbardata
            GROUP BY
                symbol, exchange, `interval`
            ON DUPLICATE KEY UPDATE
                `count` = VALUES(`count`),
                `start` = VALUES(`start`),
                `end` = VALUES(`end`);