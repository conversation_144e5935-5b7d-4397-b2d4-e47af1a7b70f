from datetime import datetime
from . import db
from .logConf import logger

class TradeInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True, index=True)
    symbol = db.Column(db.String(50))
    author = db.Column(db.String(100), nullable=False)
    direction = db.Column(db.String(50))
    offset = db.Column(db.String(50))
    price = db.Column(db.Float)
    volume = db.Column(db.Float)
    create_date = db.Column(db.DateTime)
    datetime = db.Column(db.DateTime)
    pnl = db.Column(db.Float)
    remarks = db.Column(db.String(200))
    strategy_name = db.Column(db.String(50))
    # symbol、offset、datetime、strategy_name四个字段组成唯一索引
    __table_args__ = (
        db.UniqueConstraint('symbol', 'direction', 'offset', 'datetime', 'strategy_name',
                            name='uix_symbol_direction_offset_datetime_strategy_name'),
    )

    def __init__(self, symbol, author, direction, offset, price, volume, create_date, datetime, pnl, remarks,
                 strategy_name):
        self.symbol = symbol
        self.author = author
        self.direction = direction
        self.offset = offset
        self.price = price
        self.volume = volume
        self.create_date = create_date
        self.datetime = datetime
        self.pnl = pnl
        self.remarks = remarks
        self.strategy_name = strategy_name

    def to_json(self):
        json_post = {
            'id': self.id,
            'symbol': self.symbol,
            'author': self.author,
            'direction': self.direction,
            'offset': self.offset,
            'price': self.price,
            'volume': self.volume,
            # 'create_date': self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else None,
            'create_date': self.create_date,
            # 'datetime': self.datetime.strftime('%Y-%m-%d %H:%M:%S') if self.datetime else None,
            'datetime': self.datetime,
            'pnl': self.pnl,
            'remarks': self.remarks,
            'strategy_name': self.strategy_name
        }
        return json_post

    @staticmethod
    def from_json(data):
        return TradeInfo(
            symbol=data.get('symbol'),
            author=data.get('author'),
            direction=data.get('direction'),
            offset=data.get('offset'),
            price=data.get('price'),
            volume=data.get('volume'),
            create_date=datetime.strptime(data.get('create_date'), '%Y-%m-%d %H:%M:%S') if data.get('create_date') else None,
            datetime=datetime.strptime(data.get('datetime'), '%Y-%m-%d %H:%M:%S') if data.get('datetime') else None,
            pnl=data.get('pnl'),
            remarks=data.get('remarks'),
            strategy_name=data.get('strategy_name')
        )
